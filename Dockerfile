# user cd rentreport, 确保build文件夹存在，而且没被dockerignored
FROM alpine:latest
# 安装 bash，解决 /bin/bash not found 的问题
# apk add --no-cache 会安装包并且不保留缓存，以保持镜像体积小
RUN apk add --no-cache bash

RUN addgroup -S appgroup && adduser -S -u 1100 -G appgroup appuser

WORKDIR /app
RUN mkdir -p /app/uploads /app/configs /app/web/dist /app/logs /app/docs \
  && mkdir -p /tmp/uploads/ \
  && chown -R appuser:appgroup /tmp/uploads/ /app \
  && chmod -R 755 /tmp

# 复制所有脚本
COPY build/bin/ /app/bin/
COPY scripts/ /app/scripts/
COPY configs/ /app/configs/
#COPY docs/ /app/docs/

RUN chown -R appuser:appgroup /app \
  && chmod +x /app/bin/* /app/scripts/*

USER appuser

# 默认不执行任务，等 CronJob 覆盖 command/args
<PERSON><PERSON> ["sleep", "3600"]

