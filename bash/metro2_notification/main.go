package main

import (
	"context"
	"fmt"
	"os"
	"rent_report/entities"
	"rent_report/utils"
	"rent_report/utils/encryption"
	"strings"

	"github.com/real-rm/goconfig"
	"github.com/real-rm/golog"
	"github.com/real-rm/gomongo"
)

func main() {
	// Set default config file if none specified
	if os.Getenv("RMBASE_FILE_CFG") == "" {
		os.Setenv("RMBASE_FILE_CFG", "configs/local.ini")
	}

	// Load configuration
	if err := goconfig.LoadConfig(); err != nil {
		os.Exit(1)
	}

	// Initialize logging
	if err := golog.InitLog(); err != nil {
		os.Exit(1)
	}

	// Initialize MongoDB connection
	if err := gomongo.InitMongoDB(); err != nil {
		os.Exit(1)
	}

	// Initialize encryption
	if err := encryption.InitEncryption(); err != nil {
		os.Exit(1)
	}

	// Run Metro2 notification processing
	if err := runMetro2NotificationProcessing(); err != nil {
		os.Exit(1)
	}

}

// Metro2NotificationConfig Metro2通知配置
type Metro2NotificationConfig struct {
	Enabled                bool
	DelayHours             int
	DelayMinutes           int
	ProcessIntervalMinutes int
	CleanupDays            int
}

// getMetro2NotificationConfig 获取Metro2通知配置
func getMetro2NotificationConfig() Metro2NotificationConfig {
	config := Metro2NotificationConfig{
		Enabled:                true,
		DelayHours:             24,
		DelayMinutes:           0,
		ProcessIntervalMinutes: 60,
		CleanupDays:            30,
	}

	// 读取配置
	if enabled := goconfig.Config("metro2NotificationEmail.enabled"); enabled != nil {
		if enabledBool, ok := enabled.(bool); ok {
			config.Enabled = enabledBool
		}
	}

	if delayHours := goconfig.Config("metro2NotificationEmail.delayHours"); delayHours != nil {
		if delayInt, ok := delayHours.(int); ok {
			config.DelayHours = delayInt
		} else if delayInt64, ok := delayHours.(int64); ok {
			config.DelayHours = int(delayInt64)
		}
	}

	if delayMinutes := goconfig.Config("metro2NotificationEmail.delayMinutes"); delayMinutes != nil {
		if delayInt, ok := delayMinutes.(int); ok {
			config.DelayMinutes = delayInt
		} else if delayInt64, ok := delayMinutes.(int64); ok {
			config.DelayMinutes = int(delayInt64)
		}
	}

	if processInterval := goconfig.Config("metro2NotificationEmail.processIntervalMinutes"); processInterval != nil {
		if intervalInt, ok := processInterval.(int); ok {
			config.ProcessIntervalMinutes = intervalInt
		} else if intervalInt64, ok := processInterval.(int64); ok {
			config.ProcessIntervalMinutes = int(intervalInt64)
		}
	}

	if cleanupDays := goconfig.Config("metro2NotificationEmail.cleanupDays"); cleanupDays != nil {
		if daysInt, ok := cleanupDays.(int); ok {
			config.CleanupDays = daysInt
		} else if daysInt64, ok := cleanupDays.(int64); ok {
			config.CleanupDays = int(daysInt64)
		}
	}

	return config
}

// runMetro2NotificationProcessing 运行Metro2通知处理
func runMetro2NotificationProcessing() error {
	config := getMetro2NotificationConfig()

	if !config.Enabled {
		golog.Info("Metro2 notification scheduler is disabled")
		return nil
	}

	golog.Info("Processing Metro2 notification tasks",
		"delayHours", config.DelayHours,
		"delayMinutes", config.DelayMinutes,
		"processIntervalMinutes", config.ProcessIntervalMinutes,
		"cleanupDays", config.CleanupDays)

	// 处理Metro2通知任务
	if err := processMetro2NotificationTasks(config); err != nil {
		return fmt.Errorf("failed to process Metro2 notification tasks: %v", err)
	}

	// 执行清理任务
	if err := cleanupOldMetro2NotificationTasks(config); err != nil {
		golog.Error("Failed to cleanup old Metro2 notification tasks", "error", err)
		// 清理失败不影响主要处理流程
	}

	golog.Info("Metro2 notification CLI completed successfully")
	return nil
}

// processMetro2NotificationTasks 处理Metro2通知任务
func processMetro2NotificationTasks(config Metro2NotificationConfig) error {
	ctx := context.Background()

	golog.Info("Processing Metro2 notification tasks")

	// 获取待执行的任务
	tasks, err := entities.GetPendingMetro2NotificationTasks(ctx)
	if err != nil {
		return fmt.Errorf("failed to get pending Metro2 notification tasks: %v", err)
	}

	golog.Info("Retrieved Metro2 notification tasks", "count", len(tasks))

	if len(tasks) == 0 {
		golog.Info("No pending Metro2 notification tasks found")
		return nil
	}

	golog.Info("Found pending Metro2 notification tasks", "count", len(tasks))

	// 处理每个任务
	for _, task := range tasks {
		err := processMetro2NotificationTask(ctx, &task)
		if err != nil {
			golog.Error("Failed to process Metro2 notification task",
				"taskId", task.ID,
				"metro2GenerationId", task.Metro2GenerationID,
				"reportMonth", task.ReportMonth,
				"error", err)

			// 标记任务完成但记录错误
			task.MarkCompleted(ctx, 0, err.Error())
		}
	}

	golog.Info("Completed Metro2 notification tasks processing", "processedCount", len(tasks))
	return nil
}

// cleanupOldMetro2NotificationTasks 清理旧的Metro2通知任务
func cleanupOldMetro2NotificationTasks(config Metro2NotificationConfig) error {
	ctx := context.Background()
	err := entities.CleanupOldMetro2NotificationTasks(ctx, config.CleanupDays)
	if err != nil {
		return err
	}
	golog.Info("Completed Metro2 notification tasks cleanup", "cleanupDays", config.CleanupDays)
	return nil
}

// processMetro2NotificationTask 处理单个Metro2通知任务
func processMetro2NotificationTask(ctx context.Context, task *entities.Metro2NotificationTask) error {
	golog.Info("Processing Metro2 notification task",
		"taskId", task.ID,
		"metro2GenerationId", task.Metro2GenerationID,
		"reportMonth", task.ReportMonth,
		"scheduledTime", task.ScheduledTime)

	// 获取Metro2生成日志
	generationLog, err := entities.GetMetro2GenerationLogByID(ctx, task.Metro2GenerationID)
	if err != nil {
		return fmt.Errorf("failed to get Metro2 generation log: %v", err)
	}

	// 获取涉及的有欠款租约和租户（已按房东分组）
	tenantsByLandlord, landlordInfoMap, err := getTenantsWithDebtFromMetro2Report(ctx, generationLog)
	if err != nil {
		return fmt.Errorf("failed to get tenants with debt: %v", err)
	}

	if len(tenantsByLandlord) == 0 {
		golog.Info("No tenants with debt found for Metro2 notification",
			"metro2GenerationId", task.Metro2GenerationID,
			"reportMonth", task.ReportMonth)

		// 标记任务完成
		return task.MarkCompleted(ctx, 0, "")
	}

	// 直接遍历已分组的租户发送邮件
	emailsSent := 0
	for landlordId, landlordTenants := range tenantsByLandlord {
		landlordInfo := landlordInfoMap[landlordId]

		// 发送邮件 - 使用同步方式等待完成
		actualSentCount := sendMetro2NotificationEmailsSync(landlordTenants, landlordInfo, task.ReportMonth)
		emailsSent += actualSentCount

		golog.Info("Sent Metro2 reported notification emails",
			"landlordId", landlordId,
			"tenantCount", len(landlordTenants),
			"reportMonth", task.ReportMonth)
	}

	// 标记任务完成
	err = task.MarkCompleted(ctx, emailsSent, "")
	if err != nil {
		golog.Error("Failed to mark Metro2 notification task as completed",
			"taskId", task.ID,
			"error", err)
		return err
	}

	golog.Info("Completed Metro2 notification task",
		"taskId", task.ID,
		"emailsSent", emailsSent,
		"reportMonth", task.ReportMonth)

	return nil
}

// getTenantsWithDebtFromMetro2Report 从Metro2报告中获取有欠款的租户，按房东分组
func getTenantsWithDebtFromMetro2Report(ctx context.Context, generationLog *entities.Metro2GenerationLog) (map[string][]utils.Metro2TenantEmailInfo, map[string]utils.LandlordInfo, error) {
	tenantsByLandlord := make(map[string][]utils.Metro2TenantEmailInfo)
	landlordInfoMap := make(map[string]utils.LandlordInfo)

	// 遍历处理的租约
	golog.Info("Processing Metro2 generation log",
		"totalProcessedLeases", len(generationLog.ProcessedLeases))

	for _, leaseInfo := range generationLog.ProcessedLeases {

		// 只处理有欠款的租约
		if leaseInfo.CurrentBalance <= 0 {
			continue
		}

		// 获取租约详细信息 - 使用管理员权限绕过权限检查
		lease, err := entities.GetLease(ctx, leaseInfo.LeaseID, "")
		if err != nil {
			golog.Error("Failed to get lease for Metro2 notification",
				"leaseId", leaseInfo.LeaseID,
				"error", err)
			continue
		}

		// 获取房东信息（如果还没有获取过）
		if _, exists := landlordInfoMap[lease.UserID]; !exists {
			landlordInfo, err := getLandlordInfoForMetro2(ctx, lease.UserID, lease.PropertyID)
			if err != nil {
				golog.Error("Failed to get landlord info for Metro2 notification",
					"landlordId", lease.UserID,
					"propertyId", lease.PropertyID,
					"error", err)
				continue
			}
			landlordInfoMap[lease.UserID] = landlordInfo
		}

		// 获取当前租户信息，直接按房东分组
		for _, tenant := range lease.CurrentTenants {
			if tenant.Email != "" && tenant.FirstName != "" {
				// 格式化租金和余额
				rentAmount := fmt.Sprintf("%.2f", lease.RentAmount)
				remainingBalance := fmt.Sprintf("$%.2f", leaseInfo.CurrentBalance)

				tenantInfo := utils.Metro2TenantEmailInfo{
					Email:            tenant.Email,
					FirstName:        tenant.FirstName,
					RentAmount:       rentAmount,
					RemainingBalance: remainingBalance,
				}

				// 直接按房东ID分组
				tenantsByLandlord[lease.UserID] = append(tenantsByLandlord[lease.UserID], tenantInfo)
			}
		}
	}

	return tenantsByLandlord, landlordInfoMap, nil
}

// getLandlordInfoForMetro2 获取房东信息用于Metro2通知
func getLandlordInfoForMetro2(ctx context.Context, landlordId, propertyId string) (utils.LandlordInfo, error) {
	// 获取房东信息
	user, err := entities.GetUserByID(ctx, landlordId)
	if err != nil {
		return utils.LandlordInfo{}, fmt.Errorf("failed to get user: %v", err)
	}

	// 获取属性信息
	property, err := entities.GetPropertyByID(ctx, propertyId, landlordId)
	if err != nil {
		return utils.LandlordInfo{}, fmt.Errorf("failed to get property: %v", err)
	}

	// 构建地址字符串
	address := fmt.Sprintf("%s", property.Name)
	if property.Address.Street != "" {
		address = fmt.Sprintf("%s, %s", property.Name, property.Address.Street)
		if property.Address.City != "" {
			address = fmt.Sprintf("%s, %s", address, property.Address.City)
		}
	}

	// 从用户名中解析姓名（用户名格式通常是 "FirstName LastName"）
	firstName := ""
	lastName := ""
	if user.Username != "" {
		parts := strings.Fields(user.Username)
		if len(parts) >= 1 {
			firstName = parts[0]
		}
		if len(parts) >= 2 {
			lastName = strings.Join(parts[1:], " ")
		}
	}

	return utils.LandlordInfo{
		FirstName:       firstName,
		LastName:        lastName,
		PropertyAddress: address,
	}, nil
}

// sendMetro2NotificationEmailsSync 同步发送Metro2通知邮件，返回成功发送的邮件数量
func sendMetro2NotificationEmailsSync(tenants []utils.Metro2TenantEmailInfo, landlordInfo utils.LandlordInfo, reportMonth string) int {
	if len(tenants) == 0 {
		golog.Info("No tenants to send Metro2 notification emails to")
		return 0
	}

	landlordFullName := fmt.Sprintf("%s %s", landlordInfo.FirstName, landlordInfo.LastName)
	fmt.Printf("DEBUG: Starting sync Metro2 notification email sending for %d tenants to landlord %s (month: %s)\n",
		len(tenants), landlordFullName, reportMonth)

	golog.Info("Starting sync Metro2 notification email sending",
		"tenantCount", len(tenants),
		"landlordName", landlordFullName,
		"reportMonth", reportMonth)

	successCount := 0
	errorCount := 0

	for i, tenant := range tenants {
		fmt.Printf("DEBUG: Sending Metro2 email %d/%d to %s (%s)\n",
			i+1, len(tenants), tenant.FirstName, tenant.Email)

		golog.Info("Sending Metro2 notification email",
			"index", i+1,
			"firstName", tenant.FirstName,
			"email", tenant.Email,
			"rentAmount", tenant.RentAmount,
			"remainingBalance", tenant.RemainingBalance,
			"reportMonth", reportMonth)

		// 发送邮件
		err := utils.SendMetro2ReportedNotificationEmail(tenant.Email, tenant.FirstName, landlordFullName,
			landlordInfo.PropertyAddress, reportMonth, tenant.RentAmount, tenant.RemainingBalance)
		if err != nil {
			fmt.Printf("DEBUG: Failed to send Metro2 email to %s: %v\n", tenant.Email, err)
			golog.Error("Failed to send Metro2 notification email", "firstName", tenant.FirstName, "email", tenant.Email, "reportMonth", reportMonth, "error", err)
			errorCount++
		} else {
			fmt.Printf("DEBUG: Successfully sent Metro2 email to %s\n", tenant.Email)
			golog.Info("Successfully sent Metro2 notification email", "firstName", tenant.FirstName, "email", tenant.Email, "reportMonth", reportMonth)
			successCount++
		}
	}

	fmt.Printf("DEBUG: Completed Metro2 email sending - Success: %d, Errors: %d\n", successCount, errorCount)
	golog.Info("Completed sending Metro2 notification emails",
		"tenantCount", len(tenants),
		"successCount", successCount,
		"errorCount", errorCount,
		"landlordName", landlordFullName,
		"reportMonth", reportMonth)

	// 返回成功发送的邮件数量
	return successCount
}
