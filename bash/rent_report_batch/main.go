package main

import (
	"context"
	"fmt"
	"os"
	"rent_report/entities"
	"rent_report/utils"
	"time"

	"github.com/real-rm/goconfig"
	"github.com/real-rm/golog"
	"github.com/real-rm/gomongo"
)

func main() {
	// Set default config file if none specified
	if os.Getenv("RMBASE_FILE_CFG") == "" {
		os.Setenv("RMBASE_FILE_CFG", "configs/local.ini")
	}

	// Load configuration
	if err := goconfig.LoadConfig(); err != nil {
		os.Exit(1)
	}

	// Initialize logging
	if err := golog.InitLog(); err != nil {
		os.Exit(1)
	}

	// Initialize MongoDB
	err := gomongo.InitMongoDB()
	if err != nil {
		os.Exit(1)
	}

	// Run rent report email batch processing
	golog.Info("Starting rent report email batch CLI execution...")

	if err := runRentReportEmailBatch(); err != nil {
		golog.Error("Rent report email batch CLI failed", "error", err)
		os.Exit(1)
	}

	golog.Info("Rent report email batch CLI completed successfully")
}

// runRentReportEmailBatch 运行租金报告邮件批处理
func runRentReportEmailBatch() error {
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Minute)
	defer cancel()

	// 获取配置参数，使用默认值
	processWindowMinutes := 120 // 默认处理前120分钟（2小时）的记录
	if val := goconfig.Config("rentReportEmailBatch.processWindowMinutes"); val != nil {
		if windowInt, ok := val.(int); ok {
			processWindowMinutes = windowInt
		}
	}

	golog.Info("Processing rent report email batch", "windowMinutes", processWindowMinutes)

	// 获取未处理的记录
	records, err := entities.GetUnprocessedRentReportEmailQueue(ctx, processWindowMinutes)
	if err != nil {
		return fmt.Errorf("failed to get unprocessed records: %v", err)
	}

	if len(records) == 0 {
		golog.Info("No unprocessed rent report email records found")
		return nil
	}

	golog.Info("Found unprocessed rent report email records", "count", len(records))

	// 处理每个记录
	successCount := 0
	for _, record := range records {
		err := processEmailRecord(ctx, &record)
		if err != nil {
			golog.Error("Failed to process email record",
				"error", err,
				"recordId", record.ID,
				"tenantId", record.TntId,
				"tenantEmail", record.TntEmail)
			continue
		}

		// 标记为已处理
		err = entities.MarkRentReportEmailQueueProcessed(ctx, record.ID)
		if err != nil {
			golog.Error("Failed to mark record as processed",
				"error", err,
				"recordId", record.ID)
			continue
		}

		successCount++
	}

	golog.Info("Completed rent report email batch processing",
		"totalRecords", len(records),
		"successCount", successCount,
		"failedCount", len(records)-successCount)

	// 执行清理操作
	cleanupDays := 7 // 默认清理7天前的记录
	if val := goconfig.Config("rentReportEmailBatch.cleanupDays"); val != nil {
		if cleanupInt, ok := val.(int); ok {
			cleanupDays = cleanupInt
		}
	}

	err = entities.CleanupOldRentReportEmailQueue(ctx, cleanupDays)
	if err != nil {
		golog.Error("Failed to cleanup old records", "error", err)
		// 不返回错误，清理失败不影响主要功能
	} else {
		golog.Info("Completed cleanup of old rent report email records", "cleanupDays", cleanupDays)
	}

	return nil
}

// processEmailRecord 处理单个邮件记录
func processEmailRecord(ctx context.Context, record *entities.RentReportEmailQueue) error {
	golog.Info("Processing email record",
		"recordId", record.ID,
		"tenantId", record.TntId,
		"tenantEmail", record.TntEmail,
		"initialStatus", record.InitialStatus,
		"finalStatus", record.FinalStatus)

	// 检查状态变化是否需要发送邮件
	if record.InitialStatus == record.FinalStatus {
		golog.Info("No status change, skipping email",
			"status", record.FinalStatus,
			"tenantId", record.TntId)
		return nil
	}

	// 根据最终状态发送邮件
	switch record.FinalStatus {
	case "active":
		return sendRentReportingEnabledEmail(ctx, record)
	case "inactive":
		return sendRentReportingPausedEmail(ctx, record)
	default:
		golog.Warn("Unknown final status, skipping email",
			"finalStatus", record.FinalStatus,
			"tenantId", record.TntId)
		return nil
	}
}

// sendRentReportingEnabledEmail 发送租金报告启用邮件
func sendRentReportingEnabledEmail(ctx context.Context, record *entities.RentReportEmailQueue) error {
	// 获取房东和属性信息
	landlordInfo, err := getLandlordInfo(ctx, record.LandlordId, record.PropId)
	if err != nil {
		return fmt.Errorf("failed to get landlord info: %v", err)
	}

	// 准备租户邮件信息
	tenants := []utils.TenantEmailInfo{
		{
			Email:     record.TntEmail,
			FirstName: record.TntFirstName,
		},
	}

	// 发送启用邮件 - 使用同步方式等待完成
	fmt.Printf("DEBUG: Sending rent reporting enabled email to %s (%s)\n", record.TntFirstName, record.TntEmail)
	err = sendRentReportingNotificationEmailsSync(tenants, landlordInfo)
	if err != nil {
		fmt.Printf("DEBUG: Failed to send enabled email: %v\n", err)
		return fmt.Errorf("failed to send enabled email: %v", err)
	}

	fmt.Printf("DEBUG: Successfully sent rent reporting enabled email to %s\n", record.TntEmail)
	golog.Info("Sent rent reporting enabled email",
		"tenantEmail", record.TntEmail,
		"tenantId", record.TntId,
		"leaseId", record.LeaseId)

	return nil
}

// sendRentReportingPausedEmail 发送租金报告暂停邮件
func sendRentReportingPausedEmail(ctx context.Context, record *entities.RentReportEmailQueue) error {
	// 获取房东和属性信息
	landlordInfo, err := getLandlordInfo(ctx, record.LandlordId, record.PropId)
	if err != nil {
		return fmt.Errorf("failed to get landlord info: %v", err)
	}

	// 准备租户邮件信息
	tenants := []utils.TenantEmailInfo{
		{
			Email:     record.TntEmail,
			FirstName: record.TntFirstName,
		},
	}

	// 发送暂停邮件 - 使用同步方式等待完成
	fmt.Printf("DEBUG: Sending rent reporting paused email to %s (%s)\n", record.TntFirstName, record.TntEmail)
	err = sendRentReportingPausedNotificationEmailsSync(tenants, landlordInfo)
	if err != nil {
		fmt.Printf("DEBUG: Failed to send paused email: %v\n", err)
		return fmt.Errorf("failed to send paused email: %v", err)
	}

	fmt.Printf("DEBUG: Successfully sent rent reporting paused email to %s\n", record.TntEmail)
	golog.Info("Sent rent reporting paused email",
		"tenantEmail", record.TntEmail,
		"tenantId", record.TntId,
		"leaseId", record.LeaseId)

	return nil
}

// getLandlordInfo 获取房东信息
func getLandlordInfo(ctx context.Context, landlordId, propId string) (utils.LandlordInfo, error) {
	// 获取房东信息
	user, err := entities.GetUserByID(ctx, landlordId)
	if err != nil {
		return utils.LandlordInfo{}, fmt.Errorf("failed to get landlord: %v", err)
	}

	// 获取属性信息
	property, err := entities.GetProperty(ctx, propId, landlordId)
	if err != nil {
		golog.Error("Failed to get property info", "error", err, "propertyId", propId)
		// 不返回错误，使用默认值
	}

	// 构建地址字符串
	propertyAddress := "your property"
	if property != nil {
		propertyAddress = property.Name
		if property.Address.Street != "" {
			propertyAddress = fmt.Sprintf("%s, %s", property.Name, property.Address.Street)
			if property.Address.City != "" {
				propertyAddress = fmt.Sprintf("%s, %s", propertyAddress, property.Address.City)
			}
		}
	}

	return utils.LandlordInfo{
		FirstName:       user.Username,
		LastName:        "",
		PropertyAddress: propertyAddress,
	}, nil
}

// sendRentReportingNotificationEmailsSync 同步发送租金报告启用通知邮件
func sendRentReportingNotificationEmailsSync(tenants []utils.TenantEmailInfo, landlordInfo utils.LandlordInfo) error {
	if len(tenants) == 0 {
		golog.Info("No tenants to send rent reporting enabled emails to")
		return nil
	}

	landlordFullName := landlordInfo.FirstName
	if landlordInfo.LastName != "" {
		landlordFullName = fmt.Sprintf("%s %s", landlordInfo.FirstName, landlordInfo.LastName)
	}

	fmt.Printf("DEBUG: Starting sync rent reporting enabled email sending for %d tenants to landlord %s\n",
		len(tenants), landlordFullName)

	golog.Info("Starting sync rent reporting enabled email sending",
		"tenantCount", len(tenants),
		"landlordName", landlordFullName)

	successCount := 0
	errorCount := 0

	for i, tenant := range tenants {
		fmt.Printf("DEBUG: Sending enabled email %d/%d to %s (%s)\n",
			i+1, len(tenants), tenant.FirstName, tenant.Email)

		golog.Info("Sending rent reporting enabled email",
			"index", i+1,
			"firstName", tenant.FirstName,
			"email", tenant.Email)

		// 发送邮件
		err := utils.SendRentReportingNotificationEmail(tenant.Email, tenant.FirstName, landlordInfo.FirstName, landlordInfo.LastName, landlordInfo.PropertyAddress)
		if err != nil {
			fmt.Printf("DEBUG: Failed to send enabled email to %s: %v\n", tenant.Email, err)
			golog.Error("Failed to send rent reporting enabled email", "firstName", tenant.FirstName, "email", tenant.Email, "error", err)
			errorCount++
		} else {
			fmt.Printf("DEBUG: Successfully sent enabled email to %s\n", tenant.Email)
			golog.Info("Successfully sent rent reporting enabled email", "firstName", tenant.FirstName, "email", tenant.Email)
			successCount++
		}
	}

	fmt.Printf("DEBUG: Completed enabled email sending - Success: %d, Errors: %d\n", successCount, errorCount)
	golog.Info("Completed sending rent reporting enabled emails",
		"tenantCount", len(tenants),
		"successCount", successCount,
		"errorCount", errorCount,
		"landlordName", landlordFullName)

	if errorCount > 0 {
		return fmt.Errorf("failed to send %d out of %d enabled emails", errorCount, len(tenants))
	}
	return nil
}

// sendRentReportingPausedNotificationEmailsSync 同步发送租金报告暂停通知邮件
func sendRentReportingPausedNotificationEmailsSync(tenants []utils.TenantEmailInfo, landlordInfo utils.LandlordInfo) error {
	if len(tenants) == 0 {
		golog.Info("No tenants to send rent reporting paused emails to")
		return nil
	}

	landlordFullName := landlordInfo.FirstName
	if landlordInfo.LastName != "" {
		landlordFullName = fmt.Sprintf("%s %s", landlordInfo.FirstName, landlordInfo.LastName)
	}

	fmt.Printf("DEBUG: Starting sync rent reporting paused email sending for %d tenants to landlord %s\n",
		len(tenants), landlordFullName)

	golog.Info("Starting sync rent reporting paused email sending",
		"tenantCount", len(tenants),
		"landlordName", landlordFullName)

	successCount := 0
	errorCount := 0

	for i, tenant := range tenants {
		fmt.Printf("DEBUG: Sending paused email %d/%d to %s (%s)\n",
			i+1, len(tenants), tenant.FirstName, tenant.Email)

		golog.Info("Sending rent reporting paused email",
			"index", i+1,
			"firstName", tenant.FirstName,
			"email", tenant.Email)

		// 发送邮件
		err := utils.SendRentReportingPausedNotificationEmail(tenant.Email, tenant.FirstName, landlordFullName, landlordInfo.PropertyAddress)
		if err != nil {
			fmt.Printf("DEBUG: Failed to send paused email to %s: %v\n", tenant.Email, err)
			golog.Error("Failed to send rent reporting paused email", "firstName", tenant.FirstName, "email", tenant.Email, "error", err)
			errorCount++
		} else {
			fmt.Printf("DEBUG: Successfully sent paused email to %s\n", tenant.Email)
			golog.Info("Successfully sent rent reporting paused email", "firstName", tenant.FirstName, "email", tenant.Email)
			successCount++
		}
	}

	fmt.Printf("DEBUG: Completed paused email sending - Success: %d, Errors: %d\n", successCount, errorCount)
	golog.Info("Completed sending rent reporting paused emails",
		"tenantCount", len(tenants),
		"successCount", successCount,
		"errorCount", errorCount,
		"landlordName", landlordFullName)

	if errorCount > 0 {
		return fmt.Errorf("failed to send %d out of %d paused emails", errorCount, len(tenants))
	}
	return nil
}
