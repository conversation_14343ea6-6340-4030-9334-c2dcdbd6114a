# Rent Report Application Configuration (TOML format)
# This file contains all configuration for the application including goupload

# Database configuration
[dbs]
verbose = 3

[dbs.rr]
uri = "mongodb://r1:r1@************:27017/rr?authSource=admin&replicaSet=rs0&readPreference=nearest&minPoolSize=1&maxPoolSize=20&tls=false"

[dbs.rr_test]
uri = "mongodb://r1:r1@************:27017/rr?authSource=admin&replicaSet=rs0&readPreference=nearest&minPoolSize=1&maxPoolSize=20&tls=false"

[dbs.tmp]
uri = "mongodb://r1:r1@************:27017/rr?authSource=admin&replicaSet=rs0&readPreference=nearest&minPoolSize=1&maxPoolSize=20&tls=false"

# Server configuration
[server]
port = "8089"
baseUrl = "http://localhost:8089"
static_path = "/home/<USER>/go-repos/rent_report/build/web/dist"
serve_static = true
developer_mode = true

# Logging configuration
[golog]
dir = "/home/<USER>/go-repos/rent_report/logs"
level = "debug"
verbose = "verbose.log"
info = "info.log"
error = "error.log"
format = "text"
standard_output = true

# OAuth client configuration
[oauthClient]
clientId = "report_rentals"
clientSecret = "b9lzajneH2MiTrUeRzaSyCzCBgaRfib29lzajna"
redirectUrl = "http://localhost:8089/v1/auth/oauth/callback"
authUrl = "https://d8w.realmaster.com/provider/authorize"
tokenUrl = "https://d8w.realmaster.com/provider/token"
userInfoUrl = "https://d8w.realmaster.com/provider/userinfo"

# Encryption configuration
[encryption]
key = "b9lzajneH2MiTrUeRzaSyCzCBgaRfib2"

# Authentication configuration
[auth]
jwtSecret = "rUeRzaSyCzCBgaRfib2b9lzajneH2MiT"

# Stripe configuration
[stripe]
secret_key = "sk_test_51RYw3ERdRW2qyPyrKO4b5ZdEr5hyioKB7XIhytmyfoYx1yDx8I9b42sSemwBPmiL3pCfEcYXo2NvhECvTGPcpVCR00pCRBwLIF"
webhook_secret = "whsec_3ab1b9d27a7fc917f2b599da1a518d34b563c7351bd828b641a71156375dd116"

# Auto payment configuration
[autoPayment]
enabled = true
executionHour = 1
maxRetries = 3

# Pre-defined collections for gomail logging
[preDefColls.mailLog]
collName = "mail_log"
dbName = "tmp"

[preDefColls.mailLog.options]
expireAfterSeconds = 2592000

# Mail engine configuration
[mailEngine.smtp]
service = "gmail"

[mailEngine.gmail]
service = "gmail"
from = "Real Master Info <<EMAIL>>"
defaultEmail = "<EMAIL>"

[mailEngine.gmail.auth]
user = "<EMAIL>"
pass = "3GQi18O5H80zbjh3"
host = "cac1.realmaster.me"
port = 587

[mailEngine.ses]
region = "us-east-1"
accessKeyId = "placeholder"
secretAccessKey = "placeholder"

[mailEngine.sesH]
region = "us-east-1"
accessKeyId = "placeholder"
secretAccessKey = "placeholder"

[mailEngine.rmMail]
defaultEmail = "<EMAIL>"
url = "https://ml1.realmaster.cc/send"

[mailEngine.mockMail]
mock = false
verbose = 1

# 租金报告邮件批处理配置
[rentReportEmailBatch]
enabled = true
processIntervalMinutes = 3
processWindowMinutes = 2
cleanupDays = 7

# Metro2上报通知邮件配置
[metro2NotificationEmail]
enabled = true
delayHours = 0
delayMinutes = 1
processIntervalMinutes = 1
cleanupDays = 30

# 欠款提醒邮件配置
[debtReminderEmail]
enabled = true
sendDay = 5
testMode = true
processIntervalMinutes = 60

# Metro2自动生成和邮件发送配置
[metro2AutoGeneration]
enabled = true
sendDay = 6
sendHour = 15
recipientEmail = "<EMAIL>"
processIntervalMinutes = 60
testMode = true

# Global write options for goupload
[write_options]
max_retries = 3
retry_delay = "1s"
s3_timeout = "30s"
chunk_size = 5242880
enable_logging = true
validate_content = true
enable_metadata = true

# User upload configuration
[userupload]
site = "TEST"

  # Lease documents upload configuration
  [[userupload.types]]
    entryName = "lease_documents"
    prefix = "/lease_docs"
    tmpPath = "/tmp/uploads/lease_docs"
    maxSize = "50MB"
    storage = [
      { type = "s3", target = "garage-primary", bucket = "reportrentals" }
    ]

  # Property documents upload configuration
  [[userupload.types]]
    entryName = "property_documents"
    prefix = "/property_docs"
    tmpPath = "/tmp/uploads/property_docs"
    maxSize = "50MB"
    storage = [
      { type = "s3", target = "garage-primary", bucket = "reportrentals" }
    ]

  # Problem report files upload configuration
  [[userupload.types]]
    entryName = "problem_reports"
    prefix = "/problem_files"
    tmpPath = "/tmp/uploads/problem_files"
    maxSize = "50MB"
    storage = [
      { type = "s3", target = "garage-primary", bucket = "reportrentals" }
    ]

  # Metro2 report files upload configuration
  [[userupload.types]]
    entryName = "metro2_reports"
    prefix = "/metro2_files"
    tmpPath = "/tmp/uploads/metro2_files"
    maxSize = "50MB"
    storage = [
      { type = "s3", target = "garage-primary", bucket = "reportrentals" }
    ]

# Connection sources configuration (empty for local-only setup)
[connection_sources]
  [[connection_sources.s3_providers]]
    name = "garage-primary"
    endpoint = "http://192.168.0.1:3900"  # GarageHQ S3 API 端口
    key = "GK839b8fd8949662e45ead1624"  # 实际生成的主要测试密钥
    pass = "4b2cb497cc520c25768c5d3a97ff88ffe0f6b332a0bd6609b2417612e4e25494"
    region = "garage"  # GarageHQ 使用自定义 region
# L2 directory configuration
#[source_l2_size]
#TEST = 256