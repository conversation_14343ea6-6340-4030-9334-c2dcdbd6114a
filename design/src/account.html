<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Account</title>
    <script src="https://unpkg.com/htmx.org@1.9.10" defer></script>
    <link href="output.css" rel="stylesheet" />
</head>
<body class="min-h-screen bg-gray-200"></body>
    <!-- Nav bar-->
    <div class="flex h-screen">
      <div
        hx-get="components/navbar.html"
        hx-trigger="load"
        hx-swap="innerHTML"
      ></div>

      <main class="h-screen w-full bg-slate-100 overflow-auto lg:px-16 md:px-8 px-4">
        <div class="max-w-[1440px] w-full mx-auto">
            <div class="flex-1 mt-20 lg:mt-8 pb-0 w-full">
                <h1 class="text-2xl">My Account</h1>
            </div>
            <!-- Tab Container -->
            <div class="flex flex-col sm:flex-row gap-4 sm:p-6 mt-8">
                <!-- Mobile Dropdown -->
                <div class="relative w-full sm:hidden mb-4">
                    <select 
                        class="w-full p-4 bg-white border border-gray-200 rounded-lg appearance-none cursor-pointer text-sm font-medium"
                        onchange="openTab(event, this.value)"
                    >
                        <option value="Account">Account Settings</option>
                        <option value="Organization">Organization</option>
                        <option value="Billing">Billing</option>
                        <option value="Plans">Plans</option>
                        <option value="SysAdmin">System Admin</option>
                    </select>
                    <!-- Dropdown Arrow -->
                    <div class="absolute inset-y-0 right-0 flex items-center px-4 pointer-events-none">
                        <svg class="w-4 h-4 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"/>
                        </svg>
                    </div>
                </div>

                <!-- Desktop Tab Navigation -->
                <div class="hidden sm:block w-full sm:w-1/4 ">
                    <nav class="flex flex-col bg-white rounded-lg border border-gray-200 max-h-fit overflow-hidden" aria-label="Tabs">
                        <button 
                            class="tablinks min-h-16 py-4 text-sm font-medium text-left px-6 border-l-2 border-black bg-gray-50 text-slate-800 hover:bg-gray-50 hover:border-gray-300 hover:text-gray-600 active-tab" 
                            onclick="openTab(event, 'Account')"
                        >
                            Account Settings
                        </button>
                        <button 
                            class="tablinks min-h-16 py-4 text-sm font-medium text-left px-6 border-l-2 border-black hover:bg-gray-50 hover:border-gray-300 hover:text-gray-600" 
                            onclick="openTab(event, 'Organization')"
                        >
                            Organization
                        </button>
                        <button 
                            class="tablinks min-h-16 py-4 text-sm font-medium text-left px-6 border-l-2 border-black hover:bg-gray-50 hover:border-gray-300 hover:text-gray-600" 
                            onclick="openTab(event, 'Billing')"
                        >
                            Billing
                        </button>
                        <button 
                            class="tablinks min-h-16 py-4 text-sm font-medium text-left px-6 border-l-2 border-black hover:bg-gray-50 hover:border-gray-300 hover:text-gray-600" 
                            onclick="openTab(event, 'Plans')"
                        >
                            Plans
                        </button>
                        <button 
                            class="tablinks min-h-16 py-4 text-sm font-medium text-left px-6 border-l-2 border-black hover:bg-gray-50 hover:border-gray-300 hover:text-gray-600" 
                            onclick="openTab(event, 'SysAdmin')"
                        >
                            System Admin
                        </button>
                        <!-- Bring them back to marketing site home page when they log out -->
                        <button 
                            class="tablinks min-h-16 py-4 text-sm font-medium text-left px-6 border-l-2 border-black hover:bg-gray-50 hover:border-gray-300 hover:text-gray-600"
                        >
                            Log Out
                        </button>
                    </nav>
                </div>
                
                <!-- Tab content -->
                <div class="md:w-3/4 w-full">
                    <!-- Account Tab -->
                    <div id="Account" class="tabcontent bg-white p-4 sm:p-6 rounded-lg border border-gray-200 mb-16">
                        <div class="block">
                            <h2 class="text-lg mb-2">Account Information</h2>
                            <div class="flex flex-col sm:flex-row gap-2 items-start w-full justify-between px-0 md:px-2 py-8 text-sm border-b">
                                <h3 class="font-semibold w-full md:w-1/4">Name</h3>
                                <div class="w-full md:w-1/2" id="name-field">
                                    <p>John Doe</p>
                                </div>
                                <button 
                                    class="font-bold text-blue-500 hover:underline w-full md:w-1/5 text-left sm:text-right mt-2 md:mt-0"
                                    hx-get="components/modal-medium.html"
                                    hx-target="#modal"
                                    hx-swap="innerHTML"
                                    id="change-name-btn"
                                >
                                    Update
                                </button>
                            </div>
                            <div class="flex flex-col sm:flex-row gap-2 items-start w-full justify-between px-0 md:px-2 py-8 text-sm border-b">
                                <h3 class="font-semibold w-full md:w-1/4">Email</h3>
                                <p class="w-full md:w-1/2"><EMAIL></p>
                                <a href="#" class="font-bold text-blue-500 hover:underline w-full md:w-1/5 text-left sm:text-right mt-2 md:mt-0">Update</a>
                            </div>
                            <div class="flex flex-col sm:flex-row gap-2 items-start w-full justify-between px-0 md:px-2 py-8 text-sm border-b">
                                <h3 class="font-semibold w-full md:w-1/4">Current Address</h3>
                                <div class="w-full md:w-1/2">
                                    <p>1234 Main St, Unit 108</p>
                                    <p>Anytown, CA 91234</p>
                                    <p>USA</p>
                                </div>
                                <a href="#" class="font-bold text-blue-500 hover:underline w-full md:w-1/5 text-left sm:text-right mt-2 md:mt-0">Update</a>
                            </div>
                            <div class="flex flex-col sm:flex-row gap-2 items-start w-full justify-between px-0 md:px-2 py-8 text-sm border-b">
                                <h3 class="font-semibold w-full md:w-1/4">Phone Number</h3>
                                <div class="w-full md:w-1/2 flex gap-2"><p>**********</p><p class="text-gray-500 italic"> (Primary)</p></div>
                                <a href="#" class="font-bold text-blue-500 hover:underline w-full md:w-1/5 text-left sm:text-right mt-2 md:mt-0">Update</a>
                            </div>
                            <div class="flex flex-col sm:flex-row gap-2 justify-between items-start content-center w-full px-0 md:px-2 py-8 text-sm border-b">
                                <h3 class="font-semibold w-full md:w-1/4">Change Account Mode</h3>
                                <button class="bg-slate-800 text-white px-4 py-2 rounded text-sm mt-4 sm:mt-0"
                                > Switch to Tenant View
                                </button>
                            </div>
                            <h2 class="text-lg mt-16 mb-2">Security</h2>
                            <div class="flex flex-col sm:flex-row gap-2 items-start w-full justify-between px-0 md:px-2 py-8 text-sm border-b">
                                <h3 class="font-semibold w-full md:w-1/4">Password</h3>
                                <div class="w-full md:w-1/2 flex gap-2"><p>*******</p></div>
                                <a href="#" class="font-bold text-blue-500 hover:underline w-full md:w-1/5 text-left sm:text-right mt-2 md:mt-0">Change</a>
                            </div>
                            <div class="flex flex-col sm:flex-row gap-2 items-start w-full justify-between px-0 md:px-2 py-8 text-sm border-b">
                                <h3 class="font-semibold w-full md:w-1/4">Account Status</h3>
                                <div class="w-full md:w-1/2 flex gap-2"><p>Unverified</p></div>
                                <a href="#" class="font-bold text-blue-500 hover:underline w-full md:w-1/5 text-left sm:text-right mt-2 md:mt-0">Start Verification</a>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Organization Tab -->
                    <div id="Organization" class="tabcontent hidden bg-white p-4 sm:p-6 rounded-lg border border-gray-200 mb-16">
                        <div class="block">
                            <div class="flex justify-between items-center mb-2">
                                <h2 class="text-lg">Organization</h2>
                            </div>
                            <div class="flex flex-col sm:flex-row gap-2 items-start w-full justify-between px-0 md:px-2 py-8 text-sm border-b">
                                <h3 class="font-semibold w-full md:w-1/4">Account Type</h3>
                                <div class="w-full md:w-1/2 flex gap-2"><p>Basic Account</p></div>
                                <a href="#" class="font-bold text-blue-500 hover:underline w-full md:w-1/5 text-left sm:text-right mt-2 md:mt-0">Upgrade Account</a>
                            </div>
                            <div class="flex flex-col sm:flex-row gap-2 items-start w-full justify-between px-0 md:px-2 py-8 text-sm border-b">
                                <h3 class="font-semibold w-full md:w-1/4">Organization Name</h3>
                                <div class="w-full md:w-1/2 flex gap-2"><p>123 landlord inc.</p></div>
                                <a href="#" class="font-bold text-blue-500 hover:underline w-full md:w-1/5 text-left sm:text-right mt-2 md:mt-0">Update</a>
                            </div>
                            <div class="flex flex-col sm:flex-row gap-2 items-start w-full justify-between px-0 md:px-2 py-8 text-sm border-b">
                                <h3 class="font-semibold w-full md:w-1/4">Business Email</h3>
                                <div class="w-full md:w-1/2 flex gap-2"><p><EMAIL></p></div>
                                <a href="#" class="font-bold text-blue-500 hover:underline w-full md:w-1/5 text-left sm:text-right mt-2 md:mt-0">Update</a>
                            </div>
                            <div class="flex flex-col sm:flex-row gap-2 items-start w-full justify-between px-0 md:px-2 py-8 text-sm border-b">
                                <h3 class="font-semibold w-full md:w-1/4">Business Address</h3>
                                <div class="w-full md:w-1/2">
                                    <p>1234 Main St, Unit 108</p>
                                    <p>Anytown, CA 91234</p>
                                    <p>USA</p>
                                </div>
                                <a href="#" class="font-bold text-blue-500 hover:underline w-full md:w-1/5 text-left sm:text-right mt-2 md:mt-0">Update</a>
                            </div>
                            <!-- Team Section Header -->
                            <div class="flex flex-col sm:flex-row gap-2 items-start w-full justify-between px-0 md:px-2 py-4 text-sm">
                                <h2 class="text-lg">Team</h2>
                                <button 
                                    class="bg-slate-800 text-white px-4 py-2 rounded text-sm mt-4 sm:mt-0"
                                    onclick="addNewTeamMember()"
                                >
                                    Invite Member
                                </button>
                            </div>
                            <!-- Team List Container; when "Remove Invitation" is clicked, the email is removed from the list -->
                            <div id="teamList">
                                <!-- Existing team member 1 - The Boss -->
                                <div class="flex flex-col sm:flex-row gap-2 w-full justify-between px-0 md:px-2 my-6 text-sm items-center">
                                    <div class="md:w-2/5 w-full block">
                                        <h3 class="font-semibold w-full">The Boss</h3>
                                        <div class="w-full md:w-1/2 flex gap-2"><p class="text-gray-500"><EMAIL></p></div>
                                    </div>
                                    <div class="relative w-full md:w-2/5">
                                        <select
                                            class="p-2 w-full focus:outline-gray-100 border border-gray-200 pr-8 disabled:bg-gray-50 disabled:text-gray-700 disabled:cursor-not-allowed appearance-none"
                                            id="role"
                                            disabled
                                        >    
                                            <option>Owner</option>
                                        </select>
                                        <svg 
                                            class="w-4 h-4 absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-500 pointer-events-none" 
                                            fill="none" 
                                            stroke="currentColor" 
                                            viewBox="0 0 24 24"
                                        >
                                            <path 
                                                stroke-linecap="round" 
                                                stroke-linejoin="round" 
                                                stroke-width="2" 
                                                d="M19 9l-7 7-7-7"
                                            />
                                        </svg>
                                    </div>
                                </div>
                                <!-- Team Member 2 - Normal select -->
                                <div class="flex flex-col sm:flex-row gap-2 w-full justify-between px-0 md:px-2 my-6 text-sm items-center">
                                    <div class="md:w-2/5 w-full block">
                                        <h3 class="font-semibold w-full">Firstname Lastname here</h3>
                                        <div class="w-full md:w-1/2 flex gap-2"><p class="text-gray-500"><EMAIL></p></div>
                                    </div>
                                    <div class="relative w-full md:w-2/5">
                                        <select
                                            class="p-2 w-full focus:outline-gray-100 border border-gray-200 pr-8 appearance-none"
                                            id="role"
                                        >    
                                            <option>Admin</option>
                                            <option>Member</option>
                                            <option>Remove</option>
                                        </select>
                                        <svg 
                                            class="w-4 h-4 absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-500" 
                                            fill="none" 
                                            stroke="currentColor" 
                                            viewBox="0 0 24 24"
                                        >
                                            <path 
                                                stroke-linecap="round" 
                                                stroke-linejoin="round" 
                                                stroke-width="2" 
                                                d="M19 9l-7 7-7-7"
                                            />
                                        </svg>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div id="Billing" class="tabcontent hidden bg-white p-4 sm:p-6 rounded-lg border border-gray-200 mb-16">
                        <div class="block">
                            
                            <h2 class="text-lg mb-2">Billing Information</h2>
                            
                            <div class="flex flex-col sm:flex-row gap-2 items-start w-full justify-between px-0 md:px-2 py-8 text-sm border-b">
                                <h3 class="font-semibold w-full md:w-1/4">Billing Address</h3>
                                <div class="w-full md:w-1/2">
                                    <p>1234 Main St, Unit 108</p>
                                    <p>Anytown, CA 91234</p>
                                    <p>USA</p>
                                </div>
                                <a href="#" class="font-bold text-blue-500 hover:underline w-full md:w-1/5 text-left sm:text-right mt-2 md:mt-0">Update</a>
                            </div>
                            <div class="flex flex-col sm:flex-row gap-2 items-start w-full justify-between px-0 md:px-2 py-8 text-sm border-b">
                                <h3 class="font-semibold w-full md:w-1/4">Payment Information</h3>
                                <div class="w-full md:w-1/2">
                                    <p>VISA1234</p>
                                </div>
                                <a href="#" class="font-bold text-blue-500 hover:underline w-full md:w-1/5 text-left sm:text-right mt-2 md:mt-0">Change</a>
                            </div>

                            <h2 class="text-lg mt-16 mb-2">Billing History</h2>
                            <p class="text-sm">Showing billing history for past 12 months.</p>

                            <!--Desktop billing table-->
                            <div class="hidden sm:block">
                                <!-- Table Header -->
                                <div class="grid grid-cols-4 text-sm border-b border-gray-400">
                                <div class="p-4 font-semibold w-1/4 text-left">Date</div>
                                <div class="p-4 font-semibold w-1/4 text-left">Amount</div>
                                <div class="p-4 font-semibold w-1/4 text-left">Payment</div>
                                <div class="p-4 font-semibold w-1/4 text-left">Invoice</div>
                                </div>

                                <!-- Table Rows -->
                                <div class="grid grid-cols-4 text-sm border-b border-gray-100">
                                <div class="p-4 w-1/4 whitespace-nowrap">Jan 12, 2025</div>
                                <div class="p-4 w-1/4">$19.99CAD</div>
                                <div class="p-4 w-1/4">VISA1234</div>
                                <div class="p-4 w-1/4 whitespace-nowrap">
                                    <span class="underline cursor-pointer"
                                    hx-get="components/modal-small.html"
                                    hx-method="innerHTML"
                                    hx-trigger="click"
                                    hx-target="#modal"
                                    >Resend bill</span>
                                </div>
                                </div>
                                <div class="grid grid-cols-4 text-sm border-b border-gray-100">
                                    <div class="p-4 w-1/4 whitespace-nowrap">Feb 12, 2025</div>
                                    <div class="p-4 w-1/4">$19.99CAD</div>
                                    <div class="p-4 w-1/4">VISA1234</div>
                                    <div class="p-4 w-1/4 whitespace-nowrap">
                                    <span class="underline cursor-pointer"
                                        hx-get="components/modal-small.html"
                                        hx-method="innerHTML"
                                        hx-trigger="click"
                                        hx-target="#modal"
                                  >Resend bill</span>
                                </div>
                              </div>
                            </div>

                            <!--Mobile billing table-->
                            <div class="sm:hidden flex flex-col gap-4 mt-4">
                                <div class="flex flex-row justify-between bg-white p-4 border border-gray-100 rounded-md">
                                    <div class="flex flex-col gap-1">
                                        <span class="text-sm text-gray-400">Jan 12, 2025</span>
                                        <span class="text-lg h-full content-center">$19.99CAD</span>
                                    </div>
                                    <div class="flex flex-col gap-1">
                                        <span class="text-sm text-gray-400 text-right">VISA1234</span>
                                    <button class=" btn btn-ghost"
                                        hx-get="components/modal-small.html"
                                        hx-method="innerHTML"
                                        hx-trigger="click"
                                        hx-target="#modal"
                                  >Resend bill</button>
                                    </div>
                                    
                                </div>
                                <div class="flex flex-row justify-between bg-white p-4 border border-gray-100 rounded-md">
                                    <div class="flex flex-col gap-1">
                                        <span class="text-sm text-gray-400">Feb 12, 2025</span>
                                        <span class="text-lg h-full content-center">$19.99CAD</span>
                                    </div>
                                    <div class="flex flex-col gap-1">
                                        <span class="text-sm text-gray-400 text-right">VISA1234</span>
                                    <button class=" btn btn-ghost"
                                        hx-get="components/modal-small.html"
                                        hx-method="innerHTML"
                                        hx-trigger="click"
                                        hx-target="#modal"
                                  >Resend bill</button>
                                    </div>
                                    
                                </div>
                            </div>
                        </div>
                    </div>

                    <div id="Plans" class="tabcontent hidden bg-white p-4 sm:p-6 rounded-lg border border-gray-200 mb-16">
                        <div class="block">
                            
                            <h2 class="text-lg mb-2">Plans</h2>

                            <span class="text-sm text-left text-gray-600">To protect your data and comply with Equifax requirements, we verify your identity to access paid features. Most users can do this automatically with a credit card. If that doesn't work, we may ask for government ID.</span>

                            <div class="flex flex-col xl:flex-row gap-4 mt-4">
                                <div class="flex flex-col justify-between bg-white w-full rounded-md py-4 px-4 md:px-8 gap-8 border border-blue-100 shadow-xl shadow-blue-400/10">
                                    <div class="flex flex-col gap-1">
                                        <span class=" w-full">Investor</span>
                                        <span class="text-xl w-full font-bold">$19.99</span>
                                        <span class="text-sm text-gray-400 w-full mb-2">Per month, billed monthly</span>
                                        <hr class="border-gray-200">
                                        <ul class="md:text-sm text-xs">
                                            <li class="mt-4 flex flex-row gap-2 items-center">
                                                <div class="w-4 h-4"><svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="size-4">
                                                <path stroke-linecap="round" stroke-linejoin="round" d="M9 12.75 11.25 15 15 9.75M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z" />
                                              </svg></div>
                                              Free 20 reports a month to Equifax
                                            </li>
                                            <li class="mt-2 flex flex-row gap-2 items-center">
                                                <div class="w-4 h-4"><svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="size-4">
                                                    <path stroke-linecap="round" stroke-linejoin="round" d="M9 12.75 11.25 15 15 9.75M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z" />
                                                  </svg></div>
                                                Unlimited lease tracking
                                            </li>
                                            <li class="mt-2 flex flex-row gap-2 items-center">
                                                <div class="w-4 h-4"><svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="size-4">
                                                    <path stroke-linecap="round" stroke-linejoin="round" d="M9 12.75 11.25 15 15 9.75M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z" />
                                                  </svg></div>
                                                Unlimited properties tracking
                                            </li>
                                            <li class="mt-2 flex flex-row gap-2 items-center">
                                                <div class="w-4 h-4"> <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="size-4">
                                                    <path stroke-linecap="round" stroke-linejoin="round" d="M9 12.75 11.25 15 15 9.75M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z" />
                                                  </svg></div>
                                                Up to 10 team members
                                            </li>
                                        </ul>
                                    </div>

                                    <div class="flex flex-col items-center gap-2 w-full">
                                        <button class="btn btn-primary btn-soft" onclick="openTab(event, 'checkout')">Subscribe</button>
                                    </div>
                                    <!--
                                    Use this code if they are already subscribed
                                    <div class="flex flex-col items-center gap-2 w-full">
                                        <div class="badge badge-soft badge-primary">Current Plan</div>
                                        <button class="btn btn-ghost text-gray-400">Cancel Plan</button>
                                    </div>
                                    -->
                                </div>
                                <div class="flex flex-col justify-between bg-white w-full rounded-md py-4 px-4 md:px-8 gap-8 border border-gray-100">
                                    <div class="flex flex-col gap-1">
                                        <span class=" w-full">Property Manager</span>
                                        <span class="text-xl w-full font-bold">Contact Sales</span>
                                        <span class="text-sm text-gray-400 w-full mb-2">Perfect for large teams</span>
                                        <hr class="border-gray-200">
                                        <ul class="md:text-sm text-xs">
                                            <li class="mt-4 flex flex-row gap-2 items-center">
                                                <div class="w-4 h-4"><svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="size-4">
                                                <path stroke-linecap="round" stroke-linejoin="round" d="M9 12.75 11.25 15 15 9.75M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z" />
                                              </svg></div>
                                              Unlimited reports to Equifax
                                            </li>
                                            <li class="mt-2 flex flex-row gap-2 items-center">
                                                <div class="w-4 h-4"><svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="size-4">
                                                    <path stroke-linecap="round" stroke-linejoin="round" d="M9 12.75 11.25 15 15 9.75M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z" />
                                                  </svg></div>
                                                  Prioritized customer support
                                            </li>
                                            <li class="mt-2 flex flex-row gap-2 items-center">
                                                <div class="w-4 h-4"><svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="size-4">
                                                    <path stroke-linecap="round" stroke-linejoin="round" d="M9 12.75 11.25 15 15 9.75M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z" />
                                                  </svg></div>
                                                  Customized role permissions
                                            </li>
                                            <li class="mt-2 flex flex-row gap-2 items-center">
                                                <div class="w-4 h-4"><svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="size-4">
                                                    <path stroke-linecap="round" stroke-linejoin="round" d="M9 12.75 11.25 15 15 9.75M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z" />
                                                  </svg></div>
                                                  Prioritized customer support
                                            </li>
                                        </ul>
                                    </div>
                                    <div class="flex flex-col items-center gap-2 w-full">
                                        <button class="btn btn-soft" onclick="openTab(event, 'checkout')">Subscribe</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div id="checkout" class="tabcontent hidden bg-white p-4 sm:p-6 rounded-lg border border-gray-200 mb-16">
                        <div class="block">
                            <h2 class="text-lg mb-4">Upgrade Account</h2>
                            <div class="flex flex-col sm:flex-row gap-2 justify-between">
                                <div id="monthly-billing" class="flex flex-col rounded-md border border-blue-300 bg-blue-50 p-4 w-full">
                                    <span class="font-bold ">Monthly billing</span>
                                    <span class="text-gray-400 text-sm mt-2">$19.99 per month</span>
                                </div>

                                <div id="annual-billing" class="flex flex-col rounded-md border border-gray-300 p-4 w-full">
                                    <span class="font-bold flex flex-row justify-between">Annual billing <div class="badge badge-soft badge-primary text-xs">Save 20%</div></span>
                                    <span class="text-gray-400 text-sm mt-2">$15.99 per month</span>
                                </div>
                            </div>
                            <h2 class="text-lg mt-8 mb-4">Summary</h2>

                            <hr class=" border-gray-300 mb-4">
                            <div class="flex flex-row justify-between mt-4 mb-8">
                                <div class="flex flex-col">
                                    <span class="text-sm font-medium">Upgrade account to Investor</span>
                                    <span class="text-sm text-gray-400 mb-2">Your plan will renew on January 1, 2026</span>
                                </div>
                                <span class="text-sm font-medium">$19.99</span>
                            </div>
                            <div class="flex flex-row justify-between mt-4 mb-8">
                                <div class="flex flex-col">
                                    <span class="text-sm font-medium">20 X Rent reports to Equifax</span>
                                    <span class="text-sm text-gray-400 mb-2">Monthly free reports as part of the investor plan</span>
                                </div>
                                <span class="text-sm font-medium">$0.00</span>
                            </div>
                            <div class="flex flex-col-reverse  sm:flex-row my-4 justify-between">
                                <button class="btn"
                                hx-get="./components/modal-collect-id.html"
                                hx-target="#modal"
                                hx-trigger="click"
                                hx-swap="innerHTML"
                                >Confirm and pay</button>
                                <span class="text-lg font-bold lg:mb-0 mb-4 text-right">Total: <span class="text-lg font-bold ml-4">$19.99</span></span>
                            </div>
                        </div>
                    </div>

                    <div id="SysAdmin" class="tabcontent hidden bg-white p-4 sm:p-6 rounded-lg border border-gray-200 mb-16">
                        <div class="block">
                            <h2 class="text-lg mb-2">Feature Settings</h2>
                            <div class="flex flex-col sm:flex-row gap-2 items-start w-full justify-between px-0 md:px-2 py-8 text-sm border-b">
                                <h3 class="font-semibold w-full md:w-1/4">Product Theme</h3>
                                <div class="w-full md:w-1/2">
                                    <p>Slate (Default)</p>
                                </div>
                                <a href="#" class="font-bold text-blue-500 hover:underline w-full md:w-1/5 text-left sm:text-right mt-2 md:mt-0">Change</a>
                            </div>
                            <div class="flex flex-col sm:flex-row gap-2 items-start w-full justify-between px-0 md:px-2 py-8 text-sm border-b">
                                <h3 class="font-semibold w-full md:w-1/4">Mailbox</h3>
                                <div class="w-full md:w-1/2">
                                    <p>Enable mailbox feature for all users</p>
                                </div>
                                <div class="w-full md:w-1/5 flex justify-start md:justify-end mt-2 md:mt-0">
                                    <div
                                        hx-get="components/toggle-switch.html"
                                        hx-trigger="load"
                                        hx-swap="innerHTML"
                                    ></div>
                                </div>
                            </div>
                            <div class="flex flex-col sm:flex-row gap-2 items-start w-full justify-between px-0 md:px-2 py-8 text-sm border-b">
                                <h3 class="font-semibold w-full md:w-1/4">Forum</h3>
                                <div class="w-full md:w-1/2">
                                    <p>Enable the reporting board feature for all users</p>
                                </div>
                                <div class="w-full md:w-1/5 flex justify-start md:justify-end mt-2 md:mt-0">
                                    <div
                                        hx-get="components/toggle-switch.html"
                                        hx-trigger="load"
                                        hx-swap="innerHTML"
                                    ></div>
                                </div>
                            </div>
                            <div class="flex flex-col sm:flex-row gap-2 items-start w-full justify-between px-0 md:px-2 py-8 text-sm border-b">
                                <h3 class="font-semibold w-full md:w-1/4">Apply Changes</h3>
                                <div class="w-full md:w-1/2">
                                    <p>Apply these changes to global users</p>
                                </div>
                                <div class="w-full md:w-1/5 flex justify-start md:justify-end mt-2 md:mt-0">
                                    <button class="bg-slate-800 text-white px-4 py-2 rounded text-sm"
                                    hx-get="components/modal-small.html"
                                    hx-trigger="click"
                                    hx-swap="innerHTML"
                                    hx-target="#modal"
                                    >Apply Changes</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <script>
                function openTab(evt, tabName) {
                    // Hide all tab content
                    var tabcontent = document.getElementsByClassName("tabcontent");
                    for (var i = 0; i < tabcontent.length; i++) {
                        tabcontent[i].classList.add("hidden");
                        tabcontent[i].classList.remove("block");
                    }

                    // Remove active class from all tab buttons
                    var tablinks = document.getElementsByClassName("tablinks");
                    for (var i = 0; i < tablinks.length; i++) {
                        tablinks[i].classList.remove("bg-gray-50", "border-black", "text-slate-800", "active-tab");
                        tablinks[i].classList.add("border-transparent");
                        // Reset hover states
                        tablinks[i].classList.remove("hover:border-black");
                        tablinks[i].classList.add("hover:border-gray-300");
                    }

                    // Show the selected tab content
                    document.getElementById(tabName).classList.remove("hidden");
                    document.getElementById(tabName).classList.add("block");

                    // If event has currentTarget (button click), add active class
                    if (evt.currentTarget) {
                        evt.currentTarget.classList.add("bg-gray-50", "border-black", "text-slate-800", "active-tab");
                        evt.currentTarget.classList.remove("border-transparent", "hover:border-gray-300");
                        evt.currentTarget.classList.add("hover:border-black");
                    }
                    // If using dropdown, find and activate corresponding button
                    else if (evt.target && evt.target.tagName === 'SELECT') {
                        const button = document.querySelector(`button[onclick*="${tabName}"]`);
                        button.classList.add("bg-gray-50", "border-black", "text-slate-800", "active-tab");
                        button.classList.remove("border-transparent", "hover:border-gray-300");
                        button.classList.add("hover:border-black");
                    }
                }

                // Set first tab as active by default
                document.addEventListener('DOMContentLoaded', function() {
                    document.querySelector('.tablinks').click();
                });

                function addNewTeamMember() {
                    const newMemberHtml = `
                        <div class="flex flex-col sm:flex-row gap-2 w-full justify-between px-0 md:px-2 py-8 text-sm items-center border-b">
                            <div class="w-full md:w-2/5">
                                <input 
                                    type="email" 
                                    placeholder="Enter email here" 
                                    class="w-full p-2 border border-gray-200 rounded"
                                >
                            </div>
                            <div class="w-full md:w-1/5 flex justify-start md:justify-end mt-2 md:mt-0">
                                <button 
                                    class="font-bold text-blue-500 hover:underline"
                                    onclick="this.innerHTML = 'Remove Invitation';"
                                >
                                    Send
                                </button>
                            </div>
                        </div>
                    `;
                    
                    const teamList = document.getElementById('teamList');
                    teamList.insertAdjacentHTML('afterbegin', newMemberHtml);
                }
            </script>
        </div>
      </main>
    </div>
    <!-- Modal Container -->
    <div id="modal" class="relative"></div>
</body>
</html>