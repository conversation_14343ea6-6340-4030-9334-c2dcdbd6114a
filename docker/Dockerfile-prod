# cd build, podman build -t rent_report:latest .
FROM alpine:latest
RUN addgroup -S appgroup
RUN adduser -S -u 1100 -G appgroup appuser
WORKDIR /app
RUN mkdir -p /app/uploads /app/configs /app/web/dist /app/logs /app/docs
# rr.go will check for existence(which should not check), see: rent_report/src/config/upload_init.go
RUN mkdir -p /tmp/uploads/
RUN chown -R appuser:appgroup /tmp/uploads/
RUN chmod -R 755 /tmp
RUN chown -R appuser:appgroup /app
COPY bin/rent_report /app/server
COPY web/dist/ /app/web/dist/
COPY configs/ /app/configs/
COPY docs/ /app/docs/
RUN chown -R appuser:appgroup /app
RUN chmod +x /app/server
USER appuser
EXPOSE 8089
CMD ["/app/server"]