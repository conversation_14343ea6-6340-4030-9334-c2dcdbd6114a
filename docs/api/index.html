<!DOCTYPE html>
<html>

<head>
  <meta charset="utf8" />
  <title>Rent Report API Documentation</title>
  <!-- needed for adaptive design -->
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <style>
    body {
      padding: 0;
      margin: 0;
    }
  </style>
  <script src="https://cdn.redocly.com/redoc/v2.5.0/bundles/redoc.standalone.js"></script><style data-styled="true" data-styled-version="6.1.19">.fqkwbU{width:calc(100% - 40%);padding:0 40px;}/*!sc*/
@media print,screen and (max-width: 75rem){.fqkwbU{width:100%;padding:40px 40px;}}/*!sc*/
.dCzIPc{width:calc(100% - 40%);padding:0 40px;}/*!sc*/
@media print,screen and (max-width: 75rem){.dCzIPc{width:100%;padding:0px 40px;}}/*!sc*/
data-styled.g4[id="sc-ggWZvA"]{content:"fqkwbU,dCzIPc,"}/*!sc*/
.bPmFpz{padding:40px 0;}/*!sc*/
.bPmFpz:last-child{min-height:calc(100vh + 1px);}/*!sc*/
.bPmFpz>.bPmFpz:last-child{min-height:initial;}/*!sc*/
@media print,screen and (max-width: 75rem){.bPmFpz{padding:0;}}/*!sc*/
.gHrCVQ{padding:40px 0;position:relative;}/*!sc*/
.gHrCVQ:last-child{min-height:calc(100vh + 1px);}/*!sc*/
.gHrCVQ>.gHrCVQ:last-child{min-height:initial;}/*!sc*/
@media print,screen and (max-width: 75rem){.gHrCVQ{padding:0;}}/*!sc*/
.gHrCVQ:not(:last-of-type):after{position:absolute;bottom:0;width:100%;display:block;content:'';border-bottom:1px solid rgba(0, 0, 0, 0.2);}/*!sc*/
data-styled.g5[id="sc-dTvVRJ"]{content:"bPmFpz,gHrCVQ,"}/*!sc*/
.bDYKKx{width:40%;color:#ffffff;background-color:#263238;padding:0 40px;}/*!sc*/
@media print,screen and (max-width: 75rem){.bDYKKx{width:100%;padding:40px 40px;}}/*!sc*/
data-styled.g6[id="sc-jwTyAe"]{content:"bDYKKx,"}/*!sc*/
.FFPsr{background-color:#263238;}/*!sc*/
data-styled.g7[id="sc-hjsuWn"]{content:"FFPsr,"}/*!sc*/
.gkiSyE{display:flex;width:100%;padding:0;}/*!sc*/
@media print,screen and (max-width: 75rem){.gkiSyE{flex-direction:column;}}/*!sc*/
data-styled.g8[id="sc-jJLAfE"]{content:"gkiSyE,"}/*!sc*/
.wYHiz{font-family:Montserrat,sans-serif;font-weight:400;font-size:1.85714em;line-height:1.6em;color:#333333;}/*!sc*/
data-styled.g9[id="sc-hwkwBN"]{content:"wYHiz,"}/*!sc*/
.iFSqkw{font-family:Montserrat,sans-serif;font-weight:400;font-size:1.57143em;line-height:1.6em;color:#333333;margin:0 0 20px;}/*!sc*/
data-styled.g10[id="sc-kNOymR"]{content:"iFSqkw,"}/*!sc*/
.drJHMo{color:#ffffff;}/*!sc*/
data-styled.g12[id="sc-lgpSej"]{content:"drJHMo,"}/*!sc*/
.czjApA{border-bottom:1px solid rgba(38, 50, 56, 0.3);margin:1em 0 1em 0;color:rgba(38, 50, 56, 0.5);font-weight:normal;text-transform:uppercase;font-size:0.929em;line-height:20px;}/*!sc*/
data-styled.g13[id="sc-eqYatC"]{content:"czjApA,"}/*!sc*/
.fRdsOi{cursor:pointer;margin-left:-20px;padding:0;line-height:1;width:20px;display:inline-block;outline:0;}/*!sc*/
.fRdsOi:before{content:'';width:15px;height:15px;background-size:contain;background-image:url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZlcnNpb249IjEuMSIgeD0iMCIgeT0iMCIgd2lkdGg9IjUxMiIgaGVpZ2h0PSI1MTIiIHZpZXdCb3g9IjAgMCA1MTIgNTEyIiBlbmFibGUtYmFja2dyb3VuZD0ibmV3IDAgMCA1MTIgNTEyIiB4bWw6c3BhY2U9InByZXNlcnZlIj48cGF0aCBmaWxsPSIjMDEwMTAxIiBkPSJNNDU5LjcgMjMzLjRsLTkwLjUgOTAuNWMtNTAgNTAtMTMxIDUwLTE4MSAwIC03LjktNy44LTE0LTE2LjctMTkuNC0yNS44bDQyLjEtNDIuMWMyLTIgNC41LTMuMiA2LjgtNC41IDIuOSA5LjkgOCAxOS4zIDE1LjggMjcuMiAyNSAyNSA2NS42IDI0LjkgOTAuNSAwbDkwLjUtOTAuNWMyNS0yNSAyNS02NS42IDAtOTAuNSAtMjQuOS0yNS02NS41LTI1LTkwLjUgMGwtMzIuMiAzMi4yYy0yNi4xLTEwLjItNTQuMi0xMi45LTgxLjYtOC45bDY4LjYtNjguNmM1MC01MCAxMzEtNTAgMTgxIDBDNTA5LjYgMTAyLjMgNTA5LjYgMTgzLjQgNDU5LjcgMjMzLjR6TTIyMC4zIDM4Mi4ybC0zMi4yIDMyLjJjLTI1IDI0LjktNjUuNiAyNC45LTkwLjUgMCAtMjUtMjUtMjUtNjUuNiAwLTkwLjVsOTAuNS05MC41YzI1LTI1IDY1LjUtMjUgOTAuNSAwIDcuOCA3LjggMTIuOSAxNy4yIDE1LjggMjcuMSAyLjQtMS40IDQuOC0yLjUgNi44LTQuNWw0Mi4xLTQyYy01LjQtOS4yLTExLjYtMTgtMTkuNC0yNS44IC01MC01MC0xMzEtNTAtMTgxIDBsLTkwLjUgOTAuNWMtNTAgNTAtNTAgMTMxIDAgMTgxIDUwIDUwIDEzMSA1MCAxODEgMGw2OC42LTY4LjZDMjc0LjYgMzk1LjEgMjQ2LjQgMzkyLjMgMjIwLjMgMzgyLjJ6Ii8+PC9zdmc+Cg==');opacity:0.5;visibility:hidden;display:inline-block;vertical-align:middle;}/*!sc*/
h1:hover>.fRdsOi::before,h2:hover>.fRdsOi::before,.fRdsOi:hover::before{visibility:visible;}/*!sc*/
data-styled.g14[id="sc-kcLKEh"]{content:"fRdsOi,"}/*!sc*/
.dUlzCe{height:18px;width:18px;min-width:18px;vertical-align:middle;float:right;transition:transform 0.2s ease-out;transform:rotateZ(-90deg);}/*!sc*/
.cGxVlA{height:1.5em;width:1.5em;min-width:1.5em;vertical-align:middle;float:left;transition:transform 0.2s ease-out;transform:rotateZ(-90deg);}/*!sc*/
.cGxVlA polygon{fill:#1d8127;}/*!sc*/
.jKYZgc{height:1.5em;width:1.5em;min-width:1.5em;vertical-align:middle;float:left;transition:transform 0.2s ease-out;transform:rotateZ(-90deg);}/*!sc*/
.jKYZgc polygon{fill:#d41f1c;}/*!sc*/
.iuNpUs{height:20px;width:20px;min-width:20px;vertical-align:middle;float:right;transition:transform 0.2s ease-out;transform:rotateZ(0);}/*!sc*/
.iuNpUs polygon{fill:white;}/*!sc*/
.dOPmTa{height:18px;width:18px;min-width:18px;vertical-align:middle;transition:transform 0.2s ease-out;transform:rotateZ(-90deg);}/*!sc*/
data-styled.g15[id="sc-dntSTA"]{content:"dUlzCe,cGxVlA,jKYZgc,iuNpUs,dOPmTa,"}/*!sc*/
.gdmNWp{border-left:1px solid #7c7cbb;box-sizing:border-box;position:relative;padding:10px 10px 10px 0;}/*!sc*/
@media screen and (max-width: 50rem){.gdmNWp{display:block;overflow:hidden;}}/*!sc*/
tr:first-of-type>.gdmNWp,tr.last>.gdmNWp{border-left-width:0;background-position:top left;background-repeat:no-repeat;background-size:1px 100%;}/*!sc*/
tr:first-of-type>.gdmNWp{background-image:linear-gradient(
      to bottom,
      transparent 0%,
      transparent 22px,
      #7c7cbb 22px,
      #7c7cbb 100%
    );}/*!sc*/
tr.last>.gdmNWp{background-image:linear-gradient(
      to bottom,
      #7c7cbb 0%,
      #7c7cbb 22px,
      transparent 22px,
      transparent 100%
    );}/*!sc*/
tr.last+tr>.gdmNWp{border-left-color:transparent;}/*!sc*/
tr.last:first-child>.gdmNWp{background:none;border-left-color:transparent;}/*!sc*/
data-styled.g18[id="sc-kCuUfV"]{content:"gdmNWp,"}/*!sc*/
.dFOJWJ{vertical-align:top;line-height:20px;white-space:nowrap;font-size:13px;font-family:Courier,monospace;}/*!sc*/
.dFOJWJ.deprecated{text-decoration:line-through;color:#707070;}/*!sc*/
data-styled.g20[id="sc-fbQrwq"]{content:"dFOJWJ,"}/*!sc*/
.ixGaBD{border-bottom:1px solid #9fb4be;padding:10px 0;width:75%;box-sizing:border-box;}/*!sc*/
tr.expanded .ixGaBD{border-bottom:none;}/*!sc*/
@media screen and (max-width: 50rem){.ixGaBD{padding:0 20px;border-bottom:none;border-left:1px solid #7c7cbb;}tr.last>.ixGaBD{border-left:none;}}/*!sc*/
data-styled.g21[id="sc-gGKoUb"]{content:"ixGaBD,"}/*!sc*/
.cteAyA{color:#7c7cbb;font-family:Courier,monospace;margin-right:10px;}/*!sc*/
.cteAyA::before{content:'';display:inline-block;vertical-align:middle;width:10px;height:1px;background:#7c7cbb;}/*!sc*/
.cteAyA::after{content:'';display:inline-block;vertical-align:middle;width:1px;background:#7c7cbb;height:7px;}/*!sc*/
data-styled.g22[id="sc-hwddKA"]{content:"cteAyA,"}/*!sc*/
.icJLQx{border-collapse:separate;border-radius:3px;font-size:14px;border-spacing:0;width:100%;}/*!sc*/
.icJLQx >tr{vertical-align:middle;}/*!sc*/
@media screen and (max-width: 50rem){.icJLQx{display:block;}.icJLQx >tr,.icJLQx >tbody>tr{display:block;}}/*!sc*/
@media screen and (max-width: 50rem) and (-ms-high-contrast:none){.icJLQx td{float:left;width:100%;}}/*!sc*/
.icJLQx .sc-jaXbil,.icJLQx .sc-jaXbil .sc-jaXbil .sc-jaXbil,.icJLQx .sc-jaXbil .sc-jaXbil .sc-jaXbil .sc-jaXbil .sc-jaXbil{margin:1em;margin-right:0;background:#fafafa;}/*!sc*/
.icJLQx .sc-jaXbil .sc-jaXbil,.icJLQx .sc-jaXbil .sc-jaXbil .sc-jaXbil .sc-jaXbil,.icJLQx .sc-jaXbil .sc-jaXbil .sc-jaXbil .sc-jaXbil .sc-jaXbil .sc-jaXbil{background:#ffffff;}/*!sc*/
data-styled.g24[id="sc-eqNDNG"]{content:"icJLQx,"}/*!sc*/
.fyxuKi >ul{list-style:none;padding:0;margin:0;margin:0 -5px;}/*!sc*/
.fyxuKi >ul >li{padding:5px 10px;display:inline-block;background-color:#11171a;border-bottom:1px solid rgba(0, 0, 0, 0.5);cursor:pointer;text-align:center;outline:none;color:#ccc;margin:0 5px 5px 5px;border:1px solid #07090b;border-radius:5px;min-width:60px;font-size:0.9em;font-weight:bold;}/*!sc*/
.fyxuKi >ul >li.react-tabs__tab--selected{color:#333333;background:#ffffff;}/*!sc*/
.fyxuKi >ul >li.react-tabs__tab--selected:focus{outline:auto;}/*!sc*/
.fyxuKi >ul >li:only-child{flex:none;min-width:100px;}/*!sc*/
.fyxuKi >ul >li.tab-success{color:#1d8127;}/*!sc*/
.fyxuKi >ul >li.tab-redirect{color:#ffa500;}/*!sc*/
.fyxuKi >ul >li.tab-info{color:#87ceeb;}/*!sc*/
.fyxuKi >ul >li.tab-error{color:#d41f1c;}/*!sc*/
.fyxuKi >.react-tabs__tab-panel{background:#11171a;}/*!sc*/
.fyxuKi >.react-tabs__tab-panel>div,.fyxuKi >.react-tabs__tab-panel>pre{padding:20px;margin:0;}/*!sc*/
.fyxuKi >.react-tabs__tab-panel>div>pre{padding:0;}/*!sc*/
data-styled.g30[id="sc-cOpnSz"]{content:"fyxuKi,"}/*!sc*/
.kIppRw code[class*='language-'],.kIppRw pre[class*='language-']{text-shadow:0 -0.1em 0.2em black;text-align:left;white-space:pre;word-spacing:normal;word-break:normal;word-wrap:normal;line-height:1.5;-moz-tab-size:4;-o-tab-size:4;tab-size:4;-webkit-hyphens:none;-moz-hyphens:none;-ms-hyphens:none;hyphens:none;}/*!sc*/
@media print{.kIppRw code[class*='language-'],.kIppRw pre[class*='language-']{text-shadow:none;}}/*!sc*/
.kIppRw pre[class*='language-']{padding:1em;margin:0.5em 0;overflow:auto;}/*!sc*/
.kIppRw .token.comment,.kIppRw .token.prolog,.kIppRw .token.doctype,.kIppRw .token.cdata{color:hsl(30, 20%, 50%);}/*!sc*/
.kIppRw .token.punctuation{opacity:0.7;}/*!sc*/
.kIppRw .namespace{opacity:0.7;}/*!sc*/
.kIppRw .token.property,.kIppRw .token.tag,.kIppRw .token.number,.kIppRw .token.constant,.kIppRw .token.symbol{color:#4a8bb3;}/*!sc*/
.kIppRw .token.boolean{color:#e64441;}/*!sc*/
.kIppRw .token.selector,.kIppRw .token.attr-name,.kIppRw .token.string,.kIppRw .token.char,.kIppRw .token.builtin,.kIppRw .token.inserted{color:#a0fbaa;}/*!sc*/
.kIppRw .token.selector+a,.kIppRw .token.attr-name+a,.kIppRw .token.string+a,.kIppRw .token.char+a,.kIppRw .token.builtin+a,.kIppRw .token.inserted+a,.kIppRw .token.selector+a:visited,.kIppRw .token.attr-name+a:visited,.kIppRw .token.string+a:visited,.kIppRw .token.char+a:visited,.kIppRw .token.builtin+a:visited,.kIppRw .token.inserted+a:visited{color:#4ed2ba;text-decoration:underline;}/*!sc*/
.kIppRw .token.property.string{color:white;}/*!sc*/
.kIppRw .token.operator,.kIppRw .token.entity,.kIppRw .token.url,.kIppRw .token.variable{color:hsl(40, 90%, 60%);}/*!sc*/
.kIppRw .token.atrule,.kIppRw .token.attr-value,.kIppRw .token.keyword{color:hsl(350, 40%, 70%);}/*!sc*/
.kIppRw .token.regex,.kIppRw .token.important{color:#e90;}/*!sc*/
.kIppRw .token.important,.kIppRw .token.bold{font-weight:bold;}/*!sc*/
.kIppRw .token.italic{font-style:italic;}/*!sc*/
.kIppRw .token.entity{cursor:help;}/*!sc*/
.kIppRw .token.deleted{color:red;}/*!sc*/
data-styled.g32[id="sc-eVqvcJ"]{content:"kIppRw,"}/*!sc*/
.bBWkcI{opacity:0.7;transition:opacity 0.3s ease;text-align:right;}/*!sc*/
.bBWkcI:focus-within{opacity:1;}/*!sc*/
.bBWkcI >button{background-color:transparent;border:0;color:inherit;padding:2px 10px;font-family:Roboto,sans-serif;font-size:14px;line-height:1.5em;cursor:pointer;outline:0;}/*!sc*/
.bBWkcI >button :hover,.bBWkcI >button :focus{background:rgba(255, 255, 255, 0.1);}/*!sc*/
data-styled.g33[id="sc-bbbBoY"]{content:"bBWkcI,"}/*!sc*/
.ghzOpX{position:relative;}/*!sc*/
data-styled.g37[id="sc-eknHtZ"]{content:"ghzOpX,"}/*!sc*/
.cFlAeY{margin-left:10px;text-transform:none;font-size:0.929em;color:black;}/*!sc*/
data-styled.g41[id="sc-dNFkOE"]{content:"cFlAeY,"}/*!sc*/
.kbZred{font-family:Roboto,sans-serif;font-weight:400;line-height:1.5em;}/*!sc*/
.kbZred p:last-child{margin-bottom:0;}/*!sc*/
.kbZred h1{font-family:Montserrat,sans-serif;font-weight:400;font-size:1.85714em;line-height:1.6em;color:#32329f;margin-top:0;}/*!sc*/
.kbZred h2{font-family:Montserrat,sans-serif;font-weight:400;font-size:1.57143em;line-height:1.6em;color:#333333;}/*!sc*/
.kbZred code{color:#e53935;background-color:rgba(38, 50, 56, 0.05);font-family:Courier,monospace;border-radius:2px;border:1px solid rgba(38, 50, 56, 0.1);padding:0 5px;font-size:13px;font-weight:400;word-break:break-word;}/*!sc*/
.kbZred pre{font-family:Courier,monospace;white-space:pre;background-color:#11171a;color:white;padding:20px;overflow-x:auto;line-height:normal;border-radius:0;border:1px solid rgba(38, 50, 56, 0.1);}/*!sc*/
.kbZred pre code{background-color:transparent;color:white;padding:0;}/*!sc*/
.kbZred pre code:before,.kbZred pre code:after{content:none;}/*!sc*/
.kbZred blockquote{margin:0;margin-bottom:1em;padding:0 15px;color:#777;border-left:4px solid #ddd;}/*!sc*/
.kbZred img{max-width:100%;box-sizing:content-box;}/*!sc*/
.kbZred ul,.kbZred ol{padding-left:2em;margin:0;margin-bottom:1em;}/*!sc*/
.kbZred ul ul,.kbZred ol ul,.kbZred ul ol,.kbZred ol ol{margin-bottom:0;margin-top:0;}/*!sc*/
.kbZred table{display:block;width:100%;overflow:auto;word-break:normal;word-break:keep-all;border-collapse:collapse;border-spacing:0;margin-top:1.5em;margin-bottom:1.5em;}/*!sc*/
.kbZred table tr{background-color:#fff;border-top:1px solid #ccc;}/*!sc*/
.kbZred table tr:nth-child(2n){background-color:#fafafa;}/*!sc*/
.kbZred table th,.kbZred table td{padding:6px 13px;border:1px solid #ddd;}/*!sc*/
.kbZred table th{text-align:left;font-weight:bold;}/*!sc*/
.kbZred .share-link{cursor:pointer;margin-left:-20px;padding:0;line-height:1;width:20px;display:inline-block;outline:0;}/*!sc*/
.kbZred .share-link:before{content:'';width:15px;height:15px;background-size:contain;background-image:url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZlcnNpb249IjEuMSIgeD0iMCIgeT0iMCIgd2lkdGg9IjUxMiIgaGVpZ2h0PSI1MTIiIHZpZXdCb3g9IjAgMCA1MTIgNTEyIiBlbmFibGUtYmFja2dyb3VuZD0ibmV3IDAgMCA1MTIgNTEyIiB4bWw6c3BhY2U9InByZXNlcnZlIj48cGF0aCBmaWxsPSIjMDEwMTAxIiBkPSJNNDU5LjcgMjMzLjRsLTkwLjUgOTAuNWMtNTAgNTAtMTMxIDUwLTE4MSAwIC03LjktNy44LTE0LTE2LjctMTkuNC0yNS44bDQyLjEtNDIuMWMyLTIgNC41LTMuMiA2LjgtNC41IDIuOSA5LjkgOCAxOS4zIDE1LjggMjcuMiAyNSAyNSA2NS42IDI0LjkgOTAuNSAwbDkwLjUtOTAuNWMyNS0yNSAyNS02NS42IDAtOTAuNSAtMjQuOS0yNS02NS41LTI1LTkwLjUgMGwtMzIuMiAzMi4yYy0yNi4xLTEwLjItNTQuMi0xMi45LTgxLjYtOC45bDY4LjYtNjguNmM1MC01MCAxMzEtNTAgMTgxIDBDNTA5LjYgMTAyLjMgNTA5LjYgMTgzLjQgNDU5LjcgMjMzLjR6TTIyMC4zIDM4Mi4ybC0zMi4yIDMyLjJjLTI1IDI0LjktNjUuNiAyNC45LTkwLjUgMCAtMjUtMjUtMjUtNjUuNiAwLTkwLjVsOTAuNS05MC41YzI1LTI1IDY1LjUtMjUgOTAuNSAwIDcuOCA3LjggMTIuOSAxNy4yIDE1LjggMjcuMSAyLjQtMS40IDQuOC0yLjUgNi44LTQuNWw0Mi4xLTQyYy01LjQtOS4yLTExLjYtMTgtMTkuNC0yNS44IC01MC01MC0xMzEtNTAtMTgxIDBsLTkwLjUgOTAuNWMtNTAgNTAtNTAgMTMxIDAgMTgxIDUwIDUwIDEzMSA1MCAxODEgMGw2OC42LTY4LjZDMjc0LjYgMzk1LjEgMjQ2LjQgMzkyLjMgMjIwLjMgMzgyLjJ6Ii8+PC9zdmc+Cg==');opacity:0.5;visibility:hidden;display:inline-block;vertical-align:middle;}/*!sc*/
.kbZred h1:hover>.share-link::before,.kbZred h2:hover>.share-link::before,.kbZred .share-link:hover::before{visibility:visible;}/*!sc*/
.kbZred a{text-decoration:auto;color:#32329f;}/*!sc*/
.kbZred a:visited{color:#32329f;}/*!sc*/
.kbZred a:hover{color:#6868cf;text-decoration:auto;}/*!sc*/
.drqpJr{font-family:Roboto,sans-serif;font-weight:400;line-height:1.5em;}/*!sc*/
.drqpJr p:last-child{margin-bottom:0;}/*!sc*/
.drqpJr p:first-child{margin-top:0;}/*!sc*/
.drqpJr p:last-child{margin-bottom:0;}/*!sc*/
.drqpJr h1{font-family:Montserrat,sans-serif;font-weight:400;font-size:1.85714em;line-height:1.6em;color:#32329f;margin-top:0;}/*!sc*/
.drqpJr h2{font-family:Montserrat,sans-serif;font-weight:400;font-size:1.57143em;line-height:1.6em;color:#333333;}/*!sc*/
.drqpJr code{color:#e53935;background-color:rgba(38, 50, 56, 0.05);font-family:Courier,monospace;border-radius:2px;border:1px solid rgba(38, 50, 56, 0.1);padding:0 5px;font-size:13px;font-weight:400;word-break:break-word;}/*!sc*/
.drqpJr pre{font-family:Courier,monospace;white-space:pre;background-color:#11171a;color:white;padding:20px;overflow-x:auto;line-height:normal;border-radius:0;border:1px solid rgba(38, 50, 56, 0.1);}/*!sc*/
.drqpJr pre code{background-color:transparent;color:white;padding:0;}/*!sc*/
.drqpJr pre code:before,.drqpJr pre code:after{content:none;}/*!sc*/
.drqpJr blockquote{margin:0;margin-bottom:1em;padding:0 15px;color:#777;border-left:4px solid #ddd;}/*!sc*/
.drqpJr img{max-width:100%;box-sizing:content-box;}/*!sc*/
.drqpJr ul,.drqpJr ol{padding-left:2em;margin:0;margin-bottom:1em;}/*!sc*/
.drqpJr ul ul,.drqpJr ol ul,.drqpJr ul ol,.drqpJr ol ol{margin-bottom:0;margin-top:0;}/*!sc*/
.drqpJr table{display:block;width:100%;overflow:auto;word-break:normal;word-break:keep-all;border-collapse:collapse;border-spacing:0;margin-top:1.5em;margin-bottom:1.5em;}/*!sc*/
.drqpJr table tr{background-color:#fff;border-top:1px solid #ccc;}/*!sc*/
.drqpJr table tr:nth-child(2n){background-color:#fafafa;}/*!sc*/
.drqpJr table th,.drqpJr table td{padding:6px 13px;border:1px solid #ddd;}/*!sc*/
.drqpJr table th{text-align:left;font-weight:bold;}/*!sc*/
.drqpJr .share-link{cursor:pointer;margin-left:-20px;padding:0;line-height:1;width:20px;display:inline-block;outline:0;}/*!sc*/
.drqpJr .share-link:before{content:'';width:15px;height:15px;background-size:contain;background-image:url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZlcnNpb249IjEuMSIgeD0iMCIgeT0iMCIgd2lkdGg9IjUxMiIgaGVpZ2h0PSI1MTIiIHZpZXdCb3g9IjAgMCA1MTIgNTEyIiBlbmFibGUtYmFja2dyb3VuZD0ibmV3IDAgMCA1MTIgNTEyIiB4bWw6c3BhY2U9InByZXNlcnZlIj48cGF0aCBmaWxsPSIjMDEwMTAxIiBkPSJNNDU5LjcgMjMzLjRsLTkwLjUgOTAuNWMtNTAgNTAtMTMxIDUwLTE4MSAwIC03LjktNy44LTE0LTE2LjctMTkuNC0yNS44bDQyLjEtNDIuMWMyLTIgNC41LTMuMiA2LjgtNC41IDIuOSA5LjkgOCAxOS4zIDE1LjggMjcuMiAyNSAyNSA2NS42IDI0LjkgOTAuNSAwbDkwLjUtOTAuNWMyNS0yNSAyNS02NS42IDAtOTAuNSAtMjQuOS0yNS02NS41LTI1LTkwLjUgMGwtMzIuMiAzMi4yYy0yNi4xLTEwLjItNTQuMi0xMi45LTgxLjYtOC45bDY4LjYtNjguNmM1MC01MCAxMzEtNTAgMTgxIDBDNTA5LjYgMTAyLjMgNTA5LjYgMTgzLjQgNDU5LjcgMjMzLjR6TTIyMC4zIDM4Mi4ybC0zMi4yIDMyLjJjLTI1IDI0LjktNjUuNiAyNC45LTkwLjUgMCAtMjUtMjUtMjUtNjUuNiAwLTkwLjVsOTAuNS05MC41YzI1LTI1IDY1LjUtMjUgOTAuNSAwIDcuOCA3LjggMTIuOSAxNy4yIDE1LjggMjcuMSAyLjQtMS40IDQuOC0yLjUgNi44LTQuNWw0Mi4xLTQyYy01LjQtOS4yLTExLjYtMTgtMTkuNC0yNS44IC01MC01MC0xMzEtNTAtMTgxIDBsLTkwLjUgOTAuNWMtNTAgNTAtNTAgMTMxIDAgMTgxIDUwIDUwIDEzMSA1MCAxODEgMGw2OC42LTY4LjZDMjc0LjYgMzk1LjEgMjQ2LjQgMzkyLjMgMjIwLjMgMzgyLjJ6Ii8+PC9zdmc+Cg==');opacity:0.5;visibility:hidden;display:inline-block;vertical-align:middle;}/*!sc*/
.drqpJr h1:hover>.share-link::before,.drqpJr h2:hover>.share-link::before,.drqpJr .share-link:hover::before{visibility:visible;}/*!sc*/
.drqpJr a{text-decoration:auto;color:#32329f;}/*!sc*/
.drqpJr a:visited{color:#32329f;}/*!sc*/
.drqpJr a:hover{color:#6868cf;text-decoration:auto;}/*!sc*/
.jnwENr{font-family:Roboto,sans-serif;font-weight:400;line-height:1.5em;}/*!sc*/
.jnwENr p:last-child{margin-bottom:0;}/*!sc*/
.jnwENr p:first-child{margin-top:0;}/*!sc*/
.jnwENr p:last-child{margin-bottom:0;}/*!sc*/
.jnwENr p{display:inline-block;}/*!sc*/
.jnwENr h1{font-family:Montserrat,sans-serif;font-weight:400;font-size:1.85714em;line-height:1.6em;color:#32329f;margin-top:0;}/*!sc*/
.jnwENr h2{font-family:Montserrat,sans-serif;font-weight:400;font-size:1.57143em;line-height:1.6em;color:#333333;}/*!sc*/
.jnwENr code{color:#e53935;background-color:rgba(38, 50, 56, 0.05);font-family:Courier,monospace;border-radius:2px;border:1px solid rgba(38, 50, 56, 0.1);padding:0 5px;font-size:13px;font-weight:400;word-break:break-word;}/*!sc*/
.jnwENr pre{font-family:Courier,monospace;white-space:pre;background-color:#11171a;color:white;padding:20px;overflow-x:auto;line-height:normal;border-radius:0;border:1px solid rgba(38, 50, 56, 0.1);}/*!sc*/
.jnwENr pre code{background-color:transparent;color:white;padding:0;}/*!sc*/
.jnwENr pre code:before,.jnwENr pre code:after{content:none;}/*!sc*/
.jnwENr blockquote{margin:0;margin-bottom:1em;padding:0 15px;color:#777;border-left:4px solid #ddd;}/*!sc*/
.jnwENr img{max-width:100%;box-sizing:content-box;}/*!sc*/
.jnwENr ul,.jnwENr ol{padding-left:2em;margin:0;margin-bottom:1em;}/*!sc*/
.jnwENr ul ul,.jnwENr ol ul,.jnwENr ul ol,.jnwENr ol ol{margin-bottom:0;margin-top:0;}/*!sc*/
.jnwENr table{display:block;width:100%;overflow:auto;word-break:normal;word-break:keep-all;border-collapse:collapse;border-spacing:0;margin-top:1.5em;margin-bottom:1.5em;}/*!sc*/
.jnwENr table tr{background-color:#fff;border-top:1px solid #ccc;}/*!sc*/
.jnwENr table tr:nth-child(2n){background-color:#fafafa;}/*!sc*/
.jnwENr table th,.jnwENr table td{padding:6px 13px;border:1px solid #ddd;}/*!sc*/
.jnwENr table th{text-align:left;font-weight:bold;}/*!sc*/
.jnwENr .share-link{cursor:pointer;margin-left:-20px;padding:0;line-height:1;width:20px;display:inline-block;outline:0;}/*!sc*/
.jnwENr .share-link:before{content:'';width:15px;height:15px;background-size:contain;background-image:url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZlcnNpb249IjEuMSIgeD0iMCIgeT0iMCIgd2lkdGg9IjUxMiIgaGVpZ2h0PSI1MTIiIHZpZXdCb3g9IjAgMCA1MTIgNTEyIiBlbmFibGUtYmFja2dyb3VuZD0ibmV3IDAgMCA1MTIgNTEyIiB4bWw6c3BhY2U9InByZXNlcnZlIj48cGF0aCBmaWxsPSIjMDEwMTAxIiBkPSJNNDU5LjcgMjMzLjRsLTkwLjUgOTAuNWMtNTAgNTAtMTMxIDUwLTE4MSAwIC03LjktNy44LTE0LTE2LjctMTkuNC0yNS44bDQyLjEtNDIuMWMyLTIgNC41LTMuMiA2LjgtNC41IDIuOSA5LjkgOCAxOS4zIDE1LjggMjcuMiAyNSAyNSA2NS42IDI0LjkgOTAuNSAwbDkwLjUtOTAuNWMyNS0yNSAyNS02NS42IDAtOTAuNSAtMjQuOS0yNS02NS41LTI1LTkwLjUgMGwtMzIuMiAzMi4yYy0yNi4xLTEwLjItNTQuMi0xMi45LTgxLjYtOC45bDY4LjYtNjguNmM1MC01MCAxMzEtNTAgMTgxIDBDNTA5LjYgMTAyLjMgNTA5LjYgMTgzLjQgNDU5LjcgMjMzLjR6TTIyMC4zIDM4Mi4ybC0zMi4yIDMyLjJjLTI1IDI0LjktNjUuNiAyNC45LTkwLjUgMCAtMjUtMjUtMjUtNjUuNiAwLTkwLjVsOTAuNS05MC41YzI1LTI1IDY1LjUtMjUgOTAuNSAwIDcuOCA3LjggMTIuOSAxNy4yIDE1LjggMjcuMSAyLjQtMS40IDQuOC0yLjUgNi44LTQuNWw0Mi4xLTQyYy01LjQtOS4yLTExLjYtMTgtMTkuNC0yNS44IC01MC01MC0xMzEtNTAtMTgxIDBsLTkwLjUgOTAuNWMtNTAgNTAtNTAgMTMxIDAgMTgxIDUwIDUwIDEzMSA1MCAxODEgMGw2OC42LTY4LjZDMjc0LjYgMzk1LjEgMjQ2LjQgMzkyLjMgMjIwLjMgMzgyLjJ6Ii8+PC9zdmc+Cg==');opacity:0.5;visibility:hidden;display:inline-block;vertical-align:middle;}/*!sc*/
.jnwENr h1:hover>.share-link::before,.jnwENr h2:hover>.share-link::before,.jnwENr .share-link:hover::before{visibility:visible;}/*!sc*/
.jnwENr a{text-decoration:auto;color:#32329f;}/*!sc*/
.jnwENr a:visited{color:#32329f;}/*!sc*/
.jnwENr a:hover{color:#6868cf;text-decoration:auto;}/*!sc*/
data-styled.g42[id="sc-fszimp"]{content:"kbZred,drqpJr,jnwENr,"}/*!sc*/
.ljKHqG{display:inline;}/*!sc*/
data-styled.g43[id="sc-etsjJW"]{content:"ljKHqG,"}/*!sc*/
.iNCOCX{position:relative;}/*!sc*/
data-styled.g44[id="sc-fYmhhH"]{content:"iNCOCX,"}/*!sc*/
.fdRrNy:hover>.sc-bbbBoY{opacity:1;}/*!sc*/
data-styled.g49[id="sc-dClGHI"]{content:"fdRrNy,"}/*!sc*/
.dFvLDb{font-family:Courier,monospace;font-size:13px;white-space:pre;contain:content;overflow-x:auto;}/*!sc*/
.dFvLDb .redoc-json code>.collapser{display:none;pointer-events:none;}/*!sc*/
.dFvLDb .callback-function{color:gray;}/*!sc*/
.dFvLDb .collapser:after{content:'-';cursor:pointer;}/*!sc*/
.dFvLDb .collapsed>.collapser:after{content:'+';cursor:pointer;}/*!sc*/
.dFvLDb .ellipsis:after{content:' … ';}/*!sc*/
.dFvLDb .collapsible{margin-left:2em;}/*!sc*/
.dFvLDb .hoverable{padding-top:1px;padding-bottom:1px;padding-left:2px;padding-right:2px;border-radius:2px;}/*!sc*/
.dFvLDb .hovered{background-color:rgba(235, 238, 249, 1);}/*!sc*/
.dFvLDb .collapser{background-color:transparent;border:0;color:#fff;font-family:Courier,monospace;font-size:13px;padding-right:6px;padding-left:6px;padding-top:0;padding-bottom:0;display:flex;align-items:center;justify-content:center;width:15px;height:15px;position:absolute;top:4px;left:-1.5em;cursor:default;user-select:none;-webkit-user-select:none;padding:2px;}/*!sc*/
.dFvLDb .collapser:focus{outline-color:#fff;outline-style:dotted;outline-width:1px;}/*!sc*/
.dFvLDb ul{list-style-type:none;padding:0px;margin:0px 0px 0px 26px;}/*!sc*/
.dFvLDb li{position:relative;display:block;}/*!sc*/
.dFvLDb .hoverable{display:inline-block;}/*!sc*/
.dFvLDb .selected{outline-style:solid;outline-width:1px;outline-style:dotted;}/*!sc*/
.dFvLDb .collapsed>.collapsible{display:none;}/*!sc*/
.dFvLDb .ellipsis{display:none;}/*!sc*/
.dFvLDb .collapsed>.ellipsis{display:inherit;}/*!sc*/
data-styled.g50[id="sc-fhfEft"]{content:"dFvLDb,"}/*!sc*/
.iNRAJK{padding:0.9em;background-color:rgba(38,50,56,0.4);margin:0 0 10px 0;display:block;font-family:Montserrat,sans-serif;font-size:0.929em;line-height:1.5em;}/*!sc*/
data-styled.g51[id="sc-bAehkN"]{content:"iNRAJK,"}/*!sc*/
.cXitJ{font-family:Montserrat,sans-serif;font-size:12px;position:absolute;z-index:1;top:-11px;left:12px;font-weight:600;color:rgba(255,255,255,0.7);}/*!sc*/
data-styled.g52[id="sc-gahYZc"]{content:"cXitJ,"}/*!sc*/
.iLdyBp{position:relative;}/*!sc*/
data-styled.g53[id="sc-bSFBcf"]{content:"iLdyBp,"}/*!sc*/
.eKKwxo{margin-top:15px;}/*!sc*/
data-styled.g56[id="sc-blIAwI"]{content:"eKKwxo,"}/*!sc*/
.kdPQHX.deprecated span.property-name{text-decoration:line-through;color:#707070;}/*!sc*/
.kdPQHX button{background-color:transparent;border:0;outline:0;font-size:13px;font-family:Courier,monospace;cursor:pointer;padding:0;color:#333333;}/*!sc*/
.kdPQHX button:focus{font-weight:600;}/*!sc*/
.kdPQHX .sc-dntSTA{height:1.1em;width:1.1em;}/*!sc*/
.kdPQHX .sc-dntSTA polygon{fill:#666;}/*!sc*/
data-styled.g57[id="sc-itBLYH"]{content:"kdPQHX,"}/*!sc*/
.lhyyLL{vertical-align:middle;font-size:13px;line-height:20px;}/*!sc*/
data-styled.g58[id="sc-bEjUoa"]{content:"lhyyLL,"}/*!sc*/
.jYezsP{color:rgba(102,102,102,0.9);}/*!sc*/
data-styled.g59[id="sc-boKDdR"]{content:"jYezsP,"}/*!sc*/
.dbKJYq{color:#666;}/*!sc*/
data-styled.g60[id="sc-fOOuSg"]{content:"dbKJYq,"}/*!sc*/
.nwQTz{color:#666;word-break:break-word;}/*!sc*/
data-styled.g61[id="sc-hdBJTi"]{content:"nwQTz,"}/*!sc*/
.crXmiY{color:#d41f1c;font-size:0.9em;font-weight:normal;margin-left:20px;line-height:1;}/*!sc*/
data-styled.g62[id="sc-iIvHqT"]{content:"crXmiY,"}/*!sc*/
.UZcrz{color:#0e7c86;font-family:Courier,monospace;font-size:12px;}/*!sc*/
.UZcrz::before,.UZcrz::after{content:' ';}/*!sc*/
data-styled.g65[id="sc-cpclqO"]{content:"UZcrz,"}/*!sc*/
.kMQdIk{border-radius:2px;word-break:break-word;background-color:rgba(51,51,51,0.05);color:rgba(51,51,51,0.9);padding:0 5px;border:1px solid rgba(51,51,51,0.1);font-family:Courier,monospace;}/*!sc*/
+{margin-left:0;}/*!sc*/
data-styled.g66[id="sc-dTWiOz"]{content:"kMQdIk,"}/*!sc*/
.bDfgbe{border-radius:2px;background-color:rgba(104,104,207,0.05);color:rgba(50,50,159,0.9);margin:0 5px;padding:0 5px;border:1px solid rgba(50,50,159,0.1);}/*!sc*/
+{margin-left:0;}/*!sc*/
data-styled.g68[id="sc-goiVcJ"]{content:"bDfgbe,"}/*!sc*/
.hPcPCj{margin-top:0;margin-bottom:0.5em;}/*!sc*/
data-styled.g92[id="sc-jCWzJg"]{content:"hPcPCj,"}/*!sc*/
.NmQLu{width:9ex;display:inline-block;height:13px;line-height:13px;background-color:#333;border-radius:3px;background-repeat:no-repeat;background-position:6px 4px;font-size:7px;font-family:Verdana,sans-serif;color:white;text-transform:uppercase;text-align:center;font-weight:bold;vertical-align:middle;margin-right:6px;margin-top:2px;}/*!sc*/
.NmQLu.get{background-color:#2F8132;}/*!sc*/
.NmQLu.post{background-color:#186FAF;}/*!sc*/
.NmQLu.put{background-color:#95507c;}/*!sc*/
.NmQLu.options{background-color:#947014;}/*!sc*/
.NmQLu.patch{background-color:#bf581d;}/*!sc*/
.NmQLu.delete{background-color:#cc3333;}/*!sc*/
.NmQLu.basic{background-color:#707070;}/*!sc*/
.NmQLu.link{background-color:#07818F;}/*!sc*/
.NmQLu.head{background-color:#A23DAD;}/*!sc*/
.NmQLu.hook{background-color:#32329f;}/*!sc*/
.NmQLu.schema{background-color:#707070;}/*!sc*/
data-styled.g100[id="sc-jxYSNo"]{content:"NmQLu,"}/*!sc*/
.gAPKXX{margin:0;padding:0;}/*!sc*/
.gAPKXX:first-child{padding-bottom:32px;}/*!sc*/
.sc-zOxLx .sc-zOxLx{font-size:0.929em;}/*!sc*/
.dQnkdy{margin:0;padding:0;display:none;}/*!sc*/
.dQnkdy:first-child{padding-bottom:32px;}/*!sc*/
.sc-zOxLx .sc-zOxLx{font-size:0.929em;}/*!sc*/
data-styled.g101[id="sc-zOxLx"]{content:"gAPKXX,dQnkdy,"}/*!sc*/
.ixknQI{list-style:none inside none;overflow:hidden;text-overflow:ellipsis;padding:0;}/*!sc*/
data-styled.g102[id="sc-cgHfjM"]{content:"ixknQI,"}/*!sc*/
.lgPGwq{cursor:pointer;color:#333333;margin:0;padding:12.5px 20px;display:flex;justify-content:space-between;font-family:Montserrat,sans-serif;font-size:0.929em;text-transform:none;background-color:#fafafa;}/*!sc*/
.lgPGwq:hover{color:#32329f;background-color:#e1e1e1;}/*!sc*/
.lgPGwq .sc-dntSTA{height:1.5em;width:1.5em;}/*!sc*/
.lgPGwq .sc-dntSTA polygon{fill:#333333;}/*!sc*/
.iHRgeo{cursor:pointer;color:#333333;margin:0;padding:12.5px 20px;display:flex;justify-content:space-between;font-family:Montserrat,sans-serif;background-color:#fafafa;}/*!sc*/
.iHRgeo:hover{color:#32329f;background-color:#ededed;}/*!sc*/
.iHRgeo .sc-dntSTA{height:1.5em;width:1.5em;}/*!sc*/
.iHRgeo .sc-dntSTA polygon{fill:#333333;}/*!sc*/
data-styled.g103[id="sc-fpikKz"]{content:"lgPGwq,iHRgeo,"}/*!sc*/
.cxcra{display:inline-block;vertical-align:middle;width:calc(100% - 38px);overflow:hidden;text-overflow:ellipsis;}/*!sc*/
data-styled.g104[id="sc-gWaSiO"]{content:"cxcra,"}/*!sc*/
.QuyG{font-size:0.8em;margin-top:10px;text-align:center;position:fixed;width:260px;bottom:0;background:#fafafa;}/*!sc*/
.QuyG a,.QuyG a:visited,.QuyG a:hover{color:#333333!important;padding:5px 0;border-top:1px solid #e1e1e1;text-decoration:none;display:flex;align-items:center;justify-content:center;}/*!sc*/
.QuyG img{width:15px;margin-right:5px;}/*!sc*/
@media screen and (max-width: 50rem){.QuyG{width:100%;}}/*!sc*/
data-styled.g105[id="sc-kSaXSp"]{content:"QuyG,"}/*!sc*/
.jjnszm{cursor:pointer;position:relative;margin-bottom:5px;}/*!sc*/
data-styled.g111[id="sc-eZSpzM"]{content:"jjnszm,"}/*!sc*/
.kZcHWP{font-family:Courier,monospace;margin-left:10px;flex:1;overflow-x:hidden;text-overflow:ellipsis;}/*!sc*/
data-styled.g112[id="sc-jvKoal"]{content:"kZcHWP,"}/*!sc*/
.iPCVMX{outline:0;color:inherit;width:100%;text-align:left;cursor:pointer;padding:10px 30px 10px 20px;border-radius:4px 4px 0 0;background-color:#11171a;display:flex;white-space:nowrap;align-items:center;border:1px solid transparent;border-bottom:0;transition:border-color 0.25s ease;}/*!sc*/
.iPCVMX ..sc-jvKoal{color:#ffffff;}/*!sc*/
.iPCVMX:focus{box-shadow:inset 0 2px 2px rgba(0, 0, 0, 0.45),0 2px 0 rgba(128, 128, 128, 0.25);}/*!sc*/
data-styled.g113[id="sc-buTqWO"]{content:"iPCVMX,"}/*!sc*/
.kwcmyC{font-size:0.929em;line-height:20px;background-color:#186FAF;color:#ffffff;padding:3px 10px;text-transform:uppercase;font-family:Montserrat,sans-serif;margin:0;}/*!sc*/
.dynMBc{font-size:0.929em;line-height:20px;background-color:#2F8132;color:#ffffff;padding:3px 10px;text-transform:uppercase;font-family:Montserrat,sans-serif;margin:0;}/*!sc*/
.dBzsUh{font-size:0.929em;line-height:20px;background-color:#95507c;color:#ffffff;padding:3px 10px;text-transform:uppercase;font-family:Montserrat,sans-serif;margin:0;}/*!sc*/
.gKcHYQ{font-size:0.929em;line-height:20px;background-color:#cc3333;color:#ffffff;padding:3px 10px;text-transform:uppercase;font-family:Montserrat,sans-serif;margin:0;}/*!sc*/
data-styled.g114[id="sc-fQLpxn"]{content:"kwcmyC,dynMBc,dBzsUh,gKcHYQ,"}/*!sc*/
.ga-DQLq{position:absolute;width:100%;z-index:100;background:#fafafa;color:#263238;box-sizing:border-box;box-shadow:0 0 6px rgba(0, 0, 0, 0.33);overflow:hidden;border-bottom-left-radius:4px;border-bottom-right-radius:4px;transition:all 0.25s ease;visibility:hidden;transform:translateY(-50%) scaleY(0);}/*!sc*/
data-styled.g115[id="sc-ecJghI"]{content:"ga-DQLq,"}/*!sc*/
.icOxsG{padding:10px;}/*!sc*/
data-styled.g116[id="sc-iyBeIh"]{content:"icOxsG,"}/*!sc*/
.okJpy{padding:5px;border:1px solid #ccc;background:#fff;word-break:break-all;color:#32329f;}/*!sc*/
.okJpy >span{color:#333333;}/*!sc*/
data-styled.g117[id="sc-xKhEK"]{content:"okJpy,"}/*!sc*/
.lkmdtA{display:block;border:0;width:100%;text-align:left;padding:10px;border-radius:2px;margin-bottom:4px;line-height:1.5em;cursor:pointer;color:#1d8127;background-color:rgba(29,129,39,0.07);}/*!sc*/
.lkmdtA:focus{outline:auto #1d8127;}/*!sc*/
.ifAHvq{display:block;border:0;width:100%;text-align:left;padding:10px;border-radius:2px;margin-bottom:4px;line-height:1.5em;cursor:pointer;color:#d41f1c;background-color:rgba(212,31,28,0.07);}/*!sc*/
.ifAHvq:focus{outline:auto #d41f1c;}/*!sc*/
.kQCDrg{display:block;border:0;width:100%;text-align:left;padding:10px;border-radius:2px;margin-bottom:4px;line-height:1.5em;cursor:pointer;color:#d41f1c;background-color:rgba(212,31,28,0.07);cursor:default;}/*!sc*/
.kQCDrg:focus{outline:auto #d41f1c;}/*!sc*/
.kQCDrg::before{content:"—";font-weight:bold;width:1.5em;text-align:center;display:inline-block;vertical-align:top;}/*!sc*/
.kQCDrg:focus{outline:0;}/*!sc*/
.oZuve{display:block;border:0;width:100%;text-align:left;padding:10px;border-radius:2px;margin-bottom:4px;line-height:1.5em;cursor:pointer;color:#1d8127;background-color:rgba(29,129,39,0.07);cursor:default;}/*!sc*/
.oZuve:focus{outline:auto #1d8127;}/*!sc*/
.oZuve::before{content:"—";font-weight:bold;width:1.5em;text-align:center;display:inline-block;vertical-align:top;}/*!sc*/
.oZuve:focus{outline:0;}/*!sc*/
data-styled.g120[id="sc-jIDBmd"]{content:"lkmdtA,ifAHvq,kQCDrg,oZuve,"}/*!sc*/
.fBhAXU{vertical-align:top;}/*!sc*/
data-styled.g123[id="sc-eJvlPh"]{content:"fBhAXU,"}/*!sc*/
.kjrVcG{font-size:1.3em;padding:0.2em 0;margin:3em 0 1.1em;color:#333333;font-weight:normal;}/*!sc*/
data-styled.g124[id="sc-gDzyrw"]{content:"kjrVcG,"}/*!sc*/
.txIPi{margin-bottom:30px;}/*!sc*/
data-styled.g129[id="sc-bfjeOH"]{content:"txIPi,"}/*!sc*/
.crXcHD{user-select:none;width:20px;height:20px;align-self:center;display:flex;flex-direction:column;color:#32329f;}/*!sc*/
data-styled.g130[id="sc-cZnrqW"]{content:"crXcHD,"}/*!sc*/
.dsiHUZ{width:260px;background-color:#fafafa;overflow:hidden;display:flex;flex-direction:column;backface-visibility:hidden;height:100vh;position:sticky;position:-webkit-sticky;top:0;}/*!sc*/
@media screen and (max-width: 50rem){.dsiHUZ{position:fixed;z-index:20;width:100%;background:#fafafa;display:none;}}/*!sc*/
@media print{.dsiHUZ{display:none;}}/*!sc*/
data-styled.g131[id="sc-fstJre"]{content:"dsiHUZ,"}/*!sc*/
.bovaLG{outline:none;user-select:none;background-color:#f2f2f2;color:#32329f;display:none;cursor:pointer;position:fixed;right:20px;z-index:100;border-radius:50%;box-shadow:0 0 20px rgba(0, 0, 0, 0.3);bottom:44px;width:60px;height:60px;padding:0 20px;}/*!sc*/
@media screen and (max-width: 50rem){.bovaLG{display:flex;}}/*!sc*/
.bovaLG svg{color:#0065FB;}/*!sc*/
@media print{.bovaLG{display:none;}}/*!sc*/
data-styled.g132[id="sc-jOlHRD"]{content:"bovaLG,"}/*!sc*/
.eHdqcJ{font-family:Roboto,sans-serif;font-size:14px;font-weight:400;line-height:1.5em;color:#333333;display:flex;position:relative;text-align:left;-webkit-font-smoothing:antialiased;font-smoothing:antialiased;text-rendering:optimizeSpeed!important;tap-highlight-color:rgba(0, 0, 0, 0);text-size-adjust:100%;}/*!sc*/
.eHdqcJ *{box-sizing:border-box;-webkit-tap-highlight-color:rgba(255, 255, 255, 0);}/*!sc*/
data-styled.g133[id="sc-Pgsbw"]{content:"eHdqcJ,"}/*!sc*/
.buanwU{z-index:1;position:relative;overflow:hidden;width:calc(100% - 260px);contain:layout;}/*!sc*/
@media print,screen and (max-width: 50rem){.buanwU{width:100%;}}/*!sc*/
data-styled.g134[id="sc-fkYqBV"]{content:"buanwU,"}/*!sc*/
.iZqpqg{background:#263238;position:absolute;top:0;bottom:0;right:0;width:calc((100% - 260px) * 0.4);}/*!sc*/
@media print,screen and (max-width: 75rem){.iZqpqg{display:none;}}/*!sc*/
data-styled.g135[id="sc-evkzZa"]{content:"iZqpqg,"}/*!sc*/
.gzMPIt{padding:5px 0;}/*!sc*/
data-styled.g136[id="sc-iRcyzz"]{content:"gzMPIt,"}/*!sc*/
.iOkeQy{width:calc(100% - 40px);box-sizing:border-box;margin:0 20px;padding:5px 10px 5px 20px;border:0;border-bottom:1px solid #e1e1e1;font-family:Roboto,sans-serif;font-weight:bold;font-size:13px;color:#333333;background-color:transparent;outline:none;}/*!sc*/
data-styled.g137[id="sc-lhsSio"]{content:"iOkeQy,"}/*!sc*/
.SikXG{position:absolute;left:20px;height:1.8em;width:0.9em;}/*!sc*/
.SikXG path{fill:#333333;}/*!sc*/
data-styled.g138[id="sc-enPhjR"]{content:"SikXG,"}/*!sc*/
</style>
  <link href="https://fonts.googleapis.com/css?family=Montserrat:300,400,700|Roboto:300,400,700" rel="stylesheet">
</head>

<body>
  
      <div id="redoc"><div class="sc-Pgsbw eHdqcJ redoc-wrap"><div class="sc-fstJre dsiHUZ menu-content" style="top:0px;height:calc(100vh - 0px)"><div role="search" class="sc-iRcyzz gzMPIt"><svg class="sc-enPhjR SikXG search-icon" version="1.1" viewBox="0 0 1000 1000" x="0px" xmlns="http://www.w3.org/2000/svg" y="0px"><path d="M968.2,849.4L667.3,549c83.9-136.5,66.7-317.4-51.7-435.6C477.1-25,252.5-25,113.9,113.4c-138.5,138.3-138.5,362.6,0,501C219.2,730.1,413.2,743,547.6,666.5l301.9,301.4c43.6,43.6,76.9,14.9,104.2-12.4C981,928.3,1011.8,893,968.2,849.4z M524.5,522c-88.9,88.7-233,88.7-321.8,0c-88.9-88.7-88.9-232.6,0-321.3c88.9-88.7,233-88.7,321.8,0C613.4,289.4,613.4,433.3,524.5,522z"></path></svg><input placeholder="Search..." aria-label="Search" type="text" class="sc-lhsSio iOkeQy search-input" value=""/></div><div class="sc-eknHtZ ghzOpX scrollbar-container undefined"><ul role="menu" class="sc-zOxLx gAPKXX"><li tabindex="0" depth="1" data-item-id="tag/Authentication" role="menuitem" aria-label="Authentication" aria-expanded="false" class="sc-cgHfjM ixknQI"><label class="sc-fpikKz lgPGwq -depth1"><span width="calc(100% - 38px)" title="Authentication" class="sc-gWaSiO cxcra">Authentication</span><svg class="sc-dntSTA dUlzCe" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg></label><ul class="sc-zOxLx dQnkdy"><li tabindex="0" depth="2" data-item-id="tag/Authentication/paths/~1auth~1signup/post" role="menuitem" aria-label="User Registration" aria-expanded="false" class="sc-cgHfjM ixknQI"><label class="sc-fpikKz iHRgeo -depth2"><span type="post" class="sc-jxYSNo NmQLu operation-type post">post</span><span tabindex="0" width="calc(100% - 38px)" class="sc-gWaSiO cxcra">User Registration</span></label></li><li tabindex="0" depth="2" data-item-id="tag/Authentication/paths/~1auth~1login/post" role="menuitem" aria-label="User Login" aria-expanded="false" class="sc-cgHfjM ixknQI"><label class="sc-fpikKz iHRgeo -depth2"><span type="post" class="sc-jxYSNo NmQLu operation-type post">post</span><span tabindex="0" width="calc(100% - 38px)" class="sc-gWaSiO cxcra">User Login</span></label></li><li tabindex="0" depth="2" data-item-id="tag/Authentication/paths/~1auth~1logout/post" role="menuitem" aria-label="User Logout" aria-expanded="false" class="sc-cgHfjM ixknQI"><label class="sc-fpikKz iHRgeo -depth2"><span type="post" class="sc-jxYSNo NmQLu operation-type post">post</span><span tabindex="0" width="calc(100% - 38px)" class="sc-gWaSiO cxcra">User Logout</span></label></li></ul></li><li tabindex="0" depth="1" data-item-id="tag/Properties" role="menuitem" aria-label="Properties" aria-expanded="false" class="sc-cgHfjM ixknQI"><label class="sc-fpikKz lgPGwq -depth1"><span width="calc(100% - 38px)" title="Properties" class="sc-gWaSiO cxcra">Properties</span><svg class="sc-dntSTA dUlzCe" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg></label><ul class="sc-zOxLx dQnkdy"><li tabindex="0" depth="2" data-item-id="tag/Properties/paths/~1properties/post" role="menuitem" aria-label="Create Property" aria-expanded="false" class="sc-cgHfjM ixknQI"><label class="sc-fpikKz iHRgeo -depth2"><span type="post" class="sc-jxYSNo NmQLu operation-type post">post</span><span tabindex="0" width="calc(100% - 38px)" class="sc-gWaSiO cxcra">Create Property</span></label></li><li tabindex="0" depth="2" data-item-id="tag/Properties/paths/~1properties/get" role="menuitem" aria-label="Get Properties" aria-expanded="false" class="sc-cgHfjM ixknQI"><label class="sc-fpikKz iHRgeo -depth2"><span type="get" class="sc-jxYSNo NmQLu operation-type get">get</span><span tabindex="0" width="calc(100% - 38px)" class="sc-gWaSiO cxcra">Get Properties</span></label></li><li tabindex="0" depth="2" data-item-id="tag/Properties/paths/~1properties~1{propertyId}/get" role="menuitem" aria-label="Get Property Details" aria-expanded="false" class="sc-cgHfjM ixknQI"><label class="sc-fpikKz iHRgeo -depth2"><span type="get" class="sc-jxYSNo NmQLu operation-type get">get</span><span tabindex="0" width="calc(100% - 38px)" class="sc-gWaSiO cxcra">Get Property Details</span></label></li><li tabindex="0" depth="2" data-item-id="tag/Properties/paths/~1properties~1{propertyId}/put" role="menuitem" aria-label="Update Property" aria-expanded="false" class="sc-cgHfjM ixknQI"><label class="sc-fpikKz iHRgeo -depth2"><span type="put" class="sc-jxYSNo NmQLu operation-type put">put</span><span tabindex="0" width="calc(100% - 38px)" class="sc-gWaSiO cxcra">Update Property</span></label></li><li tabindex="0" depth="2" data-item-id="tag/Properties/paths/~1properties~1{propertyId}/delete" role="menuitem" aria-label="Delete Property" aria-expanded="false" class="sc-cgHfjM ixknQI"><label class="sc-fpikKz iHRgeo -depth2"><span type="delete" class="sc-jxYSNo NmQLu operation-type delete">del</span><span tabindex="0" width="calc(100% - 38px)" class="sc-gWaSiO cxcra">Delete Property</span></label></li><li tabindex="0" depth="2" data-item-id="tag/Properties/paths/~1properties~1{propertyId}~1rooms/post" role="menuitem" aria-label="Create Room" aria-expanded="false" class="sc-cgHfjM ixknQI"><label class="sc-fpikKz iHRgeo -depth2"><span type="post" class="sc-jxYSNo NmQLu operation-type post">post</span><span tabindex="0" width="calc(100% - 38px)" class="sc-gWaSiO cxcra">Create Room</span></label></li><li tabindex="0" depth="2" data-item-id="tag/Properties/paths/~1properties~1{propertyId}~1rooms~1{roomId}/get" role="menuitem" aria-label="/properties/{propertyId}/rooms/{roomId}" aria-expanded="false" class="sc-cgHfjM ixknQI"><label class="sc-fpikKz iHRgeo -depth2"><span type="get" class="sc-jxYSNo NmQLu operation-type get">get</span><span tabindex="0" width="calc(100% - 38px)" class="sc-gWaSiO cxcra">/properties/{propertyId}/rooms/{roomId}</span></label></li><li tabindex="0" depth="2" data-item-id="tag/Properties/paths/~1properties~1{propertyId}~1rooms~1{roomId}/put" role="menuitem" aria-label="/properties/{propertyId}/rooms/{roomId}" aria-expanded="false" class="sc-cgHfjM ixknQI"><label class="sc-fpikKz iHRgeo -depth2"><span type="put" class="sc-jxYSNo NmQLu operation-type put">put</span><span tabindex="0" width="calc(100% - 38px)" class="sc-gWaSiO cxcra">/properties/{propertyId}/rooms/{roomId}</span></label></li><li tabindex="0" depth="2" data-item-id="tag/Properties/paths/~1properties~1{propertyId}~1rooms~1{roomId}/delete" role="menuitem" aria-label="/properties/{propertyId}/rooms/{roomId}" aria-expanded="false" class="sc-cgHfjM ixknQI"><label class="sc-fpikKz iHRgeo -depth2"><span type="delete" class="sc-jxYSNo NmQLu operation-type delete">del</span><span tabindex="0" width="calc(100% - 38px)" class="sc-gWaSiO cxcra">/properties/{propertyId}/rooms/{roomId}</span></label></li></ul></li><li tabindex="0" depth="1" data-item-id="tag/Leases" role="menuitem" aria-label="Leases" aria-expanded="false" class="sc-cgHfjM ixknQI"><label class="sc-fpikKz lgPGwq -depth1"><span width="calc(100% - 38px)" title="Leases" class="sc-gWaSiO cxcra">Leases</span><svg class="sc-dntSTA dUlzCe" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg></label><ul class="sc-zOxLx dQnkdy"><li tabindex="0" depth="2" data-item-id="tag/Leases/paths/~1leases/post" role="menuitem" aria-label="Create Lease" aria-expanded="false" class="sc-cgHfjM ixknQI"><label class="sc-fpikKz iHRgeo -depth2"><span type="post" class="sc-jxYSNo NmQLu operation-type post">post</span><span tabindex="0" width="calc(100% - 38px)" class="sc-gWaSiO cxcra">Create Lease</span></label></li><li tabindex="0" depth="2" data-item-id="tag/Leases/paths/~1leases/get" role="menuitem" aria-label="Get Leases" aria-expanded="false" class="sc-cgHfjM ixknQI"><label class="sc-fpikKz iHRgeo -depth2"><span type="get" class="sc-jxYSNo NmQLu operation-type get">get</span><span tabindex="0" width="calc(100% - 38px)" class="sc-gWaSiO cxcra">Get Leases</span></label></li><li tabindex="0" depth="2" data-item-id="tag/Leases/paths/~1leases~1{leaseId}/get" role="menuitem" aria-label="/leases/{leaseId}" aria-expanded="false" class="sc-cgHfjM ixknQI"><label class="sc-fpikKz iHRgeo -depth2"><span type="get" class="sc-jxYSNo NmQLu operation-type get">get</span><span tabindex="0" width="calc(100% - 38px)" class="sc-gWaSiO cxcra">/leases/{leaseId}</span></label></li><li tabindex="0" depth="2" data-item-id="tag/Leases/paths/~1leases~1{leaseId}/put" role="menuitem" aria-label="/leases/{leaseId}" aria-expanded="false" class="sc-cgHfjM ixknQI"><label class="sc-fpikKz iHRgeo -depth2"><span type="put" class="sc-jxYSNo NmQLu operation-type put">put</span><span tabindex="0" width="calc(100% - 38px)" class="sc-gWaSiO cxcra">/leases/{leaseId}</span></label></li><li tabindex="0" depth="2" data-item-id="tag/Leases/paths/~1leases~1{leaseId}/delete" role="menuitem" aria-label="/leases/{leaseId}" aria-expanded="false" class="sc-cgHfjM ixknQI"><label class="sc-fpikKz iHRgeo -depth2"><span type="delete" class="sc-jxYSNo NmQLu operation-type delete">del</span><span tabindex="0" width="calc(100% - 38px)" class="sc-gWaSiO cxcra">/leases/{leaseId}</span></label></li><li tabindex="0" depth="2" data-item-id="tag/Leases/paths/~1v1~1leases~1{leaseId}~1tenants~1self/put" role="menuitem" aria-label="/v1/leases/{leaseId}/tenants/self" aria-expanded="false" class="sc-cgHfjM ixknQI"><label class="sc-fpikKz iHRgeo -depth2"><span type="put" class="sc-jxYSNo NmQLu operation-type put">put</span><span tabindex="0" width="calc(100% - 38px)" class="sc-gWaSiO cxcra">/v1/leases/{leaseId}/tenants/self</span></label></li></ul></li><li tabindex="0" depth="1" data-item-id="tag/Tenants" role="menuitem" aria-label="Tenants" aria-expanded="false" class="sc-cgHfjM ixknQI"><label class="sc-fpikKz lgPGwq -depth1"><span width="calc(100% - 38px)" title="Tenants" class="sc-gWaSiO cxcra">Tenants</span><svg class="sc-dntSTA dUlzCe" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg></label><ul class="sc-zOxLx dQnkdy"><li tabindex="0" depth="2" data-item-id="tag/Tenants/paths/~1leases~1{leaseId}~1tenants/post" role="menuitem" aria-label="/leases/{leaseId}/tenants" aria-expanded="false" class="sc-cgHfjM ixknQI"><label class="sc-fpikKz iHRgeo -depth2"><span type="post" class="sc-jxYSNo NmQLu operation-type post">post</span><span tabindex="0" width="calc(100% - 38px)" class="sc-gWaSiO cxcra">/leases/{leaseId}/tenants</span></label></li><li tabindex="0" depth="2" data-item-id="tag/Tenants/paths/~1leases~1{leaseId}~1tenants~1{tenantId}/get" role="menuitem" aria-label="/leases/{leaseId}/tenants/{tenantId}" aria-expanded="false" class="sc-cgHfjM ixknQI"><label class="sc-fpikKz iHRgeo -depth2"><span type="get" class="sc-jxYSNo NmQLu operation-type get">get</span><span tabindex="0" width="calc(100% - 38px)" class="sc-gWaSiO cxcra">/leases/{leaseId}/tenants/{tenantId}</span></label></li><li tabindex="0" depth="2" data-item-id="tag/Tenants/paths/~1leases~1{leaseId}~1tenants~1{tenantId}/put" role="menuitem" aria-label="/leases/{leaseId}/tenants/{tenantId}" aria-expanded="false" class="sc-cgHfjM ixknQI"><label class="sc-fpikKz iHRgeo -depth2"><span type="put" class="sc-jxYSNo NmQLu operation-type put">put</span><span tabindex="0" width="calc(100% - 38px)" class="sc-gWaSiO cxcra">/leases/{leaseId}/tenants/{tenantId}</span></label></li><li tabindex="0" depth="2" data-item-id="tag/Tenants/paths/~1leases~1{leaseId}~1tenants~1{tenantId}/delete" role="menuitem" aria-label="/leases/{leaseId}/tenants/{tenantId}" aria-expanded="false" class="sc-cgHfjM ixknQI"><label class="sc-fpikKz iHRgeo -depth2"><span type="delete" class="sc-jxYSNo NmQLu operation-type delete">del</span><span tabindex="0" width="calc(100% - 38px)" class="sc-gWaSiO cxcra">/leases/{leaseId}/tenants/{tenantId}</span></label></li></ul></li><li tabindex="0" depth="1" data-item-id="tag/Payments" role="menuitem" aria-label="Payments" aria-expanded="false" class="sc-cgHfjM ixknQI"><label class="sc-fpikKz lgPGwq -depth1"><span width="calc(100% - 38px)" title="Payments" class="sc-gWaSiO cxcra">Payments</span><svg class="sc-dntSTA dUlzCe" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg></label><ul class="sc-zOxLx dQnkdy"><li tabindex="0" depth="2" data-item-id="tag/Payments/paths/~1leases~1{leaseId}~1tenant-payments/post" role="menuitem" aria-label="/leases/{leaseId}/tenant-payments" aria-expanded="false" class="sc-cgHfjM ixknQI"><label class="sc-fpikKz iHRgeo -depth2"><span type="post" class="sc-jxYSNo NmQLu operation-type post">post</span><span tabindex="0" width="calc(100% - 38px)" class="sc-gWaSiO cxcra">/leases/{leaseId}/tenant-payments</span></label></li><li tabindex="0" depth="2" data-item-id="tag/Payments/paths/~1leases~1{leaseId}~1tenant-payments~1{tenantPaymentId}/get" role="menuitem" aria-label="/leases/{leaseId}/tenant-payments/{tenantPaymentId}" aria-expanded="false" class="sc-cgHfjM ixknQI"><label class="sc-fpikKz iHRgeo -depth2"><span type="get" class="sc-jxYSNo NmQLu operation-type get">get</span><span tabindex="0" width="calc(100% - 38px)" class="sc-gWaSiO cxcra">/leases/{leaseId}/tenant-payments/{tenantPaymentId}</span></label></li><li tabindex="0" depth="2" data-item-id="tag/Payments/paths/~1leases~1{leaseId}~1tenant-payments~1{tenantPaymentId}/put" role="menuitem" aria-label="/leases/{leaseId}/tenant-payments/{tenantPaymentId}" aria-expanded="false" class="sc-cgHfjM ixknQI"><label class="sc-fpikKz iHRgeo -depth2"><span type="put" class="sc-jxYSNo NmQLu operation-type put">put</span><span tabindex="0" width="calc(100% - 38px)" class="sc-gWaSiO cxcra">/leases/{leaseId}/tenant-payments/{tenantPaymentId}</span></label></li><li tabindex="0" depth="2" data-item-id="tag/Payments/paths/~1leases~1{leaseId}~1tenant-payments~1{tenantPaymentId}/delete" role="menuitem" aria-label="/leases/{leaseId}/tenant-payments/{tenantPaymentId}" aria-expanded="false" class="sc-cgHfjM ixknQI"><label class="sc-fpikKz iHRgeo -depth2"><span type="delete" class="sc-jxYSNo NmQLu operation-type delete">del</span><span tabindex="0" width="calc(100% - 38px)" class="sc-gWaSiO cxcra">/leases/{leaseId}/tenant-payments/{tenantPaymentId}</span></label></li></ul></li><li tabindex="0" depth="1" data-item-id="tag/Documents" role="menuitem" aria-label="Documents" aria-expanded="false" class="sc-cgHfjM ixknQI"><label class="sc-fpikKz lgPGwq -depth1"><span width="calc(100% - 38px)" title="Documents" class="sc-gWaSiO cxcra">Documents</span><svg class="sc-dntSTA dUlzCe" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg></label><ul class="sc-zOxLx dQnkdy"><li tabindex="0" depth="2" data-item-id="tag/Documents/paths/~1v1~1documents~1{fileId}/get" role="menuitem" aria-label="/v1/documents/{fileId}" aria-expanded="false" class="sc-cgHfjM ixknQI"><label class="sc-fpikKz iHRgeo -depth2"><span type="get" class="sc-jxYSNo NmQLu operation-type get">get</span><span tabindex="0" width="calc(100% - 38px)" class="sc-gWaSiO cxcra">/v1/documents/{fileId}</span></label></li></ul></li><li tabindex="0" depth="1" data-item-id="tag/Referrals" role="menuitem" aria-label="Referrals" aria-expanded="false" class="sc-cgHfjM ixknQI"><label class="sc-fpikKz lgPGwq -depth1"><span width="calc(100% - 38px)" title="Referrals" class="sc-gWaSiO cxcra">Referrals</span><svg class="sc-dntSTA dUlzCe" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg></label><ul class="sc-zOxLx dQnkdy"><li tabindex="0" depth="2" data-item-id="tag/Referrals/paths/~1v1~1referral-codes~1{code}~1validation/get" role="menuitem" aria-label="/v1/referral-codes/{code}/validation" aria-expanded="false" class="sc-cgHfjM ixknQI"><label class="sc-fpikKz iHRgeo -depth2"><span type="get" class="sc-jxYSNo NmQLu operation-type get">get</span><span tabindex="0" width="calc(100% - 38px)" class="sc-gWaSiO cxcra">/v1/referral-codes/{code}/validation</span></label></li><li tabindex="0" depth="2" data-item-id="tag/Referrals/paths/~1v1~1referral-code-applications/post" role="menuitem" aria-label="/v1/referral-code-applications" aria-expanded="false" class="sc-cgHfjM ixknQI"><label class="sc-fpikKz iHRgeo -depth2"><span type="post" class="sc-jxYSNo NmQLu operation-type post">post</span><span tabindex="0" width="calc(100% - 38px)" class="sc-gWaSiO cxcra">/v1/referral-code-applications</span></label></li></ul></li><li tabindex="0" depth="1" data-item-id="tag/Subscriptions" role="menuitem" aria-label="Subscriptions" aria-expanded="false" class="sc-cgHfjM ixknQI"><label class="sc-fpikKz lgPGwq -depth1"><span width="calc(100% - 38px)" title="Subscriptions" class="sc-gWaSiO cxcra">Subscriptions</span><svg class="sc-dntSTA dUlzCe" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg></label><ul class="sc-zOxLx dQnkdy"><li tabindex="0" depth="2" data-item-id="tag/Subscriptions/paths/~1v1~1subscriptions~1{id}/delete" role="menuitem" aria-label="/v1/subscriptions/{id}" aria-expanded="false" class="sc-cgHfjM ixknQI"><label class="sc-fpikKz iHRgeo -depth2"><span type="delete" class="sc-jxYSNo NmQLu operation-type delete">del</span><span tabindex="0" width="calc(100% - 38px)" class="sc-gWaSiO cxcra">/v1/subscriptions/{id}</span></label></li></ul></li><li tabindex="0" depth="1" data-item-id="tag/Invitations" role="menuitem" aria-label="Invitations" aria-expanded="false" class="sc-cgHfjM ixknQI"><label class="sc-fpikKz lgPGwq -depth1"><span width="calc(100% - 38px)" title="Invitations" class="sc-gWaSiO cxcra">Invitations</span><svg class="sc-dntSTA dUlzCe" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg></label><ul class="sc-zOxLx dQnkdy"><li tabindex="0" depth="2" data-item-id="tag/Invitations/paths/~1v1~1invitations~1{code}~1status/put" role="menuitem" aria-label="/v1/invitations/{code}/status" aria-expanded="false" class="sc-cgHfjM ixknQI"><label class="sc-fpikKz iHRgeo -depth2"><span type="put" class="sc-jxYSNo NmQLu operation-type put">put</span><span tabindex="0" width="calc(100% - 38px)" class="sc-gWaSiO cxcra">/v1/invitations/{code}/status</span></label></li></ul></li></ul><div class="sc-kSaXSp QuyG"><a target="_blank" rel="noopener noreferrer" href="https://redocly.com/redoc/">API docs by Redocly</a></div></div></div><div class="sc-jOlHRD bovaLG"><div class="sc-cZnrqW crXcHD"><svg class="" style="transform:translate(2px, -4px) rotate(180deg);transition:transform 0.2s ease" viewBox="0 0 926.23699 573.74994" version="1.1" x="0px" y="0px" width="15" height="15"><g transform="translate(904.92214,-879.1482)"><path d="
          m -673.67664,1221.6502 -231.2455,-231.24803 55.6165,
          -55.627 c 30.5891,-30.59485 56.1806,-55.627 56.8701,-55.627 0.6894,
          0 79.8637,78.60862 175.9427,174.68583 l 174.6892,174.6858 174.6892,
          -174.6858 c 96.079,-96.07721 175.253196,-174.68583 175.942696,
          -174.68583 0.6895,0 26.281,25.03215 56.8701,
          55.627 l 55.6165,55.627 -231.245496,231.24803 c -127.185,127.1864
          -231.5279,231.248 -231.873,231.248 -0.3451,0 -104.688,
          -104.0616 -231.873,-231.248 z
        " fill="currentColor"></path></g></svg><svg class="" style="transform:translate(2px, 4px);transition:transform 0.2s ease" viewBox="0 0 926.23699 573.74994" version="1.1" x="0px" y="0px" width="15" height="15"><g transform="translate(904.92214,-879.1482)"><path d="
          m -673.67664,1221.6502 -231.2455,-231.24803 55.6165,
          -55.627 c 30.5891,-30.59485 56.1806,-55.627 56.8701,-55.627 0.6894,
          0 79.8637,78.60862 175.9427,174.68583 l 174.6892,174.6858 174.6892,
          -174.6858 c 96.079,-96.07721 175.253196,-174.68583 175.942696,
          -174.68583 0.6895,0 26.281,25.03215 56.8701,
          55.627 l 55.6165,55.627 -231.245496,231.24803 c -127.185,127.1864
          -231.5279,231.248 -231.873,231.248 -0.3451,0 -104.688,
          -104.0616 -231.873,-231.248 z
        " fill="currentColor"></path></g></svg></div></div><div class="sc-fkYqBV buanwU api-content"><div class="sc-dTvVRJ bPmFpz"><div class="sc-jJLAfE gkiSyE"><div class="sc-ggWZvA fqkwbU api-info"><h1 class="sc-hwkwBN sc-jCWzJg wYHiz hPcPCj">Rent Report API<!-- --> <span>(<!-- -->1.0.0<!-- -->)</span></h1><p>Download OpenAPI specification<!-- -->:</p><div class="sc-eVqvcJ sc-fszimp kIppRw kbZred"></div><div data-role="redoc-summary" html="" class="sc-eVqvcJ sc-fszimp kIppRw kbZred"></div><div data-role="redoc-description" html="&lt;p&gt;RESTful API for the Rent Report application - a comprehensive property and lease management system for landlords and tenants.&lt;/p&gt;
" class="sc-eVqvcJ sc-fszimp kIppRw kbZred"><p>RESTful API for the Rent Report application - a comprehensive property and lease management system for landlords and tenants.</p>
</div></div></div></div><div id="tag/Authentication" data-section-id="tag/Authentication" class="sc-dTvVRJ bPmFpz"><div class="sc-jJLAfE gkiSyE"><div class="sc-ggWZvA fqkwbU"><h2 class="sc-kNOymR iFSqkw"><a class="sc-kcLKEh fRdsOi" href="#tag/Authentication" aria-label="tag/Authentication"></a>Authentication</h2></div></div><div class="sc-ggWZvA dCzIPc"><div class="sc-eVqvcJ sc-fszimp kIppRw kbZred redoc-markdown " html="&lt;p&gt;User authentication and authorization&lt;/p&gt;
"><p>User authentication and authorization</p>
</div></div></div><div id="tag/Authentication/paths/~1auth~1signup/post" data-section-id="tag/Authentication/paths/~1auth~1signup/post" class="sc-dTvVRJ gHrCVQ"><div class="sc-jJLAfE gkiSyE"><div class="sc-ggWZvA fqkwbU"><h2 class="sc-kNOymR iFSqkw"><a class="sc-kcLKEh fRdsOi" href="#tag/Authentication/paths/~1auth~1signup/post" aria-label="tag/Authentication/paths/~1auth~1signup/post"></a>User Registration<!-- --> </h2><div class="sc-bfjeOH txIPi"><div html="&lt;p&gt;Register a new user account with email, password, and username&lt;/p&gt;
" class="sc-eVqvcJ sc-fszimp kIppRw kbZred"><p>Register a new user account with email, password, and username</p>
</div></div><h5 class="sc-eqYatC czjApA">Request Body schema: <span class="sc-dNFkOE cFlAeY">application/json</span></h5><div html="" class="sc-eVqvcJ sc-fszimp kIppRw kbZred"></div><table class="sc-eqNDNG icJLQx"><tbody><tr class=""><td kind="field" title="email" class="sc-kCuUfV sc-fbQrwq gdmNWp dFOJWJ"><span class="sc-hwddKA cteAyA"></span><span class="property-name">email</span><div class="sc-bEjUoa sc-iIvHqT lhyyLL crXmiY">required</div></td><td class="sc-gGKoUb ixGaBD"><div><div><span class="sc-bEjUoa sc-boKDdR lhyyLL jYezsP"></span><span class="sc-bEjUoa sc-fOOuSg lhyyLL dbKJYq">string</span><span class="sc-bEjUoa sc-fOOuSg lhyyLL dbKJYq"> <!-- -->&lt;<!-- -->email<!-- -->&gt;<!-- --> </span></div> <div><div html="" class="sc-eVqvcJ sc-fszimp kIppRw drqpJr"></div></div></div></td></tr><tr class=""><td kind="field" title="password" class="sc-kCuUfV sc-fbQrwq gdmNWp dFOJWJ"><span class="sc-hwddKA cteAyA"></span><span class="property-name">password</span><div class="sc-bEjUoa sc-iIvHqT lhyyLL crXmiY">required</div></td><td class="sc-gGKoUb ixGaBD"><div><div><span class="sc-bEjUoa sc-boKDdR lhyyLL jYezsP"></span><span class="sc-bEjUoa sc-fOOuSg lhyyLL dbKJYq">string</span><span> <span class="sc-bEjUoa sc-goiVcJ lhyyLL bDfgbe"> <!-- -->&gt;= 8 characters<!-- --> </span></span></div> <div><div html="" class="sc-eVqvcJ sc-fszimp kIppRw drqpJr"></div></div></div></td></tr><tr class="last "><td kind="field" title="username" class="sc-kCuUfV sc-fbQrwq gdmNWp dFOJWJ"><span class="sc-hwddKA cteAyA"></span><span class="property-name">username</span><div class="sc-bEjUoa sc-iIvHqT lhyyLL crXmiY">required</div></td><td class="sc-gGKoUb ixGaBD"><div><div><span class="sc-bEjUoa sc-boKDdR lhyyLL jYezsP"></span><span class="sc-bEjUoa sc-fOOuSg lhyyLL dbKJYq">string</span></div> <div><div html="" class="sc-eVqvcJ sc-fszimp kIppRw drqpJr"></div></div></div></td></tr></tbody></table><div><h3 class="sc-gDzyrw kjrVcG">Responses</h3><div><button class="sc-jIDBmd lkmdtA"><svg class="sc-dntSTA cGxVlA" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg><strong class="sc-eJvlPh fBhAXU">201<!-- --> </strong><div html="&lt;p&gt;User created successfully&lt;/p&gt;
" class="sc-eVqvcJ sc-fszimp sc-etsjJW kIppRw jnwENr ljKHqG"><p>User created successfully</p>
</div></button></div><div><button class="sc-jIDBmd ifAHvq"><svg class="sc-dntSTA jKYZgc" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg><strong class="sc-eJvlPh fBhAXU">400<!-- --> </strong><div html="&lt;p&gt;Invalid input&lt;/p&gt;
" class="sc-eVqvcJ sc-fszimp sc-etsjJW kIppRw jnwENr ljKHqG"><p>Invalid input</p>
</div></button></div></div></div><div class="sc-jwTyAe sc-hjsuWn bDYKKx FFPsr"><div class="sc-eZSpzM jjnszm"><button class="sc-buTqWO iPCVMX"><span type="post" class="sc-fQLpxn kwcmyC http-verb post">post</span><span class="sc-jvKoal kZcHWP">/auth/signup</span><svg class="sc-dntSTA iuNpUs" style="margin-right:-25px" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg></button><div aria-hidden="true" class="sc-ecJghI ga-DQLq"><div class="sc-iyBeIh icOxsG"><div html="&lt;p&gt;Development server&lt;/p&gt;
" class="sc-eVqvcJ sc-fszimp kIppRw drqpJr"><p>Development server</p>
</div><div tabindex="0" role="button"><div class="sc-xKhEK okJpy"><span>http://localhost:8089</span>/auth/signup</div></div></div><div class="sc-iyBeIh icOxsG"><div html="&lt;p&gt;Production server&lt;/p&gt;
" class="sc-eVqvcJ sc-fszimp kIppRw drqpJr"><p>Production server</p>
</div><div tabindex="0" role="button"><div class="sc-xKhEK okJpy"><span>https://api.rentreport.com</span>/auth/signup</div></div></div></div></div><div><h3 class="sc-lgpSej drJHMo"> <!-- -->Request samples<!-- --> </h3><div class="sc-cOpnSz fyxuKi" data-rttabs="true"><ul class="react-tabs__tab-list" role="tablist"><li class="react-tabs__tab react-tabs__tab--selected" role="tab" id="tab«R155gq»0" aria-selected="true" aria-disabled="false" aria-controls="panel«R155gq»0" tabindex="0" data-rttab="true">Payload</li></ul><div class="react-tabs__tab-panel react-tabs__tab-panel--selected" role="tabpanel" id="panel«R155gq»0" aria-labelledby="tab«R155gq»0"><div><div class="sc-bSFBcf iLdyBp"><span class="sc-gahYZc cXitJ">Content type</span><div class="sc-bAehkN iNRAJK">application/json</div></div><div class="sc-blIAwI eKKwxo"><div class="sc-dClGHI fdRrNy"><div class="sc-bbbBoY bBWkcI"><button><div class="sc-fYmhhH iNCOCX">Copy</div></button></div><div tabindex="0" class="sc-eVqvcJ kIppRw sc-fhfEft dFvLDb"><div class="redoc-json"><code><button class="collapser" aria-label="collapse"></button><span class="token punctuation">{</span><span class="ellipsis"></span><ul class="obj collapsible"><li><div class="hoverable "><span class="property token string">"email"</span>: <span class="token string">&quot;<EMAIL>&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"password"</span>: <span class="token string">&quot;SecurePass123!&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"username"</span>: <span class="token string">&quot;john_doe&quot;</span></div></li></ul><span class="token punctuation">}</span></code></div></div></div></div></div></div></div></div><div><h3 class="sc-lgpSej drJHMo"> <!-- -->Response samples<!-- --> </h3><div class="sc-cOpnSz fyxuKi" data-rttabs="true"><ul class="react-tabs__tab-list" role="tablist"><li class="tab-success react-tabs__tab--selected" role="tab" id="tab«R175gq»0" aria-selected="true" aria-disabled="false" aria-controls="panel«R175gq»0" tabindex="0" data-rttab="true">201</li><li class="tab-error" role="tab" id="tab«R175gq»1" aria-selected="false" aria-disabled="false" aria-controls="panel«R175gq»1" data-rttab="true">400</li></ul><div class="react-tabs__tab-panel react-tabs__tab-panel--selected" role="tabpanel" id="panel«R175gq»0" aria-labelledby="tab«R175gq»0"><div><div class="sc-bSFBcf iLdyBp"><span class="sc-gahYZc cXitJ">Content type</span><div class="sc-bAehkN iNRAJK">application/json</div></div><div class="sc-blIAwI eKKwxo"><div class="sc-dClGHI fdRrNy"><div class="sc-bbbBoY bBWkcI"><button><div class="sc-fYmhhH iNCOCX">Copy</div></button></div><div tabindex="0" class="sc-eVqvcJ kIppRw sc-fhfEft dFvLDb"><div class="redoc-json"><code><button class="collapser" aria-label="collapse"></button><span class="token punctuation">{</span><span class="ellipsis"></span><ul class="obj collapsible"><li><div class="hoverable "><span class="property token string">"ok"</span>: <span class="token number">1</span></div></li></ul><span class="token punctuation">}</span></code></div></div></div></div></div></div><div class="react-tabs__tab-panel" role="tabpanel" id="panel«R175gq»1" aria-labelledby="tab«R175gq»1"></div></div></div></div></div></div><div id="tag/Authentication/paths/~1auth~1login/post" data-section-id="tag/Authentication/paths/~1auth~1login/post" class="sc-dTvVRJ gHrCVQ"><div class="sc-jJLAfE gkiSyE"><div class="sc-ggWZvA fqkwbU"><h2 class="sc-kNOymR iFSqkw"><a class="sc-kcLKEh fRdsOi" href="#tag/Authentication/paths/~1auth~1login/post" aria-label="tag/Authentication/paths/~1auth~1login/post"></a>User Login<!-- --> </h2><div class="sc-bfjeOH txIPi"><div html="&lt;p&gt;Authenticate user with email and password&lt;/p&gt;
" class="sc-eVqvcJ sc-fszimp kIppRw kbZred"><p>Authenticate user with email and password</p>
</div></div><h5 class="sc-eqYatC czjApA">Request Body schema: <span class="sc-dNFkOE cFlAeY">application/json</span></h5><div html="" class="sc-eVqvcJ sc-fszimp kIppRw kbZred"></div><table class="sc-eqNDNG icJLQx"><tbody><tr class=""><td kind="field" title="email" class="sc-kCuUfV sc-fbQrwq gdmNWp dFOJWJ"><span class="sc-hwddKA cteAyA"></span><span class="property-name">email</span><div class="sc-bEjUoa sc-iIvHqT lhyyLL crXmiY">required</div></td><td class="sc-gGKoUb ixGaBD"><div><div><span class="sc-bEjUoa sc-boKDdR lhyyLL jYezsP"></span><span class="sc-bEjUoa sc-fOOuSg lhyyLL dbKJYq">string</span><span class="sc-bEjUoa sc-fOOuSg lhyyLL dbKJYq"> <!-- -->&lt;<!-- -->email<!-- -->&gt;<!-- --> </span></div> <div><div html="" class="sc-eVqvcJ sc-fszimp kIppRw drqpJr"></div></div></div></td></tr><tr class="last "><td kind="field" title="password" class="sc-kCuUfV sc-fbQrwq gdmNWp dFOJWJ"><span class="sc-hwddKA cteAyA"></span><span class="property-name">password</span><div class="sc-bEjUoa sc-iIvHqT lhyyLL crXmiY">required</div></td><td class="sc-gGKoUb ixGaBD"><div><div><span class="sc-bEjUoa sc-boKDdR lhyyLL jYezsP"></span><span class="sc-bEjUoa sc-fOOuSg lhyyLL dbKJYq">string</span></div> <div><div html="" class="sc-eVqvcJ sc-fszimp kIppRw drqpJr"></div></div></div></td></tr></tbody></table><div><h3 class="sc-gDzyrw kjrVcG">Responses</h3><div><button class="sc-jIDBmd lkmdtA"><svg class="sc-dntSTA cGxVlA" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg><strong class="sc-eJvlPh fBhAXU">200<!-- --> </strong><div html="&lt;p&gt;Login successful&lt;/p&gt;
" class="sc-eVqvcJ sc-fszimp sc-etsjJW kIppRw jnwENr ljKHqG"><p>Login successful</p>
</div></button></div></div></div><div class="sc-jwTyAe sc-hjsuWn bDYKKx FFPsr"><div class="sc-eZSpzM jjnszm"><button class="sc-buTqWO iPCVMX"><span type="post" class="sc-fQLpxn kwcmyC http-verb post">post</span><span class="sc-jvKoal kZcHWP">/auth/login</span><svg class="sc-dntSTA iuNpUs" style="margin-right:-25px" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg></button><div aria-hidden="true" class="sc-ecJghI ga-DQLq"><div class="sc-iyBeIh icOxsG"><div html="&lt;p&gt;Development server&lt;/p&gt;
" class="sc-eVqvcJ sc-fszimp kIppRw drqpJr"><p>Development server</p>
</div><div tabindex="0" role="button"><div class="sc-xKhEK okJpy"><span>http://localhost:8089</span>/auth/login</div></div></div><div class="sc-iyBeIh icOxsG"><div html="&lt;p&gt;Production server&lt;/p&gt;
" class="sc-eVqvcJ sc-fszimp kIppRw drqpJr"><p>Production server</p>
</div><div tabindex="0" role="button"><div class="sc-xKhEK okJpy"><span>https://api.rentreport.com</span>/auth/login</div></div></div></div></div><div><h3 class="sc-lgpSej drJHMo"> <!-- -->Request samples<!-- --> </h3><div class="sc-cOpnSz fyxuKi" data-rttabs="true"><ul class="react-tabs__tab-list" role="tablist"><li class="react-tabs__tab react-tabs__tab--selected" role="tab" id="tab«R156gq»0" aria-selected="true" aria-disabled="false" aria-controls="panel«R156gq»0" tabindex="0" data-rttab="true">Payload</li></ul><div class="react-tabs__tab-panel react-tabs__tab-panel--selected" role="tabpanel" id="panel«R156gq»0" aria-labelledby="tab«R156gq»0"><div><div class="sc-bSFBcf iLdyBp"><span class="sc-gahYZc cXitJ">Content type</span><div class="sc-bAehkN iNRAJK">application/json</div></div><div class="sc-blIAwI eKKwxo"><div class="sc-dClGHI fdRrNy"><div class="sc-bbbBoY bBWkcI"><button><div class="sc-fYmhhH iNCOCX">Copy</div></button></div><div tabindex="0" class="sc-eVqvcJ kIppRw sc-fhfEft dFvLDb"><div class="redoc-json"><code><span class="token string">&quot;sZq0gcKRNrOK3hcDugzqJ&quot;</span></code></div></div></div></div></div></div></div></div><div><h3 class="sc-lgpSej drJHMo"> <!-- -->Response samples<!-- --> </h3><div class="sc-cOpnSz fyxuKi" data-rttabs="true"><ul class="react-tabs__tab-list" role="tablist"><li class="tab-success react-tabs__tab--selected" role="tab" id="tab«R176gq»0" aria-selected="true" aria-disabled="false" aria-controls="panel«R176gq»0" tabindex="0" data-rttab="true">200</li></ul><div class="react-tabs__tab-panel react-tabs__tab-panel--selected" role="tabpanel" id="panel«R176gq»0" aria-labelledby="tab«R176gq»0"><div><div class="sc-bSFBcf iLdyBp"><span class="sc-gahYZc cXitJ">Content type</span><div class="sc-bAehkN iNRAJK">application/json</div></div><div class="sc-blIAwI eKKwxo"><div class="sc-dClGHI fdRrNy"><div class="sc-bbbBoY bBWkcI"><button><div class="sc-fYmhhH iNCOCX">Copy</div></button><button> Expand all </button><button> Collapse all </button></div><div tabindex="0" class="sc-eVqvcJ kIppRw sc-fhfEft dFvLDb"><div class="redoc-json"><code><button class="collapser" aria-label="collapse"></button><span class="token punctuation">{</span><span class="ellipsis"></span><ul class="obj collapsible"><li><div class="hoverable "><span class="property token string">"token"</span>: <span class="token string">&quot;string&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"user"</span>: <button class="collapser" aria-label="collapse"></button><span class="token punctuation">{</span><span class="ellipsis"></span><ul class="obj collapsible"><li><div class="hoverable collapsed"><span class="property token string">"id"</span>: <span class="token string">&quot;sZq0gcKRNrOK3hcDugzqJ&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"email"</span>: <span class="token string">&quot;<EMAIL>&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"username"</span>: <span class="token string">&quot;string&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"settings"</span>: <span class="token punctuation">{ }</span></div></li></ul><span class="token punctuation">}</span></div></li></ul><span class="token punctuation">}</span></code></div></div></div></div></div></div></div></div></div></div></div><div id="tag/Authentication/paths/~1auth~1logout/post" data-section-id="tag/Authentication/paths/~1auth~1logout/post" class="sc-dTvVRJ gHrCVQ"><div class="sc-jJLAfE gkiSyE"><div class="sc-ggWZvA fqkwbU"><h2 class="sc-kNOymR iFSqkw"><a class="sc-kcLKEh fRdsOi" href="#tag/Authentication/paths/~1auth~1logout/post" aria-label="tag/Authentication/paths/~1auth~1logout/post"></a>User Logout<!-- --> </h2><div class="sc-bfjeOH txIPi"><div html="&lt;p&gt;Log out the current user and invalidate session&lt;/p&gt;
" class="sc-eVqvcJ sc-fszimp kIppRw kbZred"><p>Log out the current user and invalidate session</p>
</div></div><div><h3 class="sc-gDzyrw kjrVcG">Responses</h3><div><button class="sc-jIDBmd lkmdtA"><svg class="sc-dntSTA cGxVlA" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg><strong class="sc-eJvlPh fBhAXU">204<!-- --> </strong><div html="&lt;p&gt;Logout successful&lt;/p&gt;
" class="sc-eVqvcJ sc-fszimp sc-etsjJW kIppRw jnwENr ljKHqG"><p>Logout successful</p>
</div></button></div></div></div><div class="sc-jwTyAe sc-hjsuWn bDYKKx FFPsr"><div class="sc-eZSpzM jjnszm"><button class="sc-buTqWO iPCVMX"><span type="post" class="sc-fQLpxn kwcmyC http-verb post">post</span><span class="sc-jvKoal kZcHWP">/auth/logout</span><svg class="sc-dntSTA iuNpUs" style="margin-right:-25px" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg></button><div aria-hidden="true" class="sc-ecJghI ga-DQLq"><div class="sc-iyBeIh icOxsG"><div html="&lt;p&gt;Development server&lt;/p&gt;
" class="sc-eVqvcJ sc-fszimp kIppRw drqpJr"><p>Development server</p>
</div><div tabindex="0" role="button"><div class="sc-xKhEK okJpy"><span>http://localhost:8089</span>/auth/logout</div></div></div><div class="sc-iyBeIh icOxsG"><div html="&lt;p&gt;Production server&lt;/p&gt;
" class="sc-eVqvcJ sc-fszimp kIppRw drqpJr"><p>Production server</p>
</div><div tabindex="0" role="button"><div class="sc-xKhEK okJpy"><span>https://api.rentreport.com</span>/auth/logout</div></div></div></div></div><div><h3 class="sc-lgpSej drJHMo"> <!-- -->Response samples<!-- --> </h3><div class="sc-cOpnSz fyxuKi" data-rttabs="true"><ul class="react-tabs__tab-list" role="tablist"><li class="tab-success react-tabs__tab--selected" role="tab" id="tab«R177gq»0" aria-selected="true" aria-disabled="false" aria-controls="panel«R177gq»0" tabindex="0" data-rttab="true">204</li></ul><div class="react-tabs__tab-panel react-tabs__tab-panel--selected" role="tabpanel" id="panel«R177gq»0" aria-labelledby="tab«R177gq»0"><div><div class="sc-bSFBcf iLdyBp"><span class="sc-gahYZc cXitJ">Content type</span><div class="sc-bAehkN iNRAJK">application/json</div></div><div class="sc-blIAwI eKKwxo"><div class="sc-dClGHI fdRrNy"><div class="sc-bbbBoY bBWkcI"><button><div class="sc-fYmhhH iNCOCX">Copy</div></button></div><div tabindex="0" class="sc-eVqvcJ kIppRw sc-fhfEft dFvLDb"><div class="redoc-json"><code><span class="token punctuation">{ }</span></code></div></div></div></div></div></div></div></div></div></div></div><div id="tag/Properties" data-section-id="tag/Properties" class="sc-dTvVRJ bPmFpz"><div class="sc-jJLAfE gkiSyE"><div class="sc-ggWZvA fqkwbU"><h2 class="sc-kNOymR iFSqkw"><a class="sc-kcLKEh fRdsOi" href="#tag/Properties" aria-label="tag/Properties"></a>Properties</h2></div></div><div class="sc-ggWZvA dCzIPc"><div class="sc-eVqvcJ sc-fszimp kIppRw kbZred redoc-markdown " html="&lt;p&gt;Property management operations&lt;/p&gt;
"><p>Property management operations</p>
</div></div></div><div id="tag/Properties/paths/~1properties/post" data-section-id="tag/Properties/paths/~1properties/post" class="sc-dTvVRJ gHrCVQ"><div class="sc-jJLAfE gkiSyE"><div class="sc-ggWZvA fqkwbU"><h2 class="sc-kNOymR iFSqkw"><a class="sc-kcLKEh fRdsOi" href="#tag/Properties/paths/~1properties/post" aria-label="tag/Properties/paths/~1properties/post"></a>Create Property<!-- --> </h2><div class="sc-bfjeOH txIPi"><div html="&lt;p&gt;Create a new property for the authenticated user&lt;/p&gt;
" class="sc-eVqvcJ sc-fszimp kIppRw kbZred"><p>Create a new property for the authenticated user</p>
</div></div><h5 class="sc-eqYatC czjApA">Request Body schema: <span class="sc-dNFkOE cFlAeY">application/json</span></h5><div html="" class="sc-eVqvcJ sc-fszimp kIppRw kbZred"></div><table class="sc-eqNDNG icJLQx"><tbody><tr class=""><td kind="field" title="name" class="sc-kCuUfV sc-fbQrwq gdmNWp dFOJWJ"><span class="sc-hwddKA cteAyA"></span><span class="property-name">name</span></td><td class="sc-gGKoUb ixGaBD"><div><div><span class="sc-bEjUoa sc-boKDdR lhyyLL jYezsP"></span><span class="sc-bEjUoa sc-fOOuSg lhyyLL dbKJYq">string</span></div> <div><div html="" class="sc-eVqvcJ sc-fszimp kIppRw drqpJr"></div></div></div></td></tr><tr class=""><td kind="field" title="address" class="sc-kCuUfV sc-fbQrwq sc-itBLYH gdmNWp dFOJWJ kdPQHX"><span class="sc-hwddKA cteAyA"></span><button aria-label="expand address"><span class="property-name">address</span><svg class="sc-dntSTA dOPmTa" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg></button></td><td class="sc-gGKoUb ixGaBD"><div><div><span class="sc-bEjUoa sc-boKDdR lhyyLL jYezsP"></span><span class="sc-bEjUoa sc-fOOuSg lhyyLL dbKJYq">object</span></div> <div><div html="" class="sc-eVqvcJ sc-fszimp kIppRw drqpJr"></div></div></div></td></tr><tr class=""><td kind="field" title="propertyType" class="sc-kCuUfV sc-fbQrwq gdmNWp dFOJWJ"><span class="sc-hwddKA cteAyA"></span><span class="property-name">propertyType</span></td><td class="sc-gGKoUb ixGaBD"><div><div><span class="sc-bEjUoa sc-boKDdR lhyyLL jYezsP"></span><span class="sc-bEjUoa sc-fOOuSg lhyyLL dbKJYq">string</span></div><div><span class="sc-bEjUoa lhyyLL"> <!-- -->Enum<!-- -->:</span> <span class="sc-bEjUoa sc-dTWiOz lhyyLL kMQdIk">&quot;apartment&quot;</span> <span class="sc-bEjUoa sc-dTWiOz lhyyLL kMQdIk">&quot;house&quot;</span> <span class="sc-bEjUoa sc-dTWiOz lhyyLL kMQdIk">&quot;condo&quot;</span> <span class="sc-bEjUoa sc-dTWiOz lhyyLL kMQdIk">&quot;commercial&quot;</span> </div> <div><div html="&lt;p&gt;Optional property type&lt;/p&gt;
" class="sc-eVqvcJ sc-fszimp kIppRw drqpJr"><p>Optional property type</p>
</div></div></div></td></tr><tr class=""><td kind="field" title="totalUnits" class="sc-kCuUfV sc-fbQrwq gdmNWp dFOJWJ"><span class="sc-hwddKA cteAyA"></span><span class="property-name">totalUnits</span></td><td class="sc-gGKoUb ixGaBD"><div><div><span class="sc-bEjUoa sc-boKDdR lhyyLL jYezsP"></span><span class="sc-bEjUoa sc-fOOuSg lhyyLL dbKJYq">integer</span><span> <span class="sc-bEjUoa sc-goiVcJ lhyyLL bDfgbe"> <!-- -->&gt;= 1<!-- --> </span></span></div> <div><div html="" class="sc-eVqvcJ sc-fszimp kIppRw drqpJr"></div></div></div></td></tr><tr class=""><td kind="field" title="vacantUnits" class="sc-kCuUfV sc-fbQrwq gdmNWp dFOJWJ"><span class="sc-hwddKA cteAyA"></span><span class="property-name">vacantUnits</span></td><td class="sc-gGKoUb ixGaBD"><div><div><span class="sc-bEjUoa sc-boKDdR lhyyLL jYezsP"></span><span class="sc-bEjUoa sc-fOOuSg lhyyLL dbKJYq">integer</span></div> <div><div html="" class="sc-eVqvcJ sc-fszimp kIppRw drqpJr"></div></div></div></td></tr><tr class=""><td kind="field" title="status" class="sc-kCuUfV sc-fbQrwq gdmNWp dFOJWJ"><span class="sc-hwddKA cteAyA"></span><span class="property-name">status</span></td><td class="sc-gGKoUb ixGaBD"><div><div><span class="sc-bEjUoa sc-boKDdR lhyyLL jYezsP"></span><span class="sc-bEjUoa sc-fOOuSg lhyyLL dbKJYq">string</span></div><div><span class="sc-bEjUoa lhyyLL"> <!-- -->Enum<!-- -->:</span> <span class="sc-bEjUoa sc-dTWiOz lhyyLL kMQdIk">&quot;active&quot;</span> <span class="sc-bEjUoa sc-dTWiOz lhyyLL kMQdIk">&quot;inactive&quot;</span> <span class="sc-bEjUoa sc-dTWiOz lhyyLL kMQdIk">&quot;maintenance&quot;</span> </div> <div><div html="" class="sc-eVqvcJ sc-fszimp kIppRw drqpJr"></div></div></div></td></tr><tr class="last "><td kind="field" title="notes" class="sc-kCuUfV sc-fbQrwq gdmNWp dFOJWJ"><span class="sc-hwddKA cteAyA"></span><span class="property-name">notes</span></td><td class="sc-gGKoUb ixGaBD"><div><div><span class="sc-bEjUoa sc-boKDdR lhyyLL jYezsP"></span><span class="sc-bEjUoa sc-fOOuSg lhyyLL dbKJYq">string</span></div> <div><div html="" class="sc-eVqvcJ sc-fszimp kIppRw drqpJr"></div></div></div></td></tr></tbody></table><div><h3 class="sc-gDzyrw kjrVcG">Responses</h3><div><button class="sc-jIDBmd lkmdtA"><svg class="sc-dntSTA cGxVlA" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg><strong class="sc-eJvlPh fBhAXU">201<!-- --> </strong><div html="&lt;p&gt;Property created successfully&lt;/p&gt;
" class="sc-eVqvcJ sc-fszimp sc-etsjJW kIppRw jnwENr ljKHqG"><p>Property created successfully</p>
</div></button></div></div></div><div class="sc-jwTyAe sc-hjsuWn bDYKKx FFPsr"><div class="sc-eZSpzM jjnszm"><button class="sc-buTqWO iPCVMX"><span type="post" class="sc-fQLpxn kwcmyC http-verb post">post</span><span class="sc-jvKoal kZcHWP">/properties</span><svg class="sc-dntSTA iuNpUs" style="margin-right:-25px" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg></button><div aria-hidden="true" class="sc-ecJghI ga-DQLq"><div class="sc-iyBeIh icOxsG"><div html="&lt;p&gt;Development server&lt;/p&gt;
" class="sc-eVqvcJ sc-fszimp kIppRw drqpJr"><p>Development server</p>
</div><div tabindex="0" role="button"><div class="sc-xKhEK okJpy"><span>http://localhost:8089</span>/properties</div></div></div><div class="sc-iyBeIh icOxsG"><div html="&lt;p&gt;Production server&lt;/p&gt;
" class="sc-eVqvcJ sc-fszimp kIppRw drqpJr"><p>Production server</p>
</div><div tabindex="0" role="button"><div class="sc-xKhEK okJpy"><span>https://api.rentreport.com</span>/properties</div></div></div></div></div><div><h3 class="sc-lgpSej drJHMo"> <!-- -->Request samples<!-- --> </h3><div class="sc-cOpnSz fyxuKi" data-rttabs="true"><ul class="react-tabs__tab-list" role="tablist"><li class="react-tabs__tab react-tabs__tab--selected" role="tab" id="tab«R4khha»0" aria-selected="true" aria-disabled="false" aria-controls="panel«R4khha»0" tabindex="0" data-rttab="true">Payload</li></ul><div class="react-tabs__tab-panel react-tabs__tab-panel--selected" role="tabpanel" id="panel«R4khha»0" aria-labelledby="tab«R4khha»0"><div><div class="sc-bSFBcf iLdyBp"><span class="sc-gahYZc cXitJ">Content type</span><div class="sc-bAehkN iNRAJK">application/json</div></div><div class="sc-blIAwI eKKwxo"><div class="sc-dClGHI fdRrNy"><div class="sc-bbbBoY bBWkcI"><button><div class="sc-fYmhhH iNCOCX">Copy</div></button></div><div tabindex="0" class="sc-eVqvcJ kIppRw sc-fhfEft dFvLDb"><div class="redoc-json"><code><span class="token string">&quot;sZq0gcKRNrOK3hcDugzqJ&quot;</span></code></div></div></div></div></div></div></div></div><div><h3 class="sc-lgpSej drJHMo"> <!-- -->Response samples<!-- --> </h3><div class="sc-cOpnSz fyxuKi" data-rttabs="true"><ul class="react-tabs__tab-list" role="tablist"><li class="tab-success react-tabs__tab--selected" role="tab" id="tab«R4shha»0" aria-selected="true" aria-disabled="false" aria-controls="panel«R4shha»0" tabindex="0" data-rttab="true">201</li></ul><div class="react-tabs__tab-panel react-tabs__tab-panel--selected" role="tabpanel" id="panel«R4shha»0" aria-labelledby="tab«R4shha»0"><div><div class="sc-bSFBcf iLdyBp"><span class="sc-gahYZc cXitJ">Content type</span><div class="sc-bAehkN iNRAJK">application/json</div></div><div class="sc-blIAwI eKKwxo"><div class="sc-dClGHI fdRrNy"><div class="sc-bbbBoY bBWkcI"><button><div class="sc-fYmhhH iNCOCX">Copy</div></button><button> Expand all </button><button> Collapse all </button></div><div tabindex="0" class="sc-eVqvcJ kIppRw sc-fhfEft dFvLDb"><div class="redoc-json"><code><button class="collapser" aria-label="collapse"></button><span class="token punctuation">{</span><span class="ellipsis"></span><ul class="obj collapsible"><li><div class="hoverable "><span class="property token string">"id"</span>: <span class="token string">&quot;sZq0gcKRNrOK3hcDugzqJ&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"name"</span>: <span class="token string">&quot;string&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"address"</span>: <button class="collapser" aria-label="collapse"></button><span class="token punctuation">{</span><span class="ellipsis"></span><ul class="obj collapsible"><li><div class="hoverable collapsed"><span class="property token string">"street"</span>: <span class="token string">&quot;string&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"city"</span>: <span class="token string">&quot;string&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"prov"</span>: <span class="token string">&quot;string&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"state"</span>: <span class="token string">&quot;string&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"zipCode"</span>: <span class="token string">&quot;string&quot;</span></div></li></ul><span class="token punctuation">}</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"propertyType"</span>: <span class="token string">&quot;apartment&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"totalUnits"</span>: <span class="token number">1</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"vacantUnits"</span>: <span class="token number">0</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"status"</span>: <span class="token string">&quot;active&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"notes"</span>: <span class="token string">&quot;string&quot;</span></div></li></ul><span class="token punctuation">}</span></code></div></div></div></div></div></div></div></div></div></div></div><div id="tag/Properties/paths/~1properties/get" data-section-id="tag/Properties/paths/~1properties/get" class="sc-dTvVRJ gHrCVQ"><div class="sc-jJLAfE gkiSyE"><div class="sc-ggWZvA fqkwbU"><h2 class="sc-kNOymR iFSqkw"><a class="sc-kcLKEh fRdsOi" href="#tag/Properties/paths/~1properties/get" aria-label="tag/Properties/paths/~1properties/get"></a>Get Properties<!-- --> </h2><div class="sc-bfjeOH txIPi"><div html="&lt;p&gt;Get all properties for the authenticated user with optional filtering by name, status, etc.&lt;/p&gt;
" class="sc-eVqvcJ sc-fszimp kIppRw kbZred"><p>Get all properties for the authenticated user with optional filtering by name, status, etc.</p>
</div></div><div><h5 class="sc-eqYatC czjApA">query<!-- --> Parameters</h5><table class="sc-eqNDNG icJLQx"><tbody><tr class=""><td kind="field" title="name" class="sc-kCuUfV sc-fbQrwq gdmNWp dFOJWJ"><span class="sc-hwddKA cteAyA"></span><span class="property-name">name</span></td><td class="sc-gGKoUb ixGaBD"><div><div><span class="sc-bEjUoa sc-boKDdR lhyyLL jYezsP"></span><span class="sc-bEjUoa sc-fOOuSg lhyyLL dbKJYq">string</span></div> <div><div html="&lt;p&gt;名称&lt;/p&gt;
" class="sc-eVqvcJ sc-fszimp kIppRw drqpJr"><p>名称</p>
</div></div></div></td></tr><tr class=""><td kind="field" title="propertyId" class="sc-kCuUfV sc-fbQrwq gdmNWp dFOJWJ"><span class="sc-hwddKA cteAyA"></span><span class="property-name">propertyId</span></td><td class="sc-gGKoUb ixGaBD"><div><div><span class="sc-bEjUoa sc-boKDdR lhyyLL jYezsP"></span><span class="sc-bEjUoa sc-fOOuSg lhyyLL dbKJYq">string</span></div> <div><div html="&lt;p&gt;Filter by property ID&lt;/p&gt;
" class="sc-eVqvcJ sc-fszimp kIppRw drqpJr"><p>Filter by property ID</p>
</div></div></div></td></tr><tr class=""><td kind="field" title="limit" class="sc-kCuUfV sc-fbQrwq gdmNWp dFOJWJ"><span class="sc-hwddKA cteAyA"></span><span class="property-name">limit</span></td><td class="sc-gGKoUb ixGaBD"><div><div><span class="sc-bEjUoa sc-boKDdR lhyyLL jYezsP"></span><span class="sc-bEjUoa sc-fOOuSg lhyyLL dbKJYq">string</span></div> <div><div html="&lt;p&gt;Maximum number of results to return&lt;/p&gt;
" class="sc-eVqvcJ sc-fszimp kIppRw drqpJr"><p>Maximum number of results to return</p>
</div></div></div></td></tr><tr class="last "><td kind="field" title="status" class="sc-kCuUfV sc-fbQrwq gdmNWp dFOJWJ"><span class="sc-hwddKA cteAyA"></span><span class="property-name">status</span></td><td class="sc-gGKoUb ixGaBD"><div><div><span class="sc-bEjUoa sc-boKDdR lhyyLL jYezsP"></span><span class="sc-bEjUoa sc-fOOuSg lhyyLL dbKJYq">string</span></div> <div><div html="&lt;p&gt;Filter by status (active, inactive, archived)&lt;/p&gt;
" class="sc-eVqvcJ sc-fszimp kIppRw drqpJr"><p>Filter by status (active, inactive, archived)</p>
</div></div></div></td></tr></tbody></table></div><div><h3 class="sc-gDzyrw kjrVcG">Responses</h3><div><button class="sc-jIDBmd lkmdtA"><svg class="sc-dntSTA cGxVlA" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg><strong class="sc-eJvlPh fBhAXU">200<!-- --> </strong><div html="&lt;p&gt;List of properties for the user&lt;/p&gt;
" class="sc-eVqvcJ sc-fszimp sc-etsjJW kIppRw jnwENr ljKHqG"><p>List of properties for the user</p>
</div></button></div></div></div><div class="sc-jwTyAe sc-hjsuWn bDYKKx FFPsr"><div class="sc-eZSpzM jjnszm"><button class="sc-buTqWO iPCVMX"><span type="get" class="sc-fQLpxn dynMBc http-verb get">get</span><span class="sc-jvKoal kZcHWP">/properties</span><svg class="sc-dntSTA iuNpUs" style="margin-right:-25px" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg></button><div aria-hidden="true" class="sc-ecJghI ga-DQLq"><div class="sc-iyBeIh icOxsG"><div html="&lt;p&gt;Development server&lt;/p&gt;
" class="sc-eVqvcJ sc-fszimp kIppRw drqpJr"><p>Development server</p>
</div><div tabindex="0" role="button"><div class="sc-xKhEK okJpy"><span>http://localhost:8089</span>/properties</div></div></div><div class="sc-iyBeIh icOxsG"><div html="&lt;p&gt;Production server&lt;/p&gt;
" class="sc-eVqvcJ sc-fszimp kIppRw drqpJr"><p>Production server</p>
</div><div tabindex="0" role="button"><div class="sc-xKhEK okJpy"><span>https://api.rentreport.com</span>/properties</div></div></div></div></div><div><h3 class="sc-lgpSej drJHMo"> <!-- -->Response samples<!-- --> </h3><div class="sc-cOpnSz fyxuKi" data-rttabs="true"><ul class="react-tabs__tab-list" role="tablist"><li class="tab-success react-tabs__tab--selected" role="tab" id="tab«R4siha»0" aria-selected="true" aria-disabled="false" aria-controls="panel«R4siha»0" tabindex="0" data-rttab="true">200</li></ul><div class="react-tabs__tab-panel react-tabs__tab-panel--selected" role="tabpanel" id="panel«R4siha»0" aria-labelledby="tab«R4siha»0"><div><div class="sc-bSFBcf iLdyBp"><span class="sc-gahYZc cXitJ">Content type</span><div class="sc-bAehkN iNRAJK">application/json</div></div><div class="sc-blIAwI eKKwxo"><div class="sc-dClGHI fdRrNy"><div class="sc-bbbBoY bBWkcI"><button><div class="sc-fYmhhH iNCOCX">Copy</div></button><button> Expand all </button><button> Collapse all </button></div><div tabindex="0" class="sc-eVqvcJ kIppRw sc-fhfEft dFvLDb"><div class="redoc-json"><code><button class="collapser" aria-label="collapse"></button><span class="token punctuation">{</span><span class="ellipsis"></span><ul class="obj collapsible"><li><div class="hoverable "><span class="property token string">"items"</span>: <button class="collapser" aria-label="collapse"></button><span class="token punctuation">[</span><span class="ellipsis"></span><ul class="array collapsible"><li><div class="hoverable collapsed"><button class="collapser" aria-label="expand"></button><span class="token punctuation">{</span><span class="ellipsis"></span><ul class="obj collapsible"><li><div class="hoverable collapsed"><span class="property token string">"id"</span>: <span class="token string">&quot;Av8kaomMlPJbzqSWwA1Ju&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"name"</span>: <span class="token string">&quot;Linda Beatty&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"totalUnits"</span>: <span class="token number">5</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"vacantUnits"</span>: <span class="token number">2</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"OwingBalance"</span>: <span class="token number">99</span></div></li></ul><span class="token punctuation">}</span></div></li></ul><span class="token punctuation">]</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"total"</span>: <span class="token number">1</span></div></li></ul><span class="token punctuation">}</span></code></div></div></div></div></div></div></div></div></div></div></div><div id="tag/Properties/paths/~1properties~1{propertyId}/get" data-section-id="tag/Properties/paths/~1properties~1{propertyId}/get" class="sc-dTvVRJ gHrCVQ"><div class="sc-jJLAfE gkiSyE"><div class="sc-ggWZvA fqkwbU"><h2 class="sc-kNOymR iFSqkw"><a class="sc-kcLKEh fRdsOi" href="#tag/Properties/paths/~1properties~1{propertyId}/get" aria-label="tag/Properties/paths/~1properties~1{propertyId}/get"></a>Get Property Details<!-- --> </h2><div class="sc-bfjeOH txIPi"><div html="&lt;p&gt;Get detailed information for a specific property&lt;/p&gt;
" class="sc-eVqvcJ sc-fszimp kIppRw kbZred"><p>Get detailed information for a specific property</p>
</div></div><div><h5 class="sc-eqYatC czjApA">path<!-- --> Parameters</h5><table class="sc-eqNDNG icJLQx"><tbody><tr class="last "><td kind="field" title="propertyId" class="sc-kCuUfV sc-fbQrwq gdmNWp dFOJWJ"><span class="sc-hwddKA cteAyA"></span><span class="property-name">propertyId</span><div class="sc-bEjUoa sc-iIvHqT lhyyLL crXmiY">required</div></td><td class="sc-gGKoUb ixGaBD"><div><div><span class="sc-bEjUoa sc-boKDdR lhyyLL jYezsP"></span><span class="sc-bEjUoa sc-fOOuSg lhyyLL dbKJYq">string</span><span class="sc-bEjUoa sc-hdBJTi lhyyLL nwQTz"> (<!-- -->NanoID<!-- -->) </span><span class="sc-bEjUoa sc-cpclqO lhyyLL UZcrz">^[0-9A-Za-z]{11}$</span></div> <div><span class="sc-bEjUoa lhyyLL"> <!-- -->Example:<!-- --> </span> <span class="sc-bEjUoa sc-dTWiOz lhyyLL kMQdIk">sZq0gcKRNrOK3hcDugzqJ</span></div><div><div html="&lt;p&gt;Resource identifier&lt;/p&gt;
" class="sc-eVqvcJ sc-fszimp kIppRw drqpJr"><p>Resource identifier</p>
</div></div></div></td></tr></tbody></table></div><div><h3 class="sc-gDzyrw kjrVcG">Responses</h3><div><button class="sc-jIDBmd lkmdtA"><svg class="sc-dntSTA cGxVlA" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg><strong class="sc-eJvlPh fBhAXU">200<!-- --> </strong><div html="&lt;p&gt;Property details retrieved successfully&lt;/p&gt;
" class="sc-eVqvcJ sc-fszimp sc-etsjJW kIppRw jnwENr ljKHqG"><p>Property details retrieved successfully</p>
</div></button></div></div></div><div class="sc-jwTyAe sc-hjsuWn bDYKKx FFPsr"><div class="sc-eZSpzM jjnszm"><button class="sc-buTqWO iPCVMX"><span type="get" class="sc-fQLpxn dynMBc http-verb get">get</span><span class="sc-jvKoal kZcHWP">/properties/{propertyId}</span><svg class="sc-dntSTA iuNpUs" style="margin-right:-25px" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg></button><div aria-hidden="true" class="sc-ecJghI ga-DQLq"><div class="sc-iyBeIh icOxsG"><div html="&lt;p&gt;Development server&lt;/p&gt;
" class="sc-eVqvcJ sc-fszimp kIppRw drqpJr"><p>Development server</p>
</div><div tabindex="0" role="button"><div class="sc-xKhEK okJpy"><span>http://localhost:8089</span>/properties/{propertyId}</div></div></div><div class="sc-iyBeIh icOxsG"><div html="&lt;p&gt;Production server&lt;/p&gt;
" class="sc-eVqvcJ sc-fszimp kIppRw drqpJr"><p>Production server</p>
</div><div tabindex="0" role="button"><div class="sc-xKhEK okJpy"><span>https://api.rentreport.com</span>/properties/{propertyId}</div></div></div></div></div><div><h3 class="sc-lgpSej drJHMo"> <!-- -->Response samples<!-- --> </h3><div class="sc-cOpnSz fyxuKi" data-rttabs="true"><ul class="react-tabs__tab-list" role="tablist"><li class="tab-success react-tabs__tab--selected" role="tab" id="tab«R4sjha»0" aria-selected="true" aria-disabled="false" aria-controls="panel«R4sjha»0" tabindex="0" data-rttab="true">200</li></ul><div class="react-tabs__tab-panel react-tabs__tab-panel--selected" role="tabpanel" id="panel«R4sjha»0" aria-labelledby="tab«R4sjha»0"><div><div class="sc-bSFBcf iLdyBp"><span class="sc-gahYZc cXitJ">Content type</span><div class="sc-bAehkN iNRAJK">application/json</div></div><div class="sc-blIAwI eKKwxo"><div class="sc-dClGHI fdRrNy"><div class="sc-bbbBoY bBWkcI"><button><div class="sc-fYmhhH iNCOCX">Copy</div></button><button> Expand all </button><button> Collapse all </button></div><div tabindex="0" class="sc-eVqvcJ kIppRw sc-fhfEft dFvLDb"><div class="redoc-json"><code><button class="collapser" aria-label="collapse"></button><span class="token punctuation">{</span><span class="ellipsis"></span><ul class="obj collapsible"><li><div class="hoverable "><span class="property token string">"id"</span>: <span class="token string">&quot;sZq0gcKRNrOK3hcDugzqJ&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"name"</span>: <span class="token string">&quot;string&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"address"</span>: <button class="collapser" aria-label="collapse"></button><span class="token punctuation">{</span><span class="ellipsis"></span><ul class="obj collapsible"><li><div class="hoverable collapsed"><span class="property token string">"street"</span>: <span class="token string">&quot;string&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"city"</span>: <span class="token string">&quot;string&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"prov"</span>: <span class="token string">&quot;string&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"state"</span>: <span class="token string">&quot;string&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"zipCode"</span>: <span class="token string">&quot;string&quot;</span></div></li></ul><span class="token punctuation">}</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"propertyType"</span>: <span class="token string">&quot;apartment&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"totalUnits"</span>: <span class="token number">1</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"vacantUnits"</span>: <span class="token number">0</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"status"</span>: <span class="token string">&quot;active&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"notes"</span>: <span class="token string">&quot;string&quot;</span></div></li></ul><span class="token punctuation">}</span></code></div></div></div></div></div></div></div></div></div></div></div><div id="tag/Properties/paths/~1properties~1{propertyId}/put" data-section-id="tag/Properties/paths/~1properties~1{propertyId}/put" class="sc-dTvVRJ gHrCVQ"><div class="sc-jJLAfE gkiSyE"><div class="sc-ggWZvA fqkwbU"><h2 class="sc-kNOymR iFSqkw"><a class="sc-kcLKEh fRdsOi" href="#tag/Properties/paths/~1properties~1{propertyId}/put" aria-label="tag/Properties/paths/~1properties~1{propertyId}/put"></a>Update Property<!-- --> </h2><div class="sc-bfjeOH txIPi"><div html="&lt;p&gt;Update information for a specific property&lt;/p&gt;
" class="sc-eVqvcJ sc-fszimp kIppRw kbZred"><p>Update information for a specific property</p>
</div></div><div><h5 class="sc-eqYatC czjApA">path<!-- --> Parameters</h5><table class="sc-eqNDNG icJLQx"><tbody><tr class="last "><td kind="field" title="propertyId" class="sc-kCuUfV sc-fbQrwq gdmNWp dFOJWJ"><span class="sc-hwddKA cteAyA"></span><span class="property-name">propertyId</span><div class="sc-bEjUoa sc-iIvHqT lhyyLL crXmiY">required</div></td><td class="sc-gGKoUb ixGaBD"><div><div><span class="sc-bEjUoa sc-boKDdR lhyyLL jYezsP"></span><span class="sc-bEjUoa sc-fOOuSg lhyyLL dbKJYq">string</span><span class="sc-bEjUoa sc-hdBJTi lhyyLL nwQTz"> (<!-- -->NanoID<!-- -->) </span><span class="sc-bEjUoa sc-cpclqO lhyyLL UZcrz">^[0-9A-Za-z]{11}$</span></div> <div><span class="sc-bEjUoa lhyyLL"> <!-- -->Example:<!-- --> </span> <span class="sc-bEjUoa sc-dTWiOz lhyyLL kMQdIk">sZq0gcKRNrOK3hcDugzqJ</span></div><div><div html="&lt;p&gt;Resource identifier&lt;/p&gt;
" class="sc-eVqvcJ sc-fszimp kIppRw drqpJr"><p>Resource identifier</p>
</div></div></div></td></tr></tbody></table></div><h5 class="sc-eqYatC czjApA">Request Body schema: <span class="sc-dNFkOE cFlAeY">application/json</span></h5><div html="" class="sc-eVqvcJ sc-fszimp kIppRw kbZred"></div><table class="sc-eqNDNG icJLQx"><tbody><tr class=""><td kind="field" title="id" class="sc-kCuUfV sc-fbQrwq gdmNWp dFOJWJ"><span class="sc-hwddKA cteAyA"></span><span class="property-name">id</span><div class="sc-bEjUoa sc-iIvHqT lhyyLL crXmiY">required</div></td><td class="sc-gGKoUb ixGaBD"><div><div><span class="sc-bEjUoa sc-boKDdR lhyyLL jYezsP"></span><span class="sc-bEjUoa sc-fOOuSg lhyyLL dbKJYq">string</span><span class="sc-bEjUoa sc-hdBJTi lhyyLL nwQTz"> (<!-- -->NanoID<!-- -->) </span><span class="sc-bEjUoa sc-cpclqO lhyyLL UZcrz">^[0-9A-Za-z]{11}$</span></div> <div><div html="&lt;p&gt;NanoID - 11 character alphanumeric identifier&lt;/p&gt;
" class="sc-eVqvcJ sc-fszimp kIppRw drqpJr"><p>NanoID - 11 character alphanumeric identifier</p>
</div></div></div></td></tr><tr class=""><td kind="field" title="name" class="sc-kCuUfV sc-fbQrwq gdmNWp dFOJWJ"><span class="sc-hwddKA cteAyA"></span><span class="property-name">name</span></td><td class="sc-gGKoUb ixGaBD"><div><div><span class="sc-bEjUoa sc-boKDdR lhyyLL jYezsP"></span><span class="sc-bEjUoa sc-fOOuSg lhyyLL dbKJYq">string</span></div> <div><div html="" class="sc-eVqvcJ sc-fszimp kIppRw drqpJr"></div></div></div></td></tr><tr class=""><td kind="field" title="address" class="sc-kCuUfV sc-fbQrwq sc-itBLYH gdmNWp dFOJWJ kdPQHX"><span class="sc-hwddKA cteAyA"></span><button aria-label="expand address"><span class="property-name">address</span><svg class="sc-dntSTA dOPmTa" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg></button></td><td class="sc-gGKoUb ixGaBD"><div><div><span class="sc-bEjUoa sc-boKDdR lhyyLL jYezsP"></span><span class="sc-bEjUoa sc-fOOuSg lhyyLL dbKJYq">object</span></div> <div><div html="" class="sc-eVqvcJ sc-fszimp kIppRw drqpJr"></div></div></div></td></tr><tr class=""><td kind="field" title="propertyType" class="sc-kCuUfV sc-fbQrwq gdmNWp dFOJWJ"><span class="sc-hwddKA cteAyA"></span><span class="property-name">propertyType</span></td><td class="sc-gGKoUb ixGaBD"><div><div><span class="sc-bEjUoa sc-boKDdR lhyyLL jYezsP"></span><span class="sc-bEjUoa sc-fOOuSg lhyyLL dbKJYq">string</span></div><div><span class="sc-bEjUoa lhyyLL"> <!-- -->Enum<!-- -->:</span> <span class="sc-bEjUoa sc-dTWiOz lhyyLL kMQdIk">&quot;apartment&quot;</span> <span class="sc-bEjUoa sc-dTWiOz lhyyLL kMQdIk">&quot;house&quot;</span> <span class="sc-bEjUoa sc-dTWiOz lhyyLL kMQdIk">&quot;condo&quot;</span> <span class="sc-bEjUoa sc-dTWiOz lhyyLL kMQdIk">&quot;commercial&quot;</span> </div> <div><div html="&lt;p&gt;Optional property type&lt;/p&gt;
" class="sc-eVqvcJ sc-fszimp kIppRw drqpJr"><p>Optional property type</p>
</div></div></div></td></tr><tr class=""><td kind="field" title="totalUnits" class="sc-kCuUfV sc-fbQrwq gdmNWp dFOJWJ"><span class="sc-hwddKA cteAyA"></span><span class="property-name">totalUnits</span></td><td class="sc-gGKoUb ixGaBD"><div><div><span class="sc-bEjUoa sc-boKDdR lhyyLL jYezsP"></span><span class="sc-bEjUoa sc-fOOuSg lhyyLL dbKJYq">integer</span><span> <span class="sc-bEjUoa sc-goiVcJ lhyyLL bDfgbe"> <!-- -->&gt;= 1<!-- --> </span></span></div> <div><div html="" class="sc-eVqvcJ sc-fszimp kIppRw drqpJr"></div></div></div></td></tr><tr class=""><td kind="field" title="vacantUnits" class="sc-kCuUfV sc-fbQrwq gdmNWp dFOJWJ"><span class="sc-hwddKA cteAyA"></span><span class="property-name">vacantUnits</span></td><td class="sc-gGKoUb ixGaBD"><div><div><span class="sc-bEjUoa sc-boKDdR lhyyLL jYezsP"></span><span class="sc-bEjUoa sc-fOOuSg lhyyLL dbKJYq">integer</span></div> <div><div html="" class="sc-eVqvcJ sc-fszimp kIppRw drqpJr"></div></div></div></td></tr><tr class=""><td kind="field" title="status" class="sc-kCuUfV sc-fbQrwq gdmNWp dFOJWJ"><span class="sc-hwddKA cteAyA"></span><span class="property-name">status</span></td><td class="sc-gGKoUb ixGaBD"><div><div><span class="sc-bEjUoa sc-boKDdR lhyyLL jYezsP"></span><span class="sc-bEjUoa sc-fOOuSg lhyyLL dbKJYq">string</span></div><div><span class="sc-bEjUoa lhyyLL"> <!-- -->Enum<!-- -->:</span> <span class="sc-bEjUoa sc-dTWiOz lhyyLL kMQdIk">&quot;active&quot;</span> <span class="sc-bEjUoa sc-dTWiOz lhyyLL kMQdIk">&quot;inactive&quot;</span> <span class="sc-bEjUoa sc-dTWiOz lhyyLL kMQdIk">&quot;maintenance&quot;</span> </div> <div><div html="" class="sc-eVqvcJ sc-fszimp kIppRw drqpJr"></div></div></div></td></tr><tr class="last "><td kind="field" title="notes" class="sc-kCuUfV sc-fbQrwq gdmNWp dFOJWJ"><span class="sc-hwddKA cteAyA"></span><span class="property-name">notes</span></td><td class="sc-gGKoUb ixGaBD"><div><div><span class="sc-bEjUoa sc-boKDdR lhyyLL jYezsP"></span><span class="sc-bEjUoa sc-fOOuSg lhyyLL dbKJYq">string</span></div> <div><div html="" class="sc-eVqvcJ sc-fszimp kIppRw drqpJr"></div></div></div></td></tr></tbody></table><div><h3 class="sc-gDzyrw kjrVcG">Responses</h3><div><button class="sc-jIDBmd lkmdtA"><svg class="sc-dntSTA cGxVlA" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg><strong class="sc-eJvlPh fBhAXU">200<!-- --> </strong><div html="&lt;p&gt;Property updated successfully&lt;/p&gt;
" class="sc-eVqvcJ sc-fszimp sc-etsjJW kIppRw jnwENr ljKHqG"><p>Property updated successfully</p>
</div></button></div></div></div><div class="sc-jwTyAe sc-hjsuWn bDYKKx FFPsr"><div class="sc-eZSpzM jjnszm"><button class="sc-buTqWO iPCVMX"><span type="put" class="sc-fQLpxn dBzsUh http-verb put">put</span><span class="sc-jvKoal kZcHWP">/properties/{propertyId}</span><svg class="sc-dntSTA iuNpUs" style="margin-right:-25px" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg></button><div aria-hidden="true" class="sc-ecJghI ga-DQLq"><div class="sc-iyBeIh icOxsG"><div html="&lt;p&gt;Development server&lt;/p&gt;
" class="sc-eVqvcJ sc-fszimp kIppRw drqpJr"><p>Development server</p>
</div><div tabindex="0" role="button"><div class="sc-xKhEK okJpy"><span>http://localhost:8089</span>/properties/{propertyId}</div></div></div><div class="sc-iyBeIh icOxsG"><div html="&lt;p&gt;Production server&lt;/p&gt;
" class="sc-eVqvcJ sc-fszimp kIppRw drqpJr"><p>Production server</p>
</div><div tabindex="0" role="button"><div class="sc-xKhEK okJpy"><span>https://api.rentreport.com</span>/properties/{propertyId}</div></div></div></div></div><div><h3 class="sc-lgpSej drJHMo"> <!-- -->Request samples<!-- --> </h3><div class="sc-cOpnSz fyxuKi" data-rttabs="true"><ul class="react-tabs__tab-list" role="tablist"><li class="react-tabs__tab react-tabs__tab--selected" role="tab" id="tab«R4kkha»0" aria-selected="true" aria-disabled="false" aria-controls="panel«R4kkha»0" tabindex="0" data-rttab="true">Payload</li></ul><div class="react-tabs__tab-panel react-tabs__tab-panel--selected" role="tabpanel" id="panel«R4kkha»0" aria-labelledby="tab«R4kkha»0"><div><div class="sc-bSFBcf iLdyBp"><span class="sc-gahYZc cXitJ">Content type</span><div class="sc-bAehkN iNRAJK">application/json</div></div><div class="sc-blIAwI eKKwxo"><div class="sc-dClGHI fdRrNy"><div class="sc-bbbBoY bBWkcI"><button><div class="sc-fYmhhH iNCOCX">Copy</div></button></div><div tabindex="0" class="sc-eVqvcJ kIppRw sc-fhfEft dFvLDb"><div class="redoc-json"><code><span class="token string">&quot;sZq0gcKRNrOK3hcDugzqJ&quot;</span></code></div></div></div></div></div></div></div></div><div><h3 class="sc-lgpSej drJHMo"> <!-- -->Response samples<!-- --> </h3><div class="sc-cOpnSz fyxuKi" data-rttabs="true"><ul class="react-tabs__tab-list" role="tablist"><li class="tab-success react-tabs__tab--selected" role="tab" id="tab«R4skha»0" aria-selected="true" aria-disabled="false" aria-controls="panel«R4skha»0" tabindex="0" data-rttab="true">200</li></ul><div class="react-tabs__tab-panel react-tabs__tab-panel--selected" role="tabpanel" id="panel«R4skha»0" aria-labelledby="tab«R4skha»0"><div><div class="sc-bSFBcf iLdyBp"><span class="sc-gahYZc cXitJ">Content type</span><div class="sc-bAehkN iNRAJK">application/json</div></div><div class="sc-blIAwI eKKwxo"><div class="sc-dClGHI fdRrNy"><div class="sc-bbbBoY bBWkcI"><button><div class="sc-fYmhhH iNCOCX">Copy</div></button><button> Expand all </button><button> Collapse all </button></div><div tabindex="0" class="sc-eVqvcJ kIppRw sc-fhfEft dFvLDb"><div class="redoc-json"><code><button class="collapser" aria-label="collapse"></button><span class="token punctuation">{</span><span class="ellipsis"></span><ul class="obj collapsible"><li><div class="hoverable "><span class="property token string">"id"</span>: <span class="token string">&quot;sZq0gcKRNrOK3hcDugzqJ&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"name"</span>: <span class="token string">&quot;string&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"address"</span>: <button class="collapser" aria-label="collapse"></button><span class="token punctuation">{</span><span class="ellipsis"></span><ul class="obj collapsible"><li><div class="hoverable collapsed"><span class="property token string">"street"</span>: <span class="token string">&quot;string&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"city"</span>: <span class="token string">&quot;string&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"prov"</span>: <span class="token string">&quot;string&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"state"</span>: <span class="token string">&quot;string&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"zipCode"</span>: <span class="token string">&quot;string&quot;</span></div></li></ul><span class="token punctuation">}</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"propertyType"</span>: <span class="token string">&quot;apartment&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"totalUnits"</span>: <span class="token number">1</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"vacantUnits"</span>: <span class="token number">0</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"status"</span>: <span class="token string">&quot;active&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"notes"</span>: <span class="token string">&quot;string&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"userId"</span>: <span class="token string">&quot;sZq0gcKRNrOK3hcDugzqJ&quot;</span></div></li></ul><span class="token punctuation">}</span></code></div></div></div></div></div></div></div></div></div></div></div><div id="tag/Properties/paths/~1properties~1{propertyId}/delete" data-section-id="tag/Properties/paths/~1properties~1{propertyId}/delete" class="sc-dTvVRJ gHrCVQ"><div class="sc-jJLAfE gkiSyE"><div class="sc-ggWZvA fqkwbU"><h2 class="sc-kNOymR iFSqkw"><a class="sc-kcLKEh fRdsOi" href="#tag/Properties/paths/~1properties~1{propertyId}/delete" aria-label="tag/Properties/paths/~1properties~1{propertyId}/delete"></a>Delete Property<!-- --> </h2><div class="sc-bfjeOH txIPi"><div html="&lt;p&gt;Delete a specific property&lt;/p&gt;
" class="sc-eVqvcJ sc-fszimp kIppRw kbZred"><p>Delete a specific property</p>
</div></div><div><h5 class="sc-eqYatC czjApA">path<!-- --> Parameters</h5><table class="sc-eqNDNG icJLQx"><tbody><tr class="last "><td kind="field" title="propertyId" class="sc-kCuUfV sc-fbQrwq gdmNWp dFOJWJ"><span class="sc-hwddKA cteAyA"></span><span class="property-name">propertyId</span><div class="sc-bEjUoa sc-iIvHqT lhyyLL crXmiY">required</div></td><td class="sc-gGKoUb ixGaBD"><div><div><span class="sc-bEjUoa sc-boKDdR lhyyLL jYezsP"></span><span class="sc-bEjUoa sc-fOOuSg lhyyLL dbKJYq">string</span><span class="sc-bEjUoa sc-hdBJTi lhyyLL nwQTz"> (<!-- -->NanoID<!-- -->) </span><span class="sc-bEjUoa sc-cpclqO lhyyLL UZcrz">^[0-9A-Za-z]{11}$</span></div> <div><span class="sc-bEjUoa lhyyLL"> <!-- -->Example:<!-- --> </span> <span class="sc-bEjUoa sc-dTWiOz lhyyLL kMQdIk">sZq0gcKRNrOK3hcDugzqJ</span></div><div><div html="&lt;p&gt;Resource identifier&lt;/p&gt;
" class="sc-eVqvcJ sc-fszimp kIppRw drqpJr"><p>Resource identifier</p>
</div></div></div></td></tr></tbody></table></div><div><h3 class="sc-gDzyrw kjrVcG">Responses</h3><div><button class="sc-jIDBmd lkmdtA"><svg class="sc-dntSTA cGxVlA" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg><strong class="sc-eJvlPh fBhAXU">204<!-- --> </strong><div html="&lt;p&gt;Property deleted successfully&lt;/p&gt;
" class="sc-eVqvcJ sc-fszimp sc-etsjJW kIppRw jnwENr ljKHqG"><p>Property deleted successfully</p>
</div></button></div></div></div><div class="sc-jwTyAe sc-hjsuWn bDYKKx FFPsr"><div class="sc-eZSpzM jjnszm"><button class="sc-buTqWO iPCVMX"><span type="delete" class="sc-fQLpxn gKcHYQ http-verb delete">delete</span><span class="sc-jvKoal kZcHWP">/properties/{propertyId}</span><svg class="sc-dntSTA iuNpUs" style="margin-right:-25px" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg></button><div aria-hidden="true" class="sc-ecJghI ga-DQLq"><div class="sc-iyBeIh icOxsG"><div html="&lt;p&gt;Development server&lt;/p&gt;
" class="sc-eVqvcJ sc-fszimp kIppRw drqpJr"><p>Development server</p>
</div><div tabindex="0" role="button"><div class="sc-xKhEK okJpy"><span>http://localhost:8089</span>/properties/{propertyId}</div></div></div><div class="sc-iyBeIh icOxsG"><div html="&lt;p&gt;Production server&lt;/p&gt;
" class="sc-eVqvcJ sc-fszimp kIppRw drqpJr"><p>Production server</p>
</div><div tabindex="0" role="button"><div class="sc-xKhEK okJpy"><span>https://api.rentreport.com</span>/properties/{propertyId}</div></div></div></div></div><div><h3 class="sc-lgpSej drJHMo"> <!-- -->Response samples<!-- --> </h3><div class="sc-cOpnSz fyxuKi" data-rttabs="true"><ul class="react-tabs__tab-list" role="tablist"><li class="tab-success react-tabs__tab--selected" role="tab" id="tab«R4slha»0" aria-selected="true" aria-disabled="false" aria-controls="panel«R4slha»0" tabindex="0" data-rttab="true">204</li></ul><div class="react-tabs__tab-panel react-tabs__tab-panel--selected" role="tabpanel" id="panel«R4slha»0" aria-labelledby="tab«R4slha»0"><div><div class="sc-bSFBcf iLdyBp"><span class="sc-gahYZc cXitJ">Content type</span><div class="sc-bAehkN iNRAJK">application/json</div></div><div class="sc-blIAwI eKKwxo"><div class="sc-dClGHI fdRrNy"><div class="sc-bbbBoY bBWkcI"><button><div class="sc-fYmhhH iNCOCX">Copy</div></button></div><div tabindex="0" class="sc-eVqvcJ kIppRw sc-fhfEft dFvLDb"><div class="redoc-json"><code><span class="token punctuation">{ }</span></code></div></div></div></div></div></div></div></div></div></div></div><div id="tag/Properties/paths/~1properties~1{propertyId}~1rooms/post" data-section-id="tag/Properties/paths/~1properties~1{propertyId}~1rooms/post" class="sc-dTvVRJ gHrCVQ"><div class="sc-jJLAfE gkiSyE"><div class="sc-ggWZvA fqkwbU"><h2 class="sc-kNOymR iFSqkw"><a class="sc-kcLKEh fRdsOi" href="#tag/Properties/paths/~1properties~1{propertyId}~1rooms/post" aria-label="tag/Properties/paths/~1properties~1{propertyId}~1rooms/post"></a>Create Room<!-- --> </h2><div class="sc-bfjeOH txIPi"><div html="&lt;p&gt;Create a new room/unit in a specific property&lt;/p&gt;
" class="sc-eVqvcJ sc-fszimp kIppRw kbZred"><p>Create a new room/unit in a specific property</p>
</div></div><div><h5 class="sc-eqYatC czjApA">path<!-- --> Parameters</h5><table class="sc-eqNDNG icJLQx"><tbody><tr class="last "><td kind="field" title="propertyId" class="sc-kCuUfV sc-fbQrwq gdmNWp dFOJWJ"><span class="sc-hwddKA cteAyA"></span><span class="property-name">propertyId</span><div class="sc-bEjUoa sc-iIvHqT lhyyLL crXmiY">required</div></td><td class="sc-gGKoUb ixGaBD"><div><div><span class="sc-bEjUoa sc-boKDdR lhyyLL jYezsP"></span><span class="sc-bEjUoa sc-fOOuSg lhyyLL dbKJYq">string</span><span class="sc-bEjUoa sc-hdBJTi lhyyLL nwQTz"> (<!-- -->NanoID<!-- -->) </span><span class="sc-bEjUoa sc-cpclqO lhyyLL UZcrz">^[0-9A-Za-z]{11}$</span></div> <div><span class="sc-bEjUoa lhyyLL"> <!-- -->Example:<!-- --> </span> <span class="sc-bEjUoa sc-dTWiOz lhyyLL kMQdIk">sZq0gcKRNrOK3hcDugzqJ</span></div><div><div html="&lt;p&gt;Resource identifier&lt;/p&gt;
" class="sc-eVqvcJ sc-fszimp kIppRw drqpJr"><p>Resource identifier</p>
</div></div></div></td></tr></tbody></table></div><h5 class="sc-eqYatC czjApA">Request Body schema: <span class="sc-dNFkOE cFlAeY">application/json</span></h5><div html="" class="sc-eVqvcJ sc-fszimp kIppRw kbZred"></div><table class="sc-eqNDNG icJLQx"><tbody><tr class=""><td kind="field" title="name" class="sc-kCuUfV sc-fbQrwq gdmNWp dFOJWJ"><span class="sc-hwddKA cteAyA"></span><span class="property-name">name</span><div class="sc-bEjUoa sc-iIvHqT lhyyLL crXmiY">required</div></td><td class="sc-gGKoUb ixGaBD"><div><div><span class="sc-bEjUoa sc-boKDdR lhyyLL jYezsP"></span><span class="sc-bEjUoa sc-fOOuSg lhyyLL dbKJYq">string</span></div> <div><div html="&lt;p&gt;Room/Unit name&lt;/p&gt;
" class="sc-eVqvcJ sc-fszimp kIppRw drqpJr"><p>Room/Unit name</p>
</div></div></div></td></tr><tr class=""><td kind="field" title="type" class="sc-kCuUfV sc-fbQrwq gdmNWp dFOJWJ"><span class="sc-hwddKA cteAyA"></span><span class="property-name">type</span></td><td class="sc-gGKoUb ixGaBD"><div><div><span class="sc-bEjUoa sc-boKDdR lhyyLL jYezsP"></span><span class="sc-bEjUoa sc-fOOuSg lhyyLL dbKJYq">string</span></div><div><span class="sc-bEjUoa lhyyLL"> <!-- -->Enum<!-- -->:</span> <span class="sc-bEjUoa sc-dTWiOz lhyyLL kMQdIk">&quot;studio&quot;</span> <span class="sc-bEjUoa sc-dTWiOz lhyyLL kMQdIk">&quot;oneBed&quot;</span> <span class="sc-bEjUoa sc-dTWiOz lhyyLL kMQdIk">&quot;twoBed&quot;</span> <span class="sc-bEjUoa sc-dTWiOz lhyyLL kMQdIk">&quot;commercial&quot;</span> </div> <div><div html="" class="sc-eVqvcJ sc-fszimp kIppRw drqpJr"></div></div></div></td></tr><tr class=""><td kind="field" title="status" class="sc-kCuUfV sc-fbQrwq gdmNWp dFOJWJ"><span class="sc-hwddKA cteAyA"></span><span class="property-name">status</span></td><td class="sc-gGKoUb ixGaBD"><div><div><span class="sc-bEjUoa sc-boKDdR lhyyLL jYezsP"></span><span class="sc-bEjUoa sc-fOOuSg lhyyLL dbKJYq">string</span></div><div><span class="sc-bEjUoa lhyyLL"> <!-- -->Enum<!-- -->:</span> <span class="sc-bEjUoa sc-dTWiOz lhyyLL kMQdIk">&quot;vacant&quot;</span> <span class="sc-bEjUoa sc-dTWiOz lhyyLL kMQdIk">&quot;occupied&quot;</span> <span class="sc-bEjUoa sc-dTWiOz lhyyLL kMQdIk">&quot;maintenance&quot;</span> </div> <div><div html="" class="sc-eVqvcJ sc-fszimp kIppRw drqpJr"></div></div></div></td></tr><tr class="last "><td kind="field" title="notes" class="sc-kCuUfV sc-fbQrwq gdmNWp dFOJWJ"><span class="sc-hwddKA cteAyA"></span><span class="property-name">notes</span></td><td class="sc-gGKoUb ixGaBD"><div><div><span class="sc-bEjUoa sc-boKDdR lhyyLL jYezsP"></span><span class="sc-bEjUoa sc-fOOuSg lhyyLL dbKJYq">string</span></div> <div><div html="&lt;p&gt;A place to keep notes for internal purposes&lt;/p&gt;
" class="sc-eVqvcJ sc-fszimp kIppRw drqpJr"><p>A place to keep notes for internal purposes</p>
</div></div></div></td></tr></tbody></table><div><h3 class="sc-gDzyrw kjrVcG">Responses</h3><div><button class="sc-jIDBmd lkmdtA"><svg class="sc-dntSTA cGxVlA" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg><strong class="sc-eJvlPh fBhAXU">201<!-- --> </strong><div html="&lt;p&gt;Room created successfully&lt;/p&gt;
" class="sc-eVqvcJ sc-fszimp sc-etsjJW kIppRw jnwENr ljKHqG"><p>Room created successfully</p>
</div></button></div></div></div><div class="sc-jwTyAe sc-hjsuWn bDYKKx FFPsr"><div class="sc-eZSpzM jjnszm"><button class="sc-buTqWO iPCVMX"><span type="post" class="sc-fQLpxn kwcmyC http-verb post">post</span><span class="sc-jvKoal kZcHWP">/properties/{propertyId}/rooms</span><svg class="sc-dntSTA iuNpUs" style="margin-right:-25px" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg></button><div aria-hidden="true" class="sc-ecJghI ga-DQLq"><div class="sc-iyBeIh icOxsG"><div html="&lt;p&gt;Development server&lt;/p&gt;
" class="sc-eVqvcJ sc-fszimp kIppRw drqpJr"><p>Development server</p>
</div><div tabindex="0" role="button"><div class="sc-xKhEK okJpy"><span>http://localhost:8089</span>/properties/{propertyId}/rooms</div></div></div><div class="sc-iyBeIh icOxsG"><div html="&lt;p&gt;Production server&lt;/p&gt;
" class="sc-eVqvcJ sc-fszimp kIppRw drqpJr"><p>Production server</p>
</div><div tabindex="0" role="button"><div class="sc-xKhEK okJpy"><span>https://api.rentreport.com</span>/properties/{propertyId}/rooms</div></div></div></div></div><div><h3 class="sc-lgpSej drJHMo"> <!-- -->Request samples<!-- --> </h3><div class="sc-cOpnSz fyxuKi" data-rttabs="true"><ul class="react-tabs__tab-list" role="tablist"><li class="react-tabs__tab react-tabs__tab--selected" role="tab" id="tab«R4kmha»0" aria-selected="true" aria-disabled="false" aria-controls="panel«R4kmha»0" tabindex="0" data-rttab="true">Payload</li></ul><div class="react-tabs__tab-panel react-tabs__tab-panel--selected" role="tabpanel" id="panel«R4kmha»0" aria-labelledby="tab«R4kmha»0"><div><div class="sc-bSFBcf iLdyBp"><span class="sc-gahYZc cXitJ">Content type</span><div class="sc-bAehkN iNRAJK">application/json</div></div><div class="sc-blIAwI eKKwxo"><div class="sc-dClGHI fdRrNy"><div class="sc-bbbBoY bBWkcI"><button><div class="sc-fYmhhH iNCOCX">Copy</div></button></div><div tabindex="0" class="sc-eVqvcJ kIppRw sc-fhfEft dFvLDb"><div class="redoc-json"><code><span class="token string">&quot;sZq0gcKRNrOK3hcDugzqJ&quot;</span></code></div></div></div></div></div></div></div></div><div><h3 class="sc-lgpSej drJHMo"> <!-- -->Response samples<!-- --> </h3><div class="sc-cOpnSz fyxuKi" data-rttabs="true"><ul class="react-tabs__tab-list" role="tablist"><li class="tab-success react-tabs__tab--selected" role="tab" id="tab«R4smha»0" aria-selected="true" aria-disabled="false" aria-controls="panel«R4smha»0" tabindex="0" data-rttab="true">201</li></ul><div class="react-tabs__tab-panel react-tabs__tab-panel--selected" role="tabpanel" id="panel«R4smha»0" aria-labelledby="tab«R4smha»0"><div><div class="sc-bSFBcf iLdyBp"><span class="sc-gahYZc cXitJ">Content type</span><div class="sc-bAehkN iNRAJK">application/json</div></div><div class="sc-blIAwI eKKwxo"><div class="sc-dClGHI fdRrNy"><div class="sc-bbbBoY bBWkcI"><button><div class="sc-fYmhhH iNCOCX">Copy</div></button></div><div tabindex="0" class="sc-eVqvcJ kIppRw sc-fhfEft dFvLDb"><div class="redoc-json"><code><button class="collapser" aria-label="collapse"></button><span class="token punctuation">{</span><span class="ellipsis"></span><ul class="obj collapsible"><li><div class="hoverable "><span class="property token string">"id"</span>: <span class="token string">&quot;sZq0gcKRNrOK3hcDugzqJ&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"name"</span>: <span class="token string">&quot;string&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"type"</span>: <span class="token string">&quot;studio&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"status"</span>: <span class="token string">&quot;vacant&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"notes"</span>: <span class="token string">&quot;string&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"propertyId"</span>: <span class="token string">&quot;sZq0gcKRNrOK3hcDugzqJ&quot;</span></div></li></ul><span class="token punctuation">}</span></code></div></div></div></div></div></div></div></div></div></div></div><div id="tag/Properties/paths/~1properties~1{propertyId}~1rooms~1{roomId}/get" data-section-id="tag/Properties/paths/~1properties~1{propertyId}~1rooms~1{roomId}/get" class="sc-dTvVRJ gHrCVQ"><div class="sc-jJLAfE gkiSyE"><div class="sc-ggWZvA fqkwbU"><h2 class="sc-kNOymR iFSqkw"><a class="sc-kcLKEh fRdsOi" href="#tag/Properties/paths/~1properties~1{propertyId}~1rooms~1{roomId}/get" aria-label="tag/Properties/paths/~1properties~1{propertyId}~1rooms~1{roomId}/get"></a>/properties/{propertyId}/rooms/{roomId}<!-- --> </h2><div class="sc-bfjeOH txIPi"><div html="&lt;p&gt;获取指定room的详细信息&lt;/p&gt;
" class="sc-eVqvcJ sc-fszimp kIppRw kbZred"><p>获取指定room的详细信息</p>
</div></div><div><h5 class="sc-eqYatC czjApA">path<!-- --> Parameters</h5><table class="sc-eqNDNG icJLQx"><tbody><tr class=""><td kind="field" title="propertyId" class="sc-kCuUfV sc-fbQrwq gdmNWp dFOJWJ"><span class="sc-hwddKA cteAyA"></span><span class="property-name">propertyId</span><div class="sc-bEjUoa sc-iIvHqT lhyyLL crXmiY">required</div></td><td class="sc-gGKoUb ixGaBD"><div><div><span class="sc-bEjUoa sc-boKDdR lhyyLL jYezsP"></span><span class="sc-bEjUoa sc-fOOuSg lhyyLL dbKJYq">string</span><span class="sc-bEjUoa sc-hdBJTi lhyyLL nwQTz"> (<!-- -->NanoID<!-- -->) </span><span class="sc-bEjUoa sc-cpclqO lhyyLL UZcrz">^[0-9A-Za-z]{11}$</span></div> <div><span class="sc-bEjUoa lhyyLL"> <!-- -->Example:<!-- --> </span> <span class="sc-bEjUoa sc-dTWiOz lhyyLL kMQdIk">sZq0gcKRNrOK3hcDugzqJ</span></div><div><div html="&lt;p&gt;Resource identifier&lt;/p&gt;
" class="sc-eVqvcJ sc-fszimp kIppRw drqpJr"><p>Resource identifier</p>
</div></div></div></td></tr><tr class="last "><td kind="field" title="roomId" class="sc-kCuUfV sc-fbQrwq gdmNWp dFOJWJ"><span class="sc-hwddKA cteAyA"></span><span class="property-name">roomId</span><div class="sc-bEjUoa sc-iIvHqT lhyyLL crXmiY">required</div></td><td class="sc-gGKoUb ixGaBD"><div><div><span class="sc-bEjUoa sc-boKDdR lhyyLL jYezsP"></span><span class="sc-bEjUoa sc-fOOuSg lhyyLL dbKJYq">string</span><span class="sc-bEjUoa sc-hdBJTi lhyyLL nwQTz"> (<!-- -->NanoID<!-- -->) </span><span class="sc-bEjUoa sc-cpclqO lhyyLL UZcrz">^[0-9A-Za-z]{11}$</span></div> <div><span class="sc-bEjUoa lhyyLL"> <!-- -->Example:<!-- --> </span> <span class="sc-bEjUoa sc-dTWiOz lhyyLL kMQdIk">sZq0gcKRNrOK3hcDugzqJ</span></div><div><div html="&lt;p&gt;Resource identifier&lt;/p&gt;
" class="sc-eVqvcJ sc-fszimp kIppRw drqpJr"><p>Resource identifier</p>
</div></div></div></td></tr></tbody></table></div><div><h3 class="sc-gDzyrw kjrVcG">Responses</h3><div><button class="sc-jIDBmd lkmdtA"><svg class="sc-dntSTA cGxVlA" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg><strong class="sc-eJvlPh fBhAXU">200<!-- --> </strong><div html="&lt;p&gt;Room details retrieved successfully&lt;/p&gt;
" class="sc-eVqvcJ sc-fszimp sc-etsjJW kIppRw jnwENr ljKHqG"><p>Room details retrieved successfully</p>
</div></button></div></div></div><div class="sc-jwTyAe sc-hjsuWn bDYKKx FFPsr"><div class="sc-eZSpzM jjnszm"><button class="sc-buTqWO iPCVMX"><span type="get" class="sc-fQLpxn dynMBc http-verb get">get</span><span class="sc-jvKoal kZcHWP">/properties/{propertyId}/rooms/{roomId}</span><svg class="sc-dntSTA iuNpUs" style="margin-right:-25px" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg></button><div aria-hidden="true" class="sc-ecJghI ga-DQLq"><div class="sc-iyBeIh icOxsG"><div html="&lt;p&gt;Development server&lt;/p&gt;
" class="sc-eVqvcJ sc-fszimp kIppRw drqpJr"><p>Development server</p>
</div><div tabindex="0" role="button"><div class="sc-xKhEK okJpy"><span>http://localhost:8089</span>/properties/{propertyId}/rooms/{roomId}</div></div></div><div class="sc-iyBeIh icOxsG"><div html="&lt;p&gt;Production server&lt;/p&gt;
" class="sc-eVqvcJ sc-fszimp kIppRw drqpJr"><p>Production server</p>
</div><div tabindex="0" role="button"><div class="sc-xKhEK okJpy"><span>https://api.rentreport.com</span>/properties/{propertyId}/rooms/{roomId}</div></div></div></div></div><div><h3 class="sc-lgpSej drJHMo"> <!-- -->Response samples<!-- --> </h3><div class="sc-cOpnSz fyxuKi" data-rttabs="true"><ul class="react-tabs__tab-list" role="tablist"><li class="tab-success react-tabs__tab--selected" role="tab" id="tab«R4snha»0" aria-selected="true" aria-disabled="false" aria-controls="panel«R4snha»0" tabindex="0" data-rttab="true">200</li></ul><div class="react-tabs__tab-panel react-tabs__tab-panel--selected" role="tabpanel" id="panel«R4snha»0" aria-labelledby="tab«R4snha»0"><div><div class="sc-bSFBcf iLdyBp"><span class="sc-gahYZc cXitJ">Content type</span><div class="sc-bAehkN iNRAJK">application/json</div></div><div class="sc-blIAwI eKKwxo"><div class="sc-dClGHI fdRrNy"><div class="sc-bbbBoY bBWkcI"><button><div class="sc-fYmhhH iNCOCX">Copy</div></button></div><div tabindex="0" class="sc-eVqvcJ kIppRw sc-fhfEft dFvLDb"><div class="redoc-json"><code><button class="collapser" aria-label="collapse"></button><span class="token punctuation">{</span><span class="ellipsis"></span><ul class="obj collapsible"><li><div class="hoverable "><span class="property token string">"id"</span>: <span class="token string">&quot;sZq0gcKRNrOK3hcDugzqJ&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"name"</span>: <span class="token string">&quot;string&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"type"</span>: <span class="token string">&quot;studio&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"status"</span>: <span class="token string">&quot;vacant&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"notes"</span>: <span class="token string">&quot;string&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"propertyId"</span>: <span class="token string">&quot;sZq0gcKRNrOK3hcDugzqJ&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"userId"</span>: <span class="token string">&quot;sZq0gcKRNrOK3hcDugzqJ&quot;</span></div></li></ul><span class="token punctuation">}</span></code></div></div></div></div></div></div></div></div></div></div></div><div id="tag/Properties/paths/~1properties~1{propertyId}~1rooms~1{roomId}/put" data-section-id="tag/Properties/paths/~1properties~1{propertyId}~1rooms~1{roomId}/put" class="sc-dTvVRJ gHrCVQ"><div class="sc-jJLAfE gkiSyE"><div class="sc-ggWZvA fqkwbU"><h2 class="sc-kNOymR iFSqkw"><a class="sc-kcLKEh fRdsOi" href="#tag/Properties/paths/~1properties~1{propertyId}~1rooms~1{roomId}/put" aria-label="tag/Properties/paths/~1properties~1{propertyId}~1rooms~1{roomId}/put"></a>/properties/{propertyId}/rooms/{roomId}<!-- --> </h2><div class="sc-bfjeOH txIPi"><div html="&lt;p&gt;更新指定room的信息&lt;/p&gt;
" class="sc-eVqvcJ sc-fszimp kIppRw kbZred"><p>更新指定room的信息</p>
</div></div><div><h5 class="sc-eqYatC czjApA">path<!-- --> Parameters</h5><table class="sc-eqNDNG icJLQx"><tbody><tr class=""><td kind="field" title="propertyId" class="sc-kCuUfV sc-fbQrwq gdmNWp dFOJWJ"><span class="sc-hwddKA cteAyA"></span><span class="property-name">propertyId</span><div class="sc-bEjUoa sc-iIvHqT lhyyLL crXmiY">required</div></td><td class="sc-gGKoUb ixGaBD"><div><div><span class="sc-bEjUoa sc-boKDdR lhyyLL jYezsP"></span><span class="sc-bEjUoa sc-fOOuSg lhyyLL dbKJYq">string</span><span class="sc-bEjUoa sc-hdBJTi lhyyLL nwQTz"> (<!-- -->NanoID<!-- -->) </span><span class="sc-bEjUoa sc-cpclqO lhyyLL UZcrz">^[0-9A-Za-z]{11}$</span></div> <div><span class="sc-bEjUoa lhyyLL"> <!-- -->Example:<!-- --> </span> <span class="sc-bEjUoa sc-dTWiOz lhyyLL kMQdIk">sZq0gcKRNrOK3hcDugzqJ</span></div><div><div html="&lt;p&gt;Resource identifier&lt;/p&gt;
" class="sc-eVqvcJ sc-fszimp kIppRw drqpJr"><p>Resource identifier</p>
</div></div></div></td></tr><tr class="last "><td kind="field" title="roomId" class="sc-kCuUfV sc-fbQrwq gdmNWp dFOJWJ"><span class="sc-hwddKA cteAyA"></span><span class="property-name">roomId</span><div class="sc-bEjUoa sc-iIvHqT lhyyLL crXmiY">required</div></td><td class="sc-gGKoUb ixGaBD"><div><div><span class="sc-bEjUoa sc-boKDdR lhyyLL jYezsP"></span><span class="sc-bEjUoa sc-fOOuSg lhyyLL dbKJYq">string</span><span class="sc-bEjUoa sc-hdBJTi lhyyLL nwQTz"> (<!-- -->NanoID<!-- -->) </span><span class="sc-bEjUoa sc-cpclqO lhyyLL UZcrz">^[0-9A-Za-z]{11}$</span></div> <div><span class="sc-bEjUoa lhyyLL"> <!-- -->Example:<!-- --> </span> <span class="sc-bEjUoa sc-dTWiOz lhyyLL kMQdIk">sZq0gcKRNrOK3hcDugzqJ</span></div><div><div html="&lt;p&gt;Resource identifier&lt;/p&gt;
" class="sc-eVqvcJ sc-fszimp kIppRw drqpJr"><p>Resource identifier</p>
</div></div></div></td></tr></tbody></table></div><h5 class="sc-eqYatC czjApA">Request Body schema: <span class="sc-dNFkOE cFlAeY">application/json</span></h5><div html="" class="sc-eVqvcJ sc-fszimp kIppRw kbZred"></div><table class="sc-eqNDNG icJLQx"><tbody><tr class=""><td kind="field" title="id" class="sc-kCuUfV sc-fbQrwq gdmNWp dFOJWJ"><span class="sc-hwddKA cteAyA"></span><span class="property-name">id</span><div class="sc-bEjUoa sc-iIvHqT lhyyLL crXmiY">required</div></td><td class="sc-gGKoUb ixGaBD"><div><div><span class="sc-bEjUoa sc-boKDdR lhyyLL jYezsP"></span><span class="sc-bEjUoa sc-fOOuSg lhyyLL dbKJYq">string</span><span class="sc-bEjUoa sc-hdBJTi lhyyLL nwQTz"> (<!-- -->NanoID<!-- -->) </span><span class="sc-bEjUoa sc-cpclqO lhyyLL UZcrz">^[0-9A-Za-z]{11}$</span></div> <div><div html="&lt;p&gt;NanoID - 11 character alphanumeric identifier&lt;/p&gt;
" class="sc-eVqvcJ sc-fszimp kIppRw drqpJr"><p>NanoID - 11 character alphanumeric identifier</p>
</div></div></div></td></tr><tr class=""><td kind="field" title="name" class="sc-kCuUfV sc-fbQrwq gdmNWp dFOJWJ"><span class="sc-hwddKA cteAyA"></span><span class="property-name">name</span><div class="sc-bEjUoa sc-iIvHqT lhyyLL crXmiY">required</div></td><td class="sc-gGKoUb ixGaBD"><div><div><span class="sc-bEjUoa sc-boKDdR lhyyLL jYezsP"></span><span class="sc-bEjUoa sc-fOOuSg lhyyLL dbKJYq">string</span></div> <div><div html="&lt;p&gt;Room/Unit name&lt;/p&gt;
" class="sc-eVqvcJ sc-fszimp kIppRw drqpJr"><p>Room/Unit name</p>
</div></div></div></td></tr><tr class=""><td kind="field" title="type" class="sc-kCuUfV sc-fbQrwq gdmNWp dFOJWJ"><span class="sc-hwddKA cteAyA"></span><span class="property-name">type</span></td><td class="sc-gGKoUb ixGaBD"><div><div><span class="sc-bEjUoa sc-boKDdR lhyyLL jYezsP"></span><span class="sc-bEjUoa sc-fOOuSg lhyyLL dbKJYq">string</span></div><div><span class="sc-bEjUoa lhyyLL"> <!-- -->Enum<!-- -->:</span> <span class="sc-bEjUoa sc-dTWiOz lhyyLL kMQdIk">&quot;studio&quot;</span> <span class="sc-bEjUoa sc-dTWiOz lhyyLL kMQdIk">&quot;oneBed&quot;</span> <span class="sc-bEjUoa sc-dTWiOz lhyyLL kMQdIk">&quot;twoBed&quot;</span> <span class="sc-bEjUoa sc-dTWiOz lhyyLL kMQdIk">&quot;commercial&quot;</span> </div> <div><div html="" class="sc-eVqvcJ sc-fszimp kIppRw drqpJr"></div></div></div></td></tr><tr class=""><td kind="field" title="status" class="sc-kCuUfV sc-fbQrwq gdmNWp dFOJWJ"><span class="sc-hwddKA cteAyA"></span><span class="property-name">status</span></td><td class="sc-gGKoUb ixGaBD"><div><div><span class="sc-bEjUoa sc-boKDdR lhyyLL jYezsP"></span><span class="sc-bEjUoa sc-fOOuSg lhyyLL dbKJYq">string</span></div><div><span class="sc-bEjUoa lhyyLL"> <!-- -->Enum<!-- -->:</span> <span class="sc-bEjUoa sc-dTWiOz lhyyLL kMQdIk">&quot;vacant&quot;</span> <span class="sc-bEjUoa sc-dTWiOz lhyyLL kMQdIk">&quot;occupied&quot;</span> <span class="sc-bEjUoa sc-dTWiOz lhyyLL kMQdIk">&quot;maintenance&quot;</span> </div> <div><div html="" class="sc-eVqvcJ sc-fszimp kIppRw drqpJr"></div></div></div></td></tr><tr class=""><td kind="field" title="notes" class="sc-kCuUfV sc-fbQrwq gdmNWp dFOJWJ"><span class="sc-hwddKA cteAyA"></span><span class="property-name">notes</span></td><td class="sc-gGKoUb ixGaBD"><div><div><span class="sc-bEjUoa sc-boKDdR lhyyLL jYezsP"></span><span class="sc-bEjUoa sc-fOOuSg lhyyLL dbKJYq">string</span></div> <div><div html="&lt;p&gt;A place to keep notes for internal purposes&lt;/p&gt;
" class="sc-eVqvcJ sc-fszimp kIppRw drqpJr"><p>A place to keep notes for internal purposes</p>
</div></div></div></td></tr><tr class="last "><td kind="field" title="propertyId" class="sc-kCuUfV sc-fbQrwq gdmNWp dFOJWJ"><span class="sc-hwddKA cteAyA"></span><span class="property-name">propertyId</span><div class="sc-bEjUoa sc-iIvHqT lhyyLL crXmiY">required</div></td><td class="sc-gGKoUb ixGaBD"><div><div><span class="sc-bEjUoa sc-boKDdR lhyyLL jYezsP"></span><span class="sc-bEjUoa sc-fOOuSg lhyyLL dbKJYq">string</span><span class="sc-bEjUoa sc-hdBJTi lhyyLL nwQTz"> (<!-- -->NanoID<!-- -->) </span><span class="sc-bEjUoa sc-cpclqO lhyyLL UZcrz">^[0-9A-Za-z]{11}$</span></div> <div><div html="&lt;p&gt;NanoID - 11 character alphanumeric identifier&lt;/p&gt;
" class="sc-eVqvcJ sc-fszimp kIppRw drqpJr"><p>NanoID - 11 character alphanumeric identifier</p>
</div></div></div></td></tr></tbody></table><div><h3 class="sc-gDzyrw kjrVcG">Responses</h3><div><button class="sc-jIDBmd lkmdtA"><svg class="sc-dntSTA cGxVlA" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg><strong class="sc-eJvlPh fBhAXU">200<!-- --> </strong><div html="&lt;p&gt;Room updated successfully&lt;/p&gt;
" class="sc-eVqvcJ sc-fszimp sc-etsjJW kIppRw jnwENr ljKHqG"><p>Room updated successfully</p>
</div></button></div></div></div><div class="sc-jwTyAe sc-hjsuWn bDYKKx FFPsr"><div class="sc-eZSpzM jjnszm"><button class="sc-buTqWO iPCVMX"><span type="put" class="sc-fQLpxn dBzsUh http-verb put">put</span><span class="sc-jvKoal kZcHWP">/properties/{propertyId}/rooms/{roomId}</span><svg class="sc-dntSTA iuNpUs" style="margin-right:-25px" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg></button><div aria-hidden="true" class="sc-ecJghI ga-DQLq"><div class="sc-iyBeIh icOxsG"><div html="&lt;p&gt;Development server&lt;/p&gt;
" class="sc-eVqvcJ sc-fszimp kIppRw drqpJr"><p>Development server</p>
</div><div tabindex="0" role="button"><div class="sc-xKhEK okJpy"><span>http://localhost:8089</span>/properties/{propertyId}/rooms/{roomId}</div></div></div><div class="sc-iyBeIh icOxsG"><div html="&lt;p&gt;Production server&lt;/p&gt;
" class="sc-eVqvcJ sc-fszimp kIppRw drqpJr"><p>Production server</p>
</div><div tabindex="0" role="button"><div class="sc-xKhEK okJpy"><span>https://api.rentreport.com</span>/properties/{propertyId}/rooms/{roomId}</div></div></div></div></div><div><h3 class="sc-lgpSej drJHMo"> <!-- -->Request samples<!-- --> </h3><div class="sc-cOpnSz fyxuKi" data-rttabs="true"><ul class="react-tabs__tab-list" role="tablist"><li class="react-tabs__tab react-tabs__tab--selected" role="tab" id="tab«R4koha»0" aria-selected="true" aria-disabled="false" aria-controls="panel«R4koha»0" tabindex="0" data-rttab="true">Payload</li></ul><div class="react-tabs__tab-panel react-tabs__tab-panel--selected" role="tabpanel" id="panel«R4koha»0" aria-labelledby="tab«R4koha»0"><div><div class="sc-bSFBcf iLdyBp"><span class="sc-gahYZc cXitJ">Content type</span><div class="sc-bAehkN iNRAJK">application/json</div></div><div class="sc-blIAwI eKKwxo"><div class="sc-dClGHI fdRrNy"><div class="sc-bbbBoY bBWkcI"><button><div class="sc-fYmhhH iNCOCX">Copy</div></button></div><div tabindex="0" class="sc-eVqvcJ kIppRw sc-fhfEft dFvLDb"><div class="redoc-json"><code><span class="token string">&quot;sZq0gcKRNrOK3hcDugzqJ&quot;</span></code></div></div></div></div></div></div></div></div><div><h3 class="sc-lgpSej drJHMo"> <!-- -->Response samples<!-- --> </h3><div class="sc-cOpnSz fyxuKi" data-rttabs="true"><ul class="react-tabs__tab-list" role="tablist"><li class="tab-success react-tabs__tab--selected" role="tab" id="tab«R4soha»0" aria-selected="true" aria-disabled="false" aria-controls="panel«R4soha»0" tabindex="0" data-rttab="true">200</li></ul><div class="react-tabs__tab-panel react-tabs__tab-panel--selected" role="tabpanel" id="panel«R4soha»0" aria-labelledby="tab«R4soha»0"><div><div class="sc-bSFBcf iLdyBp"><span class="sc-gahYZc cXitJ">Content type</span><div class="sc-bAehkN iNRAJK">application/json</div></div><div class="sc-blIAwI eKKwxo"><div class="sc-dClGHI fdRrNy"><div class="sc-bbbBoY bBWkcI"><button><div class="sc-fYmhhH iNCOCX">Copy</div></button></div><div tabindex="0" class="sc-eVqvcJ kIppRw sc-fhfEft dFvLDb"><div class="redoc-json"><code><button class="collapser" aria-label="collapse"></button><span class="token punctuation">{</span><span class="ellipsis"></span><ul class="obj collapsible"><li><div class="hoverable "><span class="property token string">"id"</span>: <span class="token string">&quot;sZq0gcKRNrOK3hcDugzqJ&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"name"</span>: <span class="token string">&quot;string&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"type"</span>: <span class="token string">&quot;studio&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"status"</span>: <span class="token string">&quot;vacant&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"notes"</span>: <span class="token string">&quot;string&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"propertyId"</span>: <span class="token string">&quot;sZq0gcKRNrOK3hcDugzqJ&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"userId"</span>: <span class="token string">&quot;sZq0gcKRNrOK3hcDugzqJ&quot;</span></div></li></ul><span class="token punctuation">}</span></code></div></div></div></div></div></div></div></div></div></div></div><div id="tag/Properties/paths/~1properties~1{propertyId}~1rooms~1{roomId}/delete" data-section-id="tag/Properties/paths/~1properties~1{propertyId}~1rooms~1{roomId}/delete" class="sc-dTvVRJ gHrCVQ"><div class="sc-jJLAfE gkiSyE"><div class="sc-ggWZvA fqkwbU"><h2 class="sc-kNOymR iFSqkw"><a class="sc-kcLKEh fRdsOi" href="#tag/Properties/paths/~1properties~1{propertyId}~1rooms~1{roomId}/delete" aria-label="tag/Properties/paths/~1properties~1{propertyId}~1rooms~1{roomId}/delete"></a>/properties/{propertyId}/rooms/{roomId}<!-- --> </h2><div class="sc-bfjeOH txIPi"><div html="&lt;p&gt;删除指定的room&lt;/p&gt;
" class="sc-eVqvcJ sc-fszimp kIppRw kbZred"><p>删除指定的room</p>
</div></div><div><h5 class="sc-eqYatC czjApA">path<!-- --> Parameters</h5><table class="sc-eqNDNG icJLQx"><tbody><tr class=""><td kind="field" title="propertyId" class="sc-kCuUfV sc-fbQrwq gdmNWp dFOJWJ"><span class="sc-hwddKA cteAyA"></span><span class="property-name">propertyId</span><div class="sc-bEjUoa sc-iIvHqT lhyyLL crXmiY">required</div></td><td class="sc-gGKoUb ixGaBD"><div><div><span class="sc-bEjUoa sc-boKDdR lhyyLL jYezsP"></span><span class="sc-bEjUoa sc-fOOuSg lhyyLL dbKJYq">string</span><span class="sc-bEjUoa sc-hdBJTi lhyyLL nwQTz"> (<!-- -->NanoID<!-- -->) </span><span class="sc-bEjUoa sc-cpclqO lhyyLL UZcrz">^[0-9A-Za-z]{11}$</span></div> <div><span class="sc-bEjUoa lhyyLL"> <!-- -->Example:<!-- --> </span> <span class="sc-bEjUoa sc-dTWiOz lhyyLL kMQdIk">sZq0gcKRNrOK3hcDugzqJ</span></div><div><div html="&lt;p&gt;Resource identifier&lt;/p&gt;
" class="sc-eVqvcJ sc-fszimp kIppRw drqpJr"><p>Resource identifier</p>
</div></div></div></td></tr><tr class="last "><td kind="field" title="roomId" class="sc-kCuUfV sc-fbQrwq gdmNWp dFOJWJ"><span class="sc-hwddKA cteAyA"></span><span class="property-name">roomId</span><div class="sc-bEjUoa sc-iIvHqT lhyyLL crXmiY">required</div></td><td class="sc-gGKoUb ixGaBD"><div><div><span class="sc-bEjUoa sc-boKDdR lhyyLL jYezsP"></span><span class="sc-bEjUoa sc-fOOuSg lhyyLL dbKJYq">string</span><span class="sc-bEjUoa sc-hdBJTi lhyyLL nwQTz"> (<!-- -->NanoID<!-- -->) </span><span class="sc-bEjUoa sc-cpclqO lhyyLL UZcrz">^[0-9A-Za-z]{11}$</span></div> <div><span class="sc-bEjUoa lhyyLL"> <!-- -->Example:<!-- --> </span> <span class="sc-bEjUoa sc-dTWiOz lhyyLL kMQdIk">sZq0gcKRNrOK3hcDugzqJ</span></div><div><div html="&lt;p&gt;Resource identifier&lt;/p&gt;
" class="sc-eVqvcJ sc-fszimp kIppRw drqpJr"><p>Resource identifier</p>
</div></div></div></td></tr></tbody></table></div><div><h3 class="sc-gDzyrw kjrVcG">Responses</h3><div><button class="sc-jIDBmd lkmdtA"><svg class="sc-dntSTA cGxVlA" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg><strong class="sc-eJvlPh fBhAXU">204<!-- --> </strong><div html="&lt;p&gt;Room deleted successfully&lt;/p&gt;
" class="sc-eVqvcJ sc-fszimp sc-etsjJW kIppRw jnwENr ljKHqG"><p>Room deleted successfully</p>
</div></button></div></div></div><div class="sc-jwTyAe sc-hjsuWn bDYKKx FFPsr"><div class="sc-eZSpzM jjnszm"><button class="sc-buTqWO iPCVMX"><span type="delete" class="sc-fQLpxn gKcHYQ http-verb delete">delete</span><span class="sc-jvKoal kZcHWP">/properties/{propertyId}/rooms/{roomId}</span><svg class="sc-dntSTA iuNpUs" style="margin-right:-25px" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg></button><div aria-hidden="true" class="sc-ecJghI ga-DQLq"><div class="sc-iyBeIh icOxsG"><div html="&lt;p&gt;Development server&lt;/p&gt;
" class="sc-eVqvcJ sc-fszimp kIppRw drqpJr"><p>Development server</p>
</div><div tabindex="0" role="button"><div class="sc-xKhEK okJpy"><span>http://localhost:8089</span>/properties/{propertyId}/rooms/{roomId}</div></div></div><div class="sc-iyBeIh icOxsG"><div html="&lt;p&gt;Production server&lt;/p&gt;
" class="sc-eVqvcJ sc-fszimp kIppRw drqpJr"><p>Production server</p>
</div><div tabindex="0" role="button"><div class="sc-xKhEK okJpy"><span>https://api.rentreport.com</span>/properties/{propertyId}/rooms/{roomId}</div></div></div></div></div><div><h3 class="sc-lgpSej drJHMo"> <!-- -->Response samples<!-- --> </h3><div class="sc-cOpnSz fyxuKi" data-rttabs="true"><ul class="react-tabs__tab-list" role="tablist"><li class="tab-success react-tabs__tab--selected" role="tab" id="tab«R4spha»0" aria-selected="true" aria-disabled="false" aria-controls="panel«R4spha»0" tabindex="0" data-rttab="true">204</li></ul><div class="react-tabs__tab-panel react-tabs__tab-panel--selected" role="tabpanel" id="panel«R4spha»0" aria-labelledby="tab«R4spha»0"><div><div class="sc-bSFBcf iLdyBp"><span class="sc-gahYZc cXitJ">Content type</span><div class="sc-bAehkN iNRAJK">application/json</div></div><div class="sc-blIAwI eKKwxo"><div class="sc-dClGHI fdRrNy"><div class="sc-bbbBoY bBWkcI"><button><div class="sc-fYmhhH iNCOCX">Copy</div></button></div><div tabindex="0" class="sc-eVqvcJ kIppRw sc-fhfEft dFvLDb"><div class="redoc-json"><code><span class="token punctuation">{ }</span></code></div></div></div></div></div></div></div></div></div></div></div><div id="tag/Leases" data-section-id="tag/Leases" class="sc-dTvVRJ bPmFpz"><div class="sc-jJLAfE gkiSyE"><div class="sc-ggWZvA fqkwbU"><h2 class="sc-kNOymR iFSqkw"><a class="sc-kcLKEh fRdsOi" href="#tag/Leases" aria-label="tag/Leases"></a>Leases</h2></div></div><div class="sc-ggWZvA dCzIPc"><div class="sc-eVqvcJ sc-fszimp kIppRw kbZred redoc-markdown " html="&lt;p&gt;Lease management operations&lt;/p&gt;
"><p>Lease management operations</p>
</div></div></div><div id="tag/Leases/paths/~1leases/post" data-section-id="tag/Leases/paths/~1leases/post" class="sc-dTvVRJ gHrCVQ"><div class="sc-jJLAfE gkiSyE"><div class="sc-ggWZvA fqkwbU"><h2 class="sc-kNOymR iFSqkw"><a class="sc-kcLKEh fRdsOi" href="#tag/Leases/paths/~1leases/post" aria-label="tag/Leases/paths/~1leases/post"></a>Create Lease<!-- --> </h2><div class="sc-bfjeOH txIPi"><div html="&lt;p&gt;Create a new lease agreement&lt;/p&gt;
" class="sc-eVqvcJ sc-fszimp kIppRw kbZred"><p>Create a new lease agreement</p>
</div></div><h5 class="sc-eqYatC czjApA">Request Body schema: <span class="sc-dNFkOE cFlAeY">application/json</span></h5><div html="" class="sc-eVqvcJ sc-fszimp kIppRw kbZred"></div><table class="sc-eqNDNG icJLQx"><tbody><tr class=""><td kind="field" title="propertyId" class="sc-kCuUfV sc-fbQrwq gdmNWp dFOJWJ"><span class="sc-hwddKA cteAyA"></span><span class="property-name">propertyId</span><div class="sc-bEjUoa sc-iIvHqT lhyyLL crXmiY">required</div></td><td class="sc-gGKoUb ixGaBD"><div><div><span class="sc-bEjUoa sc-boKDdR lhyyLL jYezsP"></span><span class="sc-bEjUoa sc-fOOuSg lhyyLL dbKJYq">string</span><span class="sc-bEjUoa sc-hdBJTi lhyyLL nwQTz"> (<!-- -->NanoID<!-- -->) </span><span class="sc-bEjUoa sc-cpclqO lhyyLL UZcrz">^[0-9A-Za-z]{11}$</span></div> <div><div html="&lt;p&gt;NanoID - 11 character alphanumeric identifier&lt;/p&gt;
" class="sc-eVqvcJ sc-fszimp kIppRw drqpJr"><p>NanoID - 11 character alphanumeric identifier</p>
</div></div></div></td></tr><tr class=""><td kind="field" title="roomId" class="sc-kCuUfV sc-fbQrwq gdmNWp dFOJWJ"><span class="sc-hwddKA cteAyA"></span><span class="property-name">roomId</span><div class="sc-bEjUoa sc-iIvHqT lhyyLL crXmiY">required</div></td><td class="sc-gGKoUb ixGaBD"><div><div><span class="sc-bEjUoa sc-boKDdR lhyyLL jYezsP"></span><span class="sc-bEjUoa sc-fOOuSg lhyyLL dbKJYq">string</span><span class="sc-bEjUoa sc-hdBJTi lhyyLL nwQTz"> (<!-- -->NanoID<!-- -->) </span><span class="sc-bEjUoa sc-cpclqO lhyyLL UZcrz">^[0-9A-Za-z]{11}$</span></div> <div><div html="&lt;p&gt;NanoID - 11 character alphanumeric identifier&lt;/p&gt;
" class="sc-eVqvcJ sc-fszimp kIppRw drqpJr"><p>NanoID - 11 character alphanumeric identifier</p>
</div></div></div></td></tr><tr class=""><td kind="field" title="rentAmount" class="sc-kCuUfV sc-fbQrwq gdmNWp dFOJWJ"><span class="sc-hwddKA cteAyA"></span><span class="property-name">rentAmount</span><div class="sc-bEjUoa sc-iIvHqT lhyyLL crXmiY">required</div></td><td class="sc-gGKoUb ixGaBD"><div><div><span class="sc-bEjUoa sc-boKDdR lhyyLL jYezsP"></span><span class="sc-bEjUoa sc-fOOuSg lhyyLL dbKJYq">number</span></div> <div><div html="" class="sc-eVqvcJ sc-fszimp kIppRw drqpJr"></div></div></div></td></tr><tr class=""><td kind="field" title="startDate" class="sc-kCuUfV sc-fbQrwq gdmNWp dFOJWJ"><span class="sc-hwddKA cteAyA"></span><span class="property-name">startDate</span><div class="sc-bEjUoa sc-iIvHqT lhyyLL crXmiY">required</div></td><td class="sc-gGKoUb ixGaBD"><div><div><span class="sc-bEjUoa sc-boKDdR lhyyLL jYezsP"></span><span class="sc-bEjUoa sc-fOOuSg lhyyLL dbKJYq">string</span><span class="sc-bEjUoa sc-fOOuSg lhyyLL dbKJYq"> <!-- -->&lt;<!-- -->date<!-- -->&gt;<!-- --> </span></div> <div><div html="" class="sc-eVqvcJ sc-fszimp kIppRw drqpJr"></div></div></div></td></tr><tr class=""><td kind="field" title="endDate" class="sc-kCuUfV sc-fbQrwq gdmNWp dFOJWJ"><span class="sc-hwddKA cteAyA"></span><span class="property-name">endDate</span><div class="sc-bEjUoa sc-iIvHqT lhyyLL crXmiY">required</div></td><td class="sc-gGKoUb ixGaBD"><div><div><span class="sc-bEjUoa sc-boKDdR lhyyLL jYezsP"></span><span class="sc-bEjUoa sc-fOOuSg lhyyLL dbKJYq">string</span><span class="sc-bEjUoa sc-fOOuSg lhyyLL dbKJYq"> <!-- -->&lt;<!-- -->date<!-- -->&gt;<!-- --> </span></div> <div><div html="" class="sc-eVqvcJ sc-fszimp kIppRw drqpJr"></div></div></div></td></tr><tr class=""><td kind="field" title="status" class="sc-kCuUfV sc-fbQrwq gdmNWp dFOJWJ"><span class="sc-hwddKA cteAyA"></span><span class="property-name">status</span></td><td class="sc-gGKoUb ixGaBD"><div><div><span class="sc-bEjUoa sc-boKDdR lhyyLL jYezsP"></span><span class="sc-bEjUoa sc-fOOuSg lhyyLL dbKJYq">string</span></div><div><span class="sc-bEjUoa lhyyLL"> <!-- -->Enum<!-- -->:</span> <span class="sc-bEjUoa sc-dTWiOz lhyyLL kMQdIk">&quot;active&quot;</span> <span class="sc-bEjUoa sc-dTWiOz lhyyLL kMQdIk">&quot;ended&quot;</span> <span class="sc-bEjUoa sc-dTWiOz lhyyLL kMQdIk">&quot;terminated&quot;</span> <span class="sc-bEjUoa sc-dTWiOz lhyyLL kMQdIk">&quot;pending&quot;</span> </div> <div><div html="&lt;p&gt;状态&lt;/p&gt;
" class="sc-eVqvcJ sc-fszimp kIppRw drqpJr"><p>状态</p>
</div></div></div></td></tr><tr class=""><td kind="field" title="additionalMonthlyFees" class="sc-kCuUfV sc-fbQrwq gdmNWp dFOJWJ"><span class="sc-hwddKA cteAyA"></span><span class="property-name">additionalMonthlyFees</span></td><td class="sc-gGKoUb ixGaBD"><div><div><span class="sc-bEjUoa sc-boKDdR lhyyLL jYezsP"></span><span class="sc-bEjUoa sc-fOOuSg lhyyLL dbKJYq">number</span></div> <div><div html="&lt;p&gt;Extra monthly fees&lt;/p&gt;
" class="sc-eVqvcJ sc-fszimp kIppRw drqpJr"><p>Extra monthly fees</p>
</div></div></div></td></tr><tr class=""><td kind="field" title="keyDeposit" class="sc-kCuUfV sc-fbQrwq gdmNWp dFOJWJ"><span class="sc-hwddKA cteAyA"></span><span class="property-name">keyDeposit</span></td><td class="sc-gGKoUb ixGaBD"><div><div><span class="sc-bEjUoa sc-boKDdR lhyyLL jYezsP"></span><span class="sc-bEjUoa sc-fOOuSg lhyyLL dbKJYq">number</span></div> <div><div html="" class="sc-eVqvcJ sc-fszimp kIppRw drqpJr"></div></div></div></td></tr><tr class=""><td kind="field" title="rentDeposit" class="sc-kCuUfV sc-fbQrwq gdmNWp dFOJWJ"><span class="sc-hwddKA cteAyA"></span><span class="property-name">rentDeposit</span></td><td class="sc-gGKoUb ixGaBD"><div><div><span class="sc-bEjUoa sc-boKDdR lhyyLL jYezsP"></span><span class="sc-bEjUoa sc-fOOuSg lhyyLL dbKJYq">number</span></div> <div><div html="" class="sc-eVqvcJ sc-fszimp kIppRw drqpJr"></div></div></div></td></tr><tr class=""><td kind="field" title="otherDeposits" class="sc-kCuUfV sc-fbQrwq gdmNWp dFOJWJ"><span class="sc-hwddKA cteAyA"></span><span class="property-name">otherDeposits</span></td><td class="sc-gGKoUb ixGaBD"><div><div><span class="sc-bEjUoa sc-boKDdR lhyyLL jYezsP"></span><span class="sc-bEjUoa sc-fOOuSg lhyyLL dbKJYq">number</span></div> <div><div html="" class="sc-eVqvcJ sc-fszimp kIppRw drqpJr"></div></div></div></td></tr><tr class=""><td kind="field" title="rentDueDay" class="sc-kCuUfV sc-fbQrwq gdmNWp dFOJWJ"><span class="sc-hwddKA cteAyA"></span><span class="property-name">rentDueDay</span></td><td class="sc-gGKoUb ixGaBD"><div><div><span class="sc-bEjUoa sc-boKDdR lhyyLL jYezsP"></span><span class="sc-bEjUoa sc-fOOuSg lhyyLL dbKJYq">integer</span><span> <span class="sc-bEjUoa sc-goiVcJ lhyyLL bDfgbe"> <!-- -->[ 1 .. 31 ]<!-- --> </span></span></div> <div><div html="" class="sc-eVqvcJ sc-fszimp kIppRw drqpJr"></div></div></div></td></tr><tr class=""><td kind="field" title="rentReporting" class="sc-kCuUfV sc-fbQrwq gdmNWp dFOJWJ"><span class="sc-hwddKA cteAyA"></span><span class="property-name">rentReporting</span></td><td class="sc-gGKoUb ixGaBD"><div><div><span class="sc-bEjUoa sc-boKDdR lhyyLL jYezsP"></span><span class="sc-bEjUoa sc-fOOuSg lhyyLL dbKJYq">boolean</span></div> <div><div html="&lt;p&gt;Enable rent reporting&lt;/p&gt;
" class="sc-eVqvcJ sc-fszimp kIppRw drqpJr"><p>Enable rent reporting</p>
</div></div></div></td></tr><tr class=""><td kind="field" title="autoPay" class="sc-kCuUfV sc-fbQrwq gdmNWp dFOJWJ"><span class="sc-hwddKA cteAyA"></span><span class="property-name">autoPay</span></td><td class="sc-gGKoUb ixGaBD"><div><div><span class="sc-bEjUoa sc-boKDdR lhyyLL jYezsP"></span><span class="sc-bEjUoa sc-fOOuSg lhyyLL dbKJYq">boolean</span></div> <div><div html="&lt;p&gt;Enable automatic payments&lt;/p&gt;
" class="sc-eVqvcJ sc-fszimp kIppRw drqpJr"><p>Enable automatic payments</p>
</div></div></div></td></tr><tr class=""><td kind="field" title="owingBalance" class="sc-kCuUfV sc-fbQrwq gdmNWp dFOJWJ"><span class="sc-hwddKA cteAyA"></span><span class="property-name">owingBalance</span></td><td class="sc-gGKoUb ixGaBD"><div><div><span class="sc-bEjUoa sc-boKDdR lhyyLL jYezsP"></span><span class="sc-bEjUoa sc-fOOuSg lhyyLL dbKJYq">number</span></div> <div><div html="" class="sc-eVqvcJ sc-fszimp kIppRw drqpJr"></div></div></div></td></tr><tr class=""><td kind="field" title="lastPaymentDate" class="sc-kCuUfV sc-fbQrwq gdmNWp dFOJWJ"><span class="sc-hwddKA cteAyA"></span><span class="property-name">lastPaymentDate</span></td><td class="sc-gGKoUb ixGaBD"><div><div><span class="sc-bEjUoa sc-boKDdR lhyyLL jYezsP"></span><span class="sc-bEjUoa sc-fOOuSg lhyyLL dbKJYq">string</span><span class="sc-bEjUoa sc-fOOuSg lhyyLL dbKJYq"> <!-- -->&lt;<!-- -->date<!-- -->&gt;<!-- --> </span></div> <div><div html="" class="sc-eVqvcJ sc-fszimp kIppRw drqpJr"></div></div></div></td></tr><tr class="last "><td kind="field" title="tenantId" class="sc-kCuUfV sc-fbQrwq gdmNWp dFOJWJ"><span class="sc-hwddKA cteAyA"></span><span class="property-name">tenantId</span></td><td class="sc-gGKoUb ixGaBD"><div><div><span class="sc-bEjUoa sc-boKDdR lhyyLL jYezsP"></span><span class="sc-bEjUoa sc-fOOuSg lhyyLL dbKJYq">string</span><span class="sc-bEjUoa sc-hdBJTi lhyyLL nwQTz"> (<!-- -->NanoID<!-- -->) </span><span class="sc-bEjUoa sc-cpclqO lhyyLL UZcrz">^[0-9A-Za-z]{11}$</span></div> <div><div html="&lt;p&gt;NanoID - 11 character alphanumeric identifier&lt;/p&gt;
" class="sc-eVqvcJ sc-fszimp kIppRw drqpJr"><p>NanoID - 11 character alphanumeric identifier</p>
</div></div></div></td></tr></tbody></table><div><h3 class="sc-gDzyrw kjrVcG">Responses</h3><div><button class="sc-jIDBmd lkmdtA"><svg class="sc-dntSTA cGxVlA" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg><strong class="sc-eJvlPh fBhAXU">201<!-- --> </strong><div html="&lt;p&gt;Lease created successfully&lt;/p&gt;
" class="sc-eVqvcJ sc-fszimp sc-etsjJW kIppRw jnwENr ljKHqG"><p>Lease created successfully</p>
</div></button></div></div></div><div class="sc-jwTyAe sc-hjsuWn bDYKKx FFPsr"><div class="sc-eZSpzM jjnszm"><button class="sc-buTqWO iPCVMX"><span type="post" class="sc-fQLpxn kwcmyC http-verb post">post</span><span class="sc-jvKoal kZcHWP">/leases</span><svg class="sc-dntSTA iuNpUs" style="margin-right:-25px" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg></button><div aria-hidden="true" class="sc-ecJghI ga-DQLq"><div class="sc-iyBeIh icOxsG"><div html="&lt;p&gt;Development server&lt;/p&gt;
" class="sc-eVqvcJ sc-fszimp kIppRw drqpJr"><p>Development server</p>
</div><div tabindex="0" role="button"><div class="sc-xKhEK okJpy"><span>http://localhost:8089</span>/leases</div></div></div><div class="sc-iyBeIh icOxsG"><div html="&lt;p&gt;Production server&lt;/p&gt;
" class="sc-eVqvcJ sc-fszimp kIppRw drqpJr"><p>Production server</p>
</div><div tabindex="0" role="button"><div class="sc-xKhEK okJpy"><span>https://api.rentreport.com</span>/leases</div></div></div></div></div><div><h3 class="sc-lgpSej drJHMo"> <!-- -->Request samples<!-- --> </h3><div class="sc-cOpnSz fyxuKi" data-rttabs="true"><ul class="react-tabs__tab-list" role="tablist"><li class="react-tabs__tab react-tabs__tab--selected" role="tab" id="tab«R2a9hq»0" aria-selected="true" aria-disabled="false" aria-controls="panel«R2a9hq»0" tabindex="0" data-rttab="true">Payload</li></ul><div class="react-tabs__tab-panel react-tabs__tab-panel--selected" role="tabpanel" id="panel«R2a9hq»0" aria-labelledby="tab«R2a9hq»0"><div><div class="sc-bSFBcf iLdyBp"><span class="sc-gahYZc cXitJ">Content type</span><div class="sc-bAehkN iNRAJK">application/json</div></div><div class="sc-blIAwI eKKwxo"><div class="sc-dClGHI fdRrNy"><div class="sc-bbbBoY bBWkcI"><button><div class="sc-fYmhhH iNCOCX">Copy</div></button></div><div tabindex="0" class="sc-eVqvcJ kIppRw sc-fhfEft dFvLDb"><div class="redoc-json"><code><span class="token string">&quot;sZq0gcKRNrOK3hcDugzqJ&quot;</span></code></div></div></div></div></div></div></div></div><div><h3 class="sc-lgpSej drJHMo"> <!-- -->Response samples<!-- --> </h3><div class="sc-cOpnSz fyxuKi" data-rttabs="true"><ul class="react-tabs__tab-list" role="tablist"><li class="tab-success react-tabs__tab--selected" role="tab" id="tab«R2e9hq»0" aria-selected="true" aria-disabled="false" aria-controls="panel«R2e9hq»0" tabindex="0" data-rttab="true">201</li></ul><div class="react-tabs__tab-panel react-tabs__tab-panel--selected" role="tabpanel" id="panel«R2e9hq»0" aria-labelledby="tab«R2e9hq»0"><div><div class="sc-bSFBcf iLdyBp"><span class="sc-gahYZc cXitJ">Content type</span><div class="sc-bAehkN iNRAJK">application/json</div></div><div class="sc-blIAwI eKKwxo"><div class="sc-dClGHI fdRrNy"><div class="sc-bbbBoY bBWkcI"><button><div class="sc-fYmhhH iNCOCX">Copy</div></button></div><div tabindex="0" class="sc-eVqvcJ kIppRw sc-fhfEft dFvLDb"><div class="redoc-json"><code><button class="collapser" aria-label="collapse"></button><span class="token punctuation">{</span><span class="ellipsis"></span><ul class="obj collapsible"><li><div class="hoverable "><span class="property token string">"id"</span>: <span class="token string">&quot;sZq0gcKRNrOK3hcDugzqJ&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"propertyId"</span>: <span class="token string">&quot;sZq0gcKRNrOK3hcDugzqJ&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"roomId"</span>: <span class="token string">&quot;sZq0gcKRNrOK3hcDugzqJ&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"rentAmount"</span>: <span class="token number">0</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"startDate"</span>: <span class="token string">&quot;2019-08-24&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"endDate"</span>: <span class="token string">&quot;2019-08-24&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"status"</span>: <span class="token string">&quot;active&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"additionalMonthlyFees"</span>: <span class="token number">0</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"keyDeposit"</span>: <span class="token number">0</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"rentDeposit"</span>: <span class="token number">0</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"otherDeposits"</span>: <span class="token number">0</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"rentDueDay"</span>: <span class="token number">1</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"rentReporting"</span>: <span class="token boolean">true</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"autoPay"</span>: <span class="token boolean">true</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"owingBalance"</span>: <span class="token number">0</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"lastPaymentDate"</span>: <span class="token string">&quot;2019-08-24&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"tenantId"</span>: <span class="token string">&quot;sZq0gcKRNrOK3hcDugzqJ&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"userId"</span>: <span class="token string">&quot;sZq0gcKRNrOK3hcDugzqJ&quot;</span></div></li></ul><span class="token punctuation">}</span></code></div></div></div></div></div></div></div></div></div></div></div><div id="tag/Leases/paths/~1leases/get" data-section-id="tag/Leases/paths/~1leases/get" class="sc-dTvVRJ gHrCVQ"><div class="sc-jJLAfE gkiSyE"><div class="sc-ggWZvA fqkwbU"><h2 class="sc-kNOymR iFSqkw"><a class="sc-kcLKEh fRdsOi" href="#tag/Leases/paths/~1leases/get" aria-label="tag/Leases/paths/~1leases/get"></a>Get Leases<!-- --> </h2><div class="sc-bfjeOH txIPi"><div html="&lt;p&gt;Get all leases for the authenticated user with optional filtering by propertyId, status, etc.&lt;/p&gt;
" class="sc-eVqvcJ sc-fszimp kIppRw kbZred"><p>Get all leases for the authenticated user with optional filtering by propertyId, status, etc.</p>
</div></div><div><h5 class="sc-eqYatC czjApA">query<!-- --> Parameters</h5><table class="sc-eqNDNG icJLQx"><tbody><tr class=""><td kind="field" title="propertyId" class="sc-kCuUfV sc-fbQrwq gdmNWp dFOJWJ"><span class="sc-hwddKA cteAyA"></span><span class="property-name">propertyId</span></td><td class="sc-gGKoUb ixGaBD"><div><div><span class="sc-bEjUoa sc-boKDdR lhyyLL jYezsP"></span><span class="sc-bEjUoa sc-fOOuSg lhyyLL dbKJYq">string</span><span class="sc-bEjUoa sc-hdBJTi lhyyLL nwQTz"> (<!-- -->NanoID<!-- -->) </span><span class="sc-bEjUoa sc-cpclqO lhyyLL UZcrz">^[0-9A-Za-z]{11}$</span></div> <div><span class="sc-bEjUoa lhyyLL"> <!-- -->Example:<!-- --> </span> <span class="sc-bEjUoa sc-dTWiOz lhyyLL kMQdIk">propertyId=sZq0gcKRNrOK3hcDugzqJ</span></div><div><div html="&lt;p&gt;Resource identifier&lt;/p&gt;
" class="sc-eVqvcJ sc-fszimp kIppRw drqpJr"><p>Resource identifier</p>
</div></div></div></td></tr><tr class=""><td kind="field" title="status" class="sc-kCuUfV sc-fbQrwq gdmNWp dFOJWJ"><span class="sc-hwddKA cteAyA"></span><span class="property-name">status</span></td><td class="sc-gGKoUb ixGaBD"><div><div><span class="sc-bEjUoa sc-boKDdR lhyyLL jYezsP"></span><span class="sc-bEjUoa sc-fOOuSg lhyyLL dbKJYq">string</span></div><div><span class="sc-bEjUoa lhyyLL"> <!-- -->Enum<!-- -->:</span> <span class="sc-bEjUoa sc-dTWiOz lhyyLL kMQdIk">&quot;active&quot;</span> <span class="sc-bEjUoa sc-dTWiOz lhyyLL kMQdIk">&quot;ended&quot;</span> <span class="sc-bEjUoa sc-dTWiOz lhyyLL kMQdIk">&quot;terminated&quot;</span> </div> <div><div html="&lt;p&gt;Resource identifier&lt;/p&gt;
" class="sc-eVqvcJ sc-fszimp kIppRw drqpJr"><p>Resource identifier</p>
</div></div></div></td></tr><tr class=""><td kind="field" title="limit" class="sc-kCuUfV sc-fbQrwq gdmNWp dFOJWJ"><span class="sc-hwddKA cteAyA"></span><span class="property-name">limit</span></td><td class="sc-gGKoUb ixGaBD"><div><div><span class="sc-bEjUoa sc-boKDdR lhyyLL jYezsP"></span><span class="sc-bEjUoa sc-fOOuSg lhyyLL dbKJYq">integer</span></div><div><span class="sc-bEjUoa lhyyLL"> <!-- -->Default:<!-- --> </span> <span class="sc-bEjUoa sc-dTWiOz lhyyLL kMQdIk">10</span></div> <div><div html="&lt;p&gt;Resource identifier&lt;/p&gt;
" class="sc-eVqvcJ sc-fszimp kIppRw drqpJr"><p>Resource identifier</p>
</div></div></div></td></tr><tr class="last "><td kind="field" title="name" class="sc-kCuUfV sc-fbQrwq gdmNWp dFOJWJ"><span class="sc-hwddKA cteAyA"></span><span class="property-name">name</span></td><td class="sc-gGKoUb ixGaBD"><div><div><span class="sc-bEjUoa sc-boKDdR lhyyLL jYezsP"></span><span class="sc-bEjUoa sc-fOOuSg lhyyLL dbKJYq">string</span></div> <div><div html="&lt;p&gt;Resource identifier&lt;/p&gt;
" class="sc-eVqvcJ sc-fszimp kIppRw drqpJr"><p>Resource identifier</p>
</div></div></div></td></tr></tbody></table></div><div><h3 class="sc-gDzyrw kjrVcG">Responses</h3><div><button class="sc-jIDBmd lkmdtA"><svg class="sc-dntSTA cGxVlA" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg><strong class="sc-eJvlPh fBhAXU">200<!-- --> </strong><div html="&lt;p&gt;List of leases for the user&lt;/p&gt;
" class="sc-eVqvcJ sc-fszimp sc-etsjJW kIppRw jnwENr ljKHqG"><p>List of leases for the user</p>
</div></button></div></div></div><div class="sc-jwTyAe sc-hjsuWn bDYKKx FFPsr"><div class="sc-eZSpzM jjnszm"><button class="sc-buTqWO iPCVMX"><span type="get" class="sc-fQLpxn dynMBc http-verb get">get</span><span class="sc-jvKoal kZcHWP">/leases</span><svg class="sc-dntSTA iuNpUs" style="margin-right:-25px" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg></button><div aria-hidden="true" class="sc-ecJghI ga-DQLq"><div class="sc-iyBeIh icOxsG"><div html="&lt;p&gt;Development server&lt;/p&gt;
" class="sc-eVqvcJ sc-fszimp kIppRw drqpJr"><p>Development server</p>
</div><div tabindex="0" role="button"><div class="sc-xKhEK okJpy"><span>http://localhost:8089</span>/leases</div></div></div><div class="sc-iyBeIh icOxsG"><div html="&lt;p&gt;Production server&lt;/p&gt;
" class="sc-eVqvcJ sc-fszimp kIppRw drqpJr"><p>Production server</p>
</div><div tabindex="0" role="button"><div class="sc-xKhEK okJpy"><span>https://api.rentreport.com</span>/leases</div></div></div></div></div><div><h3 class="sc-lgpSej drJHMo"> <!-- -->Response samples<!-- --> </h3><div class="sc-cOpnSz fyxuKi" data-rttabs="true"><ul class="react-tabs__tab-list" role="tablist"><li class="tab-success react-tabs__tab--selected" role="tab" id="tab«R2eahq»0" aria-selected="true" aria-disabled="false" aria-controls="panel«R2eahq»0" tabindex="0" data-rttab="true">200</li></ul><div class="react-tabs__tab-panel react-tabs__tab-panel--selected" role="tabpanel" id="panel«R2eahq»0" aria-labelledby="tab«R2eahq»0"><div><div class="sc-bSFBcf iLdyBp"><span class="sc-gahYZc cXitJ">Content type</span><div class="sc-bAehkN iNRAJK">application/json</div></div><div class="sc-blIAwI eKKwxo"><div class="sc-dClGHI fdRrNy"><div class="sc-bbbBoY bBWkcI"><button><div class="sc-fYmhhH iNCOCX">Copy</div></button><button> Expand all </button><button> Collapse all </button></div><div tabindex="0" class="sc-eVqvcJ kIppRw sc-fhfEft dFvLDb"><div class="redoc-json"><code><button class="collapser" aria-label="collapse"></button><span class="token punctuation">{</span><span class="ellipsis"></span><ul class="obj collapsible"><li><div class="hoverable "><span class="property token string">"items"</span>: <button class="collapser" aria-label="collapse"></button><span class="token punctuation">[</span><span class="ellipsis"></span><ul class="array collapsible"><li><div class="hoverable collapsed"><button class="collapser" aria-label="expand"></button><span class="token punctuation">{</span><span class="ellipsis"></span><ul class="obj collapsible"><li><div class="hoverable collapsed"><span class="property token string">"id"</span>: <span class="token string">&quot;Oiu5_hL3PBuc_vLmZOFia&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"propertyId"</span>: <span class="token string">&quot;78&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"status"</span>: <span class="token string">&quot;active&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"owingBalance"</span>: <span class="token number">9</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"lastPaymentDate"</span>: <span class="token string">&quot;2024-04-27&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"tenantId"</span>: <span class="token string">&quot;79&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable collapsed"><span class="property token string">"userId"</span>: <span class="token string">&quot;65&quot;</span></div></li></ul><span class="token punctuation">}</span></div></li></ul><span class="token punctuation">]</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"total"</span>: <span class="token number">47</span></div></li></ul><span class="token punctuation">}</span></code></div></div></div></div></div></div></div></div></div></div></div><div id="tag/Leases/paths/~1leases~1{leaseId}/get" data-section-id="tag/Leases/paths/~1leases~1{leaseId}/get" class="sc-dTvVRJ gHrCVQ"><div class="sc-jJLAfE gkiSyE"><div class="sc-ggWZvA fqkwbU"><h2 class="sc-kNOymR iFSqkw"><a class="sc-kcLKEh fRdsOi" href="#tag/Leases/paths/~1leases~1{leaseId}/get" aria-label="tag/Leases/paths/~1leases~1{leaseId}/get"></a>/leases/{leaseId}<!-- --> </h2><div class="sc-bfjeOH txIPi"><div html="&lt;p&gt;Get detailed information for a specific lease&lt;/p&gt;
" class="sc-eVqvcJ sc-fszimp kIppRw kbZred"><p>Get detailed information for a specific lease</p>
</div></div><div><h5 class="sc-eqYatC czjApA">path<!-- --> Parameters</h5><table class="sc-eqNDNG icJLQx"><tbody><tr class="last "><td kind="field" title="leaseId" class="sc-kCuUfV sc-fbQrwq gdmNWp dFOJWJ"><span class="sc-hwddKA cteAyA"></span><span class="property-name">leaseId</span><div class="sc-bEjUoa sc-iIvHqT lhyyLL crXmiY">required</div></td><td class="sc-gGKoUb ixGaBD"><div><div><span class="sc-bEjUoa sc-boKDdR lhyyLL jYezsP"></span><span class="sc-bEjUoa sc-fOOuSg lhyyLL dbKJYq">string</span><span class="sc-bEjUoa sc-hdBJTi lhyyLL nwQTz"> (<!-- -->NanoID<!-- -->) </span><span class="sc-bEjUoa sc-cpclqO lhyyLL UZcrz">^[0-9A-Za-z]{11}$</span></div> <div><span class="sc-bEjUoa lhyyLL"> <!-- -->Example:<!-- --> </span> <span class="sc-bEjUoa sc-dTWiOz lhyyLL kMQdIk">sZq0gcKRNrOK3hcDugzqJ</span></div><div><div html="&lt;p&gt;Resource identifier&lt;/p&gt;
" class="sc-eVqvcJ sc-fszimp kIppRw drqpJr"><p>Resource identifier</p>
</div></div></div></td></tr></tbody></table></div><div><h3 class="sc-gDzyrw kjrVcG">Responses</h3><div><button class="sc-jIDBmd lkmdtA"><svg class="sc-dntSTA cGxVlA" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg><strong class="sc-eJvlPh fBhAXU">200<!-- --> </strong><div html="&lt;p&gt;Lease details retrieved successfully&lt;/p&gt;
" class="sc-eVqvcJ sc-fszimp sc-etsjJW kIppRw jnwENr ljKHqG"><p>Lease details retrieved successfully</p>
</div></button></div></div></div><div class="sc-jwTyAe sc-hjsuWn bDYKKx FFPsr"><div class="sc-eZSpzM jjnszm"><button class="sc-buTqWO iPCVMX"><span type="get" class="sc-fQLpxn dynMBc http-verb get">get</span><span class="sc-jvKoal kZcHWP">/leases/{leaseId}</span><svg class="sc-dntSTA iuNpUs" style="margin-right:-25px" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg></button><div aria-hidden="true" class="sc-ecJghI ga-DQLq"><div class="sc-iyBeIh icOxsG"><div html="&lt;p&gt;Development server&lt;/p&gt;
" class="sc-eVqvcJ sc-fszimp kIppRw drqpJr"><p>Development server</p>
</div><div tabindex="0" role="button"><div class="sc-xKhEK okJpy"><span>http://localhost:8089</span>/leases/{leaseId}</div></div></div><div class="sc-iyBeIh icOxsG"><div html="&lt;p&gt;Production server&lt;/p&gt;
" class="sc-eVqvcJ sc-fszimp kIppRw drqpJr"><p>Production server</p>
</div><div tabindex="0" role="button"><div class="sc-xKhEK okJpy"><span>https://api.rentreport.com</span>/leases/{leaseId}</div></div></div></div></div><div><h3 class="sc-lgpSej drJHMo"> <!-- -->Response samples<!-- --> </h3><div class="sc-cOpnSz fyxuKi" data-rttabs="true"><ul class="react-tabs__tab-list" role="tablist"><li class="tab-success react-tabs__tab--selected" role="tab" id="tab«R2ebhq»0" aria-selected="true" aria-disabled="false" aria-controls="panel«R2ebhq»0" tabindex="0" data-rttab="true">200</li></ul><div class="react-tabs__tab-panel react-tabs__tab-panel--selected" role="tabpanel" id="panel«R2ebhq»0" aria-labelledby="tab«R2ebhq»0"><div><div class="sc-bSFBcf iLdyBp"><span class="sc-gahYZc cXitJ">Content type</span><div class="sc-bAehkN iNRAJK">application/json</div></div><div class="sc-blIAwI eKKwxo"><div class="sc-dClGHI fdRrNy"><div class="sc-bbbBoY bBWkcI"><button><div class="sc-fYmhhH iNCOCX">Copy</div></button></div><div tabindex="0" class="sc-eVqvcJ kIppRw sc-fhfEft dFvLDb"><div class="redoc-json"><code><button class="collapser" aria-label="collapse"></button><span class="token punctuation">{</span><span class="ellipsis"></span><ul class="obj collapsible"><li><div class="hoverable "><span class="property token string">"id"</span>: <span class="token string">&quot;sZq0gcKRNrOK3hcDugzqJ&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"propertyId"</span>: <span class="token string">&quot;sZq0gcKRNrOK3hcDugzqJ&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"roomId"</span>: <span class="token string">&quot;sZq0gcKRNrOK3hcDugzqJ&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"rentAmount"</span>: <span class="token number">0</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"startDate"</span>: <span class="token string">&quot;2019-08-24&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"endDate"</span>: <span class="token string">&quot;2019-08-24&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"status"</span>: <span class="token string">&quot;active&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"additionalMonthlyFees"</span>: <span class="token number">0</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"keyDeposit"</span>: <span class="token number">0</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"rentDeposit"</span>: <span class="token number">0</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"otherDeposits"</span>: <span class="token number">0</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"rentDueDay"</span>: <span class="token number">1</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"rentReporting"</span>: <span class="token boolean">true</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"autoPay"</span>: <span class="token boolean">true</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"owingBalance"</span>: <span class="token number">0</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"lastPaymentDate"</span>: <span class="token string">&quot;2019-08-24&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"tenantId"</span>: <span class="token string">&quot;sZq0gcKRNrOK3hcDugzqJ&quot;</span></div></li></ul><span class="token punctuation">}</span></code></div></div></div></div></div></div></div></div></div></div></div><div id="tag/Leases/paths/~1leases~1{leaseId}/put" data-section-id="tag/Leases/paths/~1leases~1{leaseId}/put" class="sc-dTvVRJ gHrCVQ"><div class="sc-jJLAfE gkiSyE"><div class="sc-ggWZvA fqkwbU"><h2 class="sc-kNOymR iFSqkw"><a class="sc-kcLKEh fRdsOi" href="#tag/Leases/paths/~1leases~1{leaseId}/put" aria-label="tag/Leases/paths/~1leases~1{leaseId}/put"></a>/leases/{leaseId}<!-- --> </h2><div class="sc-bfjeOH txIPi"><div html="&lt;p&gt;更新指定lease合同的信息&lt;/p&gt;
" class="sc-eVqvcJ sc-fszimp kIppRw kbZred"><p>更新指定lease合同的信息</p>
</div></div><div><h5 class="sc-eqYatC czjApA">path<!-- --> Parameters</h5><table class="sc-eqNDNG icJLQx"><tbody><tr class="last "><td kind="field" title="leaseId" class="sc-kCuUfV sc-fbQrwq gdmNWp dFOJWJ"><span class="sc-hwddKA cteAyA"></span><span class="property-name">leaseId</span><div class="sc-bEjUoa sc-iIvHqT lhyyLL crXmiY">required</div></td><td class="sc-gGKoUb ixGaBD"><div><div><span class="sc-bEjUoa sc-boKDdR lhyyLL jYezsP"></span><span class="sc-bEjUoa sc-fOOuSg lhyyLL dbKJYq">string</span><span class="sc-bEjUoa sc-hdBJTi lhyyLL nwQTz"> (<!-- -->NanoID<!-- -->) </span><span class="sc-bEjUoa sc-cpclqO lhyyLL UZcrz">^[0-9A-Za-z]{11}$</span></div> <div><span class="sc-bEjUoa lhyyLL"> <!-- -->Example:<!-- --> </span> <span class="sc-bEjUoa sc-dTWiOz lhyyLL kMQdIk">sZq0gcKRNrOK3hcDugzqJ</span></div><div><div html="&lt;p&gt;Resource identifier&lt;/p&gt;
" class="sc-eVqvcJ sc-fszimp kIppRw drqpJr"><p>Resource identifier</p>
</div></div></div></td></tr></tbody></table></div><h5 class="sc-eqYatC czjApA">Request Body schema: <span class="sc-dNFkOE cFlAeY">application/json</span></h5><div html="" class="sc-eVqvcJ sc-fszimp kIppRw kbZred"></div><table class="sc-eqNDNG icJLQx"><tbody><tr class=""><td kind="field" title="id" class="sc-kCuUfV sc-fbQrwq gdmNWp dFOJWJ"><span class="sc-hwddKA cteAyA"></span><span class="property-name">id</span><div class="sc-bEjUoa sc-iIvHqT lhyyLL crXmiY">required</div></td><td class="sc-gGKoUb ixGaBD"><div><div><span class="sc-bEjUoa sc-boKDdR lhyyLL jYezsP"></span><span class="sc-bEjUoa sc-fOOuSg lhyyLL dbKJYq">string</span><span class="sc-bEjUoa sc-hdBJTi lhyyLL nwQTz"> (<!-- -->NanoID<!-- -->) </span><span class="sc-bEjUoa sc-cpclqO lhyyLL UZcrz">^[0-9A-Za-z]{11}$</span></div> <div><div html="&lt;p&gt;NanoID - 11 character alphanumeric identifier&lt;/p&gt;
" class="sc-eVqvcJ sc-fszimp kIppRw drqpJr"><p>NanoID - 11 character alphanumeric identifier</p>
</div></div></div></td></tr><tr class=""><td kind="field" title="propertyId" class="sc-kCuUfV sc-fbQrwq gdmNWp dFOJWJ"><span class="sc-hwddKA cteAyA"></span><span class="property-name">propertyId</span><div class="sc-bEjUoa sc-iIvHqT lhyyLL crXmiY">required</div></td><td class="sc-gGKoUb ixGaBD"><div><div><span class="sc-bEjUoa sc-boKDdR lhyyLL jYezsP"></span><span class="sc-bEjUoa sc-fOOuSg lhyyLL dbKJYq">string</span><span class="sc-bEjUoa sc-hdBJTi lhyyLL nwQTz"> (<!-- -->NanoID<!-- -->) </span><span class="sc-bEjUoa sc-cpclqO lhyyLL UZcrz">^[0-9A-Za-z]{11}$</span></div> <div><div html="&lt;p&gt;NanoID - 11 character alphanumeric identifier&lt;/p&gt;
" class="sc-eVqvcJ sc-fszimp kIppRw drqpJr"><p>NanoID - 11 character alphanumeric identifier</p>
</div></div></div></td></tr><tr class=""><td kind="field" title="roomId" class="sc-kCuUfV sc-fbQrwq gdmNWp dFOJWJ"><span class="sc-hwddKA cteAyA"></span><span class="property-name">roomId</span><div class="sc-bEjUoa sc-iIvHqT lhyyLL crXmiY">required</div></td><td class="sc-gGKoUb ixGaBD"><div><div><span class="sc-bEjUoa sc-boKDdR lhyyLL jYezsP"></span><span class="sc-bEjUoa sc-fOOuSg lhyyLL dbKJYq">string</span><span class="sc-bEjUoa sc-hdBJTi lhyyLL nwQTz"> (<!-- -->NanoID<!-- -->) </span><span class="sc-bEjUoa sc-cpclqO lhyyLL UZcrz">^[0-9A-Za-z]{11}$</span></div> <div><div html="&lt;p&gt;NanoID - 11 character alphanumeric identifier&lt;/p&gt;
" class="sc-eVqvcJ sc-fszimp kIppRw drqpJr"><p>NanoID - 11 character alphanumeric identifier</p>
</div></div></div></td></tr><tr class=""><td kind="field" title="rentAmount" class="sc-kCuUfV sc-fbQrwq gdmNWp dFOJWJ"><span class="sc-hwddKA cteAyA"></span><span class="property-name">rentAmount</span><div class="sc-bEjUoa sc-iIvHqT lhyyLL crXmiY">required</div></td><td class="sc-gGKoUb ixGaBD"><div><div><span class="sc-bEjUoa sc-boKDdR lhyyLL jYezsP"></span><span class="sc-bEjUoa sc-fOOuSg lhyyLL dbKJYq">number</span></div> <div><div html="" class="sc-eVqvcJ sc-fszimp kIppRw drqpJr"></div></div></div></td></tr><tr class=""><td kind="field" title="startDate" class="sc-kCuUfV sc-fbQrwq gdmNWp dFOJWJ"><span class="sc-hwddKA cteAyA"></span><span class="property-name">startDate</span><div class="sc-bEjUoa sc-iIvHqT lhyyLL crXmiY">required</div></td><td class="sc-gGKoUb ixGaBD"><div><div><span class="sc-bEjUoa sc-boKDdR lhyyLL jYezsP"></span><span class="sc-bEjUoa sc-fOOuSg lhyyLL dbKJYq">string</span><span class="sc-bEjUoa sc-fOOuSg lhyyLL dbKJYq"> <!-- -->&lt;<!-- -->date<!-- -->&gt;<!-- --> </span></div> <div><div html="" class="sc-eVqvcJ sc-fszimp kIppRw drqpJr"></div></div></div></td></tr><tr class=""><td kind="field" title="endDate" class="sc-kCuUfV sc-fbQrwq gdmNWp dFOJWJ"><span class="sc-hwddKA cteAyA"></span><span class="property-name">endDate</span><div class="sc-bEjUoa sc-iIvHqT lhyyLL crXmiY">required</div></td><td class="sc-gGKoUb ixGaBD"><div><div><span class="sc-bEjUoa sc-boKDdR lhyyLL jYezsP"></span><span class="sc-bEjUoa sc-fOOuSg lhyyLL dbKJYq">string</span><span class="sc-bEjUoa sc-fOOuSg lhyyLL dbKJYq"> <!-- -->&lt;<!-- -->date<!-- -->&gt;<!-- --> </span></div> <div><div html="" class="sc-eVqvcJ sc-fszimp kIppRw drqpJr"></div></div></div></td></tr><tr class=""><td kind="field" title="status" class="sc-kCuUfV sc-fbQrwq gdmNWp dFOJWJ"><span class="sc-hwddKA cteAyA"></span><span class="property-name">status</span></td><td class="sc-gGKoUb ixGaBD"><div><div><span class="sc-bEjUoa sc-boKDdR lhyyLL jYezsP"></span><span class="sc-bEjUoa sc-fOOuSg lhyyLL dbKJYq">string</span></div><div><span class="sc-bEjUoa lhyyLL"> <!-- -->Enum<!-- -->:</span> <span class="sc-bEjUoa sc-dTWiOz lhyyLL kMQdIk">&quot;active&quot;</span> <span class="sc-bEjUoa sc-dTWiOz lhyyLL kMQdIk">&quot;ended&quot;</span> <span class="sc-bEjUoa sc-dTWiOz lhyyLL kMQdIk">&quot;terminated&quot;</span> <span class="sc-bEjUoa sc-dTWiOz lhyyLL kMQdIk">&quot;pending&quot;</span> </div> <div><div html="&lt;p&gt;状态&lt;/p&gt;
" class="sc-eVqvcJ sc-fszimp kIppRw drqpJr"><p>状态</p>
</div></div></div></td></tr><tr class=""><td kind="field" title="additionalMonthlyFees" class="sc-kCuUfV sc-fbQrwq gdmNWp dFOJWJ"><span class="sc-hwddKA cteAyA"></span><span class="property-name">additionalMonthlyFees</span></td><td class="sc-gGKoUb ixGaBD"><div><div><span class="sc-bEjUoa sc-boKDdR lhyyLL jYezsP"></span><span class="sc-bEjUoa sc-fOOuSg lhyyLL dbKJYq">number</span></div> <div><div html="&lt;p&gt;Extra monthly fees&lt;/p&gt;
" class="sc-eVqvcJ sc-fszimp kIppRw drqpJr"><p>Extra monthly fees</p>
</div></div></div></td></tr><tr class=""><td kind="field" title="keyDeposit" class="sc-kCuUfV sc-fbQrwq gdmNWp dFOJWJ"><span class="sc-hwddKA cteAyA"></span><span class="property-name">keyDeposit</span></td><td class="sc-gGKoUb ixGaBD"><div><div><span class="sc-bEjUoa sc-boKDdR lhyyLL jYezsP"></span><span class="sc-bEjUoa sc-fOOuSg lhyyLL dbKJYq">number</span></div> <div><div html="" class="sc-eVqvcJ sc-fszimp kIppRw drqpJr"></div></div></div></td></tr><tr class=""><td kind="field" title="rentDeposit" class="sc-kCuUfV sc-fbQrwq gdmNWp dFOJWJ"><span class="sc-hwddKA cteAyA"></span><span class="property-name">rentDeposit</span></td><td class="sc-gGKoUb ixGaBD"><div><div><span class="sc-bEjUoa sc-boKDdR lhyyLL jYezsP"></span><span class="sc-bEjUoa sc-fOOuSg lhyyLL dbKJYq">number</span></div> <div><div html="" class="sc-eVqvcJ sc-fszimp kIppRw drqpJr"></div></div></div></td></tr><tr class=""><td kind="field" title="otherDeposits" class="sc-kCuUfV sc-fbQrwq gdmNWp dFOJWJ"><span class="sc-hwddKA cteAyA"></span><span class="property-name">otherDeposits</span></td><td class="sc-gGKoUb ixGaBD"><div><div><span class="sc-bEjUoa sc-boKDdR lhyyLL jYezsP"></span><span class="sc-bEjUoa sc-fOOuSg lhyyLL dbKJYq">number</span></div> <div><div html="" class="sc-eVqvcJ sc-fszimp kIppRw drqpJr"></div></div></div></td></tr><tr class=""><td kind="field" title="rentDueDay" class="sc-kCuUfV sc-fbQrwq gdmNWp dFOJWJ"><span class="sc-hwddKA cteAyA"></span><span class="property-name">rentDueDay</span></td><td class="sc-gGKoUb ixGaBD"><div><div><span class="sc-bEjUoa sc-boKDdR lhyyLL jYezsP"></span><span class="sc-bEjUoa sc-fOOuSg lhyyLL dbKJYq">integer</span><span> <span class="sc-bEjUoa sc-goiVcJ lhyyLL bDfgbe"> <!-- -->[ 1 .. 31 ]<!-- --> </span></span></div> <div><div html="" class="sc-eVqvcJ sc-fszimp kIppRw drqpJr"></div></div></div></td></tr><tr class=""><td kind="field" title="rentReporting" class="sc-kCuUfV sc-fbQrwq gdmNWp dFOJWJ"><span class="sc-hwddKA cteAyA"></span><span class="property-name">rentReporting</span></td><td class="sc-gGKoUb ixGaBD"><div><div><span class="sc-bEjUoa sc-boKDdR lhyyLL jYezsP"></span><span class="sc-bEjUoa sc-fOOuSg lhyyLL dbKJYq">boolean</span></div> <div><div html="&lt;p&gt;Enable rent reporting&lt;/p&gt;
" class="sc-eVqvcJ sc-fszimp kIppRw drqpJr"><p>Enable rent reporting</p>
</div></div></div></td></tr><tr class=""><td kind="field" title="autoPay" class="sc-kCuUfV sc-fbQrwq gdmNWp dFOJWJ"><span class="sc-hwddKA cteAyA"></span><span class="property-name">autoPay</span></td><td class="sc-gGKoUb ixGaBD"><div><div><span class="sc-bEjUoa sc-boKDdR lhyyLL jYezsP"></span><span class="sc-bEjUoa sc-fOOuSg lhyyLL dbKJYq">boolean</span></div> <div><div html="&lt;p&gt;Enable automatic payments&lt;/p&gt;
" class="sc-eVqvcJ sc-fszimp kIppRw drqpJr"><p>Enable automatic payments</p>
</div></div></div></td></tr><tr class=""><td kind="field" title="owingBalance" class="sc-kCuUfV sc-fbQrwq gdmNWp dFOJWJ"><span class="sc-hwddKA cteAyA"></span><span class="property-name">owingBalance</span></td><td class="sc-gGKoUb ixGaBD"><div><div><span class="sc-bEjUoa sc-boKDdR lhyyLL jYezsP"></span><span class="sc-bEjUoa sc-fOOuSg lhyyLL dbKJYq">number</span></div> <div><div html="" class="sc-eVqvcJ sc-fszimp kIppRw drqpJr"></div></div></div></td></tr><tr class=""><td kind="field" title="lastPaymentDate" class="sc-kCuUfV sc-fbQrwq gdmNWp dFOJWJ"><span class="sc-hwddKA cteAyA"></span><span class="property-name">lastPaymentDate</span></td><td class="sc-gGKoUb ixGaBD"><div><div><span class="sc-bEjUoa sc-boKDdR lhyyLL jYezsP"></span><span class="sc-bEjUoa sc-fOOuSg lhyyLL dbKJYq">string</span><span class="sc-bEjUoa sc-fOOuSg lhyyLL dbKJYq"> <!-- -->&lt;<!-- -->date<!-- -->&gt;<!-- --> </span></div> <div><div html="" class="sc-eVqvcJ sc-fszimp kIppRw drqpJr"></div></div></div></td></tr><tr class="last "><td kind="field" title="tenantId" class="sc-kCuUfV sc-fbQrwq gdmNWp dFOJWJ"><span class="sc-hwddKA cteAyA"></span><span class="property-name">tenantId</span></td><td class="sc-gGKoUb ixGaBD"><div><div><span class="sc-bEjUoa sc-boKDdR lhyyLL jYezsP"></span><span class="sc-bEjUoa sc-fOOuSg lhyyLL dbKJYq">string</span><span class="sc-bEjUoa sc-hdBJTi lhyyLL nwQTz"> (<!-- -->NanoID<!-- -->) </span><span class="sc-bEjUoa sc-cpclqO lhyyLL UZcrz">^[0-9A-Za-z]{11}$</span></div> <div><div html="&lt;p&gt;NanoID - 11 character alphanumeric identifier&lt;/p&gt;
" class="sc-eVqvcJ sc-fszimp kIppRw drqpJr"><p>NanoID - 11 character alphanumeric identifier</p>
</div></div></div></td></tr></tbody></table><div><h3 class="sc-gDzyrw kjrVcG">Responses</h3><div><button class="sc-jIDBmd lkmdtA"><svg class="sc-dntSTA cGxVlA" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg><strong class="sc-eJvlPh fBhAXU">200<!-- --> </strong><div html="&lt;p&gt;Lease updated successfully&lt;/p&gt;
" class="sc-eVqvcJ sc-fszimp sc-etsjJW kIppRw jnwENr ljKHqG"><p>Lease updated successfully</p>
</div></button></div></div></div><div class="sc-jwTyAe sc-hjsuWn bDYKKx FFPsr"><div class="sc-eZSpzM jjnszm"><button class="sc-buTqWO iPCVMX"><span type="put" class="sc-fQLpxn dBzsUh http-verb put">put</span><span class="sc-jvKoal kZcHWP">/leases/{leaseId}</span><svg class="sc-dntSTA iuNpUs" style="margin-right:-25px" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg></button><div aria-hidden="true" class="sc-ecJghI ga-DQLq"><div class="sc-iyBeIh icOxsG"><div html="&lt;p&gt;Development server&lt;/p&gt;
" class="sc-eVqvcJ sc-fszimp kIppRw drqpJr"><p>Development server</p>
</div><div tabindex="0" role="button"><div class="sc-xKhEK okJpy"><span>http://localhost:8089</span>/leases/{leaseId}</div></div></div><div class="sc-iyBeIh icOxsG"><div html="&lt;p&gt;Production server&lt;/p&gt;
" class="sc-eVqvcJ sc-fszimp kIppRw drqpJr"><p>Production server</p>
</div><div tabindex="0" role="button"><div class="sc-xKhEK okJpy"><span>https://api.rentreport.com</span>/leases/{leaseId}</div></div></div></div></div><div><h3 class="sc-lgpSej drJHMo"> <!-- -->Request samples<!-- --> </h3><div class="sc-cOpnSz fyxuKi" data-rttabs="true"><ul class="react-tabs__tab-list" role="tablist"><li class="react-tabs__tab react-tabs__tab--selected" role="tab" id="tab«R2achq»0" aria-selected="true" aria-disabled="false" aria-controls="panel«R2achq»0" tabindex="0" data-rttab="true">Payload</li></ul><div class="react-tabs__tab-panel react-tabs__tab-panel--selected" role="tabpanel" id="panel«R2achq»0" aria-labelledby="tab«R2achq»0"><div><div class="sc-bSFBcf iLdyBp"><span class="sc-gahYZc cXitJ">Content type</span><div class="sc-bAehkN iNRAJK">application/json</div></div><div class="sc-blIAwI eKKwxo"><div class="sc-dClGHI fdRrNy"><div class="sc-bbbBoY bBWkcI"><button><div class="sc-fYmhhH iNCOCX">Copy</div></button></div><div tabindex="0" class="sc-eVqvcJ kIppRw sc-fhfEft dFvLDb"><div class="redoc-json"><code><span class="token string">&quot;sZq0gcKRNrOK3hcDugzqJ&quot;</span></code></div></div></div></div></div></div></div></div><div><h3 class="sc-lgpSej drJHMo"> <!-- -->Response samples<!-- --> </h3><div class="sc-cOpnSz fyxuKi" data-rttabs="true"><ul class="react-tabs__tab-list" role="tablist"><li class="tab-success react-tabs__tab--selected" role="tab" id="tab«R2echq»0" aria-selected="true" aria-disabled="false" aria-controls="panel«R2echq»0" tabindex="0" data-rttab="true">200</li></ul><div class="react-tabs__tab-panel react-tabs__tab-panel--selected" role="tabpanel" id="panel«R2echq»0" aria-labelledby="tab«R2echq»0"><div><div class="sc-bSFBcf iLdyBp"><span class="sc-gahYZc cXitJ">Content type</span><div class="sc-bAehkN iNRAJK">application/json</div></div><div class="sc-blIAwI eKKwxo"><div class="sc-dClGHI fdRrNy"><div class="sc-bbbBoY bBWkcI"><button><div class="sc-fYmhhH iNCOCX">Copy</div></button></div><div tabindex="0" class="sc-eVqvcJ kIppRw sc-fhfEft dFvLDb"><div class="redoc-json"><code><button class="collapser" aria-label="collapse"></button><span class="token punctuation">{</span><span class="ellipsis"></span><ul class="obj collapsible"><li><div class="hoverable "><span class="property token string">"id"</span>: <span class="token string">&quot;sZq0gcKRNrOK3hcDugzqJ&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"propertyId"</span>: <span class="token string">&quot;sZq0gcKRNrOK3hcDugzqJ&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"roomId"</span>: <span class="token string">&quot;sZq0gcKRNrOK3hcDugzqJ&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"rentAmount"</span>: <span class="token number">0</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"startDate"</span>: <span class="token string">&quot;2019-08-24&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"endDate"</span>: <span class="token string">&quot;2019-08-24&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"status"</span>: <span class="token string">&quot;active&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"additionalMonthlyFees"</span>: <span class="token number">0</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"keyDeposit"</span>: <span class="token number">0</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"rentDeposit"</span>: <span class="token number">0</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"otherDeposits"</span>: <span class="token number">0</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"rentDueDay"</span>: <span class="token number">1</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"rentReporting"</span>: <span class="token boolean">true</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"autoPay"</span>: <span class="token boolean">true</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"owingBalance"</span>: <span class="token number">0</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"lastPaymentDate"</span>: <span class="token string">&quot;2019-08-24&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"tenantId"</span>: <span class="token string">&quot;sZq0gcKRNrOK3hcDugzqJ&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"userId"</span>: <span class="token string">&quot;sZq0gcKRNrOK3hcDugzqJ&quot;</span></div></li></ul><span class="token punctuation">}</span></code></div></div></div></div></div></div></div></div></div></div></div><div id="tag/Leases/paths/~1leases~1{leaseId}/delete" data-section-id="tag/Leases/paths/~1leases~1{leaseId}/delete" class="sc-dTvVRJ gHrCVQ"><div class="sc-jJLAfE gkiSyE"><div class="sc-ggWZvA fqkwbU"><h2 class="sc-kNOymR iFSqkw"><a class="sc-kcLKEh fRdsOi" href="#tag/Leases/paths/~1leases~1{leaseId}/delete" aria-label="tag/Leases/paths/~1leases~1{leaseId}/delete"></a>/leases/{leaseId}<!-- --> </h2><div class="sc-bfjeOH txIPi"><div html="&lt;p&gt;删除指定的lease&lt;/p&gt;
" class="sc-eVqvcJ sc-fszimp kIppRw kbZred"><p>删除指定的lease</p>
</div></div><div><h5 class="sc-eqYatC czjApA">path<!-- --> Parameters</h5><table class="sc-eqNDNG icJLQx"><tbody><tr class="last "><td kind="field" title="leaseId" class="sc-kCuUfV sc-fbQrwq gdmNWp dFOJWJ"><span class="sc-hwddKA cteAyA"></span><span class="property-name">leaseId</span><div class="sc-bEjUoa sc-iIvHqT lhyyLL crXmiY">required</div></td><td class="sc-gGKoUb ixGaBD"><div><div><span class="sc-bEjUoa sc-boKDdR lhyyLL jYezsP"></span><span class="sc-bEjUoa sc-fOOuSg lhyyLL dbKJYq">string</span><span class="sc-bEjUoa sc-hdBJTi lhyyLL nwQTz"> (<!-- -->NanoID<!-- -->) </span><span class="sc-bEjUoa sc-cpclqO lhyyLL UZcrz">^[0-9A-Za-z]{11}$</span></div> <div><span class="sc-bEjUoa lhyyLL"> <!-- -->Example:<!-- --> </span> <span class="sc-bEjUoa sc-dTWiOz lhyyLL kMQdIk">sZq0gcKRNrOK3hcDugzqJ</span></div><div><div html="&lt;p&gt;Resource identifier&lt;/p&gt;
" class="sc-eVqvcJ sc-fszimp kIppRw drqpJr"><p>Resource identifier</p>
</div></div></div></td></tr></tbody></table></div><div><h3 class="sc-gDzyrw kjrVcG">Responses</h3><div><button class="sc-jIDBmd lkmdtA"><svg class="sc-dntSTA cGxVlA" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg><strong class="sc-eJvlPh fBhAXU">204<!-- --> </strong><div html="&lt;p&gt;Lease deleted successfully&lt;/p&gt;
" class="sc-eVqvcJ sc-fszimp sc-etsjJW kIppRw jnwENr ljKHqG"><p>Lease deleted successfully</p>
</div></button></div></div></div><div class="sc-jwTyAe sc-hjsuWn bDYKKx FFPsr"><div class="sc-eZSpzM jjnszm"><button class="sc-buTqWO iPCVMX"><span type="delete" class="sc-fQLpxn gKcHYQ http-verb delete">delete</span><span class="sc-jvKoal kZcHWP">/leases/{leaseId}</span><svg class="sc-dntSTA iuNpUs" style="margin-right:-25px" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg></button><div aria-hidden="true" class="sc-ecJghI ga-DQLq"><div class="sc-iyBeIh icOxsG"><div html="&lt;p&gt;Development server&lt;/p&gt;
" class="sc-eVqvcJ sc-fszimp kIppRw drqpJr"><p>Development server</p>
</div><div tabindex="0" role="button"><div class="sc-xKhEK okJpy"><span>http://localhost:8089</span>/leases/{leaseId}</div></div></div><div class="sc-iyBeIh icOxsG"><div html="&lt;p&gt;Production server&lt;/p&gt;
" class="sc-eVqvcJ sc-fszimp kIppRw drqpJr"><p>Production server</p>
</div><div tabindex="0" role="button"><div class="sc-xKhEK okJpy"><span>https://api.rentreport.com</span>/leases/{leaseId}</div></div></div></div></div><div><h3 class="sc-lgpSej drJHMo"> <!-- -->Response samples<!-- --> </h3><div class="sc-cOpnSz fyxuKi" data-rttabs="true"><ul class="react-tabs__tab-list" role="tablist"><li class="tab-success react-tabs__tab--selected" role="tab" id="tab«R2edhq»0" aria-selected="true" aria-disabled="false" aria-controls="panel«R2edhq»0" tabindex="0" data-rttab="true">204</li></ul><div class="react-tabs__tab-panel react-tabs__tab-panel--selected" role="tabpanel" id="panel«R2edhq»0" aria-labelledby="tab«R2edhq»0"><div><div class="sc-bSFBcf iLdyBp"><span class="sc-gahYZc cXitJ">Content type</span><div class="sc-bAehkN iNRAJK">application/json</div></div><div class="sc-blIAwI eKKwxo"><div class="sc-dClGHI fdRrNy"><div class="sc-bbbBoY bBWkcI"><button><div class="sc-fYmhhH iNCOCX">Copy</div></button></div><div tabindex="0" class="sc-eVqvcJ kIppRw sc-fhfEft dFvLDb"><div class="redoc-json"><code><span class="token punctuation">{ }</span></code></div></div></div></div></div></div></div></div></div></div></div><div id="tag/Leases/paths/~1v1~1leases~1{leaseId}~1tenants~1self/put" data-section-id="tag/Leases/paths/~1v1~1leases~1{leaseId}~1tenants~1self/put" class="sc-dTvVRJ gHrCVQ"><div class="sc-jJLAfE gkiSyE"><div class="sc-ggWZvA fqkwbU"><h2 class="sc-kNOymR iFSqkw"><a class="sc-kcLKEh fRdsOi" href="#tag/Leases/paths/~1v1~1leases~1{leaseId}~1tenants~1self/put" aria-label="tag/Leases/paths/~1v1~1leases~1{leaseId}~1tenants~1self/put"></a>/v1/leases/{leaseId}/tenants/self<!-- --> </h2><div class="sc-bfjeOH txIPi"><div html="&lt;p&gt;租客更新自己的tenantId (RESTful)&lt;/p&gt;
" class="sc-eVqvcJ sc-fszimp kIppRw kbZred"><p>租客更新自己的tenantId (RESTful)</p>
</div></div><div><h5 class="sc-eqYatC czjApA">path<!-- --> Parameters</h5><table class="sc-eqNDNG icJLQx"><tbody><tr class="last "><td kind="field" title="leaseId" class="sc-kCuUfV sc-fbQrwq gdmNWp dFOJWJ"><span class="sc-hwddKA cteAyA"></span><span class="property-name">leaseId</span><div class="sc-bEjUoa sc-iIvHqT lhyyLL crXmiY">required</div></td><td class="sc-gGKoUb ixGaBD"><div><div><span class="sc-bEjUoa sc-boKDdR lhyyLL jYezsP"></span><span class="sc-bEjUoa sc-fOOuSg lhyyLL dbKJYq">string</span><span class="sc-bEjUoa sc-hdBJTi lhyyLL nwQTz"> (<!-- -->NanoID<!-- -->) </span><span class="sc-bEjUoa sc-cpclqO lhyyLL UZcrz">^[0-9A-Za-z]{11}$</span></div> <div><span class="sc-bEjUoa lhyyLL"> <!-- -->Example:<!-- --> </span> <span class="sc-bEjUoa sc-dTWiOz lhyyLL kMQdIk">sZq0gcKRNrOK3hcDugzqJ</span></div><div><div html="&lt;p&gt;租约ID&lt;/p&gt;
" class="sc-eVqvcJ sc-fszimp kIppRw drqpJr"><p>租约ID</p>
</div></div></div></td></tr></tbody></table></div><div><h3 class="sc-gDzyrw kjrVcG">Responses</h3><div><button class="sc-jIDBmd lkmdtA"><svg class="sc-dntSTA cGxVlA" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg><strong class="sc-eJvlPh fBhAXU">200<!-- --> </strong><div html="&lt;p&gt;租客信息更新成功&lt;/p&gt;
" class="sc-eVqvcJ sc-fszimp sc-etsjJW kIppRw jnwENr ljKHqG"><p>租客信息更新成功</p>
</div></button></div><div><button class="sc-jIDBmd kQCDrg" disabled=""><strong class="sc-eJvlPh fBhAXU">403<!-- --> </strong><div html="&lt;p&gt;无权限或未找到&lt;/p&gt;
" class="sc-eVqvcJ sc-fszimp sc-etsjJW kIppRw jnwENr ljKHqG"><p>无权限或未找到</p>
</div></button></div></div></div><div class="sc-jwTyAe sc-hjsuWn bDYKKx FFPsr"><div class="sc-eZSpzM jjnszm"><button class="sc-buTqWO iPCVMX"><span type="put" class="sc-fQLpxn dBzsUh http-verb put">put</span><span class="sc-jvKoal kZcHWP">/v1/leases/{leaseId}/tenants/self</span><svg class="sc-dntSTA iuNpUs" style="margin-right:-25px" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg></button><div aria-hidden="true" class="sc-ecJghI ga-DQLq"><div class="sc-iyBeIh icOxsG"><div html="&lt;p&gt;Development server&lt;/p&gt;
" class="sc-eVqvcJ sc-fszimp kIppRw drqpJr"><p>Development server</p>
</div><div tabindex="0" role="button"><div class="sc-xKhEK okJpy"><span>http://localhost:8089</span>/v1/leases/{leaseId}/tenants/self</div></div></div><div class="sc-iyBeIh icOxsG"><div html="&lt;p&gt;Production server&lt;/p&gt;
" class="sc-eVqvcJ sc-fszimp kIppRw drqpJr"><p>Production server</p>
</div><div tabindex="0" role="button"><div class="sc-xKhEK okJpy"><span>https://api.rentreport.com</span>/v1/leases/{leaseId}/tenants/self</div></div></div></div></div><div><h3 class="sc-lgpSej drJHMo"> <!-- -->Response samples<!-- --> </h3><div class="sc-cOpnSz fyxuKi" data-rttabs="true"><ul class="react-tabs__tab-list" role="tablist"><li class="tab-success react-tabs__tab--selected" role="tab" id="tab«R2eehq»0" aria-selected="true" aria-disabled="false" aria-controls="panel«R2eehq»0" tabindex="0" data-rttab="true">200</li></ul><div class="react-tabs__tab-panel react-tabs__tab-panel--selected" role="tabpanel" id="panel«R2eehq»0" aria-labelledby="tab«R2eehq»0"><div><div class="sc-bSFBcf iLdyBp"><span class="sc-gahYZc cXitJ">Content type</span><div class="sc-bAehkN iNRAJK">application/json</div></div><div class="sc-blIAwI eKKwxo"><div class="sc-dClGHI fdRrNy"><div class="sc-bbbBoY bBWkcI"><button><div class="sc-fYmhhH iNCOCX">Copy</div></button></div><div tabindex="0" class="sc-eVqvcJ kIppRw sc-fhfEft dFvLDb"><div class="redoc-json"><code><button class="collapser" aria-label="collapse"></button><span class="token punctuation">{</span><span class="ellipsis"></span><ul class="obj collapsible"><li><div class="hoverable "><span class="property token string">"success"</span>: <span class="token boolean">true</span></div></li></ul><span class="token punctuation">}</span></code></div></div></div></div></div></div></div></div></div></div></div><div id="tag/Tenants" data-section-id="tag/Tenants" class="sc-dTvVRJ bPmFpz"><div class="sc-jJLAfE gkiSyE"><div class="sc-ggWZvA fqkwbU"><h2 class="sc-kNOymR iFSqkw"><a class="sc-kcLKEh fRdsOi" href="#tag/Tenants" aria-label="tag/Tenants"></a>Tenants</h2></div></div><div class="sc-ggWZvA dCzIPc"><div class="sc-eVqvcJ sc-fszimp kIppRw kbZred redoc-markdown " html="&lt;p&gt;Tenant management operations&lt;/p&gt;
"><p>Tenant management operations</p>
</div></div></div><div id="tag/Tenants/paths/~1leases~1{leaseId}~1tenants/post" data-section-id="tag/Tenants/paths/~1leases~1{leaseId}~1tenants/post" class="sc-dTvVRJ gHrCVQ"><div class="sc-jJLAfE gkiSyE"><div class="sc-ggWZvA fqkwbU"><h2 class="sc-kNOymR iFSqkw"><a class="sc-kcLKEh fRdsOi" href="#tag/Tenants/paths/~1leases~1{leaseId}~1tenants/post" aria-label="tag/Tenants/paths/~1leases~1{leaseId}~1tenants/post"></a>/leases/{leaseId}/tenants<!-- --> </h2><div class="sc-bfjeOH txIPi"><div html="&lt;p&gt;Add a new tenant to a specific lease&lt;/p&gt;
" class="sc-eVqvcJ sc-fszimp kIppRw kbZred"><p>Add a new tenant to a specific lease</p>
</div></div><div><h5 class="sc-eqYatC czjApA">path<!-- --> Parameters</h5><table class="sc-eqNDNG icJLQx"><tbody><tr class="last "><td kind="field" title="leaseId" class="sc-kCuUfV sc-fbQrwq gdmNWp dFOJWJ"><span class="sc-hwddKA cteAyA"></span><span class="property-name">leaseId</span><div class="sc-bEjUoa sc-iIvHqT lhyyLL crXmiY">required</div></td><td class="sc-gGKoUb ixGaBD"><div><div><span class="sc-bEjUoa sc-boKDdR lhyyLL jYezsP"></span><span class="sc-bEjUoa sc-fOOuSg lhyyLL dbKJYq">string</span><span class="sc-bEjUoa sc-hdBJTi lhyyLL nwQTz"> (<!-- -->NanoID<!-- -->) </span><span class="sc-bEjUoa sc-cpclqO lhyyLL UZcrz">^[0-9A-Za-z]{11}$</span></div> <div><span class="sc-bEjUoa lhyyLL"> <!-- -->Example:<!-- --> </span> <span class="sc-bEjUoa sc-dTWiOz lhyyLL kMQdIk">sZq0gcKRNrOK3hcDugzqJ</span></div><div><div html="&lt;p&gt;Resource identifier&lt;/p&gt;
" class="sc-eVqvcJ sc-fszimp kIppRw drqpJr"><p>Resource identifier</p>
</div></div></div></td></tr></tbody></table></div><h5 class="sc-eqYatC czjApA">Request Body schema: <span class="sc-dNFkOE cFlAeY">application/json</span></h5><div html="" class="sc-eVqvcJ sc-fszimp kIppRw kbZred"></div><table class="sc-eqNDNG icJLQx"><tbody><tr class=""><td kind="field" title="firstName" class="sc-kCuUfV sc-fbQrwq gdmNWp dFOJWJ"><span class="sc-hwddKA cteAyA"></span><span class="property-name">firstName</span><div class="sc-bEjUoa sc-iIvHqT lhyyLL crXmiY">required</div></td><td class="sc-gGKoUb ixGaBD"><div><div><span class="sc-bEjUoa sc-boKDdR lhyyLL jYezsP"></span><span class="sc-bEjUoa sc-fOOuSg lhyyLL dbKJYq">string</span></div> <div><div html="&lt;p&gt;Legal first name&lt;/p&gt;
" class="sc-eVqvcJ sc-fszimp kIppRw drqpJr"><p>Legal first name</p>
</div></div></div></td></tr><tr class=""><td kind="field" title="middleName" class="sc-kCuUfV sc-fbQrwq gdmNWp dFOJWJ"><span class="sc-hwddKA cteAyA"></span><span class="property-name">middleName</span></td><td class="sc-gGKoUb ixGaBD"><div><div><span class="sc-bEjUoa sc-boKDdR lhyyLL jYezsP"></span><span class="sc-bEjUoa sc-fOOuSg lhyyLL dbKJYq">string</span></div> <div><div html="" class="sc-eVqvcJ sc-fszimp kIppRw drqpJr"></div></div></div></td></tr><tr class=""><td kind="field" title="lastName" class="sc-kCuUfV sc-fbQrwq gdmNWp dFOJWJ"><span class="sc-hwddKA cteAyA"></span><span class="property-name">lastName</span><div class="sc-bEjUoa sc-iIvHqT lhyyLL crXmiY">required</div></td><td class="sc-gGKoUb ixGaBD"><div><div><span class="sc-bEjUoa sc-boKDdR lhyyLL jYezsP"></span><span class="sc-bEjUoa sc-fOOuSg lhyyLL dbKJYq">string</span></div> <div><div html="&lt;p&gt;Legal last name&lt;/p&gt;
" class="sc-eVqvcJ sc-fszimp kIppRw drqpJr"><p>Legal last name</p>
</div></div></div></td></tr><tr class=""><td kind="field" title="email" class="sc-kCuUfV sc-fbQrwq gdmNWp dFOJWJ"><span class="sc-hwddKA cteAyA"></span><span class="property-name">email</span><div class="sc-bEjUoa sc-iIvHqT lhyyLL crXmiY">required</div></td><td class="sc-gGKoUb ixGaBD"><div><div><span class="sc-bEjUoa sc-boKDdR lhyyLL jYezsP"></span><span class="sc-bEjUoa sc-fOOuSg lhyyLL dbKJYq">string</span><span class="sc-bEjUoa sc-fOOuSg lhyyLL dbKJYq"> <!-- -->&lt;<!-- -->email<!-- -->&gt;<!-- --> </span></div> <div><div html="" class="sc-eVqvcJ sc-fszimp kIppRw drqpJr"></div></div></div></td></tr><tr class=""><td kind="field" title="phoneNumber" class="sc-kCuUfV sc-fbQrwq gdmNWp dFOJWJ"><span class="sc-hwddKA cteAyA"></span><span class="property-name">phoneNumber</span></td><td class="sc-gGKoUb ixGaBD"><div><div><span class="sc-bEjUoa sc-boKDdR lhyyLL jYezsP"></span><span class="sc-bEjUoa sc-fOOuSg lhyyLL dbKJYq">string</span></div> <div><div html="" class="sc-eVqvcJ sc-fszimp kIppRw drqpJr"></div></div></div></td></tr><tr class=""><td kind="field" title="sinNumber" class="sc-kCuUfV sc-fbQrwq gdmNWp dFOJWJ"><span class="sc-hwddKA cteAyA"></span><span class="property-name">sinNumber</span></td><td class="sc-gGKoUb ixGaBD"><div><div><span class="sc-bEjUoa sc-boKDdR lhyyLL jYezsP"></span><span class="sc-bEjUoa sc-fOOuSg lhyyLL dbKJYq">string</span></div> <div><div html="&lt;p&gt;Social Insurance Number&lt;/p&gt;
" class="sc-eVqvcJ sc-fszimp kIppRw drqpJr"><p>Social Insurance Number</p>
</div></div></div></td></tr><tr class=""><td kind="field" title="notes" class="sc-kCuUfV sc-fbQrwq gdmNWp dFOJWJ"><span class="sc-hwddKA cteAyA"></span><span class="property-name">notes</span></td><td class="sc-gGKoUb ixGaBD"><div><div><span class="sc-bEjUoa sc-boKDdR lhyyLL jYezsP"></span><span class="sc-bEjUoa sc-fOOuSg lhyyLL dbKJYq">string</span></div> <div><div html="&lt;p&gt;A place to keep notes for internal purposes&lt;/p&gt;
" class="sc-eVqvcJ sc-fszimp kIppRw drqpJr"><p>A place to keep notes for internal purposes</p>
</div></div></div></td></tr><tr class=""><td kind="field" title="leaseId" class="sc-kCuUfV sc-fbQrwq gdmNWp dFOJWJ"><span class="sc-hwddKA cteAyA"></span><span class="property-name">leaseId</span><div class="sc-bEjUoa sc-iIvHqT lhyyLL crXmiY">required</div></td><td class="sc-gGKoUb ixGaBD"><div><div><span class="sc-bEjUoa sc-boKDdR lhyyLL jYezsP"></span><span class="sc-bEjUoa sc-fOOuSg lhyyLL dbKJYq">string</span></div> <div><div html="" class="sc-eVqvcJ sc-fszimp kIppRw drqpJr"></div></div></div></td></tr><tr class="last "><td kind="field" title="tenantId" class="sc-kCuUfV sc-fbQrwq gdmNWp dFOJWJ"><span class="sc-hwddKA cteAyA"></span><span class="property-name">tenantId</span></td><td class="sc-gGKoUb ixGaBD"><div><div><span class="sc-bEjUoa sc-boKDdR lhyyLL jYezsP"></span><span class="sc-bEjUoa sc-fOOuSg lhyyLL dbKJYq">string</span></div> <div><div html="&lt;p&gt;绑定的租客用户ID（邀请完成后才有）&lt;/p&gt;
" class="sc-eVqvcJ sc-fszimp kIppRw drqpJr"><p>绑定的租客用户ID（邀请完成后才有）</p>
</div></div></div></td></tr></tbody></table><div><h3 class="sc-gDzyrw kjrVcG">Responses</h3><div><button class="sc-jIDBmd lkmdtA"><svg class="sc-dntSTA cGxVlA" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg><strong class="sc-eJvlPh fBhAXU">201<!-- --> </strong><div html="&lt;p&gt;Tenant added successfully&lt;/p&gt;
" class="sc-eVqvcJ sc-fszimp sc-etsjJW kIppRw jnwENr ljKHqG"><p>Tenant added successfully</p>
</div></button></div></div></div><div class="sc-jwTyAe sc-hjsuWn bDYKKx FFPsr"><div class="sc-eZSpzM jjnszm"><button class="sc-buTqWO iPCVMX"><span type="post" class="sc-fQLpxn kwcmyC http-verb post">post</span><span class="sc-jvKoal kZcHWP">/leases/{leaseId}/tenants</span><svg class="sc-dntSTA iuNpUs" style="margin-right:-25px" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg></button><div aria-hidden="true" class="sc-ecJghI ga-DQLq"><div class="sc-iyBeIh icOxsG"><div html="&lt;p&gt;Development server&lt;/p&gt;
" class="sc-eVqvcJ sc-fszimp kIppRw drqpJr"><p>Development server</p>
</div><div tabindex="0" role="button"><div class="sc-xKhEK okJpy"><span>http://localhost:8089</span>/leases/{leaseId}/tenants</div></div></div><div class="sc-iyBeIh icOxsG"><div html="&lt;p&gt;Production server&lt;/p&gt;
" class="sc-eVqvcJ sc-fszimp kIppRw drqpJr"><p>Production server</p>
</div><div tabindex="0" role="button"><div class="sc-xKhEK okJpy"><span>https://api.rentreport.com</span>/leases/{leaseId}/tenants</div></div></div></div></div><div><h3 class="sc-lgpSej drJHMo"> <!-- -->Request samples<!-- --> </h3><div class="sc-cOpnSz fyxuKi" data-rttabs="true"><ul class="react-tabs__tab-list" role="tablist"><li class="react-tabs__tab react-tabs__tab--selected" role="tab" id="tab«R2a9ia»0" aria-selected="true" aria-disabled="false" aria-controls="panel«R2a9ia»0" tabindex="0" data-rttab="true">Payload</li></ul><div class="react-tabs__tab-panel react-tabs__tab-panel--selected" role="tabpanel" id="panel«R2a9ia»0" aria-labelledby="tab«R2a9ia»0"><div><div class="sc-bSFBcf iLdyBp"><span class="sc-gahYZc cXitJ">Content type</span><div class="sc-bAehkN iNRAJK">application/json</div></div><div class="sc-blIAwI eKKwxo"><div class="sc-dClGHI fdRrNy"><div class="sc-bbbBoY bBWkcI"><button><div class="sc-fYmhhH iNCOCX">Copy</div></button></div><div tabindex="0" class="sc-eVqvcJ kIppRw sc-fhfEft dFvLDb"><div class="redoc-json"><code><span class="token string">&quot;sZq0gcKRNrOK3hcDugzqJ&quot;</span></code></div></div></div></div></div></div></div></div><div><h3 class="sc-lgpSej drJHMo"> <!-- -->Response samples<!-- --> </h3><div class="sc-cOpnSz fyxuKi" data-rttabs="true"><ul class="react-tabs__tab-list" role="tablist"><li class="tab-success react-tabs__tab--selected" role="tab" id="tab«R2e9ia»0" aria-selected="true" aria-disabled="false" aria-controls="panel«R2e9ia»0" tabindex="0" data-rttab="true">201</li></ul><div class="react-tabs__tab-panel react-tabs__tab-panel--selected" role="tabpanel" id="panel«R2e9ia»0" aria-labelledby="tab«R2e9ia»0"><div><div class="sc-bSFBcf iLdyBp"><span class="sc-gahYZc cXitJ">Content type</span><div class="sc-bAehkN iNRAJK">application/json</div></div><div class="sc-blIAwI eKKwxo"><div class="sc-dClGHI fdRrNy"><div class="sc-bbbBoY bBWkcI"><button><div class="sc-fYmhhH iNCOCX">Copy</div></button></div><div tabindex="0" class="sc-eVqvcJ kIppRw sc-fhfEft dFvLDb"><div class="redoc-json"><code><button class="collapser" aria-label="collapse"></button><span class="token punctuation">{</span><span class="ellipsis"></span><ul class="obj collapsible"><li><div class="hoverable "><span class="property token string">"id"</span>: <span class="token string">&quot;sZq0gcKRNrOK3hcDugzqJ&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"firstName"</span>: <span class="token string">&quot;string&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"middleName"</span>: <span class="token string">&quot;string&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"lastName"</span>: <span class="token string">&quot;string&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"email"</span>: <span class="token string">&quot;<EMAIL>&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"phoneNumber"</span>: <span class="token string">&quot;string&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"sinNumber"</span>: <span class="token string">&quot;string&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"notes"</span>: <span class="token string">&quot;string&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"leaseId"</span>: <span class="token string">&quot;sZq0gcKRNrOK3hcDugzqJ&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"tenantId"</span>: <span class="token string">&quot;string&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"userId"</span>: <span class="token string">&quot;sZq0gcKRNrOK3hcDugzqJ&quot;</span></div></li></ul><span class="token punctuation">}</span></code></div></div></div></div></div></div></div></div></div></div></div><div id="tag/Tenants/paths/~1leases~1{leaseId}~1tenants~1{tenantId}/get" data-section-id="tag/Tenants/paths/~1leases~1{leaseId}~1tenants~1{tenantId}/get" class="sc-dTvVRJ gHrCVQ"><div class="sc-jJLAfE gkiSyE"><div class="sc-ggWZvA fqkwbU"><h2 class="sc-kNOymR iFSqkw"><a class="sc-kcLKEh fRdsOi" href="#tag/Tenants/paths/~1leases~1{leaseId}~1tenants~1{tenantId}/get" aria-label="tag/Tenants/paths/~1leases~1{leaseId}~1tenants~1{tenantId}/get"></a>/leases/{leaseId}/tenants/{tenantId}<!-- --> </h2><div class="sc-bfjeOH txIPi"><div html="&lt;p&gt;Get detailed information for a specific tenant&lt;/p&gt;
" class="sc-eVqvcJ sc-fszimp kIppRw kbZred"><p>Get detailed information for a specific tenant</p>
</div></div><div><h5 class="sc-eqYatC czjApA">path<!-- --> Parameters</h5><table class="sc-eqNDNG icJLQx"><tbody><tr class=""><td kind="field" title="leaseId" class="sc-kCuUfV sc-fbQrwq gdmNWp dFOJWJ"><span class="sc-hwddKA cteAyA"></span><span class="property-name">leaseId</span><div class="sc-bEjUoa sc-iIvHqT lhyyLL crXmiY">required</div></td><td class="sc-gGKoUb ixGaBD"><div><div><span class="sc-bEjUoa sc-boKDdR lhyyLL jYezsP"></span><span class="sc-bEjUoa sc-fOOuSg lhyyLL dbKJYq">string</span><span class="sc-bEjUoa sc-hdBJTi lhyyLL nwQTz"> (<!-- -->NanoID<!-- -->) </span><span class="sc-bEjUoa sc-cpclqO lhyyLL UZcrz">^[0-9A-Za-z]{11}$</span></div> <div><span class="sc-bEjUoa lhyyLL"> <!-- -->Example:<!-- --> </span> <span class="sc-bEjUoa sc-dTWiOz lhyyLL kMQdIk">sZq0gcKRNrOK3hcDugzqJ</span></div><div><div html="&lt;p&gt;Resource identifier&lt;/p&gt;
" class="sc-eVqvcJ sc-fszimp kIppRw drqpJr"><p>Resource identifier</p>
</div></div></div></td></tr><tr class="last "><td kind="field" title="tenantId" class="sc-kCuUfV sc-fbQrwq gdmNWp dFOJWJ"><span class="sc-hwddKA cteAyA"></span><span class="property-name">tenantId</span><div class="sc-bEjUoa sc-iIvHqT lhyyLL crXmiY">required</div></td><td class="sc-gGKoUb ixGaBD"><div><div><span class="sc-bEjUoa sc-boKDdR lhyyLL jYezsP"></span><span class="sc-bEjUoa sc-fOOuSg lhyyLL dbKJYq">string</span><span class="sc-bEjUoa sc-hdBJTi lhyyLL nwQTz"> (<!-- -->NanoID<!-- -->) </span><span class="sc-bEjUoa sc-cpclqO lhyyLL UZcrz">^[0-9A-Za-z]{11}$</span></div> <div><span class="sc-bEjUoa lhyyLL"> <!-- -->Example:<!-- --> </span> <span class="sc-bEjUoa sc-dTWiOz lhyyLL kMQdIk">sZq0gcKRNrOK3hcDugzqJ</span></div><div><div html="&lt;p&gt;Resource identifier&lt;/p&gt;
" class="sc-eVqvcJ sc-fszimp kIppRw drqpJr"><p>Resource identifier</p>
</div></div></div></td></tr></tbody></table></div><div><h3 class="sc-gDzyrw kjrVcG">Responses</h3><div><button class="sc-jIDBmd lkmdtA"><svg class="sc-dntSTA cGxVlA" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg><strong class="sc-eJvlPh fBhAXU">200<!-- --> </strong><div html="&lt;p&gt;Tenant details retrieved successfully&lt;/p&gt;
" class="sc-eVqvcJ sc-fszimp sc-etsjJW kIppRw jnwENr ljKHqG"><p>Tenant details retrieved successfully</p>
</div></button></div></div></div><div class="sc-jwTyAe sc-hjsuWn bDYKKx FFPsr"><div class="sc-eZSpzM jjnszm"><button class="sc-buTqWO iPCVMX"><span type="get" class="sc-fQLpxn dynMBc http-verb get">get</span><span class="sc-jvKoal kZcHWP">/leases/{leaseId}/tenants/{tenantId}</span><svg class="sc-dntSTA iuNpUs" style="margin-right:-25px" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg></button><div aria-hidden="true" class="sc-ecJghI ga-DQLq"><div class="sc-iyBeIh icOxsG"><div html="&lt;p&gt;Development server&lt;/p&gt;
" class="sc-eVqvcJ sc-fszimp kIppRw drqpJr"><p>Development server</p>
</div><div tabindex="0" role="button"><div class="sc-xKhEK okJpy"><span>http://localhost:8089</span>/leases/{leaseId}/tenants/{tenantId}</div></div></div><div class="sc-iyBeIh icOxsG"><div html="&lt;p&gt;Production server&lt;/p&gt;
" class="sc-eVqvcJ sc-fszimp kIppRw drqpJr"><p>Production server</p>
</div><div tabindex="0" role="button"><div class="sc-xKhEK okJpy"><span>https://api.rentreport.com</span>/leases/{leaseId}/tenants/{tenantId}</div></div></div></div></div><div><h3 class="sc-lgpSej drJHMo"> <!-- -->Response samples<!-- --> </h3><div class="sc-cOpnSz fyxuKi" data-rttabs="true"><ul class="react-tabs__tab-list" role="tablist"><li class="tab-success react-tabs__tab--selected" role="tab" id="tab«R2eaia»0" aria-selected="true" aria-disabled="false" aria-controls="panel«R2eaia»0" tabindex="0" data-rttab="true">200</li></ul><div class="react-tabs__tab-panel react-tabs__tab-panel--selected" role="tabpanel" id="panel«R2eaia»0" aria-labelledby="tab«R2eaia»0"><div><div class="sc-bSFBcf iLdyBp"><span class="sc-gahYZc cXitJ">Content type</span><div class="sc-bAehkN iNRAJK">application/json</div></div><div class="sc-blIAwI eKKwxo"><div class="sc-dClGHI fdRrNy"><div class="sc-bbbBoY bBWkcI"><button><div class="sc-fYmhhH iNCOCX">Copy</div></button></div><div tabindex="0" class="sc-eVqvcJ kIppRw sc-fhfEft dFvLDb"><div class="redoc-json"><code><button class="collapser" aria-label="collapse"></button><span class="token punctuation">{</span><span class="ellipsis"></span><ul class="obj collapsible"><li><div class="hoverable "><span class="property token string">"id"</span>: <span class="token string">&quot;sZq0gcKRNrOK3hcDugzqJ&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"firstName"</span>: <span class="token string">&quot;string&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"middleName"</span>: <span class="token string">&quot;string&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"lastName"</span>: <span class="token string">&quot;string&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"email"</span>: <span class="token string">&quot;<EMAIL>&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"phoneNumber"</span>: <span class="token string">&quot;string&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"sinNumber"</span>: <span class="token string">&quot;string&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"notes"</span>: <span class="token string">&quot;string&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"leaseId"</span>: <span class="token string">&quot;sZq0gcKRNrOK3hcDugzqJ&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"tenantId"</span>: <span class="token string">&quot;string&quot;</span></div></li></ul><span class="token punctuation">}</span></code></div></div></div></div></div></div></div></div></div></div></div><div id="tag/Tenants/paths/~1leases~1{leaseId}~1tenants~1{tenantId}/put" data-section-id="tag/Tenants/paths/~1leases~1{leaseId}~1tenants~1{tenantId}/put" class="sc-dTvVRJ gHrCVQ"><div class="sc-jJLAfE gkiSyE"><div class="sc-ggWZvA fqkwbU"><h2 class="sc-kNOymR iFSqkw"><a class="sc-kcLKEh fRdsOi" href="#tag/Tenants/paths/~1leases~1{leaseId}~1tenants~1{tenantId}/put" aria-label="tag/Tenants/paths/~1leases~1{leaseId}~1tenants~1{tenantId}/put"></a>/leases/{leaseId}/tenants/{tenantId}<!-- --> </h2><div class="sc-bfjeOH txIPi"><div html="&lt;p&gt;Update information for a specific tenant&lt;/p&gt;
" class="sc-eVqvcJ sc-fszimp kIppRw kbZred"><p>Update information for a specific tenant</p>
</div></div><div><h5 class="sc-eqYatC czjApA">path<!-- --> Parameters</h5><table class="sc-eqNDNG icJLQx"><tbody><tr class=""><td kind="field" title="leaseId" class="sc-kCuUfV sc-fbQrwq gdmNWp dFOJWJ"><span class="sc-hwddKA cteAyA"></span><span class="property-name">leaseId</span><div class="sc-bEjUoa sc-iIvHqT lhyyLL crXmiY">required</div></td><td class="sc-gGKoUb ixGaBD"><div><div><span class="sc-bEjUoa sc-boKDdR lhyyLL jYezsP"></span><span class="sc-bEjUoa sc-fOOuSg lhyyLL dbKJYq">string</span><span class="sc-bEjUoa sc-hdBJTi lhyyLL nwQTz"> (<!-- -->NanoID<!-- -->) </span><span class="sc-bEjUoa sc-cpclqO lhyyLL UZcrz">^[0-9A-Za-z]{11}$</span></div> <div><span class="sc-bEjUoa lhyyLL"> <!-- -->Example:<!-- --> </span> <span class="sc-bEjUoa sc-dTWiOz lhyyLL kMQdIk">sZq0gcKRNrOK3hcDugzqJ</span></div><div><div html="&lt;p&gt;Resource identifier&lt;/p&gt;
" class="sc-eVqvcJ sc-fszimp kIppRw drqpJr"><p>Resource identifier</p>
</div></div></div></td></tr><tr class="last "><td kind="field" title="tenantId" class="sc-kCuUfV sc-fbQrwq gdmNWp dFOJWJ"><span class="sc-hwddKA cteAyA"></span><span class="property-name">tenantId</span><div class="sc-bEjUoa sc-iIvHqT lhyyLL crXmiY">required</div></td><td class="sc-gGKoUb ixGaBD"><div><div><span class="sc-bEjUoa sc-boKDdR lhyyLL jYezsP"></span><span class="sc-bEjUoa sc-fOOuSg lhyyLL dbKJYq">string</span><span class="sc-bEjUoa sc-hdBJTi lhyyLL nwQTz"> (<!-- -->NanoID<!-- -->) </span><span class="sc-bEjUoa sc-cpclqO lhyyLL UZcrz">^[0-9A-Za-z]{11}$</span></div> <div><span class="sc-bEjUoa lhyyLL"> <!-- -->Example:<!-- --> </span> <span class="sc-bEjUoa sc-dTWiOz lhyyLL kMQdIk">sZq0gcKRNrOK3hcDugzqJ</span></div><div><div html="&lt;p&gt;Resource identifier&lt;/p&gt;
" class="sc-eVqvcJ sc-fszimp kIppRw drqpJr"><p>Resource identifier</p>
</div></div></div></td></tr></tbody></table></div><h5 class="sc-eqYatC czjApA">Request Body schema: <span class="sc-dNFkOE cFlAeY">application/json</span></h5><div html="" class="sc-eVqvcJ sc-fszimp kIppRw kbZred"></div><table class="sc-eqNDNG icJLQx"><tbody><tr class=""><td kind="field" title="firstName" class="sc-kCuUfV sc-fbQrwq gdmNWp dFOJWJ"><span class="sc-hwddKA cteAyA"></span><span class="property-name">firstName</span><div class="sc-bEjUoa sc-iIvHqT lhyyLL crXmiY">required</div></td><td class="sc-gGKoUb ixGaBD"><div><div><span class="sc-bEjUoa sc-boKDdR lhyyLL jYezsP"></span><span class="sc-bEjUoa sc-fOOuSg lhyyLL dbKJYq">string</span></div> <div><div html="&lt;p&gt;Legal first name&lt;/p&gt;
" class="sc-eVqvcJ sc-fszimp kIppRw drqpJr"><p>Legal first name</p>
</div></div></div></td></tr><tr class=""><td kind="field" title="middleName" class="sc-kCuUfV sc-fbQrwq gdmNWp dFOJWJ"><span class="sc-hwddKA cteAyA"></span><span class="property-name">middleName</span></td><td class="sc-gGKoUb ixGaBD"><div><div><span class="sc-bEjUoa sc-boKDdR lhyyLL jYezsP"></span><span class="sc-bEjUoa sc-fOOuSg lhyyLL dbKJYq">string</span></div> <div><div html="" class="sc-eVqvcJ sc-fszimp kIppRw drqpJr"></div></div></div></td></tr><tr class=""><td kind="field" title="lastName" class="sc-kCuUfV sc-fbQrwq gdmNWp dFOJWJ"><span class="sc-hwddKA cteAyA"></span><span class="property-name">lastName</span><div class="sc-bEjUoa sc-iIvHqT lhyyLL crXmiY">required</div></td><td class="sc-gGKoUb ixGaBD"><div><div><span class="sc-bEjUoa sc-boKDdR lhyyLL jYezsP"></span><span class="sc-bEjUoa sc-fOOuSg lhyyLL dbKJYq">string</span></div> <div><div html="&lt;p&gt;Legal last name&lt;/p&gt;
" class="sc-eVqvcJ sc-fszimp kIppRw drqpJr"><p>Legal last name</p>
</div></div></div></td></tr><tr class=""><td kind="field" title="email" class="sc-kCuUfV sc-fbQrwq gdmNWp dFOJWJ"><span class="sc-hwddKA cteAyA"></span><span class="property-name">email</span><div class="sc-bEjUoa sc-iIvHqT lhyyLL crXmiY">required</div></td><td class="sc-gGKoUb ixGaBD"><div><div><span class="sc-bEjUoa sc-boKDdR lhyyLL jYezsP"></span><span class="sc-bEjUoa sc-fOOuSg lhyyLL dbKJYq">string</span><span class="sc-bEjUoa sc-fOOuSg lhyyLL dbKJYq"> <!-- -->&lt;<!-- -->email<!-- -->&gt;<!-- --> </span></div> <div><div html="" class="sc-eVqvcJ sc-fszimp kIppRw drqpJr"></div></div></div></td></tr><tr class=""><td kind="field" title="phoneNumber" class="sc-kCuUfV sc-fbQrwq gdmNWp dFOJWJ"><span class="sc-hwddKA cteAyA"></span><span class="property-name">phoneNumber</span></td><td class="sc-gGKoUb ixGaBD"><div><div><span class="sc-bEjUoa sc-boKDdR lhyyLL jYezsP"></span><span class="sc-bEjUoa sc-fOOuSg lhyyLL dbKJYq">string</span></div> <div><div html="" class="sc-eVqvcJ sc-fszimp kIppRw drqpJr"></div></div></div></td></tr><tr class=""><td kind="field" title="sinNumber" class="sc-kCuUfV sc-fbQrwq gdmNWp dFOJWJ"><span class="sc-hwddKA cteAyA"></span><span class="property-name">sinNumber</span></td><td class="sc-gGKoUb ixGaBD"><div><div><span class="sc-bEjUoa sc-boKDdR lhyyLL jYezsP"></span><span class="sc-bEjUoa sc-fOOuSg lhyyLL dbKJYq">string</span></div> <div><div html="&lt;p&gt;Social Insurance Number&lt;/p&gt;
" class="sc-eVqvcJ sc-fszimp kIppRw drqpJr"><p>Social Insurance Number</p>
</div></div></div></td></tr><tr class=""><td kind="field" title="notes" class="sc-kCuUfV sc-fbQrwq gdmNWp dFOJWJ"><span class="sc-hwddKA cteAyA"></span><span class="property-name">notes</span></td><td class="sc-gGKoUb ixGaBD"><div><div><span class="sc-bEjUoa sc-boKDdR lhyyLL jYezsP"></span><span class="sc-bEjUoa sc-fOOuSg lhyyLL dbKJYq">string</span></div> <div><div html="&lt;p&gt;A place to keep notes for internal purposes&lt;/p&gt;
" class="sc-eVqvcJ sc-fszimp kIppRw drqpJr"><p>A place to keep notes for internal purposes</p>
</div></div></div></td></tr><tr class=""><td kind="field" title="leaseId" class="sc-kCuUfV sc-fbQrwq gdmNWp dFOJWJ"><span class="sc-hwddKA cteAyA"></span><span class="property-name">leaseId</span><div class="sc-bEjUoa sc-iIvHqT lhyyLL crXmiY">required</div></td><td class="sc-gGKoUb ixGaBD"><div><div><span class="sc-bEjUoa sc-boKDdR lhyyLL jYezsP"></span><span class="sc-bEjUoa sc-fOOuSg lhyyLL dbKJYq">string</span><span class="sc-bEjUoa sc-hdBJTi lhyyLL nwQTz"> (<!-- -->NanoID<!-- -->) </span><span class="sc-bEjUoa sc-cpclqO lhyyLL UZcrz">^[0-9A-Za-z]{11}$</span></div> <div><div html="&lt;p&gt;NanoID - 11 character alphanumeric identifier&lt;/p&gt;
" class="sc-eVqvcJ sc-fszimp kIppRw drqpJr"><p>NanoID - 11 character alphanumeric identifier</p>
</div></div></div></td></tr><tr class="last "><td kind="field" title="tenantId" class="sc-kCuUfV sc-fbQrwq gdmNWp dFOJWJ"><span class="sc-hwddKA cteAyA"></span><span class="property-name">tenantId</span></td><td class="sc-gGKoUb ixGaBD"><div><div><span class="sc-bEjUoa sc-boKDdR lhyyLL jYezsP"></span><span class="sc-bEjUoa sc-fOOuSg lhyyLL dbKJYq">string</span></div> <div><div html="&lt;p&gt;绑定的租客用户ID（邀请完成后才有）&lt;/p&gt;
" class="sc-eVqvcJ sc-fszimp kIppRw drqpJr"><p>绑定的租客用户ID（邀请完成后才有）</p>
</div></div></div></td></tr></tbody></table><div><h3 class="sc-gDzyrw kjrVcG">Responses</h3><div><button class="sc-jIDBmd lkmdtA"><svg class="sc-dntSTA cGxVlA" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg><strong class="sc-eJvlPh fBhAXU">200<!-- --> </strong><div html="&lt;p&gt;Tenant updated successfully&lt;/p&gt;
" class="sc-eVqvcJ sc-fszimp sc-etsjJW kIppRw jnwENr ljKHqG"><p>Tenant updated successfully</p>
</div></button></div></div></div><div class="sc-jwTyAe sc-hjsuWn bDYKKx FFPsr"><div class="sc-eZSpzM jjnszm"><button class="sc-buTqWO iPCVMX"><span type="put" class="sc-fQLpxn dBzsUh http-verb put">put</span><span class="sc-jvKoal kZcHWP">/leases/{leaseId}/tenants/{tenantId}</span><svg class="sc-dntSTA iuNpUs" style="margin-right:-25px" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg></button><div aria-hidden="true" class="sc-ecJghI ga-DQLq"><div class="sc-iyBeIh icOxsG"><div html="&lt;p&gt;Development server&lt;/p&gt;
" class="sc-eVqvcJ sc-fszimp kIppRw drqpJr"><p>Development server</p>
</div><div tabindex="0" role="button"><div class="sc-xKhEK okJpy"><span>http://localhost:8089</span>/leases/{leaseId}/tenants/{tenantId}</div></div></div><div class="sc-iyBeIh icOxsG"><div html="&lt;p&gt;Production server&lt;/p&gt;
" class="sc-eVqvcJ sc-fszimp kIppRw drqpJr"><p>Production server</p>
</div><div tabindex="0" role="button"><div class="sc-xKhEK okJpy"><span>https://api.rentreport.com</span>/leases/{leaseId}/tenants/{tenantId}</div></div></div></div></div><div><h3 class="sc-lgpSej drJHMo"> <!-- -->Request samples<!-- --> </h3><div class="sc-cOpnSz fyxuKi" data-rttabs="true"><ul class="react-tabs__tab-list" role="tablist"><li class="react-tabs__tab react-tabs__tab--selected" role="tab" id="tab«R2abia»0" aria-selected="true" aria-disabled="false" aria-controls="panel«R2abia»0" tabindex="0" data-rttab="true">Payload</li></ul><div class="react-tabs__tab-panel react-tabs__tab-panel--selected" role="tabpanel" id="panel«R2abia»0" aria-labelledby="tab«R2abia»0"><div><div class="sc-bSFBcf iLdyBp"><span class="sc-gahYZc cXitJ">Content type</span><div class="sc-bAehkN iNRAJK">application/json</div></div><div class="sc-blIAwI eKKwxo"><div class="sc-dClGHI fdRrNy"><div class="sc-bbbBoY bBWkcI"><button><div class="sc-fYmhhH iNCOCX">Copy</div></button></div><div tabindex="0" class="sc-eVqvcJ kIppRw sc-fhfEft dFvLDb"><div class="redoc-json"><code><span class="token string">&quot;sZq0gcKRNrOK3hcDugzqJ&quot;</span></code></div></div></div></div></div></div></div></div><div><h3 class="sc-lgpSej drJHMo"> <!-- -->Response samples<!-- --> </h3><div class="sc-cOpnSz fyxuKi" data-rttabs="true"><ul class="react-tabs__tab-list" role="tablist"><li class="tab-success react-tabs__tab--selected" role="tab" id="tab«R2ebia»0" aria-selected="true" aria-disabled="false" aria-controls="panel«R2ebia»0" tabindex="0" data-rttab="true">200</li></ul><div class="react-tabs__tab-panel react-tabs__tab-panel--selected" role="tabpanel" id="panel«R2ebia»0" aria-labelledby="tab«R2ebia»0"><div><div class="sc-bSFBcf iLdyBp"><span class="sc-gahYZc cXitJ">Content type</span><div class="sc-bAehkN iNRAJK">application/json</div></div><div class="sc-blIAwI eKKwxo"><div class="sc-dClGHI fdRrNy"><div class="sc-bbbBoY bBWkcI"><button><div class="sc-fYmhhH iNCOCX">Copy</div></button></div><div tabindex="0" class="sc-eVqvcJ kIppRw sc-fhfEft dFvLDb"><div class="redoc-json"><code><button class="collapser" aria-label="collapse"></button><span class="token punctuation">{</span><span class="ellipsis"></span><ul class="obj collapsible"><li><div class="hoverable "><span class="property token string">"id"</span>: <span class="token string">&quot;sZq0gcKRNrOK3hcDugzqJ&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"firstName"</span>: <span class="token string">&quot;string&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"middleName"</span>: <span class="token string">&quot;string&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"lastName"</span>: <span class="token string">&quot;string&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"email"</span>: <span class="token string">&quot;<EMAIL>&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"phoneNumber"</span>: <span class="token string">&quot;string&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"sinNumber"</span>: <span class="token string">&quot;string&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"notes"</span>: <span class="token string">&quot;string&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"leaseId"</span>: <span class="token string">&quot;sZq0gcKRNrOK3hcDugzqJ&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"tenantId"</span>: <span class="token string">&quot;string&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"userId"</span>: <span class="token string">&quot;sZq0gcKRNrOK3hcDugzqJ&quot;</span></div></li></ul><span class="token punctuation">}</span></code></div></div></div></div></div></div></div></div></div></div></div><div id="tag/Tenants/paths/~1leases~1{leaseId}~1tenants~1{tenantId}/delete" data-section-id="tag/Tenants/paths/~1leases~1{leaseId}~1tenants~1{tenantId}/delete" class="sc-dTvVRJ gHrCVQ"><div class="sc-jJLAfE gkiSyE"><div class="sc-ggWZvA fqkwbU"><h2 class="sc-kNOymR iFSqkw"><a class="sc-kcLKEh fRdsOi" href="#tag/Tenants/paths/~1leases~1{leaseId}~1tenants~1{tenantId}/delete" aria-label="tag/Tenants/paths/~1leases~1{leaseId}~1tenants~1{tenantId}/delete"></a>/leases/{leaseId}/tenants/{tenantId}<!-- --> </h2><div class="sc-bfjeOH txIPi"><div html="&lt;p&gt;Remove a tenant from a lease&lt;/p&gt;
" class="sc-eVqvcJ sc-fszimp kIppRw kbZred"><p>Remove a tenant from a lease</p>
</div></div><div><h5 class="sc-eqYatC czjApA">path<!-- --> Parameters</h5><table class="sc-eqNDNG icJLQx"><tbody><tr class=""><td kind="field" title="leaseId" class="sc-kCuUfV sc-fbQrwq gdmNWp dFOJWJ"><span class="sc-hwddKA cteAyA"></span><span class="property-name">leaseId</span><div class="sc-bEjUoa sc-iIvHqT lhyyLL crXmiY">required</div></td><td class="sc-gGKoUb ixGaBD"><div><div><span class="sc-bEjUoa sc-boKDdR lhyyLL jYezsP"></span><span class="sc-bEjUoa sc-fOOuSg lhyyLL dbKJYq">string</span><span class="sc-bEjUoa sc-hdBJTi lhyyLL nwQTz"> (<!-- -->NanoID<!-- -->) </span><span class="sc-bEjUoa sc-cpclqO lhyyLL UZcrz">^[0-9A-Za-z]{11}$</span></div> <div><span class="sc-bEjUoa lhyyLL"> <!-- -->Example:<!-- --> </span> <span class="sc-bEjUoa sc-dTWiOz lhyyLL kMQdIk">sZq0gcKRNrOK3hcDugzqJ</span></div><div><div html="&lt;p&gt;Resource identifier&lt;/p&gt;
" class="sc-eVqvcJ sc-fszimp kIppRw drqpJr"><p>Resource identifier</p>
</div></div></div></td></tr><tr class="last "><td kind="field" title="tenantId" class="sc-kCuUfV sc-fbQrwq gdmNWp dFOJWJ"><span class="sc-hwddKA cteAyA"></span><span class="property-name">tenantId</span><div class="sc-bEjUoa sc-iIvHqT lhyyLL crXmiY">required</div></td><td class="sc-gGKoUb ixGaBD"><div><div><span class="sc-bEjUoa sc-boKDdR lhyyLL jYezsP"></span><span class="sc-bEjUoa sc-fOOuSg lhyyLL dbKJYq">string</span><span class="sc-bEjUoa sc-hdBJTi lhyyLL nwQTz"> (<!-- -->NanoID<!-- -->) </span><span class="sc-bEjUoa sc-cpclqO lhyyLL UZcrz">^[0-9A-Za-z]{11}$</span></div> <div><span class="sc-bEjUoa lhyyLL"> <!-- -->Example:<!-- --> </span> <span class="sc-bEjUoa sc-dTWiOz lhyyLL kMQdIk">sZq0gcKRNrOK3hcDugzqJ</span></div><div><div html="&lt;p&gt;Resource identifier&lt;/p&gt;
" class="sc-eVqvcJ sc-fszimp kIppRw drqpJr"><p>Resource identifier</p>
</div></div></div></td></tr></tbody></table></div><div><h3 class="sc-gDzyrw kjrVcG">Responses</h3><div><button class="sc-jIDBmd lkmdtA"><svg class="sc-dntSTA cGxVlA" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg><strong class="sc-eJvlPh fBhAXU">204<!-- --> </strong><div html="&lt;p&gt;Tenant removed successfully&lt;/p&gt;
" class="sc-eVqvcJ sc-fszimp sc-etsjJW kIppRw jnwENr ljKHqG"><p>Tenant removed successfully</p>
</div></button></div></div></div><div class="sc-jwTyAe sc-hjsuWn bDYKKx FFPsr"><div class="sc-eZSpzM jjnszm"><button class="sc-buTqWO iPCVMX"><span type="delete" class="sc-fQLpxn gKcHYQ http-verb delete">delete</span><span class="sc-jvKoal kZcHWP">/leases/{leaseId}/tenants/{tenantId}</span><svg class="sc-dntSTA iuNpUs" style="margin-right:-25px" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg></button><div aria-hidden="true" class="sc-ecJghI ga-DQLq"><div class="sc-iyBeIh icOxsG"><div html="&lt;p&gt;Development server&lt;/p&gt;
" class="sc-eVqvcJ sc-fszimp kIppRw drqpJr"><p>Development server</p>
</div><div tabindex="0" role="button"><div class="sc-xKhEK okJpy"><span>http://localhost:8089</span>/leases/{leaseId}/tenants/{tenantId}</div></div></div><div class="sc-iyBeIh icOxsG"><div html="&lt;p&gt;Production server&lt;/p&gt;
" class="sc-eVqvcJ sc-fszimp kIppRw drqpJr"><p>Production server</p>
</div><div tabindex="0" role="button"><div class="sc-xKhEK okJpy"><span>https://api.rentreport.com</span>/leases/{leaseId}/tenants/{tenantId}</div></div></div></div></div><div><h3 class="sc-lgpSej drJHMo"> <!-- -->Response samples<!-- --> </h3><div class="sc-cOpnSz fyxuKi" data-rttabs="true"><ul class="react-tabs__tab-list" role="tablist"><li class="tab-success react-tabs__tab--selected" role="tab" id="tab«R2ecia»0" aria-selected="true" aria-disabled="false" aria-controls="panel«R2ecia»0" tabindex="0" data-rttab="true">204</li></ul><div class="react-tabs__tab-panel react-tabs__tab-panel--selected" role="tabpanel" id="panel«R2ecia»0" aria-labelledby="tab«R2ecia»0"><div><div class="sc-bSFBcf iLdyBp"><span class="sc-gahYZc cXitJ">Content type</span><div class="sc-bAehkN iNRAJK">application/json</div></div><div class="sc-blIAwI eKKwxo"><div class="sc-dClGHI fdRrNy"><div class="sc-bbbBoY bBWkcI"><button><div class="sc-fYmhhH iNCOCX">Copy</div></button></div><div tabindex="0" class="sc-eVqvcJ kIppRw sc-fhfEft dFvLDb"><div class="redoc-json"><code><span class="token punctuation">{ }</span></code></div></div></div></div></div></div></div></div></div></div></div><div id="tag/Payments" data-section-id="tag/Payments" class="sc-dTvVRJ bPmFpz"><div class="sc-jJLAfE gkiSyE"><div class="sc-ggWZvA fqkwbU"><h2 class="sc-kNOymR iFSqkw"><a class="sc-kcLKEh fRdsOi" href="#tag/Payments" aria-label="tag/Payments"></a>Payments</h2></div></div><div class="sc-ggWZvA dCzIPc"><div class="sc-eVqvcJ sc-fszimp kIppRw kbZred redoc-markdown " html="&lt;p&gt;Payment tracking and management&lt;/p&gt;
"><p>Payment tracking and management</p>
</div></div></div><div id="tag/Payments/paths/~1leases~1{leaseId}~1tenant-payments/post" data-section-id="tag/Payments/paths/~1leases~1{leaseId}~1tenant-payments/post" class="sc-dTvVRJ gHrCVQ"><div class="sc-jJLAfE gkiSyE"><div class="sc-ggWZvA fqkwbU"><h2 class="sc-kNOymR iFSqkw"><a class="sc-kcLKEh fRdsOi" href="#tag/Payments/paths/~1leases~1{leaseId}~1tenant-payments/post" aria-label="tag/Payments/paths/~1leases~1{leaseId}~1tenant-payments/post"></a>/leases/{leaseId}/tenant-payments<!-- --> </h2><div class="sc-bfjeOH txIPi"><div html="&lt;p&gt;Add a new tenant payment record for a specific lease&lt;/p&gt;
" class="sc-eVqvcJ sc-fszimp kIppRw kbZred"><p>Add a new tenant payment record for a specific lease</p>
</div></div><div><h5 class="sc-eqYatC czjApA">path<!-- --> Parameters</h5><table class="sc-eqNDNG icJLQx"><tbody><tr class="last "><td kind="field" title="leaseId" class="sc-kCuUfV sc-fbQrwq gdmNWp dFOJWJ"><span class="sc-hwddKA cteAyA"></span><span class="property-name">leaseId</span><div class="sc-bEjUoa sc-iIvHqT lhyyLL crXmiY">required</div></td><td class="sc-gGKoUb ixGaBD"><div><div><span class="sc-bEjUoa sc-boKDdR lhyyLL jYezsP"></span><span class="sc-bEjUoa sc-fOOuSg lhyyLL dbKJYq">string</span><span class="sc-bEjUoa sc-hdBJTi lhyyLL nwQTz"> (<!-- -->NanoID<!-- -->) </span><span class="sc-bEjUoa sc-cpclqO lhyyLL UZcrz">^[0-9A-Za-z]{11}$</span></div> <div><span class="sc-bEjUoa lhyyLL"> <!-- -->Example:<!-- --> </span> <span class="sc-bEjUoa sc-dTWiOz lhyyLL kMQdIk">sZq0gcKRNrOK3hcDugzqJ</span></div><div><div html="&lt;p&gt;Resource identifier&lt;/p&gt;
" class="sc-eVqvcJ sc-fszimp kIppRw drqpJr"><p>Resource identifier</p>
</div></div></div></td></tr></tbody></table></div><h5 class="sc-eqYatC czjApA">Request Body schema: <span class="sc-dNFkOE cFlAeY">application/json</span></h5><div html="" class="sc-eVqvcJ sc-fszimp kIppRw kbZred"></div><table class="sc-eqNDNG icJLQx"><tbody><tr class=""><td kind="field" title="id" class="sc-kCuUfV sc-fbQrwq gdmNWp dFOJWJ"><span class="sc-hwddKA cteAyA"></span><span class="property-name">id</span><div class="sc-bEjUoa sc-iIvHqT lhyyLL crXmiY">required</div></td><td class="sc-gGKoUb ixGaBD"><div><div><span class="sc-bEjUoa sc-boKDdR lhyyLL jYezsP"></span><span class="sc-bEjUoa sc-fOOuSg lhyyLL dbKJYq">string</span><span class="sc-bEjUoa sc-hdBJTi lhyyLL nwQTz"> (<!-- -->NanoID<!-- -->) </span><span class="sc-bEjUoa sc-cpclqO lhyyLL UZcrz">^[0-9A-Za-z]{11}$</span></div> <div><div html="&lt;p&gt;NanoID - 11 character alphanumeric identifier&lt;/p&gt;
" class="sc-eVqvcJ sc-fszimp kIppRw drqpJr"><p>NanoID - 11 character alphanumeric identifier</p>
</div></div></div></td></tr><tr class=""><td kind="field" title="amount" class="sc-kCuUfV sc-fbQrwq gdmNWp dFOJWJ"><span class="sc-hwddKA cteAyA"></span><span class="property-name">amount</span><div class="sc-bEjUoa sc-iIvHqT lhyyLL crXmiY">required</div></td><td class="sc-gGKoUb ixGaBD"><div><div><span class="sc-bEjUoa sc-boKDdR lhyyLL jYezsP"></span><span class="sc-bEjUoa sc-fOOuSg lhyyLL dbKJYq">number</span></div> <div><div html="&lt;p&gt;Amount paid by tenant&lt;/p&gt;
" class="sc-eVqvcJ sc-fszimp kIppRw drqpJr"><p>Amount paid by tenant</p>
</div></div></div></td></tr><tr class=""><td kind="field" title="date" class="sc-kCuUfV sc-fbQrwq gdmNWp dFOJWJ"><span class="sc-hwddKA cteAyA"></span><span class="property-name">date</span><div class="sc-bEjUoa sc-iIvHqT lhyyLL crXmiY">required</div></td><td class="sc-gGKoUb ixGaBD"><div><div><span class="sc-bEjUoa sc-boKDdR lhyyLL jYezsP"></span><span class="sc-bEjUoa sc-fOOuSg lhyyLL dbKJYq">string</span><span class="sc-bEjUoa sc-fOOuSg lhyyLL dbKJYq"> <!-- -->&lt;<!-- -->date<!-- -->&gt;<!-- --> </span></div> <div><div html="&lt;p&gt;Tenant payment date&lt;/p&gt;
" class="sc-eVqvcJ sc-fszimp kIppRw drqpJr"><p>Tenant payment date</p>
</div></div></div></td></tr><tr class=""><td kind="field" title="notes" class="sc-kCuUfV sc-fbQrwq gdmNWp dFOJWJ"><span class="sc-hwddKA cteAyA"></span><span class="property-name">notes</span></td><td class="sc-gGKoUb ixGaBD"><div><div><span class="sc-bEjUoa sc-boKDdR lhyyLL jYezsP"></span><span class="sc-bEjUoa sc-fOOuSg lhyyLL dbKJYq">string</span></div> <div><div html="" class="sc-eVqvcJ sc-fszimp kIppRw drqpJr"></div></div></div></td></tr><tr class=""><td kind="field" title="remainingBalance" class="sc-kCuUfV sc-fbQrwq gdmNWp dFOJWJ"><span class="sc-hwddKA cteAyA"></span><span class="property-name">remainingBalance</span></td><td class="sc-gGKoUb ixGaBD"><div><div><span class="sc-bEjUoa sc-boKDdR lhyyLL jYezsP"></span><span class="sc-bEjUoa sc-fOOuSg lhyyLL dbKJYq">number</span></div> <div><div html="&lt;p&gt;Tenant&amp;#39;s remaining balance after payment&lt;/p&gt;
" class="sc-eVqvcJ sc-fszimp kIppRw drqpJr"><p>Tenant&#39;s remaining balance after payment</p>
</div></div></div></td></tr><tr class=""><td kind="field" title="status" class="sc-kCuUfV sc-fbQrwq gdmNWp dFOJWJ"><span class="sc-hwddKA cteAyA"></span><span class="property-name">status</span></td><td class="sc-gGKoUb ixGaBD"><div><div><span class="sc-bEjUoa sc-boKDdR lhyyLL jYezsP"></span><span class="sc-bEjUoa sc-fOOuSg lhyyLL dbKJYq">string</span></div><div><span class="sc-bEjUoa lhyyLL"> <!-- -->Enum<!-- -->:</span> <span class="sc-bEjUoa sc-dTWiOz lhyyLL kMQdIk">&quot;pending&quot;</span> <span class="sc-bEjUoa sc-dTWiOz lhyyLL kMQdIk">&quot;completed&quot;</span> <span class="sc-bEjUoa sc-dTWiOz lhyyLL kMQdIk">&quot;failed&quot;</span> </div> <div><div html="" class="sc-eVqvcJ sc-fszimp kIppRw drqpJr"></div></div></div></td></tr><tr class="last "><td kind="field" title="leaseId" class="sc-kCuUfV sc-fbQrwq gdmNWp dFOJWJ"><span class="sc-hwddKA cteAyA"></span><span class="property-name">leaseId</span><div class="sc-bEjUoa sc-iIvHqT lhyyLL crXmiY">required</div></td><td class="sc-gGKoUb ixGaBD"><div><div><span class="sc-bEjUoa sc-boKDdR lhyyLL jYezsP"></span><span class="sc-bEjUoa sc-fOOuSg lhyyLL dbKJYq">string</span><span class="sc-bEjUoa sc-hdBJTi lhyyLL nwQTz"> (<!-- -->NanoID<!-- -->) </span><span class="sc-bEjUoa sc-cpclqO lhyyLL UZcrz">^[0-9A-Za-z]{11}$</span></div> <div><div html="&lt;p&gt;NanoID - 11 character alphanumeric identifier&lt;/p&gt;
" class="sc-eVqvcJ sc-fszimp kIppRw drqpJr"><p>NanoID - 11 character alphanumeric identifier</p>
</div></div></div></td></tr></tbody></table><div><h3 class="sc-gDzyrw kjrVcG">Responses</h3><div><button class="sc-jIDBmd lkmdtA"><svg class="sc-dntSTA cGxVlA" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg><strong class="sc-eJvlPh fBhAXU">201<!-- --> </strong><div html="&lt;p&gt;Payment recorded successfully&lt;/p&gt;
" class="sc-eVqvcJ sc-fszimp sc-etsjJW kIppRw jnwENr ljKHqG"><p>Payment recorded successfully</p>
</div></button></div></div></div><div class="sc-jwTyAe sc-hjsuWn bDYKKx FFPsr"><div class="sc-eZSpzM jjnszm"><button class="sc-buTqWO iPCVMX"><span type="post" class="sc-fQLpxn kwcmyC http-verb post">post</span><span class="sc-jvKoal kZcHWP">/leases/{leaseId}/tenant-payments</span><svg class="sc-dntSTA iuNpUs" style="margin-right:-25px" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg></button><div aria-hidden="true" class="sc-ecJghI ga-DQLq"><div class="sc-iyBeIh icOxsG"><div html="&lt;p&gt;Development server&lt;/p&gt;
" class="sc-eVqvcJ sc-fszimp kIppRw drqpJr"><p>Development server</p>
</div><div tabindex="0" role="button"><div class="sc-xKhEK okJpy"><span>http://localhost:8089</span>/leases/{leaseId}/tenant-payments</div></div></div><div class="sc-iyBeIh icOxsG"><div html="&lt;p&gt;Production server&lt;/p&gt;
" class="sc-eVqvcJ sc-fszimp kIppRw drqpJr"><p>Production server</p>
</div><div tabindex="0" role="button"><div class="sc-xKhEK okJpy"><span>https://api.rentreport.com</span>/leases/{leaseId}/tenant-payments</div></div></div></div></div><div><h3 class="sc-lgpSej drJHMo"> <!-- -->Request samples<!-- --> </h3><div class="sc-cOpnSz fyxuKi" data-rttabs="true"><ul class="react-tabs__tab-list" role="tablist"><li class="react-tabs__tab react-tabs__tab--selected" role="tab" id="tab«R2a9iq»0" aria-selected="true" aria-disabled="false" aria-controls="panel«R2a9iq»0" tabindex="0" data-rttab="true">Payload</li></ul><div class="react-tabs__tab-panel react-tabs__tab-panel--selected" role="tabpanel" id="panel«R2a9iq»0" aria-labelledby="tab«R2a9iq»0"><div><div class="sc-bSFBcf iLdyBp"><span class="sc-gahYZc cXitJ">Content type</span><div class="sc-bAehkN iNRAJK">application/json</div></div><div class="sc-blIAwI eKKwxo"><div class="sc-dClGHI fdRrNy"><div class="sc-bbbBoY bBWkcI"><button><div class="sc-fYmhhH iNCOCX">Copy</div></button></div><div tabindex="0" class="sc-eVqvcJ kIppRw sc-fhfEft dFvLDb"><div class="redoc-json"><code><span class="token string">&quot;sZq0gcKRNrOK3hcDugzqJ&quot;</span></code></div></div></div></div></div></div></div></div><div><h3 class="sc-lgpSej drJHMo"> <!-- -->Response samples<!-- --> </h3><div class="sc-cOpnSz fyxuKi" data-rttabs="true"><ul class="react-tabs__tab-list" role="tablist"><li class="tab-success react-tabs__tab--selected" role="tab" id="tab«R2e9iq»0" aria-selected="true" aria-disabled="false" aria-controls="panel«R2e9iq»0" tabindex="0" data-rttab="true">201</li></ul><div class="react-tabs__tab-panel react-tabs__tab-panel--selected" role="tabpanel" id="panel«R2e9iq»0" aria-labelledby="tab«R2e9iq»0"><div><div class="sc-bSFBcf iLdyBp"><span class="sc-gahYZc cXitJ">Content type</span><div class="sc-bAehkN iNRAJK">application/json</div></div><div class="sc-blIAwI eKKwxo"><div class="sc-dClGHI fdRrNy"><div class="sc-bbbBoY bBWkcI"><button><div class="sc-fYmhhH iNCOCX">Copy</div></button></div><div tabindex="0" class="sc-eVqvcJ kIppRw sc-fhfEft dFvLDb"><div class="redoc-json"><code><button class="collapser" aria-label="collapse"></button><span class="token punctuation">{</span><span class="ellipsis"></span><ul class="obj collapsible"><li><div class="hoverable "><span class="property token string">"id"</span>: <span class="token string">&quot;sZq0gcKRNrOK3hcDugzqJ&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"amount"</span>: <span class="token number">0</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"date"</span>: <span class="token string">&quot;2019-08-24&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"notes"</span>: <span class="token string">&quot;string&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"remainingBalance"</span>: <span class="token number">0</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"status"</span>: <span class="token string">&quot;pending&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"leaseId"</span>: <span class="token string">&quot;sZq0gcKRNrOK3hcDugzqJ&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"userId"</span>: <span class="token string">&quot;sZq0gcKRNrOK3hcDugzqJ&quot;</span></div></li></ul><span class="token punctuation">}</span></code></div></div></div></div></div></div></div></div></div></div></div><div id="tag/Payments/paths/~1leases~1{leaseId}~1tenant-payments~1{tenantPaymentId}/get" data-section-id="tag/Payments/paths/~1leases~1{leaseId}~1tenant-payments~1{tenantPaymentId}/get" class="sc-dTvVRJ gHrCVQ"><div class="sc-jJLAfE gkiSyE"><div class="sc-ggWZvA fqkwbU"><h2 class="sc-kNOymR iFSqkw"><a class="sc-kcLKEh fRdsOi" href="#tag/Payments/paths/~1leases~1{leaseId}~1tenant-payments~1{tenantPaymentId}/get" aria-label="tag/Payments/paths/~1leases~1{leaseId}~1tenant-payments~1{tenantPaymentId}/get"></a>/leases/{leaseId}/tenant-payments/{tenantPaymentId}<!-- --> </h2><div class="sc-bfjeOH txIPi"><div html="&lt;p&gt;Get detailed information for a specific tenant payment&lt;/p&gt;
" class="sc-eVqvcJ sc-fszimp kIppRw kbZred"><p>Get detailed information for a specific tenant payment</p>
</div></div><div><h5 class="sc-eqYatC czjApA">path<!-- --> Parameters</h5><table class="sc-eqNDNG icJLQx"><tbody><tr class=""><td kind="field" title="leaseId" class="sc-kCuUfV sc-fbQrwq gdmNWp dFOJWJ"><span class="sc-hwddKA cteAyA"></span><span class="property-name">leaseId</span><div class="sc-bEjUoa sc-iIvHqT lhyyLL crXmiY">required</div></td><td class="sc-gGKoUb ixGaBD"><div><div><span class="sc-bEjUoa sc-boKDdR lhyyLL jYezsP"></span><span class="sc-bEjUoa sc-fOOuSg lhyyLL dbKJYq">string</span><span class="sc-bEjUoa sc-hdBJTi lhyyLL nwQTz"> (<!-- -->NanoID<!-- -->) </span><span class="sc-bEjUoa sc-cpclqO lhyyLL UZcrz">^[0-9A-Za-z]{11}$</span></div> <div><span class="sc-bEjUoa lhyyLL"> <!-- -->Example:<!-- --> </span> <span class="sc-bEjUoa sc-dTWiOz lhyyLL kMQdIk">sZq0gcKRNrOK3hcDugzqJ</span></div><div><div html="&lt;p&gt;Resource identifier&lt;/p&gt;
" class="sc-eVqvcJ sc-fszimp kIppRw drqpJr"><p>Resource identifier</p>
</div></div></div></td></tr><tr class="last "><td kind="field" title="tenantPaymentId" class="sc-kCuUfV sc-fbQrwq gdmNWp dFOJWJ"><span class="sc-hwddKA cteAyA"></span><span class="property-name">tenantPaymentId</span><div class="sc-bEjUoa sc-iIvHqT lhyyLL crXmiY">required</div></td><td class="sc-gGKoUb ixGaBD"><div><div><span class="sc-bEjUoa sc-boKDdR lhyyLL jYezsP"></span><span class="sc-bEjUoa sc-fOOuSg lhyyLL dbKJYq">string</span></div> <div><div html="&lt;p&gt;Resource identifier&lt;/p&gt;
" class="sc-eVqvcJ sc-fszimp kIppRw drqpJr"><p>Resource identifier</p>
</div></div></div></td></tr></tbody></table></div><div><h3 class="sc-gDzyrw kjrVcG">Responses</h3><div><button class="sc-jIDBmd lkmdtA"><svg class="sc-dntSTA cGxVlA" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg><strong class="sc-eJvlPh fBhAXU">200<!-- --> </strong><div html="&lt;p&gt;Payment details retrieved successfully&lt;/p&gt;
" class="sc-eVqvcJ sc-fszimp sc-etsjJW kIppRw jnwENr ljKHqG"><p>Payment details retrieved successfully</p>
</div></button></div></div></div><div class="sc-jwTyAe sc-hjsuWn bDYKKx FFPsr"><div class="sc-eZSpzM jjnszm"><button class="sc-buTqWO iPCVMX"><span type="get" class="sc-fQLpxn dynMBc http-verb get">get</span><span class="sc-jvKoal kZcHWP">/leases/{leaseId}/tenant-payments/{tenantPaymentId}</span><svg class="sc-dntSTA iuNpUs" style="margin-right:-25px" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg></button><div aria-hidden="true" class="sc-ecJghI ga-DQLq"><div class="sc-iyBeIh icOxsG"><div html="&lt;p&gt;Development server&lt;/p&gt;
" class="sc-eVqvcJ sc-fszimp kIppRw drqpJr"><p>Development server</p>
</div><div tabindex="0" role="button"><div class="sc-xKhEK okJpy"><span>http://localhost:8089</span>/leases/{leaseId}/tenant-payments/{tenantPaymentId}</div></div></div><div class="sc-iyBeIh icOxsG"><div html="&lt;p&gt;Production server&lt;/p&gt;
" class="sc-eVqvcJ sc-fszimp kIppRw drqpJr"><p>Production server</p>
</div><div tabindex="0" role="button"><div class="sc-xKhEK okJpy"><span>https://api.rentreport.com</span>/leases/{leaseId}/tenant-payments/{tenantPaymentId}</div></div></div></div></div><div><h3 class="sc-lgpSej drJHMo"> <!-- -->Response samples<!-- --> </h3><div class="sc-cOpnSz fyxuKi" data-rttabs="true"><ul class="react-tabs__tab-list" role="tablist"><li class="tab-success react-tabs__tab--selected" role="tab" id="tab«R2eaiq»0" aria-selected="true" aria-disabled="false" aria-controls="panel«R2eaiq»0" tabindex="0" data-rttab="true">200</li></ul><div class="react-tabs__tab-panel react-tabs__tab-panel--selected" role="tabpanel" id="panel«R2eaiq»0" aria-labelledby="tab«R2eaiq»0"><div><div class="sc-bSFBcf iLdyBp"><span class="sc-gahYZc cXitJ">Content type</span><div class="sc-bAehkN iNRAJK">application/json</div></div><div class="sc-blIAwI eKKwxo"><div class="sc-dClGHI fdRrNy"><div class="sc-bbbBoY bBWkcI"><button><div class="sc-fYmhhH iNCOCX">Copy</div></button></div><div tabindex="0" class="sc-eVqvcJ kIppRw sc-fhfEft dFvLDb"><div class="redoc-json"><code><button class="collapser" aria-label="collapse"></button><span class="token punctuation">{</span><span class="ellipsis"></span><ul class="obj collapsible"><li><div class="hoverable "><span class="property token string">"id"</span>: <span class="token string">&quot;sZq0gcKRNrOK3hcDugzqJ&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"amount"</span>: <span class="token number">0</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"date"</span>: <span class="token string">&quot;2019-08-24&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"notes"</span>: <span class="token string">&quot;string&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"remainingBalance"</span>: <span class="token number">0</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"status"</span>: <span class="token string">&quot;pending&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"leaseId"</span>: <span class="token string">&quot;sZq0gcKRNrOK3hcDugzqJ&quot;</span></div></li></ul><span class="token punctuation">}</span></code></div></div></div></div></div></div></div></div></div></div></div><div id="tag/Payments/paths/~1leases~1{leaseId}~1tenant-payments~1{tenantPaymentId}/put" data-section-id="tag/Payments/paths/~1leases~1{leaseId}~1tenant-payments~1{tenantPaymentId}/put" class="sc-dTvVRJ gHrCVQ"><div class="sc-jJLAfE gkiSyE"><div class="sc-ggWZvA fqkwbU"><h2 class="sc-kNOymR iFSqkw"><a class="sc-kcLKEh fRdsOi" href="#tag/Payments/paths/~1leases~1{leaseId}~1tenant-payments~1{tenantPaymentId}/put" aria-label="tag/Payments/paths/~1leases~1{leaseId}~1tenant-payments~1{tenantPaymentId}/put"></a>/leases/{leaseId}/tenant-payments/{tenantPaymentId}<!-- --> </h2><div class="sc-bfjeOH txIPi"><div html="&lt;p&gt;Update information for a specific tenant payment&lt;/p&gt;
" class="sc-eVqvcJ sc-fszimp kIppRw kbZred"><p>Update information for a specific tenant payment</p>
</div></div><div><h5 class="sc-eqYatC czjApA">path<!-- --> Parameters</h5><table class="sc-eqNDNG icJLQx"><tbody><tr class=""><td kind="field" title="leaseId" class="sc-kCuUfV sc-fbQrwq gdmNWp dFOJWJ"><span class="sc-hwddKA cteAyA"></span><span class="property-name">leaseId</span><div class="sc-bEjUoa sc-iIvHqT lhyyLL crXmiY">required</div></td><td class="sc-gGKoUb ixGaBD"><div><div><span class="sc-bEjUoa sc-boKDdR lhyyLL jYezsP"></span><span class="sc-bEjUoa sc-fOOuSg lhyyLL dbKJYq">string</span><span class="sc-bEjUoa sc-hdBJTi lhyyLL nwQTz"> (<!-- -->NanoID<!-- -->) </span><span class="sc-bEjUoa sc-cpclqO lhyyLL UZcrz">^[0-9A-Za-z]{11}$</span></div> <div><span class="sc-bEjUoa lhyyLL"> <!-- -->Example:<!-- --> </span> <span class="sc-bEjUoa sc-dTWiOz lhyyLL kMQdIk">sZq0gcKRNrOK3hcDugzqJ</span></div><div><div html="&lt;p&gt;Resource identifier&lt;/p&gt;
" class="sc-eVqvcJ sc-fszimp kIppRw drqpJr"><p>Resource identifier</p>
</div></div></div></td></tr><tr class="last "><td kind="field" title="tenantPaymentId" class="sc-kCuUfV sc-fbQrwq gdmNWp dFOJWJ"><span class="sc-hwddKA cteAyA"></span><span class="property-name">tenantPaymentId</span><div class="sc-bEjUoa sc-iIvHqT lhyyLL crXmiY">required</div></td><td class="sc-gGKoUb ixGaBD"><div><div><span class="sc-bEjUoa sc-boKDdR lhyyLL jYezsP"></span><span class="sc-bEjUoa sc-fOOuSg lhyyLL dbKJYq">string</span></div> <div><div html="&lt;p&gt;Resource identifier&lt;/p&gt;
" class="sc-eVqvcJ sc-fszimp kIppRw drqpJr"><p>Resource identifier</p>
</div></div></div></td></tr></tbody></table></div><h5 class="sc-eqYatC czjApA">Request Body schema: <span class="sc-dNFkOE cFlAeY">application/json</span></h5><div html="" class="sc-eVqvcJ sc-fszimp kIppRw kbZred"></div><table class="sc-eqNDNG icJLQx"><tbody><tr class=""><td kind="field" title="id" class="sc-kCuUfV sc-fbQrwq gdmNWp dFOJWJ"><span class="sc-hwddKA cteAyA"></span><span class="property-name">id</span><div class="sc-bEjUoa sc-iIvHqT lhyyLL crXmiY">required</div></td><td class="sc-gGKoUb ixGaBD"><div><div><span class="sc-bEjUoa sc-boKDdR lhyyLL jYezsP"></span><span class="sc-bEjUoa sc-fOOuSg lhyyLL dbKJYq">string</span><span class="sc-bEjUoa sc-hdBJTi lhyyLL nwQTz"> (<!-- -->NanoID<!-- -->) </span><span class="sc-bEjUoa sc-cpclqO lhyyLL UZcrz">^[0-9A-Za-z]{11}$</span></div> <div><div html="&lt;p&gt;NanoID - 11 character alphanumeric identifier&lt;/p&gt;
" class="sc-eVqvcJ sc-fszimp kIppRw drqpJr"><p>NanoID - 11 character alphanumeric identifier</p>
</div></div></div></td></tr><tr class=""><td kind="field" title="amount" class="sc-kCuUfV sc-fbQrwq gdmNWp dFOJWJ"><span class="sc-hwddKA cteAyA"></span><span class="property-name">amount</span><div class="sc-bEjUoa sc-iIvHqT lhyyLL crXmiY">required</div></td><td class="sc-gGKoUb ixGaBD"><div><div><span class="sc-bEjUoa sc-boKDdR lhyyLL jYezsP"></span><span class="sc-bEjUoa sc-fOOuSg lhyyLL dbKJYq">number</span></div> <div><div html="&lt;p&gt;Amount paid&lt;/p&gt;
" class="sc-eVqvcJ sc-fszimp kIppRw drqpJr"><p>Amount paid</p>
</div></div></div></td></tr><tr class=""><td kind="field" title="date" class="sc-kCuUfV sc-fbQrwq gdmNWp dFOJWJ"><span class="sc-hwddKA cteAyA"></span><span class="property-name">date</span><div class="sc-bEjUoa sc-iIvHqT lhyyLL crXmiY">required</div></td><td class="sc-gGKoUb ixGaBD"><div><div><span class="sc-bEjUoa sc-boKDdR lhyyLL jYezsP"></span><span class="sc-bEjUoa sc-fOOuSg lhyyLL dbKJYq">string</span><span class="sc-bEjUoa sc-fOOuSg lhyyLL dbKJYq"> <!-- -->&lt;<!-- -->date<!-- -->&gt;<!-- --> </span></div> <div><div html="&lt;p&gt;Payment date&lt;/p&gt;
" class="sc-eVqvcJ sc-fszimp kIppRw drqpJr"><p>Payment date</p>
</div></div></div></td></tr><tr class=""><td kind="field" title="notes" class="sc-kCuUfV sc-fbQrwq gdmNWp dFOJWJ"><span class="sc-hwddKA cteAyA"></span><span class="property-name">notes</span></td><td class="sc-gGKoUb ixGaBD"><div><div><span class="sc-bEjUoa sc-boKDdR lhyyLL jYezsP"></span><span class="sc-bEjUoa sc-fOOuSg lhyyLL dbKJYq">string</span></div> <div><div html="" class="sc-eVqvcJ sc-fszimp kIppRw drqpJr"></div></div></div></td></tr><tr class=""><td kind="field" title="remainingBalance" class="sc-kCuUfV sc-fbQrwq gdmNWp dFOJWJ"><span class="sc-hwddKA cteAyA"></span><span class="property-name">remainingBalance</span></td><td class="sc-gGKoUb ixGaBD"><div><div><span class="sc-bEjUoa sc-boKDdR lhyyLL jYezsP"></span><span class="sc-bEjUoa sc-fOOuSg lhyyLL dbKJYq">number</span></div> <div><div html="&lt;p&gt;Remaining balance after payment&lt;/p&gt;
" class="sc-eVqvcJ sc-fszimp kIppRw drqpJr"><p>Remaining balance after payment</p>
</div></div></div></td></tr><tr class=""><td kind="field" title="status" class="sc-kCuUfV sc-fbQrwq gdmNWp dFOJWJ"><span class="sc-hwddKA cteAyA"></span><span class="property-name">status</span></td><td class="sc-gGKoUb ixGaBD"><div><div><span class="sc-bEjUoa sc-boKDdR lhyyLL jYezsP"></span><span class="sc-bEjUoa sc-fOOuSg lhyyLL dbKJYq">string</span></div><div><span class="sc-bEjUoa lhyyLL"> <!-- -->Enum<!-- -->:</span> <span class="sc-bEjUoa sc-dTWiOz lhyyLL kMQdIk">&quot;pending&quot;</span> <span class="sc-bEjUoa sc-dTWiOz lhyyLL kMQdIk">&quot;completed&quot;</span> <span class="sc-bEjUoa sc-dTWiOz lhyyLL kMQdIk">&quot;failed&quot;</span> </div> <div><div html="" class="sc-eVqvcJ sc-fszimp kIppRw drqpJr"></div></div></div></td></tr><tr class="last "><td kind="field" title="leaseId" class="sc-kCuUfV sc-fbQrwq gdmNWp dFOJWJ"><span class="sc-hwddKA cteAyA"></span><span class="property-name">leaseId</span><div class="sc-bEjUoa sc-iIvHqT lhyyLL crXmiY">required</div></td><td class="sc-gGKoUb ixGaBD"><div><div><span class="sc-bEjUoa sc-boKDdR lhyyLL jYezsP"></span><span class="sc-bEjUoa sc-fOOuSg lhyyLL dbKJYq">string</span><span class="sc-bEjUoa sc-hdBJTi lhyyLL nwQTz"> (<!-- -->NanoID<!-- -->) </span><span class="sc-bEjUoa sc-cpclqO lhyyLL UZcrz">^[0-9A-Za-z]{11}$</span></div> <div><div html="&lt;p&gt;NanoID - 11 character alphanumeric identifier&lt;/p&gt;
" class="sc-eVqvcJ sc-fszimp kIppRw drqpJr"><p>NanoID - 11 character alphanumeric identifier</p>
</div></div></div></td></tr></tbody></table><div><h3 class="sc-gDzyrw kjrVcG">Responses</h3><div><button class="sc-jIDBmd lkmdtA"><svg class="sc-dntSTA cGxVlA" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg><strong class="sc-eJvlPh fBhAXU">200<!-- --> </strong><div html="&lt;p&gt;Payment updated successfully&lt;/p&gt;
" class="sc-eVqvcJ sc-fszimp sc-etsjJW kIppRw jnwENr ljKHqG"><p>Payment updated successfully</p>
</div></button></div></div></div><div class="sc-jwTyAe sc-hjsuWn bDYKKx FFPsr"><div class="sc-eZSpzM jjnszm"><button class="sc-buTqWO iPCVMX"><span type="put" class="sc-fQLpxn dBzsUh http-verb put">put</span><span class="sc-jvKoal kZcHWP">/leases/{leaseId}/tenant-payments/{tenantPaymentId}</span><svg class="sc-dntSTA iuNpUs" style="margin-right:-25px" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg></button><div aria-hidden="true" class="sc-ecJghI ga-DQLq"><div class="sc-iyBeIh icOxsG"><div html="&lt;p&gt;Development server&lt;/p&gt;
" class="sc-eVqvcJ sc-fszimp kIppRw drqpJr"><p>Development server</p>
</div><div tabindex="0" role="button"><div class="sc-xKhEK okJpy"><span>http://localhost:8089</span>/leases/{leaseId}/tenant-payments/{tenantPaymentId}</div></div></div><div class="sc-iyBeIh icOxsG"><div html="&lt;p&gt;Production server&lt;/p&gt;
" class="sc-eVqvcJ sc-fszimp kIppRw drqpJr"><p>Production server</p>
</div><div tabindex="0" role="button"><div class="sc-xKhEK okJpy"><span>https://api.rentreport.com</span>/leases/{leaseId}/tenant-payments/{tenantPaymentId}</div></div></div></div></div><div><h3 class="sc-lgpSej drJHMo"> <!-- -->Request samples<!-- --> </h3><div class="sc-cOpnSz fyxuKi" data-rttabs="true"><ul class="react-tabs__tab-list" role="tablist"><li class="react-tabs__tab react-tabs__tab--selected" role="tab" id="tab«R2abiq»0" aria-selected="true" aria-disabled="false" aria-controls="panel«R2abiq»0" tabindex="0" data-rttab="true">Payload</li></ul><div class="react-tabs__tab-panel react-tabs__tab-panel--selected" role="tabpanel" id="panel«R2abiq»0" aria-labelledby="tab«R2abiq»0"><div><div class="sc-bSFBcf iLdyBp"><span class="sc-gahYZc cXitJ">Content type</span><div class="sc-bAehkN iNRAJK">application/json</div></div><div class="sc-blIAwI eKKwxo"><div class="sc-dClGHI fdRrNy"><div class="sc-bbbBoY bBWkcI"><button><div class="sc-fYmhhH iNCOCX">Copy</div></button></div><div tabindex="0" class="sc-eVqvcJ kIppRw sc-fhfEft dFvLDb"><div class="redoc-json"><code><span class="token string">&quot;sZq0gcKRNrOK3hcDugzqJ&quot;</span></code></div></div></div></div></div></div></div></div><div><h3 class="sc-lgpSej drJHMo"> <!-- -->Response samples<!-- --> </h3><div class="sc-cOpnSz fyxuKi" data-rttabs="true"><ul class="react-tabs__tab-list" role="tablist"><li class="tab-success react-tabs__tab--selected" role="tab" id="tab«R2ebiq»0" aria-selected="true" aria-disabled="false" aria-controls="panel«R2ebiq»0" tabindex="0" data-rttab="true">200</li></ul><div class="react-tabs__tab-panel react-tabs__tab-panel--selected" role="tabpanel" id="panel«R2ebiq»0" aria-labelledby="tab«R2ebiq»0"><div><div class="sc-bSFBcf iLdyBp"><span class="sc-gahYZc cXitJ">Content type</span><div class="sc-bAehkN iNRAJK">application/json</div></div><div class="sc-blIAwI eKKwxo"><div class="sc-dClGHI fdRrNy"><div class="sc-bbbBoY bBWkcI"><button><div class="sc-fYmhhH iNCOCX">Copy</div></button></div><div tabindex="0" class="sc-eVqvcJ kIppRw sc-fhfEft dFvLDb"><div class="redoc-json"><code><button class="collapser" aria-label="collapse"></button><span class="token punctuation">{</span><span class="ellipsis"></span><ul class="obj collapsible"><li><div class="hoverable "><span class="property token string">"id"</span>: <span class="token string">&quot;sZq0gcKRNrOK3hcDugzqJ&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"amount"</span>: <span class="token number">0</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"date"</span>: <span class="token string">&quot;2019-08-24&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"notes"</span>: <span class="token string">&quot;string&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"remainingBalance"</span>: <span class="token number">0</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"status"</span>: <span class="token string">&quot;pending&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"leaseId"</span>: <span class="token string">&quot;sZq0gcKRNrOK3hcDugzqJ&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"userId"</span>: <span class="token string">&quot;sZq0gcKRNrOK3hcDugzqJ&quot;</span></div></li></ul><span class="token punctuation">}</span></code></div></div></div></div></div></div></div></div></div></div></div><div id="tag/Payments/paths/~1leases~1{leaseId}~1tenant-payments~1{tenantPaymentId}/delete" data-section-id="tag/Payments/paths/~1leases~1{leaseId}~1tenant-payments~1{tenantPaymentId}/delete" class="sc-dTvVRJ gHrCVQ"><div class="sc-jJLAfE gkiSyE"><div class="sc-ggWZvA fqkwbU"><h2 class="sc-kNOymR iFSqkw"><a class="sc-kcLKEh fRdsOi" href="#tag/Payments/paths/~1leases~1{leaseId}~1tenant-payments~1{tenantPaymentId}/delete" aria-label="tag/Payments/paths/~1leases~1{leaseId}~1tenant-payments~1{tenantPaymentId}/delete"></a>/leases/{leaseId}/tenant-payments/{tenantPaymentId}<!-- --> </h2><div class="sc-bfjeOH txIPi"><div html="&lt;p&gt;Delete a specific tenant payment record&lt;/p&gt;
" class="sc-eVqvcJ sc-fszimp kIppRw kbZred"><p>Delete a specific tenant payment record</p>
</div></div><div><h5 class="sc-eqYatC czjApA">path<!-- --> Parameters</h5><table class="sc-eqNDNG icJLQx"><tbody><tr class=""><td kind="field" title="leaseId" class="sc-kCuUfV sc-fbQrwq gdmNWp dFOJWJ"><span class="sc-hwddKA cteAyA"></span><span class="property-name">leaseId</span><div class="sc-bEjUoa sc-iIvHqT lhyyLL crXmiY">required</div></td><td class="sc-gGKoUb ixGaBD"><div><div><span class="sc-bEjUoa sc-boKDdR lhyyLL jYezsP"></span><span class="sc-bEjUoa sc-fOOuSg lhyyLL dbKJYq">string</span><span class="sc-bEjUoa sc-hdBJTi lhyyLL nwQTz"> (<!-- -->NanoID<!-- -->) </span><span class="sc-bEjUoa sc-cpclqO lhyyLL UZcrz">^[0-9A-Za-z]{11}$</span></div> <div><span class="sc-bEjUoa lhyyLL"> <!-- -->Example:<!-- --> </span> <span class="sc-bEjUoa sc-dTWiOz lhyyLL kMQdIk">sZq0gcKRNrOK3hcDugzqJ</span></div><div><div html="&lt;p&gt;Resource identifier&lt;/p&gt;
" class="sc-eVqvcJ sc-fszimp kIppRw drqpJr"><p>Resource identifier</p>
</div></div></div></td></tr><tr class="last "><td kind="field" title="tenantPaymentId" class="sc-kCuUfV sc-fbQrwq gdmNWp dFOJWJ"><span class="sc-hwddKA cteAyA"></span><span class="property-name">tenantPaymentId</span><div class="sc-bEjUoa sc-iIvHqT lhyyLL crXmiY">required</div></td><td class="sc-gGKoUb ixGaBD"><div><div><span class="sc-bEjUoa sc-boKDdR lhyyLL jYezsP"></span><span class="sc-bEjUoa sc-fOOuSg lhyyLL dbKJYq">string</span></div> <div><div html="&lt;p&gt;Resource identifier&lt;/p&gt;
" class="sc-eVqvcJ sc-fszimp kIppRw drqpJr"><p>Resource identifier</p>
</div></div></div></td></tr></tbody></table></div><div><h3 class="sc-gDzyrw kjrVcG">Responses</h3><div><button class="sc-jIDBmd lkmdtA"><svg class="sc-dntSTA cGxVlA" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg><strong class="sc-eJvlPh fBhAXU">204<!-- --> </strong><div html="&lt;p&gt;Payment deleted successfully&lt;/p&gt;
" class="sc-eVqvcJ sc-fszimp sc-etsjJW kIppRw jnwENr ljKHqG"><p>Payment deleted successfully</p>
</div></button></div></div></div><div class="sc-jwTyAe sc-hjsuWn bDYKKx FFPsr"><div class="sc-eZSpzM jjnszm"><button class="sc-buTqWO iPCVMX"><span type="delete" class="sc-fQLpxn gKcHYQ http-verb delete">delete</span><span class="sc-jvKoal kZcHWP">/leases/{leaseId}/tenant-payments/{tenantPaymentId}</span><svg class="sc-dntSTA iuNpUs" style="margin-right:-25px" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg></button><div aria-hidden="true" class="sc-ecJghI ga-DQLq"><div class="sc-iyBeIh icOxsG"><div html="&lt;p&gt;Development server&lt;/p&gt;
" class="sc-eVqvcJ sc-fszimp kIppRw drqpJr"><p>Development server</p>
</div><div tabindex="0" role="button"><div class="sc-xKhEK okJpy"><span>http://localhost:8089</span>/leases/{leaseId}/tenant-payments/{tenantPaymentId}</div></div></div><div class="sc-iyBeIh icOxsG"><div html="&lt;p&gt;Production server&lt;/p&gt;
" class="sc-eVqvcJ sc-fszimp kIppRw drqpJr"><p>Production server</p>
</div><div tabindex="0" role="button"><div class="sc-xKhEK okJpy"><span>https://api.rentreport.com</span>/leases/{leaseId}/tenant-payments/{tenantPaymentId}</div></div></div></div></div><div><h3 class="sc-lgpSej drJHMo"> <!-- -->Response samples<!-- --> </h3><div class="sc-cOpnSz fyxuKi" data-rttabs="true"><ul class="react-tabs__tab-list" role="tablist"><li class="tab-success react-tabs__tab--selected" role="tab" id="tab«R2eciq»0" aria-selected="true" aria-disabled="false" aria-controls="panel«R2eciq»0" tabindex="0" data-rttab="true">204</li></ul><div class="react-tabs__tab-panel react-tabs__tab-panel--selected" role="tabpanel" id="panel«R2eciq»0" aria-labelledby="tab«R2eciq»0"><div><div class="sc-bSFBcf iLdyBp"><span class="sc-gahYZc cXitJ">Content type</span><div class="sc-bAehkN iNRAJK">application/json</div></div><div class="sc-blIAwI eKKwxo"><div class="sc-dClGHI fdRrNy"><div class="sc-bbbBoY bBWkcI"><button><div class="sc-fYmhhH iNCOCX">Copy</div></button></div><div tabindex="0" class="sc-eVqvcJ kIppRw sc-fhfEft dFvLDb"><div class="redoc-json"><code><span class="token punctuation">{ }</span></code></div></div></div></div></div></div></div></div></div></div></div><div id="tag/Documents" data-section-id="tag/Documents" class="sc-dTvVRJ bPmFpz"><div class="sc-jJLAfE gkiSyE"><div class="sc-ggWZvA fqkwbU"><h2 class="sc-kNOymR iFSqkw"><a class="sc-kcLKEh fRdsOi" href="#tag/Documents" aria-label="tag/Documents"></a>Documents</h2></div></div><div class="sc-ggWZvA dCzIPc"><div class="sc-eVqvcJ sc-fszimp kIppRw kbZred redoc-markdown " html="&lt;p&gt;Document management and downloads&lt;/p&gt;
"><p>Document management and downloads</p>
</div></div></div><div id="tag/Documents/paths/~1v1~1documents~1{fileId}/get" data-section-id="tag/Documents/paths/~1v1~1documents~1{fileId}/get" class="sc-dTvVRJ gHrCVQ"><div class="sc-jJLAfE gkiSyE"><div class="sc-ggWZvA fqkwbU"><h2 class="sc-kNOymR iFSqkw"><a class="sc-kcLKEh fRdsOi" href="#tag/Documents/paths/~1v1~1documents~1{fileId}/get" aria-label="tag/Documents/paths/~1v1~1documents~1{fileId}/get"></a>/v1/documents/{fileId}<!-- --> </h2><div class="sc-bfjeOH txIPi"><div html="&lt;p&gt;下载文档文件 (RESTful)&lt;/p&gt;
" class="sc-eVqvcJ sc-fszimp kIppRw kbZred"><p>下载文档文件 (RESTful)</p>
</div></div><div><h5 class="sc-eqYatC czjApA">path<!-- --> Parameters</h5><table class="sc-eqNDNG icJLQx"><tbody><tr class="last "><td kind="field" title="fileId" class="sc-kCuUfV sc-fbQrwq gdmNWp dFOJWJ"><span class="sc-hwddKA cteAyA"></span><span class="property-name">fileId</span><div class="sc-bEjUoa sc-iIvHqT lhyyLL crXmiY">required</div></td><td class="sc-gGKoUb ixGaBD"><div><div><span class="sc-bEjUoa sc-boKDdR lhyyLL jYezsP"></span><span class="sc-bEjUoa sc-fOOuSg lhyyLL dbKJYq">string</span></div> <div><div html="&lt;p&gt;文件ID&lt;/p&gt;
" class="sc-eVqvcJ sc-fszimp kIppRw drqpJr"><p>文件ID</p>
</div></div></div></td></tr></tbody></table></div><div><h3 class="sc-gDzyrw kjrVcG">Responses</h3><div><button class="sc-jIDBmd lkmdtA"><svg class="sc-dntSTA cGxVlA" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg><strong class="sc-eJvlPh fBhAXU">200<!-- --> </strong><div html="&lt;p&gt;文件下载成功&lt;/p&gt;
" class="sc-eVqvcJ sc-fszimp sc-etsjJW kIppRw jnwENr ljKHqG"><p>文件下载成功</p>
</div></button></div><div><button class="sc-jIDBmd kQCDrg" disabled=""><strong class="sc-eJvlPh fBhAXU">404<!-- --> </strong><div html="&lt;p&gt;文件不存在&lt;/p&gt;
" class="sc-eVqvcJ sc-fszimp sc-etsjJW kIppRw jnwENr ljKHqG"><p>文件不存在</p>
</div></button></div></div></div><div class="sc-jwTyAe sc-hjsuWn bDYKKx FFPsr"><div class="sc-eZSpzM jjnszm"><button class="sc-buTqWO iPCVMX"><span type="get" class="sc-fQLpxn dynMBc http-verb get">get</span><span class="sc-jvKoal kZcHWP">/v1/documents/{fileId}</span><svg class="sc-dntSTA iuNpUs" style="margin-right:-25px" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg></button><div aria-hidden="true" class="sc-ecJghI ga-DQLq"><div class="sc-iyBeIh icOxsG"><div html="&lt;p&gt;Development server&lt;/p&gt;
" class="sc-eVqvcJ sc-fszimp kIppRw drqpJr"><p>Development server</p>
</div><div tabindex="0" role="button"><div class="sc-xKhEK okJpy"><span>http://localhost:8089</span>/v1/documents/{fileId}</div></div></div><div class="sc-iyBeIh icOxsG"><div html="&lt;p&gt;Production server&lt;/p&gt;
" class="sc-eVqvcJ sc-fszimp kIppRw drqpJr"><p>Production server</p>
</div><div tabindex="0" role="button"><div class="sc-xKhEK okJpy"><span>https://api.rentreport.com</span>/v1/documents/{fileId}</div></div></div></div></div></div></div></div><div id="tag/Referrals" data-section-id="tag/Referrals" class="sc-dTvVRJ bPmFpz"><div class="sc-jJLAfE gkiSyE"><div class="sc-ggWZvA fqkwbU"><h2 class="sc-kNOymR iFSqkw"><a class="sc-kcLKEh fRdsOi" href="#tag/Referrals" aria-label="tag/Referrals"></a>Referrals</h2></div></div><div class="sc-ggWZvA dCzIPc"><div class="sc-eVqvcJ sc-fszimp kIppRw kbZred redoc-markdown " html="&lt;p&gt;Referral code management&lt;/p&gt;
"><p>Referral code management</p>
</div></div></div><div id="tag/Referrals/paths/~1v1~1referral-codes~1{code}~1validation/get" data-section-id="tag/Referrals/paths/~1v1~1referral-codes~1{code}~1validation/get" class="sc-dTvVRJ gHrCVQ"><div class="sc-jJLAfE gkiSyE"><div class="sc-ggWZvA fqkwbU"><h2 class="sc-kNOymR iFSqkw"><a class="sc-kcLKEh fRdsOi" href="#tag/Referrals/paths/~1v1~1referral-codes~1{code}~1validation/get" aria-label="tag/Referrals/paths/~1v1~1referral-codes~1{code}~1validation/get"></a>/v1/referral-codes/{code}/validation<!-- --> </h2><div class="sc-bfjeOH txIPi"><div html="&lt;p&gt;验证推荐码是否有效 (RESTful)&lt;/p&gt;
" class="sc-eVqvcJ sc-fszimp kIppRw kbZred"><p>验证推荐码是否有效 (RESTful)</p>
</div></div><div><h5 class="sc-eqYatC czjApA">path<!-- --> Parameters</h5><table class="sc-eqNDNG icJLQx"><tbody><tr class="last "><td kind="field" title="code" class="sc-kCuUfV sc-fbQrwq gdmNWp dFOJWJ"><span class="sc-hwddKA cteAyA"></span><span class="property-name">code</span><div class="sc-bEjUoa sc-iIvHqT lhyyLL crXmiY">required</div></td><td class="sc-gGKoUb ixGaBD"><div><div><span class="sc-bEjUoa sc-boKDdR lhyyLL jYezsP"></span><span class="sc-bEjUoa sc-fOOuSg lhyyLL dbKJYq">string</span></div> <div><div html="&lt;p&gt;推荐码&lt;/p&gt;
" class="sc-eVqvcJ sc-fszimp kIppRw drqpJr"><p>推荐码</p>
</div></div></div></td></tr></tbody></table></div><div><h3 class="sc-gDzyrw kjrVcG">Responses</h3><div><button class="sc-jIDBmd lkmdtA"><svg class="sc-dntSTA cGxVlA" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg><strong class="sc-eJvlPh fBhAXU">200<!-- --> </strong><div html="&lt;p&gt;推荐码验证成功&lt;/p&gt;
" class="sc-eVqvcJ sc-fszimp sc-etsjJW kIppRw jnwENr ljKHqG"><p>推荐码验证成功</p>
</div></button></div><div><button class="sc-jIDBmd kQCDrg" disabled=""><strong class="sc-eJvlPh fBhAXU">404<!-- --> </strong><div html="&lt;p&gt;推荐码不存在&lt;/p&gt;
" class="sc-eVqvcJ sc-fszimp sc-etsjJW kIppRw jnwENr ljKHqG"><p>推荐码不存在</p>
</div></button></div></div></div><div class="sc-jwTyAe sc-hjsuWn bDYKKx FFPsr"><div class="sc-eZSpzM jjnszm"><button class="sc-buTqWO iPCVMX"><span type="get" class="sc-fQLpxn dynMBc http-verb get">get</span><span class="sc-jvKoal kZcHWP">/v1/referral-codes/{code}/validation</span><svg class="sc-dntSTA iuNpUs" style="margin-right:-25px" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg></button><div aria-hidden="true" class="sc-ecJghI ga-DQLq"><div class="sc-iyBeIh icOxsG"><div html="&lt;p&gt;Development server&lt;/p&gt;
" class="sc-eVqvcJ sc-fszimp kIppRw drqpJr"><p>Development server</p>
</div><div tabindex="0" role="button"><div class="sc-xKhEK okJpy"><span>http://localhost:8089</span>/v1/referral-codes/{code}/validation</div></div></div><div class="sc-iyBeIh icOxsG"><div html="&lt;p&gt;Production server&lt;/p&gt;
" class="sc-eVqvcJ sc-fszimp kIppRw drqpJr"><p>Production server</p>
</div><div tabindex="0" role="button"><div class="sc-xKhEK okJpy"><span>https://api.rentreport.com</span>/v1/referral-codes/{code}/validation</div></div></div></div></div><div><h3 class="sc-lgpSej drJHMo"> <!-- -->Response samples<!-- --> </h3><div class="sc-cOpnSz fyxuKi" data-rttabs="true"><ul class="react-tabs__tab-list" role="tablist"><li class="tab-success react-tabs__tab--selected" role="tab" id="tab«R175jq»0" aria-selected="true" aria-disabled="false" aria-controls="panel«R175jq»0" tabindex="0" data-rttab="true">200</li></ul><div class="react-tabs__tab-panel react-tabs__tab-panel--selected" role="tabpanel" id="panel«R175jq»0" aria-labelledby="tab«R175jq»0"><div><div class="sc-bSFBcf iLdyBp"><span class="sc-gahYZc cXitJ">Content type</span><div class="sc-bAehkN iNRAJK">application/json</div></div><div class="sc-blIAwI eKKwxo"><div class="sc-dClGHI fdRrNy"><div class="sc-bbbBoY bBWkcI"><button><div class="sc-fYmhhH iNCOCX">Copy</div></button></div><div tabindex="0" class="sc-eVqvcJ kIppRw sc-fhfEft dFvLDb"><div class="redoc-json"><code><button class="collapser" aria-label="collapse"></button><span class="token punctuation">{</span><span class="ellipsis"></span><ul class="obj collapsible"><li><div class="hoverable "><span class="property token string">"valid"</span>: <span class="token boolean">true</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"message"</span>: <span class="token string">&quot;string&quot;</span></div></li></ul><span class="token punctuation">}</span></code></div></div></div></div></div></div></div></div></div></div></div><div id="tag/Referrals/paths/~1v1~1referral-code-applications/post" data-section-id="tag/Referrals/paths/~1v1~1referral-code-applications/post" class="sc-dTvVRJ gHrCVQ"><div class="sc-jJLAfE gkiSyE"><div class="sc-ggWZvA fqkwbU"><h2 class="sc-kNOymR iFSqkw"><a class="sc-kcLKEh fRdsOi" href="#tag/Referrals/paths/~1v1~1referral-code-applications/post" aria-label="tag/Referrals/paths/~1v1~1referral-code-applications/post"></a>/v1/referral-code-applications<!-- --> </h2><div class="sc-bfjeOH txIPi"><div html="&lt;p&gt;应用推荐码 (RESTful)&lt;/p&gt;
" class="sc-eVqvcJ sc-fszimp kIppRw kbZred"><p>应用推荐码 (RESTful)</p>
</div></div><h5 class="sc-eqYatC czjApA">Request Body schema: <span class="sc-dNFkOE cFlAeY">application/json</span></h5><div html="" class="sc-eVqvcJ sc-fszimp kIppRw kbZred"></div><table class="sc-eqNDNG icJLQx"><tbody><tr class="last "><td kind="field" title="code" class="sc-kCuUfV sc-fbQrwq gdmNWp dFOJWJ"><span class="sc-hwddKA cteAyA"></span><span class="property-name">code</span><div class="sc-bEjUoa sc-iIvHqT lhyyLL crXmiY">required</div></td><td class="sc-gGKoUb ixGaBD"><div><div><span class="sc-bEjUoa sc-boKDdR lhyyLL jYezsP"></span><span class="sc-bEjUoa sc-fOOuSg lhyyLL dbKJYq">string</span></div> <div><div html="&lt;p&gt;推荐码&lt;/p&gt;
" class="sc-eVqvcJ sc-fszimp kIppRw drqpJr"><p>推荐码</p>
</div></div></div></td></tr></tbody></table><div><h3 class="sc-gDzyrw kjrVcG">Responses</h3><div><button class="sc-jIDBmd lkmdtA"><svg class="sc-dntSTA cGxVlA" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg><strong class="sc-eJvlPh fBhAXU">200<!-- --> </strong><div html="&lt;p&gt;推荐码应用成功&lt;/p&gt;
" class="sc-eVqvcJ sc-fszimp sc-etsjJW kIppRw jnwENr ljKHqG"><p>推荐码应用成功</p>
</div></button></div><div><button class="sc-jIDBmd kQCDrg" disabled=""><strong class="sc-eJvlPh fBhAXU">400<!-- --> </strong><div html="&lt;p&gt;推荐码无效或已使用&lt;/p&gt;
" class="sc-eVqvcJ sc-fszimp sc-etsjJW kIppRw jnwENr ljKHqG"><p>推荐码无效或已使用</p>
</div></button></div></div></div><div class="sc-jwTyAe sc-hjsuWn bDYKKx FFPsr"><div class="sc-eZSpzM jjnszm"><button class="sc-buTqWO iPCVMX"><span type="post" class="sc-fQLpxn kwcmyC http-verb post">post</span><span class="sc-jvKoal kZcHWP">/v1/referral-code-applications</span><svg class="sc-dntSTA iuNpUs" style="margin-right:-25px" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg></button><div aria-hidden="true" class="sc-ecJghI ga-DQLq"><div class="sc-iyBeIh icOxsG"><div html="&lt;p&gt;Development server&lt;/p&gt;
" class="sc-eVqvcJ sc-fszimp kIppRw drqpJr"><p>Development server</p>
</div><div tabindex="0" role="button"><div class="sc-xKhEK okJpy"><span>http://localhost:8089</span>/v1/referral-code-applications</div></div></div><div class="sc-iyBeIh icOxsG"><div html="&lt;p&gt;Production server&lt;/p&gt;
" class="sc-eVqvcJ sc-fszimp kIppRw drqpJr"><p>Production server</p>
</div><div tabindex="0" role="button"><div class="sc-xKhEK okJpy"><span>https://api.rentreport.com</span>/v1/referral-code-applications</div></div></div></div></div><div><h3 class="sc-lgpSej drJHMo"> <!-- -->Request samples<!-- --> </h3><div class="sc-cOpnSz fyxuKi" data-rttabs="true"><ul class="react-tabs__tab-list" role="tablist"><li class="react-tabs__tab react-tabs__tab--selected" role="tab" id="tab«R156jq»0" aria-selected="true" aria-disabled="false" aria-controls="panel«R156jq»0" tabindex="0" data-rttab="true">Payload</li></ul><div class="react-tabs__tab-panel react-tabs__tab-panel--selected" role="tabpanel" id="panel«R156jq»0" aria-labelledby="tab«R156jq»0"><div><div class="sc-bSFBcf iLdyBp"><span class="sc-gahYZc cXitJ">Content type</span><div class="sc-bAehkN iNRAJK">application/json</div></div><div class="sc-blIAwI eKKwxo"><div class="sc-dClGHI fdRrNy"><div class="sc-bbbBoY bBWkcI"><button><div class="sc-fYmhhH iNCOCX">Copy</div></button></div><div tabindex="0" class="sc-eVqvcJ kIppRw sc-fhfEft dFvLDb"><div class="redoc-json"><code><button class="collapser" aria-label="collapse"></button><span class="token punctuation">{</span><span class="ellipsis"></span><ul class="obj collapsible"><li><div class="hoverable "><span class="property token string">"code"</span>: <span class="token string">&quot;string&quot;</span></div></li></ul><span class="token punctuation">}</span></code></div></div></div></div></div></div></div></div><div><h3 class="sc-lgpSej drJHMo"> <!-- -->Response samples<!-- --> </h3><div class="sc-cOpnSz fyxuKi" data-rttabs="true"><ul class="react-tabs__tab-list" role="tablist"><li class="tab-success react-tabs__tab--selected" role="tab" id="tab«R176jq»0" aria-selected="true" aria-disabled="false" aria-controls="panel«R176jq»0" tabindex="0" data-rttab="true">200</li></ul><div class="react-tabs__tab-panel react-tabs__tab-panel--selected" role="tabpanel" id="panel«R176jq»0" aria-labelledby="tab«R176jq»0"><div><div class="sc-bSFBcf iLdyBp"><span class="sc-gahYZc cXitJ">Content type</span><div class="sc-bAehkN iNRAJK">application/json</div></div><div class="sc-blIAwI eKKwxo"><div class="sc-dClGHI fdRrNy"><div class="sc-bbbBoY bBWkcI"><button><div class="sc-fYmhhH iNCOCX">Copy</div></button></div><div tabindex="0" class="sc-eVqvcJ kIppRw sc-fhfEft dFvLDb"><div class="redoc-json"><code><button class="collapser" aria-label="collapse"></button><span class="token punctuation">{</span><span class="ellipsis"></span><ul class="obj collapsible"><li><div class="hoverable "><span class="property token string">"success"</span>: <span class="token boolean">true</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"message"</span>: <span class="token string">&quot;string&quot;</span></div></li></ul><span class="token punctuation">}</span></code></div></div></div></div></div></div></div></div></div></div></div><div id="tag/Subscriptions" data-section-id="tag/Subscriptions" class="sc-dTvVRJ bPmFpz"><div class="sc-jJLAfE gkiSyE"><div class="sc-ggWZvA fqkwbU"><h2 class="sc-kNOymR iFSqkw"><a class="sc-kcLKEh fRdsOi" href="#tag/Subscriptions" aria-label="tag/Subscriptions"></a>Subscriptions</h2></div></div></div><div id="tag/Subscriptions/paths/~1v1~1subscriptions~1{id}/delete" data-section-id="tag/Subscriptions/paths/~1v1~1subscriptions~1{id}/delete" class="sc-dTvVRJ gHrCVQ"><div class="sc-jJLAfE gkiSyE"><div class="sc-ggWZvA fqkwbU"><h2 class="sc-kNOymR iFSqkw"><a class="sc-kcLKEh fRdsOi" href="#tag/Subscriptions/paths/~1v1~1subscriptions~1{id}/delete" aria-label="tag/Subscriptions/paths/~1v1~1subscriptions~1{id}/delete"></a>/v1/subscriptions/{id}<!-- --> </h2><div class="sc-bfjeOH txIPi"><div html="&lt;p&gt;取消订阅 (RESTful)&lt;/p&gt;
" class="sc-eVqvcJ sc-fszimp kIppRw kbZred"><p>取消订阅 (RESTful)</p>
</div></div><div><h5 class="sc-eqYatC czjApA">path<!-- --> Parameters</h5><table class="sc-eqNDNG icJLQx"><tbody><tr class="last "><td kind="field" title="id" class="sc-kCuUfV sc-fbQrwq gdmNWp dFOJWJ"><span class="sc-hwddKA cteAyA"></span><span class="property-name">id</span><div class="sc-bEjUoa sc-iIvHqT lhyyLL crXmiY">required</div></td><td class="sc-gGKoUb ixGaBD"><div><div><span class="sc-bEjUoa sc-boKDdR lhyyLL jYezsP"></span><span class="sc-bEjUoa sc-fOOuSg lhyyLL dbKJYq">string</span></div> <div><div html="&lt;p&gt;订阅ID&lt;/p&gt;
" class="sc-eVqvcJ sc-fszimp kIppRw drqpJr"><p>订阅ID</p>
</div></div></div></td></tr></tbody></table></div><div><h3 class="sc-gDzyrw kjrVcG">Responses</h3><div><button class="sc-jIDBmd oZuve" disabled=""><strong class="sc-eJvlPh fBhAXU">204<!-- --> </strong><div html="&lt;p&gt;订阅取消成功&lt;/p&gt;
" class="sc-eVqvcJ sc-fszimp sc-etsjJW kIppRw jnwENr ljKHqG"><p>订阅取消成功</p>
</div></button></div><div><button class="sc-jIDBmd kQCDrg" disabled=""><strong class="sc-eJvlPh fBhAXU">404<!-- --> </strong><div html="&lt;p&gt;订阅不存在&lt;/p&gt;
" class="sc-eVqvcJ sc-fszimp sc-etsjJW kIppRw jnwENr ljKHqG"><p>订阅不存在</p>
</div></button></div></div></div><div class="sc-jwTyAe sc-hjsuWn bDYKKx FFPsr"><div class="sc-eZSpzM jjnszm"><button class="sc-buTqWO iPCVMX"><span type="delete" class="sc-fQLpxn gKcHYQ http-verb delete">delete</span><span class="sc-jvKoal kZcHWP">/v1/subscriptions/{id}</span><svg class="sc-dntSTA iuNpUs" style="margin-right:-25px" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg></button><div aria-hidden="true" class="sc-ecJghI ga-DQLq"><div class="sc-iyBeIh icOxsG"><div html="&lt;p&gt;Development server&lt;/p&gt;
" class="sc-eVqvcJ sc-fszimp kIppRw drqpJr"><p>Development server</p>
</div><div tabindex="0" role="button"><div class="sc-xKhEK okJpy"><span>http://localhost:8089</span>/v1/subscriptions/{id}</div></div></div><div class="sc-iyBeIh icOxsG"><div html="&lt;p&gt;Production server&lt;/p&gt;
" class="sc-eVqvcJ sc-fszimp kIppRw drqpJr"><p>Production server</p>
</div><div tabindex="0" role="button"><div class="sc-xKhEK okJpy"><span>https://api.rentreport.com</span>/v1/subscriptions/{id}</div></div></div></div></div></div></div></div><div id="tag/Invitations" data-section-id="tag/Invitations" class="sc-dTvVRJ bPmFpz"><div class="sc-jJLAfE gkiSyE"><div class="sc-ggWZvA fqkwbU"><h2 class="sc-kNOymR iFSqkw"><a class="sc-kcLKEh fRdsOi" href="#tag/Invitations" aria-label="tag/Invitations"></a>Invitations</h2></div></div></div><div id="tag/Invitations/paths/~1v1~1invitations~1{code}~1status/put" data-section-id="tag/Invitations/paths/~1v1~1invitations~1{code}~1status/put" class="sc-dTvVRJ gHrCVQ"><div class="sc-jJLAfE gkiSyE"><div class="sc-ggWZvA fqkwbU"><h2 class="sc-kNOymR iFSqkw"><a class="sc-kcLKEh fRdsOi" href="#tag/Invitations/paths/~1v1~1invitations~1{code}~1status/put" aria-label="tag/Invitations/paths/~1v1~1invitations~1{code}~1status/put"></a>/v1/invitations/{code}/status<!-- --> </h2><div class="sc-bfjeOH txIPi"><div html="&lt;p&gt;更新邀请状态 (RESTful)&lt;/p&gt;
" class="sc-eVqvcJ sc-fszimp kIppRw kbZred"><p>更新邀请状态 (RESTful)</p>
</div></div><div><h5 class="sc-eqYatC czjApA">path<!-- --> Parameters</h5><table class="sc-eqNDNG icJLQx"><tbody><tr class="last "><td kind="field" title="code" class="sc-kCuUfV sc-fbQrwq gdmNWp dFOJWJ"><span class="sc-hwddKA cteAyA"></span><span class="property-name">code</span><div class="sc-bEjUoa sc-iIvHqT lhyyLL crXmiY">required</div></td><td class="sc-gGKoUb ixGaBD"><div><div><span class="sc-bEjUoa sc-boKDdR lhyyLL jYezsP"></span><span class="sc-bEjUoa sc-fOOuSg lhyyLL dbKJYq">string</span></div> <div><div html="&lt;p&gt;邀请码&lt;/p&gt;
" class="sc-eVqvcJ sc-fszimp kIppRw drqpJr"><p>邀请码</p>
</div></div></div></td></tr></tbody></table></div><h5 class="sc-eqYatC czjApA">Request Body schema: <span class="sc-dNFkOE cFlAeY">application/json</span></h5><div html="" class="sc-eVqvcJ sc-fszimp kIppRw kbZred"></div><table class="sc-eqNDNG icJLQx"><tbody><tr class=""><td kind="field" title="status" class="sc-kCuUfV sc-fbQrwq gdmNWp dFOJWJ"><span class="sc-hwddKA cteAyA"></span><span class="property-name">status</span><div class="sc-bEjUoa sc-iIvHqT lhyyLL crXmiY">required</div></td><td class="sc-gGKoUb ixGaBD"><div><div><span class="sc-bEjUoa sc-boKDdR lhyyLL jYezsP"></span><span class="sc-bEjUoa sc-fOOuSg lhyyLL dbKJYq">string</span></div><div><span class="sc-bEjUoa lhyyLL"> <!-- -->Enum<!-- -->:</span> <span class="sc-bEjUoa sc-dTWiOz lhyyLL kMQdIk">&quot;active&quot;</span> <span class="sc-bEjUoa sc-dTWiOz lhyyLL kMQdIk">&quot;expired&quot;</span> <span class="sc-bEjUoa sc-dTWiOz lhyyLL kMQdIk">&quot;cancelled&quot;</span> </div> <div><div html="&lt;p&gt;邀请状态&lt;/p&gt;
" class="sc-eVqvcJ sc-fszimp kIppRw drqpJr"><p>邀请状态</p>
</div></div></div></td></tr><tr class="last "><td kind="field" title="leaseId" class="sc-kCuUfV sc-fbQrwq gdmNWp dFOJWJ"><span class="sc-hwddKA cteAyA"></span><span class="property-name">leaseId</span></td><td class="sc-gGKoUb ixGaBD"><div><div><span class="sc-bEjUoa sc-boKDdR lhyyLL jYezsP"></span><span class="sc-bEjUoa sc-fOOuSg lhyyLL dbKJYq">string</span><span class="sc-bEjUoa sc-hdBJTi lhyyLL nwQTz"> (<!-- -->NanoID<!-- -->) </span><span class="sc-bEjUoa sc-cpclqO lhyyLL UZcrz">^[0-9A-Za-z]{11}$</span></div> <div><div html="&lt;p&gt;关联的租约ID&lt;/p&gt;
" class="sc-eVqvcJ sc-fszimp kIppRw drqpJr"><p>关联的租约ID</p>
</div></div></div></td></tr></tbody></table><div><h3 class="sc-gDzyrw kjrVcG">Responses</h3><div><button class="sc-jIDBmd lkmdtA"><svg class="sc-dntSTA cGxVlA" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg><strong class="sc-eJvlPh fBhAXU">200<!-- --> </strong><div html="&lt;p&gt;邀请状态更新成功&lt;/p&gt;
" class="sc-eVqvcJ sc-fszimp sc-etsjJW kIppRw jnwENr ljKHqG"><p>邀请状态更新成功</p>
</div></button></div><div><button class="sc-jIDBmd kQCDrg" disabled=""><strong class="sc-eJvlPh fBhAXU">404<!-- --> </strong><div html="&lt;p&gt;邀请不存在&lt;/p&gt;
" class="sc-eVqvcJ sc-fszimp sc-etsjJW kIppRw jnwENr ljKHqG"><p>邀请不存在</p>
</div></button></div></div></div><div class="sc-jwTyAe sc-hjsuWn bDYKKx FFPsr"><div class="sc-eZSpzM jjnszm"><button class="sc-buTqWO iPCVMX"><span type="put" class="sc-fQLpxn dBzsUh http-verb put">put</span><span class="sc-jvKoal kZcHWP">/v1/invitations/{code}/status</span><svg class="sc-dntSTA iuNpUs" style="margin-right:-25px" version="1.1" viewBox="0 0 24 24" x="0" xmlns="http://www.w3.org/2000/svg" y="0" aria-hidden="true"><polygon points="17.3 8.3 12 13.6 6.7 8.3 5.3 9.7 12 16.4 18.7 9.7 "></polygon></svg></button><div aria-hidden="true" class="sc-ecJghI ga-DQLq"><div class="sc-iyBeIh icOxsG"><div html="&lt;p&gt;Development server&lt;/p&gt;
" class="sc-eVqvcJ sc-fszimp kIppRw drqpJr"><p>Development server</p>
</div><div tabindex="0" role="button"><div class="sc-xKhEK okJpy"><span>http://localhost:8089</span>/v1/invitations/{code}/status</div></div></div><div class="sc-iyBeIh icOxsG"><div html="&lt;p&gt;Production server&lt;/p&gt;
" class="sc-eVqvcJ sc-fszimp kIppRw drqpJr"><p>Production server</p>
</div><div tabindex="0" role="button"><div class="sc-xKhEK okJpy"><span>https://api.rentreport.com</span>/v1/invitations/{code}/status</div></div></div></div></div><div><h3 class="sc-lgpSej drJHMo"> <!-- -->Request samples<!-- --> </h3><div class="sc-cOpnSz fyxuKi" data-rttabs="true"><ul class="react-tabs__tab-list" role="tablist"><li class="react-tabs__tab react-tabs__tab--selected" role="tab" id="tab«Rijkq»0" aria-selected="true" aria-disabled="false" aria-controls="panel«Rijkq»0" tabindex="0" data-rttab="true">Payload</li></ul><div class="react-tabs__tab-panel react-tabs__tab-panel--selected" role="tabpanel" id="panel«Rijkq»0" aria-labelledby="tab«Rijkq»0"><div><div class="sc-bSFBcf iLdyBp"><span class="sc-gahYZc cXitJ">Content type</span><div class="sc-bAehkN iNRAJK">application/json</div></div><div class="sc-blIAwI eKKwxo"><div class="sc-dClGHI fdRrNy"><div class="sc-bbbBoY bBWkcI"><button><div class="sc-fYmhhH iNCOCX">Copy</div></button></div><div tabindex="0" class="sc-eVqvcJ kIppRw sc-fhfEft dFvLDb"><div class="redoc-json"><code><button class="collapser" aria-label="collapse"></button><span class="token punctuation">{</span><span class="ellipsis"></span><ul class="obj collapsible"><li><div class="hoverable "><span class="property token string">"status"</span>: <span class="token string">&quot;active&quot;</span><span class="token punctuation">,</span></div></li><li><div class="hoverable "><span class="property token string">"leaseId"</span>: <span class="token string">&quot;sZq0gcKRNrOK3hcDugzqJ&quot;</span></div></li></ul><span class="token punctuation">}</span></code></div></div></div></div></div></div></div></div><div><h3 class="sc-lgpSej drJHMo"> <!-- -->Response samples<!-- --> </h3><div class="sc-cOpnSz fyxuKi" data-rttabs="true"><ul class="react-tabs__tab-list" role="tablist"><li class="tab-success react-tabs__tab--selected" role="tab" id="tab«Rjjkq»0" aria-selected="true" aria-disabled="false" aria-controls="panel«Rjjkq»0" tabindex="0" data-rttab="true">200</li></ul><div class="react-tabs__tab-panel react-tabs__tab-panel--selected" role="tabpanel" id="panel«Rjjkq»0" aria-labelledby="tab«Rjjkq»0"><div><div class="sc-bSFBcf iLdyBp"><span class="sc-gahYZc cXitJ">Content type</span><div class="sc-bAehkN iNRAJK">application/json</div></div><div class="sc-blIAwI eKKwxo"><div class="sc-dClGHI fdRrNy"><div class="sc-bbbBoY bBWkcI"><button><div class="sc-fYmhhH iNCOCX">Copy</div></button></div><div tabindex="0" class="sc-eVqvcJ kIppRw sc-fhfEft dFvLDb"><div class="redoc-json"><code><button class="collapser" aria-label="collapse"></button><span class="token punctuation">{</span><span class="ellipsis"></span><ul class="obj collapsible"><li><div class="hoverable "><span class="property token string">"success"</span>: <span class="token boolean">true</span></div></li></ul><span class="token punctuation">}</span></code></div></div></div></div></div></div></div></div></div></div></div></div><div class="sc-evkzZa iZqpqg"></div></div></div>
      <script>
      const __redoc_state = {"menu":{"activeItemIdx":-1},"spec":{"data":{"openapi":"3.1.0","info":{"title":"Rent Report API","description":"RESTful API for the Rent Report application - a comprehensive property and lease management system for landlords and tenants.","version":"1.0.0"},"tags":[{"name":"Authentication","description":"User authentication and authorization"},{"name":"Properties","description":"Property management operations"},{"name":"Leases","description":"Lease management operations"},{"name":"Tenants","description":"Tenant management operations"},{"name":"Payments","description":"Payment tracking and management"},{"name":"Documents","description":"Document management and downloads"},{"name":"Referrals","description":"Referral code management"}],"paths":{"/auth/signup":{"post":{"summary":"User Registration","deprecated":false,"description":"Register a new user account with email, password, and username","tags":["Authentication"],"parameters":[],"requestBody":{"content":{"application/json":{"schema":{"type":"object","properties":{"email":{"type":"string","format":"email"},"password":{"type":"string","minLength":8},"username":{"type":"string"}},"required":["email","password","username"]},"example":{"email":"<EMAIL>","password":"SecurePass123!","username":"john_doe"}}}},"responses":{"201":{"description":"User created successfully","content":{"application/json":{"schema":{"type":"object","properties":{"ok":{"type":"number"}},"required":["ok"]},"examples":{"1":{"summary":"成功示例","value":{"ok":1}}}}},"headers":{}},"400":{"description":"Invalid input","content":{"application/json":{"schema":{"type":"object","properties":{"message":{"type":"string"},"errors":{"type":"array","items":{"type":"string"}}}}}},"headers":{}}},"security":[]}},"/properties":{"post":{"summary":"Create Property","deprecated":false,"description":"Create a new property for the authenticated user","tags":["Properties"],"parameters":[],"requestBody":{"content":{"application/json":{"schema":{"type":"object","properties":{"name":{"type":"string"},"address":{"type":"object","properties":{"street":{"type":"string"},"city":{"type":"string"},"prov":{"type":"string"},"state":{"type":"string"},"zipCode":{"type":"string"}}},"propertyType":{"type":"string","enum":["apartment","house","condo","commercial"],"description":"Optional property type"},"totalUnits":{"type":"integer","minimum":1},"vacantUnits":{"type":"integer"},"status":{"type":"string","enum":["active","inactive","maintenance"]},"notes":{"type":"string"}}},"example":"sZq0gcKRNrOK3hcDugzqJ"}}},"responses":{"201":{"description":"Property created successfully","content":{"application/json":{"schema":{"type":"object","properties":{"id":{"$ref":"#/components/schemas/NanoID"},"name":{"type":"string"},"address":{"type":"object","properties":{"street":{"type":"string"},"city":{"type":"string"},"prov":{"type":"string"},"state":{"type":"string"},"zipCode":{"type":"string"}}},"propertyType":{"type":"string","enum":["apartment","house","condo","commercial"],"description":"Optional property type"},"totalUnits":{"type":"integer","minimum":1},"vacantUnits":{"type":"integer"},"status":{"type":"string","enum":["active","inactive","maintenance"]},"notes":{"type":"string"}},"required":["id"]}}},"headers":{}}},"security":[]},"get":{"summary":"Get Properties","deprecated":false,"description":"Get all properties for the authenticated user with optional filtering by name, status, etc.","tags":["Properties"],"parameters":[{"name":"name","in":"query","description":"名称","required":false,"schema":{"type":"string"}},{"name":"propertyId","in":"query","description":"Filter by property ID","required":false,"schema":{"type":"string"}},{"name":"limit","in":"query","description":"Maximum number of results to return","required":false,"schema":{"type":"string"}},{"name":"status","in":"query","description":"Filter by status (active, inactive, archived)","required":false,"schema":{"type":"string"}}],"responses":{"200":{"description":"List of properties for the user","content":{"application/json":{"schema":{"type":"object","properties":{"items":{"type":"array","items":{"$ref":"#/components/schemas/PropertyList"}},"total":{"type":"integer"}}},"examples":{"1":{"summary":"成功示例","value":{"items":[{"id":"Av8kaomMlPJbzqSWwA1Ju","name":"Linda Beatty","totalUnits":5,"vacantUnits":2,"OwingBalance":99}],"total":1}}}}},"headers":{}}},"security":[]}},"/properties/{propertyId}":{"get":{"summary":"Get Property Details","deprecated":false,"description":"Get detailed information for a specific property","tags":["Properties"],"parameters":[{"name":"propertyId","in":"path","description":"Resource identifier","required":true,"example":"sZq0gcKRNrOK3hcDugzqJ","schema":{"$ref":"#/components/schemas/NanoID"}}],"responses":{"200":{"description":"Property details retrieved successfully","content":{"application/json":{"schema":{"type":"object","properties":{"id":{"$ref":"#/components/schemas/NanoID"},"name":{"type":"string"},"address":{"type":"object","properties":{"street":{"type":"string"},"city":{"type":"string"},"prov":{"type":"string"},"state":{"type":"string"},"zipCode":{"type":"string"}}},"propertyType":{"type":"string","enum":["apartment","house","condo","commercial"],"description":"Optional property type"},"totalUnits":{"type":"integer","minimum":1},"vacantUnits":{"type":"integer"},"status":{"type":"string","enum":["active","inactive","maintenance"]},"notes":{"type":"string"}},"required":["id"]}}},"headers":{}}},"security":[]},"put":{"summary":"Update Property","deprecated":false,"description":"Update information for a specific property","tags":["Properties"],"parameters":[{"name":"propertyId","in":"path","description":"Resource identifier","required":true,"example":"sZq0gcKRNrOK3hcDugzqJ","schema":{"$ref":"#/components/schemas/NanoID"}}],"requestBody":{"content":{"application/json":{"schema":{"type":"object","properties":{"id":{"$ref":"#/components/schemas/NanoID"},"name":{"type":"string"},"address":{"type":"object","properties":{"street":{"type":"string"},"city":{"type":"string"},"prov":{"type":"string"},"state":{"type":"string"},"zipCode":{"type":"string"}}},"propertyType":{"type":"string","enum":["apartment","house","condo","commercial"],"description":"Optional property type"},"totalUnits":{"type":"integer","minimum":1},"vacantUnits":{"type":"integer"},"status":{"type":"string","enum":["active","inactive","maintenance"]},"notes":{"type":"string"}},"required":["id"]},"example":"sZq0gcKRNrOK3hcDugzqJ"}}},"responses":{"200":{"description":"Property updated successfully","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Property"}}},"headers":{}}},"security":[]},"delete":{"summary":"Delete Property","deprecated":false,"description":"Delete a specific property","tags":["Properties"],"parameters":[{"name":"propertyId","in":"path","description":"Resource identifier","required":true,"example":"sZq0gcKRNrOK3hcDugzqJ","schema":{"$ref":"#/components/schemas/NanoID"}}],"responses":{"204":{"description":"Property deleted successfully","content":{"application/json":{"schema":{"type":"object","properties":{}},"examples":{"1":{"summary":"成功示例","value":{}}}}},"headers":{}}},"security":[]}},"/properties/{propertyId}/rooms":{"post":{"summary":"Create Room","deprecated":false,"description":"Create a new room/unit in a specific property","tags":["Properties"],"parameters":[{"name":"propertyId","in":"path","description":"Resource identifier","required":true,"example":"sZq0gcKRNrOK3hcDugzqJ","schema":{"$ref":"#/components/schemas/NanoID"}}],"requestBody":{"content":{"application/json":{"schema":{"type":"object","properties":{"name":{"type":"string","description":"Room/Unit name"},"type":{"type":"string","enum":["studio","oneBed","twoBed","commercial"]},"status":{"type":"string","enum":["vacant","occupied","maintenance"]},"notes":{"type":"string","description":"A place to keep notes for internal purposes"}},"required":["name"]},"example":"sZq0gcKRNrOK3hcDugzqJ"}}},"responses":{"201":{"description":"Room created successfully","content":{"application/json":{"schema":{"type":"object","properties":{"id":{"$ref":"#/components/schemas/NanoID"},"name":{"type":"string","description":"Room/Unit name"},"type":{"type":"string","enum":["studio","oneBed","twoBed","commercial"]},"status":{"type":"string","enum":["vacant","occupied","maintenance"]},"notes":{"type":"string","description":"A place to keep notes for internal purposes"},"propertyId":{"$ref":"#/components/schemas/NanoID"}},"required":["name","id","propertyId"]}}},"headers":{}}},"security":[]}},"/properties/{propertyId}/rooms/{roomId}":{"get":{"summary":"/properties/{propertyId}/rooms/{roomId}","deprecated":false,"description":"获取指定room的详细信息","tags":["Properties"],"parameters":[{"name":"propertyId","in":"path","description":"Resource identifier","required":true,"example":"sZq0gcKRNrOK3hcDugzqJ","schema":{"$ref":"#/components/schemas/NanoID"}},{"name":"roomId","in":"path","description":"Resource identifier","required":true,"example":"sZq0gcKRNrOK3hcDugzqJ","schema":{"$ref":"#/components/schemas/NanoID"}}],"responses":{"200":{"description":"Room details retrieved successfully","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Room"}}},"headers":{}}},"security":[]},"put":{"summary":"/properties/{propertyId}/rooms/{roomId}","deprecated":false,"description":"更新指定room的信息","tags":["Properties"],"parameters":[{"name":"propertyId","in":"path","description":"Resource identifier","required":true,"example":"sZq0gcKRNrOK3hcDugzqJ","schema":{"$ref":"#/components/schemas/NanoID"}},{"name":"roomId","in":"path","description":"Resource identifier","required":true,"example":"sZq0gcKRNrOK3hcDugzqJ","schema":{"$ref":"#/components/schemas/NanoID"}}],"requestBody":{"content":{"application/json":{"schema":{"type":"object","properties":{"id":{"$ref":"#/components/schemas/NanoID"},"name":{"type":"string","description":"Room/Unit name"},"type":{"type":"string","enum":["studio","oneBed","twoBed","commercial"]},"status":{"type":"string","enum":["vacant","occupied","maintenance"]},"notes":{"type":"string","description":"A place to keep notes for internal purposes"},"propertyId":{"$ref":"#/components/schemas/NanoID"}},"required":["name","id","propertyId"]},"example":"sZq0gcKRNrOK3hcDugzqJ"}}},"responses":{"200":{"description":"Room updated successfully","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Room"}}},"headers":{}}},"security":[]},"delete":{"summary":"/properties/{propertyId}/rooms/{roomId}","deprecated":false,"description":"删除指定的room","tags":["Properties"],"parameters":[{"name":"propertyId","in":"path","description":"Resource identifier","required":true,"example":"sZq0gcKRNrOK3hcDugzqJ","schema":{"$ref":"#/components/schemas/NanoID"}},{"name":"roomId","in":"path","description":"Resource identifier","required":true,"example":"sZq0gcKRNrOK3hcDugzqJ","schema":{"$ref":"#/components/schemas/NanoID"}}],"responses":{"204":{"description":"Room deleted successfully","content":{"application/json":{"schema":{"type":"object","properties":{}}}},"headers":{}}},"security":[]}},"/leases":{"post":{"summary":"Create Lease","deprecated":false,"description":"Create a new lease agreement","tags":["Leases"],"parameters":[],"requestBody":{"content":{"application/json":{"schema":{"type":"object","properties":{"propertyId":{"$ref":"#/components/schemas/NanoID"},"roomId":{"$ref":"#/components/schemas/NanoID"},"rentAmount":{"type":"number"},"startDate":{"type":"string","format":"date"},"endDate":{"type":"string","format":"date"},"status":{"type":"string","enum":["active","ended","terminated","pending"],"description":"状态"},"additionalMonthlyFees":{"type":"number","description":"Extra monthly fees"},"keyDeposit":{"type":"number"},"rentDeposit":{"type":"number"},"otherDeposits":{"type":"number"},"rentDueDay":{"type":"integer","minimum":1,"maximum":31},"rentReporting":{"type":"boolean","description":"Enable rent reporting"},"autoPay":{"type":"boolean","description":"Enable automatic payments"},"owingBalance":{"type":"number"},"lastPaymentDate":{"type":"string","format":"date"},"tenantId":{"$ref":"#/components/schemas/NanoID"}},"required":["propertyId","roomId","rentAmount","startDate","endDate"]},"example":"sZq0gcKRNrOK3hcDugzqJ"}}},"responses":{"201":{"description":"Lease created successfully","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Lease"}}},"headers":{}}},"security":[]},"get":{"summary":"Get Leases","deprecated":false,"description":"Get all leases for the authenticated user with optional filtering by propertyId, status, etc.","tags":["Leases"],"parameters":[{"name":"propertyId","in":"query","description":"Resource identifier","required":false,"schema":{"$ref":"#/components/schemas/NanoID"}},{"name":"status","in":"query","description":"Resource identifier","required":false,"schema":{"type":"string","enum":["active","ended","terminated"]}},{"name":"limit","in":"query","description":"Resource identifier","required":false,"schema":{"type":"integer","default":10}},{"name":"name","in":"query","description":"Resource identifier","required":false,"schema":{"type":"string"}}],"responses":{"200":{"description":"List of leases for the user","content":{"application/json":{"schema":{"type":"object","properties":{"items":{"type":"array","items":{"$ref":"#/components/schemas/LeaseList"}},"total":{"type":"integer"}}},"examples":{"1":{"summary":"成功示例","value":{"items":[{"id":"Oiu5_hL3PBuc_vLmZOFia","propertyId":"78","status":"active","owingBalance":9,"lastPaymentDate":"2024-04-27","tenantId":"79","userId":"65"}],"total":47}}}}},"headers":{}}},"security":[]}},"/leases/{leaseId}":{"get":{"summary":"/leases/{leaseId}","deprecated":false,"description":"Get detailed information for a specific lease","tags":["Leases"],"parameters":[{"name":"leaseId","in":"path","description":"Resource identifier","required":true,"example":"sZq0gcKRNrOK3hcDugzqJ","schema":{"$ref":"#/components/schemas/NanoID"}}],"responses":{"200":{"description":"Lease details retrieved successfully","content":{"application/json":{"schema":{"type":"object","properties":{"id":{"$ref":"#/components/schemas/NanoID"},"propertyId":{"$ref":"#/components/schemas/NanoID"},"roomId":{"$ref":"#/components/schemas/NanoID"},"rentAmount":{"type":"number"},"startDate":{"type":"string","format":"date"},"endDate":{"type":"string","format":"date"},"status":{"type":"string","enum":["active","ended","terminated"],"description":"状态"},"additionalMonthlyFees":{"type":"number","description":"Extra monthly fees"},"keyDeposit":{"type":"number"},"rentDeposit":{"type":"number"},"otherDeposits":{"type":"number"},"rentDueDay":{"type":"integer","minimum":1,"maximum":31},"rentReporting":{"type":"boolean","description":"Enable rent reporting"},"autoPay":{"type":"boolean","description":"Enable automatic payments"},"owingBalance":{"type":"number"},"lastPaymentDate":{"type":"string","format":"date"},"tenantId":{"$ref":"#/components/schemas/NanoID"}},"required":["propertyId","roomId","rentAmount","startDate","endDate","id"]}}},"headers":{}}},"security":[]},"put":{"summary":"/leases/{leaseId}","deprecated":false,"description":"更新指定lease合同的信息","tags":["Leases"],"parameters":[{"name":"leaseId","in":"path","description":"Resource identifier","required":true,"example":"sZq0gcKRNrOK3hcDugzqJ","schema":{"$ref":"#/components/schemas/NanoID"}}],"requestBody":{"content":{"application/json":{"schema":{"type":"object","properties":{"id":{"$ref":"#/components/schemas/NanoID"},"propertyId":{"$ref":"#/components/schemas/NanoID"},"roomId":{"$ref":"#/components/schemas/NanoID"},"rentAmount":{"type":"number"},"startDate":{"type":"string","format":"date"},"endDate":{"type":"string","format":"date"},"status":{"type":"string","enum":["active","ended","terminated","pending"],"description":"状态"},"additionalMonthlyFees":{"type":"number","description":"Extra monthly fees"},"keyDeposit":{"type":"number"},"rentDeposit":{"type":"number"},"otherDeposits":{"type":"number"},"rentDueDay":{"type":"integer","minimum":1,"maximum":31},"rentReporting":{"type":"boolean","description":"Enable rent reporting"},"autoPay":{"type":"boolean","description":"Enable automatic payments"},"owingBalance":{"type":"number"},"lastPaymentDate":{"type":"string","format":"date"},"tenantId":{"$ref":"#/components/schemas/NanoID"}},"required":["propertyId","roomId","rentAmount","startDate","endDate","id"]},"example":"sZq0gcKRNrOK3hcDugzqJ"}}},"responses":{"200":{"description":"Lease updated successfully","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Lease"}}},"headers":{}}},"security":[]},"delete":{"summary":"/leases/{leaseId}","deprecated":false,"description":"删除指定的lease","tags":["Leases"],"parameters":[{"name":"leaseId","in":"path","description":"Resource identifier","required":true,"example":"sZq0gcKRNrOK3hcDugzqJ","schema":{"$ref":"#/components/schemas/NanoID"}}],"responses":{"204":{"description":"Lease deleted successfully","content":{"application/json":{"schema":{"type":"object","properties":{}}}},"headers":{}}},"security":[]}},"/leases/{leaseId}/tenant-payments":{"post":{"summary":"/leases/{leaseId}/tenant-payments","deprecated":false,"description":"Add a new tenant payment record for a specific lease","tags":["Payments"],"parameters":[{"name":"leaseId","in":"path","description":"Resource identifier","required":true,"example":"sZq0gcKRNrOK3hcDugzqJ","schema":{"$ref":"#/components/schemas/NanoID"}}],"requestBody":{"content":{"application/json":{"schema":{"type":"object","properties":{"id":{"$ref":"#/components/schemas/NanoID"},"amount":{"type":"number","description":"Amount paid by tenant"},"date":{"type":"string","format":"date","description":"Tenant payment date"},"notes":{"type":"string"},"remainingBalance":{"type":"number","description":"Tenant's remaining balance after payment"},"status":{"type":"string","enum":["pending","completed","failed"]},"leaseId":{"$ref":"#/components/schemas/NanoID"}},"required":["amount","date","id","leaseId"]},"example":"sZq0gcKRNrOK3hcDugzqJ"}}},"responses":{"201":{"description":"Payment recorded successfully","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Payment"}}},"headers":{}}},"security":[]}},"/leases/{leaseId}/tenant-payments/{tenantPaymentId}":{"get":{"summary":"/leases/{leaseId}/tenant-payments/{tenantPaymentId}","deprecated":false,"description":"Get detailed information for a specific tenant payment","tags":["Payments"],"parameters":[{"name":"leaseId","in":"path","description":"Resource identifier","required":true,"example":"sZq0gcKRNrOK3hcDugzqJ","schema":{"$ref":"#/components/schemas/NanoID"}},{"name":"tenantPaymentId","in":"path","description":"Resource identifier","required":true,"schema":{"type":"string"}}],"responses":{"200":{"description":"Payment details retrieved successfully","content":{"application/json":{"schema":{"type":"object","properties":{"id":{"$ref":"#/components/schemas/NanoID"},"amount":{"type":"number","description":"Amount paid"},"date":{"type":"string","format":"date","description":"Payment date"},"notes":{"type":"string"},"remainingBalance":{"type":"number","description":"Remaining balance after payment"},"status":{"type":"string","enum":["pending","completed","failed"]},"leaseId":{"$ref":"#/components/schemas/NanoID"}},"required":["amount","date","id","leaseId"]}}},"headers":{}}},"security":[]},"put":{"summary":"/leases/{leaseId}/tenant-payments/{tenantPaymentId}","deprecated":false,"description":"Update information for a specific tenant payment","tags":["Payments"],"parameters":[{"name":"leaseId","in":"path","description":"Resource identifier","required":true,"example":"sZq0gcKRNrOK3hcDugzqJ","schema":{"$ref":"#/components/schemas/NanoID"}},{"name":"tenantPaymentId","in":"path","description":"Resource identifier","required":true,"schema":{"type":"string"}}],"requestBody":{"content":{"application/json":{"schema":{"type":"object","properties":{"id":{"$ref":"#/components/schemas/NanoID"},"amount":{"type":"number","description":"Amount paid"},"date":{"type":"string","format":"date","description":"Payment date"},"notes":{"type":"string"},"remainingBalance":{"type":"number","description":"Remaining balance after payment"},"status":{"type":"string","enum":["pending","completed","failed"]},"leaseId":{"$ref":"#/components/schemas/NanoID"}},"required":["amount","date","id","leaseId"]},"example":"sZq0gcKRNrOK3hcDugzqJ"}}},"responses":{"200":{"description":"Payment updated successfully","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Payment"}}},"headers":{}}},"security":[]},"delete":{"summary":"/leases/{leaseId}/tenant-payments/{tenantPaymentId}","deprecated":false,"description":"Delete a specific tenant payment record","tags":["Payments"],"parameters":[{"name":"leaseId","in":"path","description":"Resource identifier","required":true,"example":"sZq0gcKRNrOK3hcDugzqJ","schema":{"$ref":"#/components/schemas/NanoID"}},{"name":"tenantPaymentId","in":"path","description":"Resource identifier","required":true,"schema":{"type":"string"}}],"responses":{"204":{"description":"Payment deleted successfully","content":{"application/json":{"schema":{"type":"object","properties":{}}}},"headers":{}}},"security":[]}},"/auth/login":{"post":{"summary":"User Login","deprecated":false,"description":"Authenticate user with email and password","tags":["Authentication"],"parameters":[],"requestBody":{"content":{"application/json":{"schema":{"type":"object","required":["email","password"],"properties":{"email":{"type":"string","format":"email"},"password":{"type":"string"}}},"example":"sZq0gcKRNrOK3hcDugzqJ"}}},"responses":{"200":{"description":"Login successful","content":{"application/json":{"schema":{"type":"object","properties":{"token":{"type":"string"},"user":{"$ref":"#/components/schemas/User"}}}}},"headers":{}}},"security":[]}},"/auth/logout":{"post":{"summary":"User Logout","deprecated":false,"description":"Log out the current user and invalidate session","tags":["Authentication"],"parameters":[],"responses":{"204":{"description":"Logout successful","content":{"application/json":{"schema":{"type":"object","properties":{}}}},"headers":{}}},"security":[]}},"/leases/{leaseId}/tenants":{"post":{"summary":"/leases/{leaseId}/tenants","deprecated":false,"description":"Add a new tenant to a specific lease","tags":["Tenants"],"parameters":[{"name":"leaseId","in":"path","description":"Resource identifier","required":true,"example":"sZq0gcKRNrOK3hcDugzqJ","schema":{"$ref":"#/components/schemas/NanoID"}}],"requestBody":{"content":{"application/json":{"schema":{"type":"object","properties":{"id":{"$ref":"#/components/schemas/NanoID","readOnly":true},"firstName":{"type":"string","description":"Legal first name"},"middleName":{"type":"string"},"lastName":{"type":"string","description":"Legal last name"},"email":{"type":"string","format":"email"},"phoneNumber":{"type":"string"},"sinNumber":{"type":"string","description":"Social Insurance Number"},"notes":{"type":"string","description":"A place to keep notes for internal purposes"},"leaseId":{"type":"string"},"tenantId":{"type":"string","description":"绑定的租客用户ID（邀请完成后才有）"}},"required":["firstName","lastName","email","id","leaseId"]},"example":"sZq0gcKRNrOK3hcDugzqJ"}}},"responses":{"201":{"description":"Tenant added successfully","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Tenant"}}},"headers":{}}},"security":[]}},"/leases/{leaseId}/tenants/{tenantId}":{"get":{"summary":"/leases/{leaseId}/tenants/{tenantId}","deprecated":false,"description":"Get detailed information for a specific tenant","tags":["Tenants"],"parameters":[{"name":"leaseId","in":"path","description":"Resource identifier","required":true,"example":"sZq0gcKRNrOK3hcDugzqJ","schema":{"$ref":"#/components/schemas/NanoID"}},{"name":"tenantId","in":"path","description":"Resource identifier","required":true,"example":"sZq0gcKRNrOK3hcDugzqJ","schema":{"$ref":"#/components/schemas/NanoID"}}],"responses":{"200":{"description":"Tenant details retrieved successfully","content":{"application/json":{"schema":{"type":"object","properties":{"id":{"$ref":"#/components/schemas/NanoID","readOnly":true},"firstName":{"type":"string","description":"Legal first name"},"middleName":{"type":"string"},"lastName":{"type":"string","description":"Legal last name"},"email":{"type":"string","format":"email"},"phoneNumber":{"type":"string"},"sinNumber":{"type":"string","description":"Social Insurance Number"},"notes":{"type":"string","description":"A place to keep notes for internal purposes"},"leaseId":{"$ref":"#/components/schemas/NanoID"},"tenantId":{"type":"string","description":"绑定的租客用户ID（邀请完成后才有）"}},"required":["firstName","lastName","email","id","leaseId"]}}},"headers":{}}},"security":[]},"put":{"summary":"/leases/{leaseId}/tenants/{tenantId}","deprecated":false,"description":"Update information for a specific tenant","tags":["Tenants"],"parameters":[{"name":"leaseId","in":"path","description":"Resource identifier","required":true,"example":"sZq0gcKRNrOK3hcDugzqJ","schema":{"$ref":"#/components/schemas/NanoID"}},{"name":"tenantId","in":"path","description":"Resource identifier","required":true,"example":"sZq0gcKRNrOK3hcDugzqJ","schema":{"$ref":"#/components/schemas/NanoID"}}],"requestBody":{"content":{"application/json":{"schema":{"type":"object","properties":{"id":{"$ref":"#/components/schemas/NanoID","readOnly":true},"firstName":{"type":"string","description":"Legal first name"},"middleName":{"type":"string"},"lastName":{"type":"string","description":"Legal last name"},"email":{"type":"string","format":"email"},"phoneNumber":{"type":"string"},"sinNumber":{"type":"string","description":"Social Insurance Number"},"notes":{"type":"string","description":"A place to keep notes for internal purposes"},"leaseId":{"$ref":"#/components/schemas/NanoID"},"tenantId":{"type":"string","description":"绑定的租客用户ID（邀请完成后才有）"}},"required":["firstName","lastName","email","id","leaseId"]},"example":"sZq0gcKRNrOK3hcDugzqJ"}}},"responses":{"200":{"description":"Tenant updated successfully","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Tenant"}}},"headers":{}}},"security":[]},"delete":{"summary":"/leases/{leaseId}/tenants/{tenantId}","deprecated":false,"description":"Remove a tenant from a lease","tags":["Tenants"],"parameters":[{"name":"leaseId","in":"path","description":"Resource identifier","required":true,"example":"sZq0gcKRNrOK3hcDugzqJ","schema":{"$ref":"#/components/schemas/NanoID"}},{"name":"tenantId","in":"path","description":"Resource identifier","required":true,"example":"sZq0gcKRNrOK3hcDugzqJ","schema":{"$ref":"#/components/schemas/NanoID"}}],"responses":{"204":{"description":"Tenant removed successfully","content":{"application/json":{"schema":{"type":"object","properties":{}}}},"headers":{}}},"security":[]}},"/v1/referral-codes/{code}/validation":{"get":{"summary":"/v1/referral-codes/{code}/validation","deprecated":false,"description":"验证推荐码是否有效 (RESTful)","tags":["Referrals"],"parameters":[{"name":"code","in":"path","description":"推荐码","required":true,"schema":{"type":"string"}}],"responses":{"200":{"description":"推荐码验证成功","content":{"application/json":{"schema":{"type":"object","properties":{"valid":{"type":"boolean"},"message":{"type":"string"}}}}}},"404":{"description":"推荐码不存在"}},"security":[]}},"/v1/referral-code-applications":{"post":{"summary":"/v1/referral-code-applications","deprecated":false,"description":"应用推荐码 (RESTful)","tags":["Referrals"],"parameters":[],"requestBody":{"content":{"application/json":{"schema":{"type":"object","properties":{"code":{"type":"string","description":"推荐码"}},"required":["code"]}}}},"responses":{"200":{"description":"推荐码应用成功","content":{"application/json":{"schema":{"type":"object","properties":{"success":{"type":"boolean"},"message":{"type":"string"}}}}}},"400":{"description":"推荐码无效或已使用"}},"security":[]}},"/v1/documents/{fileId}":{"get":{"summary":"/v1/documents/{fileId}","deprecated":false,"description":"下载文档文件 (RESTful)","tags":["Documents"],"parameters":[{"name":"fileId","in":"path","description":"文件ID","required":true,"schema":{"type":"string"}}],"responses":{"200":{"description":"文件下载成功","content":{"application/octet-stream":{"schema":{"type":"string","format":"binary"}}}},"404":{"description":"文件不存在"}},"security":[]}},"/v1/subscriptions/{id}":{"delete":{"summary":"/v1/subscriptions/{id}","deprecated":false,"description":"取消订阅 (RESTful)","tags":["Subscriptions"],"parameters":[{"name":"id","in":"path","description":"订阅ID","required":true,"schema":{"type":"string"}}],"responses":{"204":{"description":"订阅取消成功"},"404":{"description":"订阅不存在"}},"security":[]}},"/v1/invitations/{code}/status":{"put":{"summary":"/v1/invitations/{code}/status","deprecated":false,"description":"更新邀请状态 (RESTful)","tags":["Invitations"],"parameters":[{"name":"code","in":"path","description":"邀请码","required":true,"schema":{"type":"string"}}],"requestBody":{"content":{"application/json":{"schema":{"type":"object","properties":{"status":{"type":"string","enum":["active","expired","cancelled"],"description":"邀请状态"},"leaseId":{"$ref":"#/components/schemas/NanoID","description":"关联的租约ID"}},"required":["status"]}}}},"responses":{"200":{"description":"邀请状态更新成功","content":{"application/json":{"schema":{"type":"object","properties":{"success":{"type":"boolean"}}}}}},"404":{"description":"邀请不存在"}},"security":[]}},"/v1/leases/{leaseId}/tenants/self":{"put":{"summary":"/v1/leases/{leaseId}/tenants/self","deprecated":false,"description":"租客更新自己的tenantId (RESTful)","tags":["Leases"],"parameters":[{"name":"leaseId","in":"path","description":"租约ID","required":true,"schema":{"$ref":"#/components/schemas/NanoID"}}],"responses":{"200":{"description":"租客信息更新成功","content":{"application/json":{"schema":{"type":"object","properties":{"success":{"type":"boolean"}}}}}},"403":{"description":"无权限或未找到"}},"security":[]}}},"components":{"schemas":{"Room":{"type":"object","required":["name","id","propertyId","userId"],"properties":{"id":{"$ref":"#/components/schemas/NanoID"},"name":{"type":"string","description":"Room/Unit name"},"type":{"type":"string","enum":["studio","oneBed","twoBed","commercial"]},"status":{"type":"string","enum":["vacant","occupied","maintenance"]},"notes":{"type":"string","description":"A place to keep notes for internal purposes"},"propertyId":{"$ref":"#/components/schemas/NanoID"},"userId":{"$ref":"#/components/schemas/NanoID"}}},"Lease":{"type":"object","required":["propertyId","roomId","rentAmount","startDate","endDate","id","userId"],"properties":{"id":{"$ref":"#/components/schemas/NanoID"},"propertyId":{"$ref":"#/components/schemas/NanoID"},"roomId":{"$ref":"#/components/schemas/NanoID"},"rentAmount":{"type":"number"},"startDate":{"type":"string","format":"date"},"endDate":{"type":"string","format":"date"},"status":{"type":"string","enum":["active","ended","terminated","pending"],"description":"状态"},"additionalMonthlyFees":{"type":"number","description":"Extra monthly fees"},"keyDeposit":{"type":"number"},"rentDeposit":{"type":"number"},"otherDeposits":{"type":"number"},"rentDueDay":{"type":"integer","minimum":1,"maximum":31},"rentReporting":{"type":"boolean","description":"Enable rent reporting"},"autoPay":{"type":"boolean","description":"Enable automatic payments"},"owingBalance":{"type":"number"},"lastPaymentDate":{"type":"string","format":"date"},"tenantId":{"$ref":"#/components/schemas/NanoID"},"userId":{"$ref":"#/components/schemas/NanoID"}}},"PropertyList":{"type":"object","required":["id","OwingBalance","vacantUnits","totalUnits","name"],"properties":{"id":{"$ref":"#/components/schemas/NanoID"},"name":{"type":"string"},"totalUnits":{"type":"integer","minimum":1},"vacantUnits":{"type":"integer"},"OwingBalance":{"type":"number"}}},"LeaseList":{"type":"object","required":["propertyId","id","tenantId","userId"],"properties":{"id":{"$ref":"#/components/schemas/NanoID"},"propertyId":{"$ref":"#/components/schemas/NanoID"},"status":{"type":"string","enum":["active","ended","terminated"],"description":"状态"},"owingBalance":{"type":"number"},"lastPaymentDate":{"type":"string","format":"date"},"tenantId":{"$ref":"#/components/schemas/NanoID"},"userId":{"$ref":"#/components/schemas/NanoID"}}},"User":{"type":"object","required":["email","username","id"],"properties":{"id":{"$ref":"#/components/schemas/NanoID"},"email":{"type":"string","format":"email"},"username":{"type":"string"},"settings":{"type":"object","properties":{}}}},"Property":{"type":"object","required":["id","userId"],"properties":{"id":{"$ref":"#/components/schemas/NanoID"},"name":{"type":"string"},"address":{"type":"object","properties":{"street":{"type":"string"},"city":{"type":"string"},"prov":{"type":"string"},"state":{"type":"string"},"zipCode":{"type":"string"}}},"propertyType":{"type":"string","enum":["apartment","house","condo","commercial"],"description":"Optional property type"},"totalUnits":{"type":"integer","minimum":1},"vacantUnits":{"type":"integer"},"status":{"type":"string","enum":["active","inactive","maintenance"]},"notes":{"type":"string"},"userId":{"$ref":"#/components/schemas/NanoID"}}},"Payment":{"type":"object","required":["amount","date","id","leaseId","userId"],"properties":{"id":{"$ref":"#/components/schemas/NanoID"},"amount":{"type":"number","description":"Amount paid"},"date":{"type":"string","format":"date","description":"Payment date"},"notes":{"type":"string"},"remainingBalance":{"type":"number","description":"Remaining balance after payment"},"status":{"type":"string","enum":["pending","completed","failed"]},"leaseId":{"$ref":"#/components/schemas/NanoID"},"userId":{"$ref":"#/components/schemas/NanoID"}}},"Tenant":{"type":"object","required":["firstName","lastName","email","id","leaseId","userId"],"properties":{"id":{"$ref":"#/components/schemas/NanoID","readOnly":true},"firstName":{"type":"string","description":"Legal first name"},"middleName":{"type":"string"},"lastName":{"type":"string","description":"Legal last name"},"email":{"type":"string","format":"email"},"phoneNumber":{"type":"string"},"sinNumber":{"type":"string","description":"Social Insurance Number"},"notes":{"type":"string","description":"A place to keep notes for internal purposes"},"leaseId":{"$ref":"#/components/schemas/NanoID"},"tenantId":{"type":"string","description":"绑定的租客用户ID（邀请完成后才有）"},"userId":{"$ref":"#/components/schemas/NanoID"}}},"Problem":{"type":"object","required":["id","userId","type","description","contactEmail","status"],"properties":{"id":{"$ref":"#/components/schemas/NanoID"},"userId":{"$ref":"#/components/schemas/NanoID"},"type":{"type":"string","enum":["billing","payment","system","other"],"description":"问题类型"},"description":{"type":"string","description":"问题描述"},"attachment":{"type":"string","description":"附件路径"},"contactEmail":{"type":"string","format":"email","description":"联系邮箱"},"status":{"type":"string","enum":["pending","in_progress","resolved","closed"],"description":"问题状态"},"leaseId":{"$ref":"#/components/schemas/NanoID","description":"关联的租约ID"},"propertyId":{"$ref":"#/components/schemas/NanoID","description":"关联的物业ID"},"createdAt":{"type":"string","format":"date-time","description":"创建时间"},"updatedAt":{"type":"string","format":"date-time","description":"更新时间"}}},"NanoID":{"type":"string","pattern":"^[0-9A-Za-z]{11}$","description":"NanoID - 11 character alphanumeric identifier","example":"sZq0gcKRNrOK3hcDugzqJ"},"ReferralCode":{"type":"string","pattern":"^[0-9A-Za-z]{6}$","description":"Referral code - 6 character alphanumeric identifier","example":"ABC123"}},"securitySchemes":{"bearerAuth":{"type":"http","scheme":"bearer","bearerFormat":"JWT"}}},"servers":[{"url":"http://localhost:8089","description":"Development server"},{"url":"https://api.rentreport.com","description":"Production server"}],"security":[{"bearerAuth":[]}]}},"searchIndex":{"store":["tag/Authentication","tag/Authentication/paths/~1auth~1signup/post","tag/Authentication/paths/~1auth~1login/post","tag/Authentication/paths/~1auth~1logout/post","tag/Properties","tag/Properties/paths/~1properties/post","tag/Properties/paths/~1properties/get","tag/Properties/paths/~1properties~1{propertyId}/get","tag/Properties/paths/~1properties~1{propertyId}/put","tag/Properties/paths/~1properties~1{propertyId}/delete","tag/Properties/paths/~1properties~1{propertyId}~1rooms/post","tag/Properties/paths/~1properties~1{propertyId}~1rooms~1{roomId}/get","tag/Properties/paths/~1properties~1{propertyId}~1rooms~1{roomId}/put","tag/Properties/paths/~1properties~1{propertyId}~1rooms~1{roomId}/delete","tag/Leases","tag/Leases/paths/~1leases/post","tag/Leases/paths/~1leases/get","tag/Leases/paths/~1leases~1{leaseId}/get","tag/Leases/paths/~1leases~1{leaseId}/put","tag/Leases/paths/~1leases~1{leaseId}/delete","tag/Leases/paths/~1v1~1leases~1{leaseId}~1tenants~1self/put","tag/Tenants","tag/Tenants/paths/~1leases~1{leaseId}~1tenants/post","tag/Tenants/paths/~1leases~1{leaseId}~1tenants~1{tenantId}/get","tag/Tenants/paths/~1leases~1{leaseId}~1tenants~1{tenantId}/put","tag/Tenants/paths/~1leases~1{leaseId}~1tenants~1{tenantId}/delete","tag/Payments","tag/Payments/paths/~1leases~1{leaseId}~1tenant-payments/post","tag/Payments/paths/~1leases~1{leaseId}~1tenant-payments~1{tenantPaymentId}/get","tag/Payments/paths/~1leases~1{leaseId}~1tenant-payments~1{tenantPaymentId}/put","tag/Payments/paths/~1leases~1{leaseId}~1tenant-payments~1{tenantPaymentId}/delete","tag/Documents","tag/Documents/paths/~1v1~1documents~1{fileId}/get","tag/Referrals","tag/Referrals/paths/~1v1~1referral-codes~1{code}~1validation/get","tag/Referrals/paths/~1v1~1referral-code-applications/post","tag/Subscriptions","tag/Subscriptions/paths/~1v1~1subscriptions~1{id}/delete","tag/Invitations","tag/Invitations/paths/~1v1~1invitations~1{code}~1status/put"],"index":{"version":"2.3.9","fields":["title","description"],"fieldVectors":[["title/0",[0,1.991]],["description/0",[0,2.09,1,1.546,2,3.753]],["title/1",[1,1.082,3,2.628]],["description/1",[1,0.998,4,2.423,5,1.349,6,2.423,7,2.049,8,2.049,9,2.423,10,2.423]],["title/2",[1,1.082,11,2.628]],["description/2",[0,1.713,1,1.267,7,2.602,8,2.602,12,3.077]],["title/3",[1,1.082,13,2.628]],["description/3",[1,1.074,14,2.607,15,2.607,16,2.607,17,2.607,18,2.607,19,2.607]],["title/4",[20,1.201]],["description/4",[20,1.26,21,2.09,22,2.792]],["title/5",[20,0.882,23,1.463]],["description/5",[0,1.572,1,1.162,5,1.572,20,1.366,23,1.572]],["title/6",[20,1.201]],["description/6",[0,1.259,1,0.932,20,1.159,24,1.913,25,1.913,26,2.262,27,1.913,28,1.913]],["title/7",[20,0.882,29,1.596]],["description/7",[20,1.033,29,1.869,30,1.58,31,1.105,32,2.289]],["title/8",[20,0.882,33,1.755]],["description/8",[20,1.033,30,1.58,31,1.105,32,2.289,33,2.055]],["title/9",[20,0.882,34,1.955]],["description/9",[20,1.136,31,1.214,32,2.516,34,2.516]],["title/10",[23,1.463,35,1.755]],["description/10",[5,1.572,20,0.948,23,1.572,31,1.014,36,2.823,37,2.823]],["title/11",[38,1.991]],["description/11",[35,2.816,38,2.348]],["title/12",[38,1.991]],["description/12",[35,2.816,38,2.348]],["title/13",[38,1.991]],["description/13",[35,2.816,38,2.348]],["title/14",[39,1.284]],["description/14",[21,2.09,22,2.792,39,1.348]],["title/15",[23,1.463,39,0.944]],["description/15",[5,1.713,23,1.713,39,1.553,40,3.077]],["title/16",[39,1.284]],["description/16",[0,1.259,1,0.932,24,1.913,25,1.913,27,1.913,28,1.913,39,1.239,41,2.262]],["title/17",[42,1.991]],["description/17",[29,1.869,30,1.58,31,1.105,39,1.105,42,1.713]],["title/18",[42,1.991]],["description/18",[39,1.514,42,2.348]],["title/19",[42,1.991]],["description/19",[39,1.514,42,2.348]],["title/20",[43,3.025]],["description/20",[43,3.174,44,3.753,45,2.09]],["title/21",[46,1.473]],["description/21",[21,2.09,22,2.792,46,1.546]],["title/22",[47,3.025]],["description/22",[5,1.572,31,1.014,39,1.014,46,1.162,47,2.387,48,2.387]],["title/23",[49,1.991]],["description/23",[29,1.869,30,1.58,31,1.105,46,1.267,49,1.713]],["title/24",[49,1.991]],["description/24",[30,1.58,31,1.105,33,2.055,46,1.267,49,1.713]],["title/25",[49,1.991]],["description/25",[39,1.214,46,1.393,49,1.883,50,3.382]],["title/26",[51,1.991]],["description/26",[21,2.09,51,2.09,52,3.753]],["title/27",[53,3.025]],["description/27",[5,1.349,31,0.87,39,0.87,46,0.998,48,2.049,51,1.349,53,2.049,54,2.049]],["title/28",[55,1.991]],["description/28",[29,1.714,30,1.45,31,1.014,46,1.162,51,1.572,55,1.572]],["title/29",[55,1.991]],["description/29",[30,1.45,31,1.014,33,1.885,46,1.162,51,1.572,55,1.572]],["title/30",[55,1.991]],["description/30",[31,1.014,34,2.1,46,1.162,51,1.572,54,2.387,55,1.572]],["title/31",[56,3.025]],["description/31",[21,2.09,56,3.174,57,3.753]],["title/32",[58,3.025]],["description/32",[45,2.09,58,3.174,59,2.279]],["title/33",[60,3.025]],["description/33",[21,2.09,60,3.174,61,3.753]],["title/34",[62,3.025]],["description/34",[45,2.09,59,2.279,62,3.174]],["title/35",[63,3.025]],["description/35",[45,2.09,59,2.279,63,3.174]],["title/36",[64,3.577]],["description/36",[]],["title/37",[65,3.025]],["description/37",[45,2.09,59,2.279,65,3.174]],["title/38",[66,3.577]],["description/38",[]],["title/39",[67,3.025]],["description/39",[45,2.09,59,2.279,67,3.174]]],"invertedIndex":[["",{"_index":59,"title":{},"description":{"32":{},"34":{},"35":{},"37":{},"39":{}}}],["account",{"_index":6,"title":{},"description":{"1":{}}}],["add",{"_index":48,"title":{},"description":{"22":{},"27":{}}}],["agreement",{"_index":40,"title":{},"description":{"15":{}}}],["auth/login",{"_index":12,"title":{},"description":{"2":{}}}],["auth/logout",{"_index":19,"title":{},"description":{"3":{}}}],["auth/signup",{"_index":10,"title":{},"description":{"1":{}}}],["authent",{"_index":0,"title":{"0":{}},"description":{"0":{},"2":{},"5":{},"6":{},"16":{}}}],["author",{"_index":2,"title":{},"description":{"0":{}}}],["code",{"_index":61,"title":{},"description":{"33":{}}}],["creat",{"_index":23,"title":{"5":{},"10":{},"15":{}},"description":{"5":{},"10":{},"15":{}}}],["current",{"_index":16,"title":{},"description":{"3":{}}}],["delet",{"_index":34,"title":{"9":{}},"description":{"9":{},"30":{}}}],["detail",{"_index":29,"title":{"7":{}},"description":{"7":{},"17":{},"23":{},"28":{}}}],["document",{"_index":56,"title":{"31":{}},"description":{"31":{}}}],["download",{"_index":57,"title":{},"description":{"31":{}}}],["email",{"_index":7,"title":{},"description":{"1":{},"2":{}}}],["etc",{"_index":28,"title":{},"description":{"6":{},"16":{}}}],["filter",{"_index":25,"title":{},"description":{"6":{},"16":{}}}],["inform",{"_index":30,"title":{},"description":{"7":{},"8":{},"17":{},"23":{},"24":{},"28":{},"29":{}}}],["invalid",{"_index":17,"title":{},"description":{"3":{}}}],["invit",{"_index":66,"title":{"38":{}},"description":{}}],["leas",{"_index":39,"title":{"14":{},"15":{},"16":{}},"description":{"14":{},"15":{},"16":{},"17":{},"18":{},"19":{},"22":{},"25":{},"27":{}}}],["leases/{leaseid",{"_index":42,"title":{"17":{},"18":{},"19":{}},"description":{"17":{},"18":{},"19":{}}}],["leases/{leaseid}/ten",{"_index":47,"title":{"22":{}},"description":{"22":{}}}],["leases/{leaseid}/tenant-pay",{"_index":53,"title":{"27":{}},"description":{"27":{}}}],["leases/{leaseid}/tenant-payments/{tenantpaymentid",{"_index":55,"title":{"28":{},"29":{},"30":{}},"description":{"28":{},"29":{},"30":{}}}],["leases/{leaseid}/tenants/{tenantid",{"_index":49,"title":{"23":{},"24":{},"25":{}},"description":{"23":{},"24":{},"25":{}}}],["log",{"_index":14,"title":{},"description":{"3":{}}}],["login",{"_index":11,"title":{"2":{}},"description":{}}],["logout",{"_index":13,"title":{"3":{}},"description":{}}],["manag",{"_index":21,"title":{},"description":{"4":{},"14":{},"21":{},"26":{},"31":{},"33":{}}}],["name",{"_index":26,"title":{},"description":{"6":{}}}],["new",{"_index":5,"title":{},"description":{"1":{},"5":{},"10":{},"15":{},"22":{},"27":{}}}],["oper",{"_index":22,"title":{},"description":{"4":{},"14":{},"21":{}}}],["option",{"_index":24,"title":{},"description":{"6":{},"16":{}}}],["out",{"_index":15,"title":{},"description":{"3":{}}}],["password",{"_index":8,"title":{},"description":{"1":{},"2":{}}}],["payment",{"_index":51,"title":{"26":{}},"description":{"26":{},"27":{},"28":{},"29":{},"30":{}}}],["properti",{"_index":20,"title":{"4":{},"5":{},"6":{},"7":{},"8":{},"9":{}},"description":{"4":{},"5":{},"6":{},"7":{},"8":{},"9":{},"10":{}}}],["properties/{propertyid",{"_index":32,"title":{},"description":{"7":{},"8":{},"9":{}}}],["properties/{propertyid}/room",{"_index":37,"title":{},"description":{"10":{}}}],["properties/{propertyid}/rooms/{roomid",{"_index":38,"title":{"11":{},"12":{},"13":{}},"description":{"11":{},"12":{},"13":{}}}],["propertyid",{"_index":41,"title":{},"description":{"16":{}}}],["record",{"_index":54,"title":{},"description":{"27":{},"30":{}}}],["referr",{"_index":60,"title":{"33":{}},"description":{"33":{}}}],["regist",{"_index":4,"title":{},"description":{"1":{}}}],["registr",{"_index":3,"title":{"1":{}},"description":{}}],["remov",{"_index":50,"title":{},"description":{"25":{}}}],["rest",{"_index":45,"title":{},"description":{"20":{},"32":{},"34":{},"35":{},"37":{},"39":{}}}],["room",{"_index":35,"title":{"10":{}},"description":{"11":{},"12":{},"13":{}}}],["room/unit",{"_index":36,"title":{},"description":{"10":{}}}],["session",{"_index":18,"title":{},"description":{"3":{}}}],["specif",{"_index":31,"title":{},"description":{"7":{},"8":{},"9":{},"10":{},"17":{},"22":{},"23":{},"24":{},"27":{},"28":{},"29":{},"30":{}}}],["statu",{"_index":27,"title":{},"description":{"6":{},"16":{}}}],["subscript",{"_index":64,"title":{"36":{}},"description":{}}],["tenant",{"_index":46,"title":{"21":{}},"description":{"21":{},"22":{},"23":{},"24":{},"25":{},"27":{},"28":{},"29":{},"30":{}}}],["tenantid",{"_index":44,"title":{},"description":{"20":{}}}],["track",{"_index":52,"title":{},"description":{"26":{}}}],["updat",{"_index":33,"title":{"8":{}},"description":{"8":{},"24":{},"29":{}}}],["user",{"_index":1,"title":{"1":{},"2":{},"3":{}},"description":{"0":{},"1":{},"2":{},"3":{},"5":{},"6":{},"16":{}}}],["usernam",{"_index":9,"title":{},"description":{"1":{}}}],["v1/documents/{fileid",{"_index":58,"title":{"32":{}},"description":{"32":{}}}],["v1/invitations/{code}/statu",{"_index":67,"title":{"39":{}},"description":{"39":{}}}],["v1/leases/{leaseid}/tenants/self",{"_index":43,"title":{"20":{}},"description":{"20":{}}}],["v1/referral-code-appl",{"_index":63,"title":{"35":{}},"description":{"35":{}}}],["v1/referral-codes/{code}/valid",{"_index":62,"title":{"34":{}},"description":{"34":{}}}],["v1/subscriptions/{id",{"_index":65,"title":{"37":{}},"description":{"37":{}}}]],"pipeline":[]}},"options":{}};

      var container = document.getElementById('redoc');
      Redoc.hydrate(__redoc_state, container);

      </script>
</body>

</html>
