package main

import (
	"encoding/json"
	"log"
	"os"
	"strings"

	"github.com/moov-io/metro2/pkg/file"
	"github.com/moov-io/metro2/pkg/utils"
)

func main() {
	// Read the JSON test file
	jsonFile, err := os.ReadFile("./metro2_example_input.json")

	if err != nil {
		log.Fatalf("failed to read json file: %v", err)
	}

	// Create a new metro2 file
	metroFile, err := file.NewFile(utils.CharacterFileFormat)
	if err != nil {
		log.Fatalf("failed to create metro file: %v", err)
	}

	// Unmarshal the json data into the metro file
	err = json.Unmarshal(jsonFile, metroFile)
	if err != nil {
		log.Fatalf("failed to unmarshal json: %v", err)
	}

	// Generate the trailer record
	trailer, err := metroFile.GeneratorTrailer()
	if err != nil {
		log.Fatalf("failed to generate trailer: %v", err)
	}
	err = metroFile.SetRecord(trailer)
	if err != nil {
		log.Fatalf("failed to set trailer record: %v", err)
	}

	if err := metroFile.Validate(); err != nil {
		panic(err)
	}

	// Convert the metro file to string
	metroString := metroFile.String(true)

	// Make sure the last line has the same length as the previous line
	lines := strings.Split(metroString, "\n")
	previousLineLength := len(lines[len(lines)-2])
	previousLineFirst4Chars := lines[len(lines)-2][:4]

	// Replace first 4 chars of last line with first 4 chars of previous line
	lines[len(lines)-1] = previousLineFirst4Chars + lines[len(lines)-1][4:]
	// Pad the last line to match the previous line's length
	lines[len(lines)-1] = lines[len(lines)-1] + strings.Repeat(" ", previousLineLength-len(lines[len(lines)-1]))
	// Ensure we join with newlines and add a final newline
	metroString = strings.Join(lines, "\n") + "\n"

	// Ensure CRLF line endings
	metroString = strings.ReplaceAll(metroString, "\n", "\r\n")
	// Fix any double CRLF that might have been created
	metroString = strings.ReplaceAll(metroString, "\r\r\n", "\r\n")

	err = os.WriteFile("./metro2_example.txt", []byte(metroString), 0644)

	if err != nil {
		log.Fatalf("failed to write metro file: %v", err)
	}

}
