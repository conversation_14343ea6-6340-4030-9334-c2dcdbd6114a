{"header": {"recordDescriptorWord": 626, "recordIdentifier": "HEADER", "EquifaxProgramIdentifier": "1101653", "activityDate": "2025-03-31T00:00:00Z", "dateCreated": "2025-03-01T00:00:00Z", "programDate": "2025-03-01T00:00:00Z", "programRevisionDate": "2025-03-01T00:00:00Z", "reporterName": "REALMASTER", "reporterAddress": "50 Acadia Ave #130, Markham, Ontario, Canada L3R 5Z2", "reporterTelephoneNumber": **********}, "data": [{"base": {"recordDescriptorWord": 626, "processingIndicator": 1, "timeStamp": "2025-03-01T01:15:22Z", "identificationNumber": "467RE01193", "consumerAccountNumber": "TN553723451", "portfolioType": "O", "accountType": "29", "dateOpened": "2025-02-01T00:00:00Z", "highestCredit": 1000, "termsDuration": "001", "scheduledMonthlyPaymentAmount": 1000, "actualPaymentAmount": 1000, "accountStatus": "11", "paymentHistoryProfile": "BBBBBBBBBBBBBBBBBBBBBBBB", "specialComment": "", "currentBalance": 0, "dateAccountInformation": "2025-03-31T00:00:00Z", "dateLastPayment": "2025-03-01T00:00:00Z", "surname": "SMITH-JONES", "firstName": "ONEONE", "generationCode": "", "socialSecurityNumber": *********, "dateBirth": "1972-04-11T00:00:00Z", "telephoneNumber": *********, "ecoaCode": "1", "countryCode": "CA", "firstLineAddress": "123 Main Street", "secondLineAddress": "Unit 451", "city": "Toronto", "state": "ON", "zipCode": "M5V 2K7", "addressIndicator": "Y"}}, {"base": {"recordDescriptorWord": 626, "processingIndicator": 1, "timeStamp": "2025-03-01T01:15:22Z", "identificationNumber": "467RE01193", "consumerAccountNumber": "TN553723452", "portfolioType": "O", "accountType": "29", "dateOpened": "2025-01-01T00:00:00Z", "highestCredit": 1500, "termsDuration": "001", "scheduledMonthlyPaymentAmount": 1500, "actualPaymentAmount": 1500, "accountStatus": "71", "paymentHistoryProfile": "1BBBBBBBBBBBBBBBBBBBBBBB", "specialComment": "", "currentBalance": 1500, "amountPastDue": 1500, "dateAccountInformation": "2025-03-31T00:00:00Z", "dateFirstDelinquency": "2025-02-01T00:00:00Z", "dateLastPayment": "2025-03-01T00:00:00Z", "surname": "SMITH-JONES", "firstName": "SEVONE", "generationCode": "", "socialSecurityNumber": *********, "dateBirth": "1972-05-12T00:00:00Z", "telephoneNumber": **********, "ecoaCode": "1", "countryCode": "CA", "firstLineAddress": "123 Main Street", "secondLineAddress": "Unit 452", "city": "Toronto", "state": "ON", "zipCode": "M5V 2K7", "addressIndicator": "Y"}}, {"base": {"recordDescriptorWord": 626, "processingIndicator": 1, "timeStamp": "2025-03-01T01:15:22Z", "identificationNumber": "467RE01193", "consumerAccountNumber": "TN553723453", "portfolioType": "O", "accountType": "29", "dateOpened": "2024-11-01T00:00:00Z", "highestCredit": 1500, "termsDuration": "001", "scheduledMonthlyPaymentAmount": 1500, "actualPaymentAmount": 1500, "accountStatus": "80", "paymentHistoryProfile": "321BBBBBBBBBBBBBBBBBBBBB", "specialComment": "", "currentBalance": 4500, "amountPastDue": 4500, "dateAccountInformation": "2025-03-31T00:00:00Z", "dateFirstDelinquency": "2024-12-01T00:00:00Z", "dateLastPayment": "2025-03-01T00:00:00Z", "surname": "SMITH-JONES", "firstName": "EIGZEO", "generationCode": "", "socialSecurityNumber": *********, "dateBirth": "1972-06-13T00:00:00Z", "telephoneNumber": *********, "ecoaCode": "1", "countryCode": "CA", "firstLineAddress": "123 Main Street", "secondLineAddress": "Unit 453", "city": "Toronto", "state": "ON", "zipCode": "M5V 2K7", "addressIndicator": "Y"}}, {"base": {"recordDescriptorWord": 626, "processingIndicator": 1, "timeStamp": "2025-03-01T01:15:22Z", "identificationNumber": "467RE01193", "consumerAccountNumber": "TN553723454", "portfolioType": "O", "accountType": "29", "dateOpened": "2025-02-01T00:00:00Z", "highestCredit": 2000, "termsDuration": "001", "scheduledMonthlyPaymentAmount": 2000, "actualPaymentAmount": 2000, "accountStatus": "11", "paymentHistoryProfile": "BBBBBBBBBBBBBBBBBBBBBBBB", "specialComment": "", "currentBalance": 0, "dateAccountInformation": "2025-03-31T00:00:00Z", "dateLastPayment": "2025-03-01T00:00:00Z", "surname": "SMITH-JONES", "firstName": "JUNIOR", "generationCode": "", "socialSecurityNumber": *********, "dateBirth": "1972-07-14T00:00:00Z", "telephoneNumber": *********, "ecoaCode": "2", "countryCode": "CA", "firstLineAddress": "123 Main Street", "secondLineAddress": "Unit 454", "city": "Toronto", "state": "ON", "zipCode": "M5V 2K7", "addressIndicator": "Y"}, "j1": [{"segmentIdentifier": "J1", "surname": "BEAUCHAMP", "firstName": "KEVIN", "generationCode": "S", "socialSecurityNumber": *********, "dateBirth": "2020-01-02T00:00:00Z", "telephoneNumber": **********, "ecoaCode": "2"}]}, {"base": {"recordDescriptorWord": 626, "processingIndicator": 1, "timeStamp": "2025-03-01T01:15:22Z", "identificationNumber": "467RE01193", "consumerAccountNumber": "TN553723455", "portfolioType": "O", "accountType": "29", "dateOpened": "2025-01-01T00:00:00Z", "highestCredit": 1500, "termsDuration": "001", "scheduledMonthlyPaymentAmount": 1500, "actualPaymentAmount": 1500, "accountStatus": "71", "paymentHistoryProfile": "1BBBBBBBBBBBBBBBBBBBBBBB", "specialComment": "", "currentBalance": 1500, "amountPastDue": 1500, "dateAccountInformation": "2025-03-31T00:00:00Z", "dateFirstDelinquency": "2025-02-01T00:00:00Z", "dateLastPayment": "2025-03-01T00:00:00Z", "surname": "SMITH-ONEJ", "firstName": "SEVONE", "generationCode": "", "socialSecurityNumber": *********, "dateBirth": "1972-04-14T00:00:00Z", "telephoneNumber": *********, "ecoaCode": "2", "countryCode": "CA", "firstLineAddress": "123 Main Street", "secondLineAddress": "Unit 455", "city": "Toronto", "state": "ON", "zipCode": "M5V 2K7", "addressIndicator": "Y"}, "j1": [{"segmentIdentifier": "J1", "surname": "BEAUCHAMP", "firstName": "KEVIN", "generationCode": "S", "socialSecurityNumber": *********, "dateBirth": "2020-02-02T00:00:00Z", "telephoneNumber": **********, "ecoaCode": "2"}]}, {"base": {"recordDescriptorWord": 626, "processingIndicator": 1, "timeStamp": "2025-03-01T01:15:22Z", "identificationNumber": "467RE01193", "consumerAccountNumber": "TN553723456", "portfolioType": "O", "accountType": "29", "dateOpened": "2024-11-01T00:00:00Z", "highestCredit": 1500, "termsDuration": "001", "scheduledMonthlyPaymentAmount": 1500, "actualPaymentAmount": 1500, "accountStatus": "80", "paymentHistoryProfile": "321BBBBBBBBBBBBBBBBBBBBB", "specialComment": "", "currentBalance": 4500, "amountPastDue": 4500, "dateAccountInformation": "2025-03-31T00:00:00Z", "dateFirstDelinquency": "2024-12-01T00:00:00Z", "dateLastPayment": "2025-03-01T00:00:00Z", "surname": "SMITH-ONEJ", "firstName": "EIGZEO", "generationCode": "", "socialSecurityNumber": *********, "dateBirth": "1972-05-14T00:00:00Z", "telephoneNumber": *********, "ecoaCode": "2", "countryCode": "CA", "firstLineAddress": "123 Main Street", "secondLineAddress": "Unit 456", "city": "Toronto", "state": "ON", "zipCode": "M5V 2K7", "addressIndicator": "Y"}, "j1": [{"segmentIdentifier": "J1", "surname": "BEAUCHAMP", "firstName": "KEVIN", "generationCode": "S", "socialSecurityNumber": *********, "dateBirth": "2020-01-02T00:00:00Z", "telephoneNumber": **********, "ecoaCode": "2"}]}, {"base": {"recordDescriptorWord": 626, "processingIndicator": 1, "timeStamp": "2025-03-01T01:15:22Z", "identificationNumber": "467RE01193", "consumerAccountNumber": "TN553723457", "portfolioType": "O", "accountType": "29", "dateOpened": "2025-02-01T00:00:00Z", "highestCredit": 2500, "termsDuration": "001", "scheduledMonthlyPaymentAmount": 2500, "actualPaymentAmount": 2500, "accountStatus": "11", "paymentHistoryProfile": "BBBBBBBBBBBBBBBBBBBBBBBB", "specialComment": "", "currentBalance": 0, "dateAccountInformation": "2025-03-31T00:00:00Z", "dateLastPayment": "2025-03-01T00:00:00Z", "surname": "SMITH-JONES", "firstName": "JUNIOR", "generationCode": "", "socialSecurityNumber": *********, "dateBirth": "1972-03-15T00:00:00Z", "telephoneNumber": **********, "ecoaCode": "2", "countryCode": "CA", "firstLineAddress": "123 Main Street", "secondLineAddress": "Unit 457", "city": "Toronto", "state": "ON", "zipCode": "M5V 2K7", "addressIndicator": "Y"}, "j1": [{"segmentIdentifier": "J1", "surname": "BEAUCHAMP", "firstName": "KEVIN", "generationCode": "S", "socialSecurityNumber": *********, "dateBirth": "2020-01-01T00:00:00Z", "telephoneNumber": **********, "ecoaCode": "2"}, {"segmentIdentifier": "J1", "surname": "BEAUCHAMP", "firstName": "KEVIN", "generationCode": "S", "socialSecurityNumber": *********, "dateBirth": "2020-01-02T00:00:00Z", "telephoneNumber": **********, "ecoaCode": "2"}]}, {"base": {"recordDescriptorWord": 626, "processingIndicator": 1, "timeStamp": "2025-03-01T01:15:22Z", "identificationNumber": "467RE01193", "consumerAccountNumber": "TN553723458", "portfolioType": "O", "accountType": "29", "dateOpened": "2025-01-01T00:00:00Z", "highestCredit": 1500, "termsDuration": "001", "scheduledMonthlyPaymentAmount": 1500, "actualPaymentAmount": 1500, "accountStatus": "71", "paymentHistoryProfile": "1BBBBBBBBBBBBBBBBBBBBBBB", "specialComment": "", "currentBalance": 1500, "amountPastDue": 1500, "dateAccountInformation": "2025-03-31T00:00:00Z", "dateFirstDelinquency": "2025-02-01T00:00:00Z", "dateLastPayment": "2025-03-01T00:00:00Z", "surname": "SMITH-TWOJ", "firstName": "SEVONE", "generationCode": "", "socialSecurityNumber": *********, "dateBirth": "1972-04-15T00:00:00Z", "telephoneNumber": **********, "ecoaCode": "2", "countryCode": "CA", "firstLineAddress": "123 Main Street", "secondLineAddress": "Unit 458", "city": "Toronto", "state": "ON", "zipCode": "M5V 2K7", "addressIndicator": "Y"}, "j1": [{"segmentIdentifier": "J1", "surname": "BEAUCHAMP", "firstName": "KEVIN", "generationCode": "S", "socialSecurityNumber": *********, "dateBirth": "2020-01-01T00:00:00Z", "telephoneNumber": **********, "ecoaCode": "2"}, {"segmentIdentifier": "J1", "surname": "BEAUCHAMP", "firstName": "KEVIN", "generationCode": "S", "socialSecurityNumber": *********, "dateBirth": "2020-01-02T00:00:00Z", "telephoneNumber": **********, "ecoaCode": "2"}]}, {"base": {"recordDescriptorWord": 626, "processingIndicator": 1, "timeStamp": "2025-03-01T01:15:22Z", "identificationNumber": "467RE01193", "consumerAccountNumber": "TN553723459", "portfolioType": "O", "accountType": "29", "dateOpened": "2024-01-01T00:00:00Z", "highestCredit": 1500, "termsDuration": "001", "scheduledMonthlyPaymentAmount": 1500, "actualPaymentAmount": 1500, "accountStatus": "80", "paymentHistoryProfile": "321BBBBBBBBBBBBBBBBBBBBB", "specialComment": "", "currentBalance": 4500, "amountPastDue": 4500, "dateAccountInformation": "2025-03-31T00:00:00Z", "dateFirstDelinquency": "2024-12-01T00:00:00Z", "dateLastPayment": "2025-03-01T00:00:00Z", "surname": "SMITH-TWOJ", "firstName": "EIGZEO", "generationCode": "", "socialSecurityNumber": *********, "dateBirth": "1972-05-15T00:00:00Z", "telephoneNumber": **********, "ecoaCode": "2", "countryCode": "CA", "firstLineAddress": "123 Main Street", "secondLineAddress": "Unit 459", "city": "Toronto", "state": "ON", "zipCode": "M5V 2K7", "addressIndicator": "Y"}, "j1": [{"segmentIdentifier": "J1", "surname": "BEAUCHAMP", "firstName": "KEVIN", "generationCode": "S", "socialSecurityNumber": *********, "dateBirth": "2020-01-01T00:00:00Z", "telephoneNumber": **********, "ecoaCode": "2"}, {"segmentIdentifier": "J1", "surname": "BEAUCHAMP", "firstName": "KEVIN", "generationCode": "S", "socialSecurityNumber": *********, "dateBirth": "2020-01-02T00:00:00Z", "telephoneNumber": **********, "ecoaCode": "2"}]}]}