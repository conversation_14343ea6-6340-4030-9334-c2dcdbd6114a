#!/bin/bash

# Debt Reminder Scheduler Script
# This script runs the debt reminder CLI program
# Designed to be executed by cron on the 15th of each month
# Usage: ./debt_reminder_scheduler.sh [-stdout] [-k]
#   -stdout: Output to stdout/stderr instead of log file
#   -k: Use /app/bin/cli_debt_reminder instead of ./build/bin/cli_debt_reminder, for k3s mode

# Parse command line arguments
STDOUT_MODE=false
KUBE_MODE=false

for arg in "$@"; do
    case $arg in
        -stdout)
            STDOUT_MODE=true
            ;;
        -k)
            KUBE_MODE=true
            ;;
    esac
done

# Set script directory and paths
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"

# Set binary path based on mode
if [[ "$KUBE_MODE" == "true" ]]; then
    CLI_BINARY="/app/bin/cli_debt_reminder"
else
    CLI_BINARY="$PROJECT_ROOT/build/bin/cli_debt_reminder"
fi

LOG_FILE="$PROJECT_ROOT/logs/debt_reminder_scheduler.log"
PID_FILE="$PROJECT_ROOT/tmp/debt_reminder.pid"

# Create necessary directories (only if not in stdout mode)
if [[ "$STDOUT_MODE" == "false" ]]; then
    mkdir -p "$PROJECT_ROOT/logs"
fi
mkdir -p "$PROJECT_ROOT/tmp"

# Function to log messages
log_message() {
    local message="[$(date '+%Y-%m-%d %H:%M:%S')] $1"
    if [[ "$STDOUT_MODE" == "true" ]]; then
        echo "$message"
    else
        echo "$message" | tee -a "$LOG_FILE"
    fi
}

# Function to log error messages (always to stderr in stdout mode)
log_error() {
    local message="[$(date '+%Y-%m-%d %H:%M:%S')] ERROR: $1"
    if [[ "$STDOUT_MODE" == "true" ]]; then
        echo "$message" >&2
    else
        echo "$message" | tee -a "$LOG_FILE"
    fi
}

# Function to cleanup on exit
cleanup() {
    if [ -f "$PID_FILE" ]; then
        rm -f "$PID_FILE"
    fi
}

# Set trap for cleanup
trap cleanup EXIT

# Check if another instance is running
if [ -f "$PID_FILE" ]; then
    OLD_PID=$(cat "$PID_FILE")
    if kill -0 "$OLD_PID" 2>/dev/null; then
        log_error "Another debt reminder process is already running (PID: $OLD_PID)"
        exit 1
    else
        log_message "WARNING: Stale PID file found, removing it"
        rm -f "$PID_FILE"
    fi
fi

# Write current PID
echo $$ > "$PID_FILE"

log_message "=== Debt Reminder Scheduler Started ==="
log_message "Script: $0"
log_message "PID: $$"
log_message "Project Root: $PROJECT_ROOT"
log_message "CLI Binary: $CLI_BINARY"
log_message "Log File: $LOG_FILE"

# Check if CLI binary exists
if [ ! -f "$CLI_BINARY" ]; then
    log_error "CLI binary not found at $CLI_BINARY"
    if [[ "$KUBE_MODE" == "true" ]]; then
        log_error "Please ensure the binary is available in the container at $CLI_BINARY"
    else
        log_error "Please compile the CLI program first:"
        log_error "  cd $PROJECT_ROOT/src && go build -o ../build/bin/cli_debt_reminder ../bash/cli_debt_reminder.go"
    fi
    exit 1
fi

# Check if CLI binary is executable
if [ ! -x "$CLI_BINARY" ]; then
    log_message "WARNING: CLI binary is not executable, making it executable"
    chmod +x "$CLI_BINARY"
fi

# Change to project root directory
cd "$PROJECT_ROOT" || {
    log_message "ERROR: Failed to change to project root directory: $PROJECT_ROOT"
    exit 1
}

# Set environment variables (only if not already set)
export RMBASE_FILE_CFG="${RMBASE_FILE_CFG:-configs/local.ini}"

log_message "Environment variables set:"
log_message "  RMBASE_FILE_CFG=$RMBASE_FILE_CFG"

# Execute the CLI program
log_message "Executing debt reminder CLI program..."
log_message "Using binary: $CLI_BINARY"
START_TIME=$(date +%s)

# Run the CLI program from project root directory
if [[ "$STDOUT_MODE" == "true" ]]; then
    # In stdout mode, let the CLI output directly to stdout/stderr
    if "$CLI_BINARY"; then
        END_TIME=$(date +%s)
        DURATION=$((END_TIME - START_TIME))
        log_message "SUCCESS: Debt reminder completed successfully in ${DURATION} seconds"
        EXIT_CODE=0
    else
        END_TIME=$(date +%s)
        DURATION=$((END_TIME - START_TIME))
        log_error "Debt reminder failed after ${DURATION} seconds"
        EXIT_CODE=1
    fi
else
    # In file mode, capture output and log it
    if "$CLI_BINARY" 2>&1 | tee -a "$LOG_FILE"; then
        END_TIME=$(date +%s)
        DURATION=$((END_TIME - START_TIME))
        log_message "SUCCESS: Debt reminder completed successfully in ${DURATION} seconds"
        EXIT_CODE=0
    else
        END_TIME=$(date +%s)
        DURATION=$((END_TIME - START_TIME))
        log_error "Debt reminder failed after ${DURATION} seconds"
        EXIT_CODE=1
    fi
fi

log_message "=== Debt Reminder Scheduler Finished ==="

# Keep log files for 30 days, clean up older ones
find "$PROJECT_ROOT/logs" -name "debt_reminder_scheduler.log" -mtime +30 -delete 2>/dev/null || true

exit $EXIT_CODE
