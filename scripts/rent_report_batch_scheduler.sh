#!/bin/bash

# Rent Report Email Batch Scheduler Script for Cron Job
# This script runs the rent report email batch processing in CLI mode
# Usage: ./rent_report_batch_scheduler.sh [-stdout] [-k]
#   -stdout: Output to stdout/stderr instead of log file
#   -k: Use /app/bin/cli_rent_report_batch instead of ./build/bin/cli_rent_report_batch, for k3s mode

# Parse command line arguments
STDOUT_MODE=false
KUBE_MODE=false

for arg in "$@"; do
    case $arg in
        -stdout)
            STDOUT_MODE=true
            ;;
        -k)
            KUBE_MODE=true
            ;;
    esac
done

# Set script directory
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"

# Set working directory to project root
cd "$PROJECT_DIR" || {
    echo "Error: Cannot change to project directory: $PROJECT_DIR"
    exit 1
}

# Set environment variables (only if not already set)
export RMBASE_FILE_CFG="${RMBASE_FILE_CFG:-configs/local.ini}"

# Set log file path (only if not in stdout mode)
if [[ "$STDOUT_MODE" == "false" ]]; then
    LOG_FILE="$PROJECT_DIR/logs/rent_report_batch_scheduler.log"
    LOG_DIR="${LOG_DIR:-$(dirname "$LOG_FILE")}"

    # Create log directory if it doesn't exist
    mkdir -p "$LOG_DIR"
fi

# Function to log messages
log_message() {
    local message="$(date '+%Y-%m-%d %H:%M:%S') - $1"
    if [[ "$STDOUT_MODE" == "true" ]]; then
        echo "$message"
    else
        echo "$message" | tee -a "$LOG_FILE"
    fi
}

# Function to log error messages (always to stderr in stdout mode)
log_error() {
    local message="$(date '+%Y-%m-%d %H:%M:%S') - ERROR: $1"
    if [[ "$STDOUT_MODE" == "true" ]]; then
        echo "$message" >&2
    else
        echo "$message" | tee -a "$LOG_FILE"
    fi
}

# Function to check if the CLI binary exists
check_binary() {
    if [[ "$KUBE_MODE" == "true" ]]; then
        CLI_BINARY="/app/bin/cli_rent_report_batch"
        if [ ! -f "$CLI_BINARY" ]; then
            log_error "CLI binary '$CLI_BINARY' not found"
            log_error "Please ensure the binary is available in the container at $CLI_BINARY"
            exit 1
        fi
    else
        CLI_BINARY="./build/bin/cli_rent_report_batch"
        if [ ! -f "$CLI_BINARY" ]; then
            log_error "CLI binary '$CLI_BINARY' not found"
            log_error "Please build the CLI first with: cd src && go build -o ../build/bin/cli_rent_report_batch ../bash/cli_rent_report_batch.go"
            exit 1
        fi
    fi
}

# Function to run the rent report batch scheduler
run_scheduler() {
    log_message "Starting rent report email batch processing..."
    log_message "Using binary: $CLI_BINARY"

    # Run the scheduler from project directory
    if [[ "$STDOUT_MODE" == "true" ]]; then
        # In stdout mode, let the CLI output directly to stdout/stderr
        "$CLI_BINARY"
        local exit_code=$?

        if [ $exit_code -eq 0 ]; then
            log_message "Rent report email batch processing completed successfully"
        else
            log_error "Rent report email batch processing failed with exit code: $exit_code"
            exit $exit_code
        fi
    else
        # In file mode, capture output and log it
        output=$("$CLI_BINARY" 2>&1)
        local exit_code=$?

        if [ $exit_code -eq 0 ]; then
            log_message "Rent report email batch processing completed successfully"
            log_message "CLI output: $output"
        else
            log_error "Rent report email batch processing failed with exit code: $exit_code"
            log_message "CLI error output: $output"
            exit $exit_code
        fi
    fi
}

# Main execution
main() {
    log_message "=== Rent Report Email Batch Scheduler Cron Job Started ==="
    
    # Check if binary exists
    check_binary
    
    # Run the scheduler
    run_scheduler
    
    log_message "=== Rent Report Email Batch Scheduler Cron Job Completed ==="
}

# Execute main function
main "$@"
