# Rent Report API Documentation

This directory contains the OpenAPI 3.1.0 specification for the Rent Report application's RESTful API.

## Files

- `openapi.yaml` - The main OpenAPI specification file

## API Documentation Features

### RESTful Endpoints

The API has been updated to follow RESTful conventions with the following new endpoints:

#### Referral Codes
- `GET /v1/referral-codes/{code}/validation` - Validate referral code (RESTful)
- `POST /v1/referral-code-applications` - Apply referral code (RESTful)

#### Documents
- `GET /v1/documents/{fileId}` - Download document file (RESTful)

#### Subscriptions
- `DELETE /v1/subscriptions/{id}` - Cancel subscription (RESTful)

#### Tenants
- `PUT /v1/leases/{leaseId}/tenants/{tenantId}` - Update tenant (RESTful)
- `DELETE /v1/leases/{leaseId}/tenants/{tenantId}` - Delete tenant (RESTful)
- `PUT /v1/leases/{leaseId}/tenants/self` - Tenant self-update (RESTful)

#### Invitations
- `PUT /v1/invitations/{code}/status` - Update invitation status (RESTful)

### Backward Compatibility

All old endpoints are still supported for backward compatibility:
- `POST /v1/referral-codes/validate`
- `POST /v1/referral-codes/apply`
- `GET /v1/leases/download/{fileId}`
- `POST /v1/usersub/cancel`
- `PUT /v1/leases/{leaseId}/ctnts/{tenantId}`
- `DELETE /v1/leases/{leaseId}/ctnts/{tenantId}`
- `PUT /v1/leases/{leaseId}/ctnts/self_id`
- `POST /v1/invitations/update_status_by_code`

## Generating Documentation

### Prerequisites

Make sure you have Node.js installed, then install the documentation tools:

```bash
make docs-install
```

### Generate HTML Documentation

Generate beautiful HTML documentation using Redoc:

```bash
make docs-generate
```

This will create `../docs/api/index.html` with the complete API documentation.

### Serve Documentation Locally

Generate and serve the documentation on a local web server:

```bash
make docs-serve
```

The documentation will be available at http://localhost:8080

### Validate OpenAPI Specification

Validate that the OpenAPI specification is correct:

```bash
make docs-validate
```

### Clean Generated Files

Remove generated documentation files:

```bash
make docs-clean
```

## API Testing

You can test the API endpoints using the generated documentation or tools like:

- Postman (import the OpenAPI spec)
- Insomnia (import the OpenAPI spec)
- curl commands
- Swagger UI (if you set it up)

## Schema Components

The API specification includes reusable schema components for:

- User
- Property
- Lease
- Tenant
- Payment
- Room
- Problem

These schemas ensure consistency across all endpoints and provide clear data structure documentation.

## Authentication

Most endpoints require authentication. The API uses JWT tokens for authentication.

## Response Formats

All API responses follow consistent patterns:
- Success responses include the requested data
- Error responses include error messages and appropriate HTTP status codes
- List endpoints include pagination information where applicable

## Development

When adding new endpoints:

1. Update the `openapi.yaml` file with the new endpoint specification
2. Ensure proper HTTP methods and status codes are used
3. Include request/response schemas
4. Add appropriate descriptions and examples
5. Regenerate documentation with `make docs-generate`

## Support

For questions about the API or documentation, please refer to the main project documentation or contact the development team.
