openapi: 3.1.0
info:
  title: Rent Report API
  description: 'RESTful API for the Rent Report application - a comprehensive property and lease management system for landlords and tenants.'
  version: 1.0.0
tags:
  - name: Authentication
    description: User authentication and authorization
  - name: Properties
    description: Property management operations
  - name: Leases
    description: Lease management operations
  - name: Tenants
    description: Tenant management operations
  - name: Payments
    description: Payment tracking and management
  - name: Documents
    description: Document management and downloads
  - name: Referrals
    description: Referral code management
paths:
  /auth/signup:
    post:
      summary: User Registration
      deprecated: false
      description: Register a new user account with email, password, and username
      tags:
        - Authentication
      parameters: []
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                email:
                  type: string
                  format: email
                password:
                  type: string
                  minLength: 8
                username:
                  type: string
              required:
                - email
                - password
                - username
            example:
              email: "<EMAIL>"
              password: "SecurePass123!"
              username: "john_doe"
      responses:
        '201':
          description: User created successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  ok:
                    type: number
                required:
                  - ok
              examples:
                '1':
                  summary: 成功示例
                  value:
                    ok: 1
          headers: {}
        '400':
          description: Invalid input
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                  errors:
                    type: array
                    items:
                      type: string
          headers: {}
      security: []
  /properties:
    post:
      summary: Create Property
      deprecated: false
      description: Create a new property for the authenticated user
      tags:
        - Properties
      parameters: []
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                name:
                  type: string
                address:
                  type: object
                  properties:
                    street:
                      type: string
                    city:
                      type: string
                    prov:
                      type: string
                    state:
                      type: string
                    zipCode:
                      type: string
                propertyType:
                  type: string
                  enum:
                    - apartment
                    - house
                    - condo
                    - commercial
                  description: Optional property type
                totalUnits:
                  type: integer
                  minimum: 1
                vacantUnits:
                  type: integer
                status:
                  type: string
                  enum:
                    - active
                    - inactive
                    - maintenance
                notes:
                  type: string
            example: 'sZq0gcKRNrOK3hcDugzqJ'
      responses:
        '201':
          description: Property created successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  id:
                    $ref: "#/components/schemas/NanoID"
                  name:
                    type: string
                  address:
                    type: object
                    properties:
                      street:
                        type: string
                      city:
                        type: string
                      prov:
                        type: string
                      state:
                        type: string
                      zipCode:
                        type: string
                  propertyType:
                    type: string
                    enum:
                      - apartment
                      - house
                      - condo
                      - commercial
                    description: Optional property type
                  totalUnits:
                    type: integer
                    minimum: 1
                  vacantUnits:
                    type: integer
                  status:
                    type: string
                    enum:
                      - active
                      - inactive
                      - maintenance
                  notes:
                    type: string
                required:
                  - id
          headers: {}
      security: []
    get:
      summary: Get Properties
      deprecated: false
      description: Get all properties for the authenticated user with optional filtering by name, status, etc.
      tags:
        - Properties
      parameters:
        - name: name
          in: query
          description: 名称
          required: false
          schema:
            type: string
        - name: propertyId
          in: query
          description: 'Filter by property ID'
          required: false
          schema:
            type: string
        - name: limit
          in: query
          description: 'Maximum number of results to return'
          required: false
          schema:
            type: string
        - name: status
          in: query
          description: 'Filter by status (active, inactive, archived)'
          required: false
          schema:
            type: string
      responses:
        '200':
          description: List of properties for the user
          content:
            application/json:
              schema:
                type: object
                properties:
                  items:
                    type: array
                    items:
                      $ref: '#/components/schemas/PropertyList'
                  total:
                    type: integer
              examples:
                '1':
                  summary: 成功示例
                  value:
                    items:
                      - id: Av8kaomMlPJbzqSWwA1Ju
                        name: Linda Beatty
                        totalUnits: 5
                        vacantUnits: 2
                        OwingBalance: 99
                    total: 1
          headers: {}
      security: []
  /properties/{propertyId}:
    get:
      summary: Get Property Details
      deprecated: false
      description: Get detailed information for a specific property
      tags:
        - Properties
      parameters:
        - name: propertyId
          in: path
          description: 'Resource identifier'
          required: true
          example: 'sZq0gcKRNrOK3hcDugzqJ'
          schema:
            $ref: "#/components/schemas/NanoID"
      responses:
        '200':
          description: Property details retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  id:
                    $ref: "#/components/schemas/NanoID"
                  name:
                    type: string
                  address:
                    type: object
                    properties:
                      street:
                        type: string
                      city:
                        type: string
                      prov:
                        type: string
                      state:
                        type: string
                      zipCode:
                        type: string
                  propertyType:
                    type: string
                    enum:
                      - apartment
                      - house
                      - condo
                      - commercial
                    description: Optional property type
                  totalUnits:
                    type: integer
                    minimum: 1
                  vacantUnits:
                    type: integer
                  status:
                    type: string
                    enum:
                      - active
                      - inactive
                      - maintenance
                  notes:
                    type: string
                required:
                  - id
          headers: {}
      security: []
    put:
      summary: Update Property
      deprecated: false
      description: Update information for a specific property
      tags:
        - Properties
      parameters:
        - name: propertyId
          in: path
          description: 'Resource identifier'
          required: true
          example: 'sZq0gcKRNrOK3hcDugzqJ'
          schema:
            $ref: "#/components/schemas/NanoID"
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                id:
                  $ref: "#/components/schemas/NanoID"
                name:
                  type: string
                address:
                  type: object
                  properties:
                    street:
                      type: string
                    city:
                      type: string
                    prov:
                      type: string
                    state:
                      type: string
                    zipCode:
                      type: string
                propertyType:
                  type: string
                  enum:
                    - apartment
                    - house
                    - condo
                    - commercial
                  description: Optional property type
                totalUnits:
                  type: integer
                  minimum: 1
                vacantUnits:
                  type: integer
                status:
                  type: string
                  enum:
                    - active
                    - inactive
                    - maintenance
                notes:
                  type: string
              required:
                - id
            example: 'sZq0gcKRNrOK3hcDugzqJ'
      responses:
        '200':
          description: Property updated successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Property'
          headers: {}
      security: []
    delete:
      summary: Delete Property
      deprecated: false
      description: Delete a specific property
      tags:
        - Properties
      parameters:
        - name: propertyId
          in: path
          description: 'Resource identifier'
          required: true
          example: sZq0gcKRNrOK3hcDugzqJ
          schema:
            $ref: "#/components/schemas/NanoID"
      responses:
        '204':
          description: Property deleted successfully
          content:
            application/json:
              schema:
                type: object
                properties: {}
              examples:
                '1':
                  summary: 成功示例
                  value: {}
          headers: {}
      security: []
  /properties/{propertyId}/rooms:
    post:
      summary: Create Room
      deprecated: false
      description: Create a new room/unit in a specific property
      tags:
        - Properties
      parameters:
        - name: propertyId
          in: path
          description: 'Resource identifier'
          required: true
          example: 'sZq0gcKRNrOK3hcDugzqJ'
          schema:
            $ref: "#/components/schemas/NanoID"
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                name:
                  type: string
                  description: Room/Unit name
                type:
                  type: string
                  enum:
                    - studio
                    - oneBed
                    - twoBed
                    - commercial
                status:
                  type: string
                  enum:
                    - vacant
                    - occupied
                    - maintenance
                notes:
                  type: string
                  description: A place to keep notes for internal purposes
              required:
                - name
            example: 'sZq0gcKRNrOK3hcDugzqJ'
      responses:
        '201':
          description: Room created successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  id:
                    $ref: "#/components/schemas/NanoID"
                  name:
                    type: string
                    description: Room/Unit name
                  type:
                    type: string
                    enum:
                      - studio
                      - oneBed
                      - twoBed
                      - commercial
                  status:
                    type: string
                    enum:
                      - vacant
                      - occupied
                      - maintenance
                  notes:
                    type: string
                    description: A place to keep notes for internal purposes
                  propertyId:
                    $ref: "#/components/schemas/NanoID"
                required:
                  - name
                  - id
                  - propertyId
          headers: {}
      security: []
  /properties/{propertyId}/rooms/{roomId}:
    get:
      summary: /properties/{propertyId}/rooms/{roomId}
      deprecated: false
      description: 获取指定room的详细信息
      tags:
        - Properties
      parameters:
        - name: propertyId
          in: path
          description: 'Resource identifier'
          required: true
          example: 'sZq0gcKRNrOK3hcDugzqJ'
          schema:
            $ref: "#/components/schemas/NanoID"
        - name: roomId
          in: path
          description: 'Resource identifier'
          required: true
          example: 'sZq0gcKRNrOK3hcDugzqJ'
          schema:
            $ref: "#/components/schemas/NanoID"
      responses:
        '200':
          description: Room details retrieved successfully
          content:
            application/json:
              schema: &ref_0
                $ref: '#/components/schemas/Room'
          headers: {}
      security: []
    put:
      summary: /properties/{propertyId}/rooms/{roomId}
      deprecated: false
      description: 更新指定room的信息
      tags:
        - Properties
      parameters:
        - name: propertyId
          in: path
          description: 'Resource identifier'
          required: true
          example: 'sZq0gcKRNrOK3hcDugzqJ'
          schema:
            $ref: "#/components/schemas/NanoID"
        - name: roomId
          in: path
          description: 'Resource identifier'
          required: true
          example: 'sZq0gcKRNrOK3hcDugzqJ'
          schema:
            $ref: "#/components/schemas/NanoID"
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                id:
                  $ref: "#/components/schemas/NanoID"
                name:
                  type: string
                  description: Room/Unit name
                type:
                  type: string
                  enum:
                    - studio
                    - oneBed
                    - twoBed
                    - commercial
                status:
                  type: string
                  enum:
                    - vacant
                    - occupied
                    - maintenance
                notes:
                  type: string
                  description: A place to keep notes for internal purposes
                propertyId:
                  $ref: "#/components/schemas/NanoID"
              required:
                - name
                - id
                - propertyId
            example: 'sZq0gcKRNrOK3hcDugzqJ'
      responses:
        '200':
          description: Room updated successfully
          content:
            application/json:
              schema: *ref_0
          headers: {}
      security: []
    delete:
      summary: /properties/{propertyId}/rooms/{roomId}
      deprecated: false
      description: 删除指定的room
      tags:
        - Properties
      parameters:
        - name: propertyId
          in: path
          description: 'Resource identifier'
          required: true
          example: 'sZq0gcKRNrOK3hcDugzqJ'
          schema:
            $ref: "#/components/schemas/NanoID"
        - name: roomId
          in: path
          description: 'Resource identifier'
          required: true
          example: 'sZq0gcKRNrOK3hcDugzqJ'
          schema:
            $ref: "#/components/schemas/NanoID"
      responses:
        '204':
          description: Room deleted successfully
          content:
            application/json:
              schema:
                type: object
                properties: {}
          headers: {}
      security: []
  /leases:
    post:
      summary: Create Lease
      deprecated: false
      description: Create a new lease agreement
      tags:
        - Leases
      parameters: []
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                propertyId:
                  $ref: "#/components/schemas/NanoID"
                roomId:
                  $ref: "#/components/schemas/NanoID"
                rentAmount:
                  type: number
                startDate:
                  type: string
                  format: date
                endDate:
                  type: string
                  format: date
                status:
                  type: string
                  enum:
                    - active
                    - ended
                    - terminated
                    - pending
                  description: 状态
                additionalMonthlyFees:
                  type: number
                  description: Extra monthly fees
                keyDeposit:
                  type: number
                rentDeposit:
                  type: number
                otherDeposits:
                  type: number
                rentDueDay:
                  type: integer
                  minimum: 1
                  maximum: 31
                rentReporting:
                  type: boolean
                  description: Enable rent reporting
                autoPay:
                  type: boolean
                  description: Enable automatic payments
                owingBalance:
                  type: number
                lastPaymentDate:
                  type: string
                  format: date
                tenantId:
                  $ref: "#/components/schemas/NanoID"
              required:
                - propertyId
                - roomId
                - rentAmount
                - startDate
                - endDate
            example: 'sZq0gcKRNrOK3hcDugzqJ'
      responses:
        '201':
          description: Lease created successfully
          content:
            application/json:
              schema: &ref_1
                $ref: '#/components/schemas/Lease'
          headers: {}
      security: []
    get:
      summary: Get Leases
      deprecated: false
      description: Get all leases for the authenticated user with optional filtering by propertyId, status, etc.
      tags:
        - Leases
      parameters:
        - name: propertyId
          in: query
          description: 'Resource identifier'
          required: false
          schema:
            $ref: "#/components/schemas/NanoID"
        - name: status
          in: query
          description: 'Resource identifier'
          required: false
          schema:
            type: string
            enum:
              - active
              - ended
              - terminated
        - name: limit
          in: query
          description: 'Resource identifier'
          required: false
          schema:
            type: integer
            default: 10
        - name: name
          in: query
          description: 'Resource identifier'
          required: false
          schema:
            type: string
      responses:
        '200':
          description: List of leases for the user
          content:
            application/json:
              schema:
                type: object
                properties:
                  items:
                    type: array
                    items:
                      $ref: '#/components/schemas/LeaseList'
                  total:
                    type: integer
              examples:
                '1':
                  summary: 成功示例
                  value:
                    items:
                      - id: Oiu5_hL3PBuc_vLmZOFia
                        propertyId: '78'
                        status: active
                        owingBalance: 9
                        lastPaymentDate: '2024-04-27'
                        tenantId: '79'
                        userId: '65'
                    total: 47
          headers: {}
      security: []
  /leases/{leaseId}:
    get:
      summary: /leases/{leaseId}
      deprecated: false
      description: Get detailed information for a specific lease
      tags:
        - Leases
      parameters:
        - name: leaseId
          in: path
          description: 'Resource identifier'
          required: true
          example: 'sZq0gcKRNrOK3hcDugzqJ'
          schema:
            $ref: "#/components/schemas/NanoID"
      responses:
        '200':
          description: Lease details retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  id:
                    $ref: "#/components/schemas/NanoID"
                  propertyId:
                    $ref: "#/components/schemas/NanoID"
                  roomId:
                    $ref: "#/components/schemas/NanoID"
                  rentAmount:
                    type: number
                  startDate:
                    type: string
                    format: date
                  endDate:
                    type: string
                    format: date
                  status:
                    type: string
                    enum:
                      - active
                      - ended
                      - terminated
                    description: 状态
                  additionalMonthlyFees:
                    type: number
                    description: Extra monthly fees
                  keyDeposit:
                    type: number
                  rentDeposit:
                    type: number
                  otherDeposits:
                    type: number
                  rentDueDay:
                    type: integer
                    minimum: 1
                    maximum: 31
                  rentReporting:
                    type: boolean
                    description: Enable rent reporting
                  autoPay:
                    type: boolean
                    description: Enable automatic payments
                  owingBalance:
                    type: number
                  lastPaymentDate:
                    type: string
                    format: date
                  tenantId:
                    $ref: "#/components/schemas/NanoID"
                required:
                  - propertyId
                  - roomId
                  - rentAmount
                  - startDate
                  - endDate
                  - id
          headers: {}
      security: []
    put:
      summary: /leases/{leaseId}
      deprecated: false
      description: 更新指定lease合同的信息
      tags:
        - Leases
      parameters:
        - name: leaseId
          in: path
          description: 'Resource identifier'
          required: true
          example: 'sZq0gcKRNrOK3hcDugzqJ'
          schema:
            $ref: "#/components/schemas/NanoID"
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                id:
                  $ref: "#/components/schemas/NanoID"
                propertyId:
                  $ref: "#/components/schemas/NanoID"
                roomId:
                  $ref: "#/components/schemas/NanoID"
                rentAmount:
                  type: number
                startDate:
                  type: string
                  format: date
                endDate:
                  type: string
                  format: date
                status:
                  type: string
                  enum:
                    - active
                    - ended
                    - terminated
                    - pending
                  description: 状态
                additionalMonthlyFees:
                  type: number
                  description: Extra monthly fees
                keyDeposit:
                  type: number
                rentDeposit:
                  type: number
                otherDeposits:
                  type: number
                rentDueDay:
                  type: integer
                  minimum: 1
                  maximum: 31
                rentReporting:
                  type: boolean
                  description: Enable rent reporting
                autoPay:
                  type: boolean
                  description: Enable automatic payments
                owingBalance:
                  type: number
                lastPaymentDate:
                  type: string
                  format: date
                tenantId:
                  $ref: "#/components/schemas/NanoID"
              required:
                - propertyId
                - roomId
                - rentAmount
                - startDate
                - endDate
                - id
            example: 'sZq0gcKRNrOK3hcDugzqJ'
      responses:
        '200':
          description: Lease updated successfully
          content:
            application/json:
              schema: *ref_1
          headers: {}
      security: []
    delete:
      summary: /leases/{leaseId}
      deprecated: false
      description: 删除指定的lease
      tags:
        - Leases
      parameters:
        - name: leaseId
          in: path
          description: 'Resource identifier'
          required: true
          example: 'sZq0gcKRNrOK3hcDugzqJ'
          schema:
            $ref: "#/components/schemas/NanoID"
      responses:
        '204':
          description: Lease deleted successfully
          content:
            application/json:
              schema:
                type: object
                properties: {}
          headers: {}
      security: []
  /leases/{leaseId}/tenant-payments:
    post:
      summary: /leases/{leaseId}/tenant-payments
      deprecated: false
      description: Add a new tenant payment record for a specific lease
      tags:
        - Payments
      parameters:
        - name: leaseId
          in: path
          description: 'Resource identifier'
          required: true
          example: 'sZq0gcKRNrOK3hcDugzqJ'
          schema:
            $ref: "#/components/schemas/NanoID"
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                id:
                  $ref: "#/components/schemas/NanoID"
                amount:
                  type: number
                  description: Amount paid by tenant
                date:
                  type: string
                  format: date
                  description: Tenant payment date
                notes:
                  type: string
                remainingBalance:
                  type: number
                  description: Tenant's remaining balance after payment
                status:
                  type: string
                  enum:
                    - pending
                    - completed
                    - failed
                leaseId:
                  $ref: "#/components/schemas/NanoID"
              required:
                - amount
                - date
                - id
                - leaseId
            example: 'sZq0gcKRNrOK3hcDugzqJ'
      responses:
        '201':
          description: Payment recorded successfully
          content:
            application/json:
              schema: &ref_2
                $ref: '#/components/schemas/Payment'
          headers: {}
      security: []
  /leases/{leaseId}/tenant-payments/{tenantPaymentId}:
    get:
      summary: /leases/{leaseId}/tenant-payments/{tenantPaymentId}
      deprecated: false
      description: Get detailed information for a specific tenant payment
      tags:
        - Payments
      parameters:
        - name: leaseId
          in: path
          description: 'Resource identifier'
          required: true
          example: 'sZq0gcKRNrOK3hcDugzqJ'
          schema:
            $ref: "#/components/schemas/NanoID"
        - name: tenantPaymentId
          in: path
          description: 'Resource identifier'
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Payment details retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  id:
                    $ref: "#/components/schemas/NanoID"
                  amount:
                    type: number
                    description: Amount paid
                  date:
                    type: string
                    format: date
                    description: Payment date
                  notes:
                    type: string
                  remainingBalance:
                    type: number
                    description: Remaining balance after payment
                  status:
                    type: string
                    enum:
                      - pending
                      - completed
                      - failed
                  leaseId:
                    $ref: "#/components/schemas/NanoID"
                required:
                  - amount
                  - date
                  - id
                  - leaseId
          headers: {}
      security: []
    put:
      summary: /leases/{leaseId}/tenant-payments/{tenantPaymentId}
      deprecated: false
      description: Update information for a specific tenant payment
      tags:
        - Payments
      parameters:
        - name: leaseId
          in: path
          description: 'Resource identifier'
          required: true
          example: 'sZq0gcKRNrOK3hcDugzqJ'
          schema:
            $ref: "#/components/schemas/NanoID"
        - name: tenantPaymentId
          in: path
          description: 'Resource identifier'
          required: true
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                id:
                  $ref: "#/components/schemas/NanoID"
                amount:
                  type: number
                  description: Amount paid
                date:
                  type: string
                  format: date
                  description: Payment date
                notes:
                  type: string
                remainingBalance:
                  type: number
                  description: Remaining balance after payment
                status:
                  type: string
                  enum:
                    - pending
                    - completed
                    - failed
                leaseId:
                  $ref: "#/components/schemas/NanoID"
              required:
                - amount
                - date
                - id
                - leaseId
            example: 'sZq0gcKRNrOK3hcDugzqJ'
      responses:
        '200':
          description: Payment updated successfully
          content:
            application/json:
              schema: *ref_2
          headers: {}
      security: []
    delete:
      summary: /leases/{leaseId}/tenant-payments/{tenantPaymentId}
      deprecated: false
      description: Delete a specific tenant payment record
      tags:
        - Payments
      parameters:
        - name: leaseId
          in: path
          description: 'Resource identifier'
          required: true
          example: 'sZq0gcKRNrOK3hcDugzqJ'
          schema:
            $ref: "#/components/schemas/NanoID"
        - name: tenantPaymentId
          in: path
          description: 'Resource identifier'
          required: true
          schema:
            type: string
      responses:
        '204':
          description: Payment deleted successfully
          content:
            application/json:
              schema:
                type: object
                properties: {}
          headers: {}
      security: []
  /auth/login:
    post:
      summary: User Login
      deprecated: false
      description: 'Authenticate user with email and password'
      tags:
        - Authentication
      parameters: []
      requestBody:
        content:
          application/json:
            schema:
              type: object
              required:
                - email
                - password
              properties:
                email:
                  type: string
                  format: email
                password:
                  type: string
            example: 'sZq0gcKRNrOK3hcDugzqJ'
      responses:
        '200':
          description: Login successful
          content:
            application/json:
              schema:
                type: object
                properties:
                  token:
                    type: string
                  user:
                    $ref: '#/components/schemas/User'
          headers: {}
      security: []
  /auth/logout:
    post:
      summary: User Logout
      deprecated: false
      description: 'Log out the current user and invalidate session'
      tags:
        - Authentication
      parameters: []
      responses:
        '204':
          description: Logout successful
          content:
            application/json:
              schema:
                type: object
                properties: {}
          headers: {}
      security: []
  /leases/{leaseId}/tenants:
    post:
      summary: /leases/{leaseId}/tenants
      deprecated: false
      description: Add a new tenant to a specific lease
      tags:
        - Tenants
      parameters:
        - name: leaseId
          in: path
          description: 'Resource identifier'
          required: true
          example: 'sZq0gcKRNrOK3hcDugzqJ'
          schema:
            $ref: "#/components/schemas/NanoID"
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                id:
                  $ref: "#/components/schemas/NanoID"
                  readOnly: true
                firstName:
                  type: string
                  description: Legal first name
                middleName:
                  type: string
                lastName:
                  type: string
                  description: Legal last name
                email:
                  type: string
                  format: email
                phoneNumber:
                  type: string
                sinNumber:
                  type: string
                  description: Social Insurance Number
                notes:
                  type: string
                  description: A place to keep notes for internal purposes
                leaseId:
                  type: string
                tenantId:
                  type: string
                  description: 绑定的租客用户ID（邀请完成后才有）
              required:
                - firstName
                - lastName
                - email
                - id
                - leaseId
            example: 'sZq0gcKRNrOK3hcDugzqJ'
      responses:
        '201':
          description: Tenant added successfully
          content:
            application/json:
              schema: &ref_3
                $ref: '#/components/schemas/Tenant'
          headers: {}
      security: []
  /leases/{leaseId}/tenants/{tenantId}:
    get:
      summary: /leases/{leaseId}/tenants/{tenantId}
      deprecated: false
      description: Get detailed information for a specific tenant
      tags:
        - Tenants
      parameters:
        - name: leaseId
          in: path
          description: 'Resource identifier'
          required: true
          example: 'sZq0gcKRNrOK3hcDugzqJ'
          schema:
            $ref: "#/components/schemas/NanoID"
        - name: tenantId
          in: path
          description: 'Resource identifier'
          required: true
          example: 'sZq0gcKRNrOK3hcDugzqJ'
          schema:
            $ref: "#/components/schemas/NanoID"
      responses:
        '200':
          description: Tenant details retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  id:
                    $ref: "#/components/schemas/NanoID"
                    readOnly: true
                  firstName:
                    type: string
                    description: Legal first name
                  middleName:
                    type: string
                  lastName:
                    type: string
                    description: Legal last name
                  email:
                    type: string
                    format: email
                  phoneNumber:
                    type: string
                  sinNumber:
                    type: string
                    description: Social Insurance Number
                  notes:
                    type: string
                    description: A place to keep notes for internal purposes
                  leaseId:
                    $ref: "#/components/schemas/NanoID"
                  tenantId:
                    type: string
                    description: 绑定的租客用户ID（邀请完成后才有）
                required:
                  - firstName
                  - lastName
                  - email
                  - id
                  - leaseId
          headers: {}
      security: []
    put:
      summary: /leases/{leaseId}/tenants/{tenantId}
      deprecated: false
      description: Update information for a specific tenant
      tags:
        - Tenants
      parameters:
        - name: leaseId
          in: path
          description: 'Resource identifier'
          required: true
          example: 'sZq0gcKRNrOK3hcDugzqJ'
          schema:
            $ref: "#/components/schemas/NanoID"
        - name: tenantId
          in: path
          description: 'Resource identifier'
          required: true
          example: 'sZq0gcKRNrOK3hcDugzqJ'
          schema:
            $ref: "#/components/schemas/NanoID"
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                id:
                  $ref: "#/components/schemas/NanoID"
                  readOnly: true
                firstName:
                  type: string
                  description: Legal first name
                middleName:
                  type: string
                lastName:
                  type: string
                  description: Legal last name
                email:
                  type: string
                  format: email
                phoneNumber:
                  type: string
                sinNumber:
                  type: string
                  description: Social Insurance Number
                notes:
                  type: string
                  description: A place to keep notes for internal purposes
                leaseId:
                  $ref: "#/components/schemas/NanoID"
                tenantId:
                  type: string
                  description: 绑定的租客用户ID（邀请完成后才有）
              required:
                - firstName
                - lastName
                - email
                - id
                - leaseId
            example: 'sZq0gcKRNrOK3hcDugzqJ'
      responses:
        '200':
          description: Tenant updated successfully
          content:
            application/json:
              schema: *ref_3
          headers: {}
      security: []
    delete:
      summary: /leases/{leaseId}/tenants/{tenantId}
      deprecated: false
      description: Remove a tenant from a lease
      tags:
        - Tenants
      parameters:
        - name: leaseId
          in: path
          description: 'Resource identifier'
          required: true
          example: 'sZq0gcKRNrOK3hcDugzqJ'
          schema:
            $ref: "#/components/schemas/NanoID"
        - name: tenantId
          in: path
          description: 'Resource identifier'
          required: true
          example: 'sZq0gcKRNrOK3hcDugzqJ'
          schema:
            $ref: "#/components/schemas/NanoID"
      responses:
        '204':
          description: Tenant removed successfully
          content:
            application/json:
              schema:
                type: object
                properties: {}
          headers: {}
      security: []
  /v1/referral-codes/{code}/validation:
    get:
      summary: /v1/referral-codes/{code}/validation
      deprecated: false
      description: 验证推荐码是否有效 (RESTful)
      tags:
        - Referrals
      parameters:
        - name: code
          in: path
          description: 推荐码
          required: true
          schema:
            type: string
      responses:
        '200':
          description: 推荐码验证成功
          content:
            application/json:
              schema:
                type: object
                properties:
                  valid:
                    type: boolean
                  message:
                    type: string
        '404':
          description: 推荐码不存在
      security: []
  /v1/referral-code-applications:
    post:
      summary: /v1/referral-code-applications
      deprecated: false
      description: 应用推荐码 (RESTful)
      tags:
        - Referrals
      parameters: []
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                code:
                  type: string
                  description: 推荐码
              required:
                - code
      responses:
        '200':
          description: 推荐码应用成功
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                  message:
                    type: string
        '400':
          description: 推荐码无效或已使用
      security: []
  /v1/documents/{fileId}:
    get:
      summary: /v1/documents/{fileId}
      deprecated: false
      description: 下载文档文件 (RESTful)
      tags:
        - Documents
      parameters:
        - name: fileId
          in: path
          description: 文件ID
          required: true
          schema:
            type: string
      responses:
        '200':
          description: 文件下载成功
          content:
            application/octet-stream:
              schema:
                type: string
                format: binary
        '404':
          description: 文件不存在
      security: []
  /v1/subscriptions/{id}:
    delete:
      summary: /v1/subscriptions/{id}
      deprecated: false
      description: 取消订阅 (RESTful)
      tags:
        - Subscriptions
      parameters:
        - name: id
          in: path
          description: 订阅ID
          required: true
          schema:
            type: string
      responses:
        '204':
          description: 订阅取消成功
        '404':
          description: 订阅不存在
      security: []
  /v1/invitations/{code}/status:
    put:
      summary: /v1/invitations/{code}/status
      deprecated: false
      description: 更新邀请状态 (RESTful)
      tags:
        - Invitations
      parameters:
        - name: code
          in: path
          description: 邀请码
          required: true
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                status:
                  type: string
                  enum:
                    - active
                    - expired
                    - cancelled
                  description: 邀请状态
                leaseId:
                  $ref: "#/components/schemas/NanoID"
                  description: 关联的租约ID
              required:
                - status
      responses:
        '200':
          description: 邀请状态更新成功
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
        '404':
          description: 邀请不存在
      security: []
  /v1/leases/{leaseId}/tenants/self:
    put:
      summary: /v1/leases/{leaseId}/tenants/self
      deprecated: false
      description: 租客更新自己的tenantId (RESTful)
      tags:
        - Leases
      parameters:
        - name: leaseId
          in: path
          description: 租约ID
          required: true
          schema:
            $ref: "#/components/schemas/NanoID"
      responses:
        '200':
          description: 租客信息更新成功
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
        '403':
          description: 无权限或未找到
      security: []
components:
  schemas:
    Room:
      type: object
      required:
        - name
        - id
        - propertyId
        - userId
      properties:
        id:
          $ref: "#/components/schemas/NanoID"
        name:
          type: string
          description: Room/Unit name
        type:
          type: string
          enum:
            - studio
            - oneBed
            - twoBed
            - commercial
        status:
          type: string
          enum:
            - vacant
            - occupied
            - maintenance
        notes:
          type: string
          description: A place to keep notes for internal purposes
        propertyId:
          $ref: "#/components/schemas/NanoID"
        userId:
          $ref: "#/components/schemas/NanoID"
    Lease:
      type: object
      required:
        - propertyId
        - roomId
        - rentAmount
        - startDate
        - endDate
        - id
        - userId
      properties:
        id:
          $ref: "#/components/schemas/NanoID"
        propertyId:
          $ref: "#/components/schemas/NanoID"
        roomId:
          $ref: "#/components/schemas/NanoID"
        rentAmount:
          type: number
        startDate:
          type: string
          format: date
        endDate:
          type: string
          format: date
        status:
          type: string
          enum:
            - active
            - ended
            - terminated
            - pending
          description: 状态
        additionalMonthlyFees:
          type: number
          description: Extra monthly fees
        keyDeposit:
          type: number
        rentDeposit:
          type: number
        otherDeposits:
          type: number
        rentDueDay:
          type: integer
          minimum: 1
          maximum: 31
        rentReporting:
          type: boolean
          description: Enable rent reporting
        autoPay:
          type: boolean
          description: Enable automatic payments
        owingBalance:
          type: number
        lastPaymentDate:
          type: string
          format: date
        tenantId:
          $ref: "#/components/schemas/NanoID"
        userId:
          $ref: "#/components/schemas/NanoID"
    PropertyList:
      type: object
      required:
        - id
        - OwingBalance
        - vacantUnits
        - totalUnits
        - name
      properties:
        id:
          $ref: "#/components/schemas/NanoID"
        name:
          type: string
        totalUnits:
          type: integer
          minimum: 1
        vacantUnits:
          type: integer
        OwingBalance:
          type: number
    LeaseList:
      type: object
      required:
        - propertyId
        - id
        - tenantId
        - userId
      properties:
        id:
          $ref: "#/components/schemas/NanoID"
        propertyId:
          $ref: "#/components/schemas/NanoID"
        status:
          type: string
          enum:
            - active
            - ended
            - terminated
          description: 状态
        owingBalance:
          type: number
        lastPaymentDate:
          type: string
          format: date
        tenantId:
          $ref: "#/components/schemas/NanoID"
        userId:
          $ref: "#/components/schemas/NanoID"
    User:
      type: object
      required:
        - email
        - username
        - id
      properties:
        id:
          $ref: "#/components/schemas/NanoID"
        email:
          type: string
          format: email
        username:
          type: string
        settings:
          type: object
          properties: {}
    Property:
      type: object
      required:
        - id
        - userId
      properties:
        id:
          $ref: "#/components/schemas/NanoID"
        name:
          type: string
        address:
          type: object
          properties:
            street:
              type: string
            city:
              type: string
            prov:
              type: string
            state:
              type: string
            zipCode:
              type: string
        propertyType:
          type: string
          enum:
            - apartment
            - house
            - condo
            - commercial
          description: Optional property type
        totalUnits:
          type: integer
          minimum: 1
        vacantUnits:
          type: integer
        status:
          type: string
          enum:
            - active
            - inactive
            - maintenance
        notes:
          type: string
        userId:
          $ref: "#/components/schemas/NanoID"
    Payment:
      type: object
      required:
        - amount
        - date
        - id
        - leaseId
        - userId
      properties:
        id:
          $ref: "#/components/schemas/NanoID"
        amount:
          type: number
          description: Amount paid
        date:
          type: string
          format: date
          description: Payment date
        notes:
          type: string
        remainingBalance:
          type: number
          description: Remaining balance after payment
        status:
          type: string
          enum:
            - pending
            - completed
            - failed
        leaseId:
          $ref: "#/components/schemas/NanoID"
        userId:
          $ref: "#/components/schemas/NanoID"
    Tenant:
      type: object
      required:
        - firstName
        - lastName
        - email
        - id
        - leaseId
        - userId
      properties:
        id:
          $ref: "#/components/schemas/NanoID"
          readOnly: true
        firstName:
          type: string
          description: Legal first name
        middleName:
          type: string
        lastName:
          type: string
          description: Legal last name
        email:
          type: string
          format: email
        phoneNumber:
          type: string
        sinNumber:
          type: string
          description: Social Insurance Number
        notes:
          type: string
          description: A place to keep notes for internal purposes
        leaseId:
          $ref: "#/components/schemas/NanoID"
        tenantId:
          type: string
          description: 绑定的租客用户ID（邀请完成后才有）
        userId:
          $ref: "#/components/schemas/NanoID"
    Problem:
      type: object
      required:
        - id
        - userId
        - type
        - description
        - contactEmail
        - status
      properties:
        id:
          $ref: "#/components/schemas/NanoID"
        userId:
          $ref: "#/components/schemas/NanoID"
        type:
          type: string
          enum:
            - billing
            - payment
            - system
            - other
          description: 问题类型
        description:
          type: string
          description: 问题描述
        attachment:
          type: string
          description: 附件路径
        contactEmail:
          type: string
          format: email
          description: 联系邮箱
        status:
          type: string
          enum:
            - pending
            - in_progress
            - resolved
            - closed
          description: 问题状态
        leaseId:
          $ref: "#/components/schemas/NanoID"
          description: 关联的租约ID
        propertyId:
          $ref: "#/components/schemas/NanoID"
          description: 关联的物业ID
        createdAt:
          type: string
          format: date-time
          description: 创建时间
        updatedAt:
          type: string
          format: date-time
          description: 更新时间
    # Common schema components
    NanoID:
      type: string
      pattern: "^[0-9A-Za-z]{11}$"
      description: "NanoID - 11 character alphanumeric identifier"
      example: "sZq0gcKRNrOK3hcDugzqJ"
    ReferralCode:
      type: string
      pattern: "^[0-9A-Za-z]{6}$"
      description: "Referral code - 6 character alphanumeric identifier"
      example: "ABC123"
  securitySchemes:
    bearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
servers:
  - url: http://localhost:8089
    description: Development server
  - url: https://api.rentreport.com
    description: Production server
security:
  - bearerAuth: []
