package controller

import (
	"context"
	"fmt"
	"net/http"
	"rent_report/entities"
	"rent_report/middleware"
	"rent_report/router"
	"rent_report/utils"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/real-rm/gomongo"
	"go.mongodb.org/mongo-driver/bson"
)

type AdminController struct{}

// UserSafe 用于API安全返回用户信息
type UserSafe struct {
	ID          string `json:"id"`
	Username    string `json:"username"`
	Email       string `json:"email"`
	Role        string `json:"role"`
	Status      string `json:"status"`
	ViewType    string `json:"viewType"`
	AccountType string `json:"accountType"`
}

func init() {
	router.Register(&AdminController{})
}

func (c *AdminController) RegisterRoutes(r *gin.Engine) {
	// 管理员路由组
	adminRouter := r.Group("/v1/admin")
	adminRouter.Use(middleware.RequireAdmin())

	// 用户管理
	adminRouter.GET("/users", c.handleListUsers)
	adminRouter.GET("/users/:id", c.handleGetUser)
	adminRouter.PUT("/users/:id", c.handleUpdateUser)
	adminRouter.DELETE("/users/:id", c.handleDeleteUser)
	adminRouter.GET("/users/:id/properties", c.handleUserProperties)
	adminRouter.GET("/users/:id/leases", c.handleUserLeases)
	adminRouter.GET("/users/:id/payments", c.handleUserPayments)

	// 日志管理
	adminRouter.POST("/logs", c.handleCreateAdminLog)
	adminRouter.GET("/logs", c.handleListAdminLogs)

	// 属性管理
	adminRouter.GET("/properties", c.handleListProperties)
	adminRouter.POST("/properties", c.handleCreateProperty)
	adminRouter.GET("/properties/:id", c.handleGetProperty)
	adminRouter.PUT("/properties/:id", c.handleUpdateProperty)
	adminRouter.DELETE("/properties/:id", c.handleDeleteProperty)
	adminRouter.POST("/properties/:id/rooms", c.handleCreateRoom)
	adminRouter.PUT("/properties/:id/rooms/:roomId", c.handleUpdateRoom)
	adminRouter.DELETE("/properties/:id/rooms/:roomId", c.handleDeleteRoom)

	// 租约管理
	adminRouter.GET("/leases", c.handleListLeases)
	adminRouter.POST("/leases", c.handleCreateLease)
	adminRouter.GET("/leases/:id", c.handleGetLease)
	adminRouter.PUT("/leases/:id", c.handleUpdateLease)
	adminRouter.DELETE("/leases/:id", c.handleDeleteLease)
	adminRouter.GET("/leases/:id/payments", c.handleLeasePayments)

	// 租户管理
	adminRouter.GET("/tenants", c.handleListTenants)
	adminRouter.GET("/tenants/:id", c.handleGetTenant)
	adminRouter.PUT("/tenants/:id", c.handleUpdateTenant)
	adminRouter.DELETE("/tenants/:id", c.handleDeleteTenant)
	adminRouter.POST("/leases/:id/tenants", c.handleAdminAddLeaseTenant)

	// 新增接口
	adminRouter.GET("/usernames", c.handleBatchUsernames)
}

// handleListUsers 获取用户列表
func (c *AdminController) handleListUsers(ctx *gin.Context) {
	// 解析分页参数
	limit := 50 // 管理员默认限制
	if limitStr := ctx.Query("limit"); limitStr != "" {
		if l, err := strconv.Atoi(limitStr); err == nil && l > 0 && l <= 200 {
			limit = l
		}
	}

	page := 1
	if pageStr := ctx.Query("page"); pageStr != "" {
		if p, err := strconv.Atoi(pageStr); err == nil && p > 0 {
			page = p
		}
	}

	// 构建过滤器
	filters := map[string]string{
		"keyword": ctx.Query("keyword"),
		"status":  ctx.Query("status"),
		"role":    ctx.Query("role"),
	}

	// 使用分页查询
	users, total, err := c.getUsersWithPagination(ctx.Request.Context(), limit, page, filters)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	// 脱敏返回
	safeUsers := make([]UserSafe, 0, len(users))
	for _, user := range users {
		safeUsers = append(safeUsers, UserSafe{
			ID:          user.ID,
			Username:    user.Username,
			Email:       user.Email,
			Role:        user.Role,
			Status:      user.Status,
			ViewType:    user.ViewType,
			AccountType: user.AccountType,
		})
	}

	ctx.JSON(http.StatusOK, gin.H{
		"data":       safeUsers,
		"total":      total,
		"page":       page,
		"limit":      limit,
		"totalPages": (int(total) + limit - 1) / limit,
	})
}

// handleGetUser 获取单个用户信息
func (c *AdminController) handleGetUser(ctx *gin.Context) {
	userID := ctx.Param("id")

	user, err := entities.GetUserByID(ctx.Request.Context(), userID)
	if err != nil {
		ctx.JSON(http.StatusNotFound, gin.H{"error": err.Error()})
		return
	}

	// 脱敏返回
	safeUser := UserSafe{
		ID:          user.ID,
		Username:    user.Username,
		Email:       user.Email,
		Role:        user.Role,
		Status:      user.Status,
		ViewType:    user.ViewType,
		AccountType: user.AccountType,
	}
	ctx.JSON(http.StatusOK, safeUser)
}

// handleUpdateUser 更新用户信息
func (c *AdminController) handleUpdateUser(ctx *gin.Context) {
	userID := ctx.Param("id")

	user, err := entities.GetUserByID(ctx.Request.Context(), userID)
	if err != nil {
		ctx.JSON(http.StatusNotFound, gin.H{"error": err.Error()})
		return
	}

	var updates map[string]interface{}
	if err := ctx.ShouldBindJSON(&updates); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request body"})
		return
	}

	if err := user.UpdateUser(ctx.Request.Context(), updates); err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	ctx.Status(http.StatusOK)
}

// handleDeleteUser 删除用户
func (c *AdminController) handleDeleteUser(ctx *gin.Context) {
	userID := ctx.Param("id")

	user, err := entities.GetUserByID(ctx.Request.Context(), userID)
	if err != nil {
		ctx.JSON(http.StatusNotFound, gin.H{"error": err.Error()})
		return
	}

	if err := user.CloseAccount(ctx.Request.Context()); err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	ctx.Status(http.StatusOK)
}

// handleListProperties 获取属性列表
func (c *AdminController) handleListProperties(ctx *gin.Context) {
	properties, err := entities.GetAllProperties(ctx.Request.Context())
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	ctx.JSON(http.StatusOK, properties)
}

// handleGetProperty 获取单个属性信息
func (c *AdminController) handleGetProperty(ctx *gin.Context) {
	propertyID := ctx.Param("id")

	property, err := entities.GetPropertyByIDAdmin(ctx.Request.Context(), propertyID)
	if err != nil {
		ctx.JSON(http.StatusNotFound, gin.H{"error": err.Error()})
		return
	}

	ctx.JSON(http.StatusOK, property)
}

// handleUpdateProperty 更新属性信息
func (c *AdminController) handleUpdateProperty(ctx *gin.Context) {
	propertyID := ctx.Param("id")

	property, err := entities.GetPropertyByIDAdmin(ctx.Request.Context(), propertyID)
	if err != nil {
		ctx.JSON(http.StatusNotFound, gin.H{"error": err.Error()})
		return
	}

	var updates map[string]interface{}
	if err := ctx.ShouldBindJSON(&updates); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request body"})
		return
	}

	// 将updates字段赋值到property对象
	if v, ok := updates["name"]; ok {
		property.Name, _ = v.(string)
	}
	if v, ok := updates["notes"]; ok {
		property.Notes, _ = v.(string)
	}
	if v, ok := updates["address"]; ok {
		if addrMap, ok := v.(map[string]interface{}); ok {
			if s, ok := addrMap["street"].(string); ok {
				property.Address.Street = s
			}
			if u, ok := addrMap["unit"].(string); ok {
				property.Address.Unit = u
			}
			if c, ok := addrMap["city"].(string); ok {
				property.Address.City = c
			}
			if co, ok := addrMap["country"].(string); ok {
				property.Address.Country = co
			}
		}
	}

	if err := property.Update(ctx.Request.Context()); err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	ctx.Status(http.StatusOK)
}

// handleDeleteProperty 删除属性
func (c *AdminController) handleDeleteProperty(ctx *gin.Context) {
	propertyID := ctx.Param("id")

	property, err := entities.GetPropertyByIDAdmin(ctx.Request.Context(), propertyID)
	if err != nil {
		ctx.JSON(http.StatusNotFound, gin.H{"error": err.Error()})
		return
	}

	if err := property.DeleteAdmin(ctx.Request.Context()); err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	ctx.Status(http.StatusOK)
}

// handleListLeases 获取租约列表
func (c *AdminController) handleListLeases(ctx *gin.Context) {
	leases, err := entities.GetAllLeases(ctx.Request.Context())
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	ctx.JSON(http.StatusOK, leases)
}

// handleGetLease 获取单个租约信息
func (c *AdminController) handleGetLease(ctx *gin.Context) {
	leaseID := ctx.Param("id")

	// admin接口不做用户校验，userID传空，保证GetLease能查出所有字段
	lease, err := entities.GetLease(ctx.Request.Context(), leaseID, "")
	if err != nil {
		ctx.JSON(http.StatusNotFound, gin.H{"error": err.Error()})
		return
	}

	ctx.JSON(http.StatusOK, lease)
}

// handleUpdateLease 更新租约信息
func (c *AdminController) handleUpdateLease(ctx *gin.Context) {
	leaseID := ctx.Param("id")

	lease, err := entities.GetLeaseByID(ctx.Request.Context(), leaseID)
	if err != nil {
		ctx.JSON(http.StatusNotFound, gin.H{"error": err.Error()})
		return
	}

	var updates map[string]interface{}
	if err := ctx.ShouldBindJSON(&updates); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request body"})
		return
	}

	// 将updates字段赋值到lease对象，忽略note
	if v, ok := updates["rentAmount"]; ok {
		if f, ok := v.(float64); ok {
			lease.RentAmount = f
		}
	}
	if v, ok := updates["additionalMonthlyFees"]; ok {
		if f, ok := v.(float64); ok {
			lease.AdditionalMonthlyFees = f
		}
	}
	if v, ok := updates["keyDeposit"]; ok {
		if f, ok := v.(float64); ok {
			lease.KeyDeposit = f
		}
	}
	if v, ok := updates["rentDeposit"]; ok {
		if f, ok := v.(float64); ok {
			lease.RentDeposit = f
		}
	}
	if v, ok := updates["otherDeposits"]; ok {
		if f, ok := v.(float64); ok {
			lease.OtherDeposits = f
		}
	}
	if v, ok := updates["status"]; ok {
		if s, ok := v.(string); ok {
			lease.Status = s
		}
	}
	if v, ok := updates["rentDueDay"]; ok {
		if i, ok := v.(float64); ok {
			lease.RentDueDay = int(i)
		}
	}
	if v, ok := updates["autoPay"]; ok {
		if b, ok := v.(bool); ok {
			lease.AutoPay = b
		}
	}
	if v, ok := updates["rentReporting"]; ok {
		if b, ok := v.(bool); ok {
			lease.RentReporting = b
		}
	}
	if v, ok := updates["startDate"]; ok {
		if s, ok := v.(string); ok {
			lease.StartDate = s
		}
	}
	if v, ok := updates["endDate"]; ok {
		if s, ok := v.(string); ok {
			lease.EndDate = s
		}
	}

	if err := lease.Update(ctx.Request.Context()); err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	ctx.Status(http.StatusOK)
}

// handleDeleteLease 删除租约
func (c *AdminController) handleDeleteLease(ctx *gin.Context) {
	leaseID := ctx.Param("id")

	lease, err := entities.GetLeaseByID(ctx.Request.Context(), leaseID)
	if err != nil {
		ctx.JSON(http.StatusNotFound, gin.H{"error": err.Error()})
		return
	}

	if err := lease.Delete(ctx.Request.Context()); err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	ctx.Status(http.StatusOK)
}

// handleListTenants 获取租户列表
func (c *AdminController) handleListTenants(ctx *gin.Context) {
	tenants, err := entities.GetAllTenants(ctx.Request.Context())
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	ctx.JSON(http.StatusOK, tenants)
}

// handleGetTenant 获取单个租户信息
func (c *AdminController) handleGetTenant(ctx *gin.Context) {
	tenantID := ctx.Param("id")

	tenant, err := entities.GetTenantByID(ctx.Request.Context(), tenantID)
	if err != nil {
		ctx.JSON(http.StatusNotFound, gin.H{"error": err.Error()})
		return
	}

	ctx.JSON(http.StatusOK, tenant)
}

// handleUpdateTenant 更新租户信息
func (c *AdminController) handleUpdateTenant(ctx *gin.Context) {
	tenantID := ctx.Param("id")

	tenant, err := entities.GetTenantByID(ctx.Request.Context(), tenantID)
	if err != nil {
		ctx.JSON(http.StatusNotFound, gin.H{"error": err.Error()})
		return
	}

	var updates map[string]interface{}
	if err := ctx.ShouldBindJSON(&updates); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request body"})
		return
	}

	if err := tenant.UpdateFields(ctx.Request.Context(), updates); err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	ctx.Status(http.StatusOK)
}

// handleDeleteTenant 删除租户
func (c *AdminController) handleDeleteTenant(ctx *gin.Context) {
	tenantID := ctx.Param("id")

	tenant, err := entities.GetTenantByID(ctx.Request.Context(), tenantID)
	if err != nil {
		ctx.JSON(http.StatusNotFound, gin.H{"error": err.Error()})
		return
	}

	if err := tenant.Delete(ctx.Request.Context()); err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	ctx.Status(http.StatusOK)
}

// 获取指定用户的所有property
func (c *AdminController) handleUserProperties(ctx *gin.Context) {
	userID := ctx.Param("id")

	// 解析分页参数
	limit := 50 // 管理员默认限制
	if limitStr := ctx.Query("limit"); limitStr != "" {
		if l, err := strconv.Atoi(limitStr); err == nil && l > 0 && l <= 200 {
			limit = l
		}
	}

	// 如果没有指定分页参数，且是从 user detail 页面访问，使用较小的限制
	if ctx.Query("page") == "" && ctx.Query("limit") == "" {
		limit = 5 // user detail 页面只显示最近5条
	}

	page := 1
	if pageStr := ctx.Query("page"); pageStr != "" {
		if p, err := strconv.Atoi(pageStr); err == nil && p > 0 {
			page = p
		}
	}

	// 构建过滤器
	filters := map[string]string{
		"userId": userID,
		"status": ctx.Query("status"),
		"name":   ctx.Query("name"),
	}

	// 计算实际的limit（考虑分页）
	actualLimit := limit * page

	properties, total, err := entities.GetProperties(ctx.Request.Context(), actualLimit, filters)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	// 计算分页信息
	startIndex := (page - 1) * limit
	endIndex := startIndex + limit
	if endIndex > len(properties) {
		endIndex = len(properties)
	}

	var pagedProperties []entities.PropertyList
	if startIndex < len(properties) {
		pagedProperties = properties[startIndex:endIndex]
	} else {
		pagedProperties = make([]entities.PropertyList, 0)
	}

	ctx.JSON(http.StatusOK, gin.H{
		"data":       pagedProperties,
		"total":      total,
		"page":       page,
		"limit":      limit,
		"totalPages": (int(total) + limit - 1) / limit,
	})
}

// 获取指定用户的所有lease
func (c *AdminController) handleUserLeases(ctx *gin.Context) {
	userID := ctx.Param("id")

	// 解析分页参数
	limit := 50 // 管理员默认限制
	if limitStr := ctx.Query("limit"); limitStr != "" {
		if l, err := strconv.Atoi(limitStr); err == nil && l > 0 && l <= 200 {
			limit = l
		}
	}

	// 如果没有指定分页参数，且是从 user detail 页面访问，使用较小的限制
	if ctx.Query("page") == "" && ctx.Query("limit") == "" {
		limit = 5 // user detail 页面只显示最近5条
	}

	page := 1
	if pageStr := ctx.Query("page"); pageStr != "" {
		if p, err := strconv.Atoi(pageStr); err == nil && p > 0 {
			page = p
		}
	}

	// 构建过滤器
	filters := map[string]string{
		"userId":     userID,
		"status":     ctx.Query("status"),
		"propertyId": ctx.Query("propertyId"),
		"role":       ctx.Query("role"), // landlord, tenant, or both
	}

	// 计算实际的limit（考虑分页）
	actualLimit := limit * page

	leases, total, err := entities.GetLeases(ctx.Request.Context(), actualLimit, filters)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	// 计算分页信息
	startIndex := (page - 1) * limit
	endIndex := startIndex + limit
	if endIndex > len(leases) {
		endIndex = len(leases)
	}

	var pagedLeases []entities.LeaseList
	if startIndex < len(leases) {
		pagedLeases = leases[startIndex:endIndex]
	} else {
		pagedLeases = make([]entities.LeaseList, 0)
	}

	ctx.JSON(http.StatusOK, gin.H{
		"data":       pagedLeases,
		"total":      total,
		"page":       page,
		"limit":      limit,
		"totalPages": (int(total) + limit - 1) / limit,
	})
}

// 获取指定用户的所有支付记录
func (c *AdminController) handleUserPayments(ctx *gin.Context) {
	userID := ctx.Param("id")

	// 解析分页参数
	limit := 50 // 管理员默认限制
	if limitStr := ctx.Query("limit"); limitStr != "" {
		if l, err := strconv.Atoi(limitStr); err == nil && l > 0 && l <= 200 {
			limit = l
		}
	}

	page := 1
	if pageStr := ctx.Query("page"); pageStr != "" {
		if p, err := strconv.Atoi(pageStr); err == nil && p > 0 {
			page = p
		}
	}

	// 使用批量查询优化性能
	allPayments, total, err := c.getUserPaymentsBatch(ctx.Request.Context(), userID, limit, page, ctx.Query("status"))
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"data":       allPayments,
		"total":      total,
		"page":       page,
		"limit":      limit,
		"totalPages": (int(total) + limit - 1) / limit,
	})
}

// getUserPaymentsBatch 批量查询用户支付记录，避免N+1查询问题
func (c *AdminController) getUserPaymentsBatch(ctx context.Context, userID string, limit, page int, statusFilter string) ([]map[string]interface{}, int64, error) {
	// 1. 首先获取用户的所有lease ID（只查询必要字段）
	leaseFilters := map[string]string{"userId": userID}
	leases, _, err := entities.GetLeases(ctx, 10000, leaseFilters) // 使用较大限制获取所有lease
	if err != nil {
		return nil, 0, fmt.Errorf("failed to get user leases: %v", err)
	}

	if len(leases) == 0 {
		return make([]map[string]interface{}, 0), 0, nil
	}

	// 2. 提取lease ID和构建lease信息映射
	leaseIDs := make([]string, len(leases))
	leaseInfoMap := make(map[string]entities.LeaseList)
	for i, lease := range leases {
		leaseIDs[i] = lease.ID
		leaseInfoMap[lease.ID] = lease
	}

	// 3. 使用批量查询获取所有支付记录
	allPayments, totalPayments, err := c.getPaymentsByLeaseIDs(ctx, leaseIDs, statusFilter)
	if err != nil {
		return nil, 0, fmt.Errorf("failed to get payments: %v", err)
	}

	// 4. 为支付记录添加lease信息
	enrichedPayments := make([]map[string]interface{}, 0, len(allPayments))
	for _, payment := range allPayments {
		leaseInfo, exists := leaseInfoMap[payment.LeaseID]
		if !exists {
			continue
		}

		item := map[string]interface{}{
			"_id":            payment.ID,
			"leaseId":        payment.LeaseID,
			"amt":            payment.Amount,
			"dt":             payment.Date,
			"stat":           payment.Status,
			"billingAddress": leaseInfo.PropertyAddress,
			"propertyName":   leaseInfo.PropertyName,
			"roomName":       leaseInfo.RoomName,
			"method":         "-", // 如有支付方式字段可补充
		}
		enrichedPayments = append(enrichedPayments, item)
	}

	// 5. 应用分页
	startIndex := (page - 1) * limit
	endIndex := startIndex + limit
	if endIndex > len(enrichedPayments) {
		endIndex = len(enrichedPayments)
	}

	var pagedPayments []map[string]interface{}
	if startIndex < len(enrichedPayments) {
		pagedPayments = enrichedPayments[startIndex:endIndex]
	} else {
		pagedPayments = make([]map[string]interface{}, 0)
	}

	return pagedPayments, totalPayments, nil
}

// getPaymentsByLeaseIDs 批量查询多个lease的支付记录
func (c *AdminController) getPaymentsByLeaseIDs(ctx context.Context, leaseIDs []string, statusFilter string) ([]entities.TenantPayment, int64, error) {
	paymentColl := gomongo.Coll("rr", "tenant_payments")
	if paymentColl == nil {
		return nil, 0, fmt.Errorf("tenant_payments collection not initialized")
	}

	// 构建查询过滤器
	filter := bson.M{
		"leaseId": bson.M{"$in": leaseIDs},
	}

	// 添加状态过滤
	if statusFilter != "" {
		filter["status"] = statusFilter
	}

	// 获取总数
	total, err := paymentColl.CountDocuments(ctx, filter)
	if err != nil {
		return nil, 0, fmt.Errorf("failed to count payments: %v", err)
	}

	// 查询所有支付记录，按日期倒序排列
	result, err := paymentColl.FindToArray(ctx, filter, gomongo.QueryOptions{
		Sort: bson.D{{Key: "dt", Value: -1}}, // 按日期倒序
	})
	if err != nil {
		return nil, 0, fmt.Errorf("failed to find payments: %v", err)
	}

	// 转换为TenantPayment结构
	payments := make([]entities.TenantPayment, 0, len(result))
	for _, item := range result {
		var payment entities.TenantPayment
		data, err := bson.Marshal(item)
		if err != nil {
			continue // 跳过无法解析的记录
		}

		if err := bson.Unmarshal(data, &payment); err != nil {
			continue // 跳过无法解析的记录
		}

		payments = append(payments, payment)
	}

	return payments, total, nil
}

// getUsersWithPagination 分页查询用户列表
func (c *AdminController) getUsersWithPagination(ctx context.Context, limit, page int, filters map[string]string) ([]entities.User, int64, error) {
	userColl := gomongo.Coll("rr", "users")
	if userColl == nil {
		return nil, 0, fmt.Errorf("users collection not initialized")
	}

	// 构建查询过滤器
	filter := bson.M{}

	// 关键词搜索（搜索ID、用户名、邮箱）
	if keyword := filters["keyword"]; keyword != "" {
		filter["$or"] = []bson.M{
			{"_id": bson.M{"$regex": keyword, "$options": "i"}},
			{"usrNm": bson.M{"$regex": keyword, "$options": "i"}},
			{"email": bson.M{"$regex": keyword, "$options": "i"}},
		}
	}

	// 状态过滤
	if status := filters["status"]; status != "" {
		filter["status"] = status
	}

	// 角色过滤
	if role := filters["role"]; role != "" {
		filter["role"] = role
	}

	// 获取总数
	total, err := userColl.CountDocuments(ctx, filter)
	if err != nil {
		return nil, 0, fmt.Errorf("failed to count users: %v", err)
	}

	// 计算跳过的记录数
	skip := (page - 1) * limit

	// 查询用户数据
	result, err := userColl.FindToArray(ctx, filter, gomongo.QueryOptions{
		Skip:  int64(skip),
		Limit: int64(limit),
		Sort:  bson.D{{Key: "email", Value: 1}}, // 按邮箱排序
	})
	if err != nil {
		return nil, 0, fmt.Errorf("failed to find users: %v", err)
	}

	// 转换为User结构
	users := make([]entities.User, 0, len(result))
	for _, item := range result {
		var user entities.User
		data, err := bson.Marshal(item)
		if err != nil {
			continue // 跳过无法解析的记录
		}

		if err := bson.Unmarshal(data, &user); err != nil {
			continue // 跳过无法解析的记录
		}

		users = append(users, user)
	}

	return users, total, nil
}

// handleLeasePayments 获取指定租约的所有支付记录
func (c *AdminController) handleLeasePayments(ctx *gin.Context) {
	leaseID := ctx.Param("id")
	payments, _, err := entities.GetTenantPayments(ctx.Request.Context(), leaseID, "")
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}
	ctx.JSON(http.StatusOK, payments)
}

// 管理员创建property接口
func (c *AdminController) handleCreateProperty(ctx *gin.Context) {
	var property entities.Property
	if err := ctx.ShouldBindJSON(&property); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request body"})
		return
	}
	// 必须指定userId
	if property.UserID == "" {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "userId is required"})
		return
	}
	property.ID = utils.GenerateNanoID()
	if err := property.Create(ctx.Request.Context()); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}
	ctx.JSON(http.StatusCreated, property)
}

// 管理员为property创建房间
func (c *AdminController) handleCreateRoom(ctx *gin.Context) {
	// 权限校验：已由RequireAdmin中间件保证
	propertyID := ctx.Param("id")

	// 获取property
	property, err := entities.GetPropertyByIDAdmin(ctx.Request.Context(), propertyID)
	if err != nil {
		ctx.JSON(http.StatusNotFound, gin.H{"error": err.Error()})
		return
	}

	var room entities.Room
	if err := ctx.ShouldBindJSON(&room); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request body"})
		return
	}

	// 生成room id
	room.ID = utils.GenerateNanoID()

	// 添加房间
	if err := property.AddRoom(room); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// 更新property
	if err := property.Update(ctx.Request.Context()); err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	ctx.JSON(http.StatusCreated, room)
}

// 管理员编辑房间
func (c *AdminController) handleUpdateRoom(ctx *gin.Context) {
	propertyID := ctx.Param("id")
	roomID := ctx.Param("roomId")
	property, err := entities.GetPropertyByIDAdmin(ctx.Request.Context(), propertyID)
	if err != nil {
		ctx.JSON(http.StatusNotFound, gin.H{"error": err.Error()})
		return
	}
	var room entities.Room
	if err := ctx.ShouldBindJSON(&room); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request body"})
		return
	}
	room.ID = roomID // 保证roomId一致
	if err := property.UpdateRoom(room); err != nil {
		ctx.JSON(http.StatusNotFound, gin.H{"error": err.Error()})
		return
	}
	if err := property.Update(ctx.Request.Context()); err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}
	ctx.JSON(http.StatusOK, room)
}

// 管理员删除房间
func (c *AdminController) handleDeleteRoom(ctx *gin.Context) {
	propertyID := ctx.Param("id")
	roomID := ctx.Param("roomId")
	property, err := entities.GetPropertyByIDAdmin(ctx.Request.Context(), propertyID)
	if err != nil {
		ctx.JSON(http.StatusNotFound, gin.H{"error": err.Error()})
		return
	}
	if err := property.DeleteRoom(ctx.Request.Context(), roomID); err != nil {
		ctx.JSON(http.StatusNotFound, gin.H{"error": err.Error()})
		return
	}
	if err := property.Update(ctx.Request.Context()); err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}
	ctx.Status(http.StatusNoContent)
}

// handleCreateLease 管理员创建租约
func (c *AdminController) handleCreateLease(ctx *gin.Context) {
	// 权限已由RequireAdmin中间件保障
	var req struct {
		UserID                string  `json:"userId"`
		PropertyID            string  `json:"propertyId"`
		RoomID                string  `json:"roomId"`
		StartDate             string  `json:"startDate"`
		RentDueDay            int     `json:"rentDueDay"`
		RentAmount            float64 `json:"rentAmount"`
		AdditionalMonthlyFees float64 `json:"additionalMonthlyFees"`
		KeyDeposit            float64 `json:"keyDeposit"`
		RentDeposit           float64 `json:"rentDeposit"`
		OtherDeposits         float64 `json:"otherDeposits"`
		Status                string  `json:"status"`
	}
	if err := ctx.ShouldBindJSON(&req); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request body"})
		return
	}
	if req.UserID == "" || req.PropertyID == "" || req.RoomID == "" || req.StartDate == "" {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "Missing required fields"})
		return
	}
	lease := &entities.Lease{
		ID:                    utils.GenerateNanoID(),
		UserID:                req.UserID,
		PropertyID:            req.PropertyID,
		RoomID:                req.RoomID,
		StartDate:             req.StartDate,
		RentDueDay:            req.RentDueDay,
		RentAmount:            req.RentAmount,
		AdditionalMonthlyFees: req.AdditionalMonthlyFees,
		KeyDeposit:            req.KeyDeposit,
		RentDeposit:           req.RentDeposit,
		OtherDeposits:         req.OtherDeposits,
		Status:                req.Status,
	}
	if err := lease.Create(ctx.Request.Context()); err != nil {
		// 增加详细日志输出
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	// 新增：更新property的vacRms和room的status
	property, err := entities.GetPropertyByIDAdmin(ctx.Request.Context(), req.PropertyID)
	if err == nil {
		// vacRms减一（最小为0）
		if property.VacantRooms > 0 {
			property.VacantRooms--
		}
		// 更新room状态
		for i, room := range property.Rooms {
			if room.ID == req.RoomID {
				property.Rooms[i].Status = "occupied"
				break
			}
		}
		_ = property.Update(ctx.Request.Context()) // 忽略错误，主流程不受影响
	}

	ctx.JSON(http.StatusCreated, lease)
}

// 管理员为租约添加租客
func (c *AdminController) handleAdminAddLeaseTenant(ctx *gin.Context) {
	leaseID := ctx.Param("id")
	var req map[string]interface{}
	if err := ctx.ShouldBindJSON(&req); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request body"})
		return
	}
	// 兼容前端firstNm等字段
	tenant := entities.Tenant{
		ID:         utils.GenerateNanoID(),
		LeaseID:    leaseID,
		UserID:     toString(req["usrId"]),
		FirstName:  toString(req["firstNm"]),
		MiddleName: toString(req["midNm"]),
		LastName:   toString(req["lastNm"]),
		Email:      toString(req["email"]),
		Notes:      toString(req["notes"]),
	}
	// dob、phone、sin加密由后端处理
	if v, ok := req["dob"].(string); ok && v != "" {
		dt, _ := time.Parse("2006-01-02", v)
		tenant.DateOfBirth = &dt
	}
	if v, ok := req["phoneNumber"].(string); ok && v != "" {
		tenant.Phone = v
	}
	if v, ok := req["ssn"].(string); ok && v != "" {
		tenant.SINNumber = v
	}
	if err := tenant.Create(ctx.Request.Context()); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}
	ctx.JSON(http.StatusCreated, tenant)
}

// 工具函数：interface转string
func toString(v interface{}) string {
	if v == nil {
		return ""
	}
	if s, ok := v.(string); ok {
		return s
	}
	return fmt.Sprintf("%v", v)
}

// handleCreateAdminLog 创建admin日志
func (c *AdminController) handleCreateAdminLog(ctx *gin.Context) {
	var log entities.AdminLog
	if err := ctx.ShouldBindJSON(&log); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request body"})
		return
	}
	// 后端通过token解析adminId
	adminId, err := utils.GetUserIDFromToken(ctx.Request)
	if err != nil || adminId == "" {
		ctx.JSON(http.StatusUnauthorized, gin.H{"error": "Unauthorized or adminId missing"})
		return
	}
	log.AdminId = adminId
	if log.UserId == "" || log.Act == "" || log.Chg == "" {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "Missing required fields"})
		return
	}
	if log.Ts.IsZero() {
		log.Ts = time.Now()
	}
	if log.ID == "" {
		log.ID = utils.GenerateNanoID()
	}
	if err := entities.InsertAdminLog(ctx.Request.Context(), &log); err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}
	ctx.JSON(http.StatusCreated, log)
}

// handleListAdminLogs 按userId查询admin log
func (c *AdminController) handleListAdminLogs(ctx *gin.Context) {
	userId := ctx.Query("userId")
	if userId == "" {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "userId is required"})
		return
	}
	logs, err := entities.ListAdminLogsByUserId(ctx.Request.Context(), userId)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}
	ctx.JSON(http.StatusOK, logs)
}

// handleBatchUsernames 批量查询用户id对应的用户名
func (c *AdminController) handleBatchUsernames(ctx *gin.Context) {
	ids := ctx.Query("ids")
	if ids == "" {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "ids is required"})
		return
	}
	idArr := utils.SplitAndTrim(ids, ",")
	if len(idArr) == 0 {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "no valid ids"})
		return
	}
	result := make(map[string]string)
	for _, id := range idArr {
		user, err := entities.GetUserByID(ctx.Request.Context(), id)
		if err == nil && user != nil && user.Username != "" {
			result[id] = user.Username
		} else {
			result[id] = id
		}
	}
	ctx.JSON(http.StatusOK, result)
}
