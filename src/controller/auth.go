package controller

import (
	"fmt"
	"net/http"
	"regexp"
	"rent_report/entities"
	"rent_report/router"
	"rent_report/utils"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/real-rm/goconfig"
	"github.com/real-rm/golog"
	"github.com/real-rm/gomongo"
)

var HomeURL string

const (
	STATUS_SUCCESS = 1
	STATUS_ERROR   = 0
)

type AuthController struct{}

func init() {
	router.Register(&AuthController{})
	baseUrl, ok := goconfig.Config("server.baseUrl").(string)
	if !ok {
		baseUrl = ""
	}
	HomeURL = baseUrl + "/pages/home/"
}

func (controller *AuthController) RegisterRoutes(router *gin.Engine) {
	// Regular authentication endpoints
	router.POST("/v1/auth/signup", controller.handleSignup)            // Regular email/password signup
	router.POST("/v1/auth/login", controller.handleLogin)              // Regular email/password login
	router.POST("/v1/auth/verify-email", controller.handleVerifyEmail) // Email verification for regular signup
	router.POST("/v1/auth/logout", controller.handleLogout)            // Logout (for both regular and OAuth)
	router.GET("/v1/auth/check", controller.handleCheckAuth)           // Check authentication status
	router.POST("/v1/auth/resend-code", controller.handleResendCode)   // Resend verification code

	// OAuth2 authentication endpoints
	router.GET("/v1/auth/oauth/login", controller.handleOAuthLogin)       // Start OAuth2 flow with RealMaster
	router.GET("/v1/auth/oauth/callback", controller.handleOAuthCallback) // Handle OAuth2 callback from RealMaster

	// Password reset endpoints
	router.POST("/v1/auth/send-reset-code", controller.handleSendResetCode)
	router.POST("/v1/auth/reset-password", controller.handleResetPassword)
}

type SignupRequest struct {
	Email    string `json:"email"`
	Password string `json:"password"`
	Username string `json:"username"`
	ViewType string `json:"viewType"`
}

type LoginRequest struct {
	Email    string `json:"email"`
	Password string `json:"password"`
}

type VerifyEmailRequest struct {
	Email string `json:"email"`
	Code  string `json:"code"`
}

type RefreshTokenRequest struct {
	RefreshToken string `json:"refresh_token"`
}

type AuthResponse struct {
	AccessToken     string        `json:"access_token"`
	RefreshToken    string        `json:"refresh_token,omitempty"`
	User            entities.User `json:"user"`
	ReferralApplied bool          `json:"referralApplied"`
}

type ResendCodeRequest struct {
	Email string `json:"email"`
}

// ResetPasswordRequest defines the structure for reset password API request
type ResetPasswordRequest struct {
	Email       string `json:"email"`
	Code        string `json:"code"`
	NewPassword string `json:"newPassword"`
}

func (controller *AuthController) handleSignup(c *gin.Context) {
	// Verify reCAPTCHA token
	if err := utils.VerifyRecaptcha(c.Request); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid reCAPTCHA"})
		return
	}

	var req map[string]interface{}
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request body"})
		return
	}

	email, _ := req["email"].(string)
	password, _ := req["password"].(string)
	username, _ := req["username"].(string)
	viewType, _ := req["viewType"].(string)
	referralCode, _ := req["referralCode"].(string)

	// Basic validation
	if email == "" || password == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Email and password are required"})
		return
	}

	// Email format validation
	if !isValidEmail(email) {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Please enter a valid email address"})
		return
	}

	// Create user entity
	user := entities.User{
		ID:       utils.GenerateNanoID(),
		Email:    email,
		Username: username,
		Role:     entities.RoleNormalUser,
		ViewType: viewType,
		RfrlCd:   referralCode,
	}

	// Create user and send verification email
	if err := user.Create(c.Request.Context(), password); err != nil {
		if err.Error() == "email already exists" {
			c.JSON(http.StatusConflict, gin.H{"error": "Email is already registered. Please use a different email or try logging in"})
			return
		}
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	// Send verification email
	var emailMessage string
	var verificationCode string
	if user.IsOAuth {
		emailMessage = "Please check your email to verify password binding"
		verificationCode = user.BindverifyCode
	} else {
		emailMessage = "Please check your email for verification code"
		verificationCode = user.VerifyCode
	}

	if err := utils.SendVerificationEmail(user.Email, verificationCode); err != nil {
		golog.Error("Failed to send verification email", "error", err, "email", user.Email)
	}

	c.JSON(http.StatusCreated, gin.H{
		"message": emailMessage,
		"ok":      STATUS_SUCCESS,
	})
}

func (controller *AuthController) handleVerifyEmail(c *gin.Context) {
	var req VerifyEmailRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request body"})
		return
	}

	// Find user by email using entity method
	user, err := entities.FindByEmail(c.Request.Context(), req.Email)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "User not found"})
		return
	}

	var verifyErr error
	if user.IsOAuth && user.Password == "" {
		// This is a password binding verification
		verifyErr = user.VerifyPasswordBinding(c.Request.Context(), req.Code)
	} else {
		// This is a regular email verification
		verifyErr = user.VerifyEmail(c.Request.Context(), req.Code)
	}

	if verifyErr != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": verifyErr.Error()})
		return
	}

	// 推荐码激活逻辑：邮箱验证成功后处理推荐码
	referralApplied := false
	if user.RfrlCd != "" {
		coll := gomongo.Coll("rr", "referral_codes")
		if coll != nil {
			var code entities.ReferralCode
			err := coll.FindOne(c.Request.Context(), map[string]interface{}{"code": user.RfrlCd, "status": "active", "rmnuse": map[string]interface{}{"$gt": 0}}).Decode(&code)
			if err == nil {
				// 原子减1
				res := coll.FindOneAndUpdate(c.Request.Context(), map[string]interface{}{"_id": code.ID, "rmnuse": map[string]interface{}{"$gt": 0}}, map[string]interface{}{"$inc": map[string]interface{}{"rmnuse": -1}})
				if res.Err() == nil {
					// 写usage表
					usage := &entities.ReferralCodeUsage{
						UID:    user.ID,
						CodeID: code.ID,
						Status: "active",
						Actv:   time.Now(),
						Exp:    code.Exp,
					}
					if usage.Create(c.Request.Context()) == nil {
						referralApplied = true
					}
				}
			}
		}
		// 清空user.RfrlCd
		_ = user.UpdateUser(c.Request.Context(), map[string]interface{}{"rfrl_cd": ""})
	}

	// Generate token pair
	tokenPair, err := utils.GenerateTokenPair(user.ID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to generate tokens"})
		return
	}

	// Set refresh token in HTTP-only cookie
	c.SetCookie("refresh_token", tokenPair.RefreshToken, int(time.Hour*24*7/time.Second), "/", "", false, false)

	c.JSON(http.StatusOK, AuthResponse{
		AccessToken:     tokenPair.AccessToken,
		User:            *user,
		ReferralApplied: referralApplied,
	})
}

func (controller *AuthController) handleOAuthLogin(c *gin.Context) {
	golog.Info("handleOAuthLogin")

	// Check if we have a refresh token first
	refreshTokenCookie, err := c.Request.Cookie("oauth_refresh_token")
	if err == nil && refreshTokenCookie.Value != "" {
		// Try to use refresh token
		token, err := utils.ExchangeRefreshTokenForAccessToken(refreshTokenCookie.Value)
		if err == nil {
			// Update refresh token cookie if we got a new one
			if token.RefreshToken != "" {
				c.SetCookie("oauth_refresh_token", token.RefreshToken, 24*60*60, "/", "", false, false)
			}

			// Get user info using new access token
			userInfo, err := utils.GetUserInfo(token)
			if err == nil {
				// Get existing user
				user, err := entities.FindByEmail(c.Request.Context(), userInfo.Email)
				if err == nil {
					// Generate token pair
					tokenPair, err := utils.GenerateTokenPair(user.ID)
					if err == nil {
						c.SetCookie("refresh_token", tokenPair.RefreshToken, int(time.Hour*24*7/time.Second), "/", "", false, false)

						c.Redirect(http.StatusSeeOther, HomeURL)
						return
					}
				}
			}
		}

		// If any error occurred during refresh token flow, clear the invalid token
		c.SetCookie("oauth_refresh_token", "", -1, "/", "", false, false)
	}

	// If we get here, either there was no refresh token or it failed
	// Proceed with normal OAuth flow
	state := utils.GenerateNanoID()

	if err := utils.SaveState(state); err != nil {
		golog.Error("Failed to save OAuth state", "error", err, "state", state)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Internal server error"})
		return
	}

	authURL := fmt.Sprintf("%s?client_id=%s&redirect_uri=%s&response_type=code&client_state=%s",
		utils.GetOAuthConfig().AuthURL,
		utils.GetOAuthConfig().ClientID,
		utils.GetOAuthConfig().RedirectURL,
		state,
	)
	golog.Info("Redirecting to auth URL", "url", authURL)

	c.Redirect(http.StatusTemporaryRedirect, authURL)
}

func (controller *AuthController) handleOAuthCallback(c *gin.Context) {
	golog.Info("handleOAuthCallback")
	state := c.Query("client_state")
	code := c.Query("code")

	golog.Info("Received callback", "state", state, "code", code)

	// Verify state
	if !utils.ValidateState(state) {
		golog.Warn("State verification failed", "state", state)
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid state"})
		return
	}

	// Exchange code for token
	token, err := utils.ExchangeCodeForToken(code)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to exchange code for token"})
		return
	}

	// Store OAuth refresh token in cookie if we got one
	if token.RefreshToken != "" {
		c.SetCookie("oauth_refresh_token", token.RefreshToken, 24*60*60, "/", "", false, false)
	}

	// Get user info from OAuth provider
	userInfo, err := utils.GetUserInfo(token)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get user info"})
		return
	}

	golog.Info("Got user info from RealMaster", "userInfo", userInfo)

	// Create or Get existing user (with binding logic)
	user, err := entities.LinkOAuthUser(c.Request.Context(), userInfo.Email, userInfo.ID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create/get user"})
		return
	}

	// Update user with RealMaster info
	if err := user.UpdateOAuthInfo(c.Request.Context(), userInfo.Email, userInfo.Name); err != nil {
		golog.Error("Failed to update user with RealMaster info", "error", err, "userID", user.ID)
	}

	// Generate token pair
	tokenPair, err := utils.GenerateTokenPair(user.ID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to generate tokens"})
		return
	}

	c.SetCookie("refresh_token", tokenPair.RefreshToken, int(time.Hour*24*7/time.Second), "/", "", false, false)

	c.Redirect(http.StatusSeeOther, HomeURL)
}

func (controller *AuthController) handleLogin(c *gin.Context) {
	// Verify reCAPTCHA token
	if err := utils.VerifyRecaptcha(c.Request); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid reCAPTCHA"})
		return
	}

	var req LoginRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request body"})
		return
	}

	// Basic validation
	if req.Email == "" || req.Password == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Email and password are required"})
		return
	}

	// Authenticate user
	user, err := entities.AuthenticateUser(c.Request.Context(), req.Email, req.Password)
	if err != nil {
		c.JSON(http.StatusUnauthorized, gin.H{"error": err.Error()})
		return
	}

	// Generate token pair
	tokenPair, err := utils.GenerateTokenPair(user.ID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to generate tokens"})
		return
	}

	c.SetCookie("refresh_token", tokenPair.RefreshToken, int(time.Hour*24*7/time.Second), "/", "", false, false)

	c.JSON(http.StatusOK, gin.H{
		"ok": STATUS_SUCCESS,
		"user": map[string]interface{}{
			"id":             user.ID,
			"role":           user.Role,
			"email":          user.Email,
			"username":       user.Username,
			"accountType":    user.AccountType,
			"status":         user.Status,
			"organizationId": user.OrganizationID,
		},
	})
}

func (controller *AuthController) handleLogout(c *gin.Context) {
	// Clear the refresh token cookie
	c.SetCookie("refresh_token", "", -1, "/", "", false, false)

	// Clear the OAuth refresh token cookie
	c.SetCookie("oauth_refresh_token", "", -1, "/", "", false, false)

	c.JSON(http.StatusNoContent, nil)
}

func (controller *AuthController) handleCheckAuth(c *gin.Context) {
	if err := utils.CheckAuth(c.Request); err != nil {
		c.JSON(http.StatusUnauthorized, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"ok": STATUS_SUCCESS,
	})
}

// handleResendCode handles the resend verification code request
func (controller *AuthController) handleResendCode(c *gin.Context) {
	var req ResendCodeRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request body"})
		return
	}

	// Basic validation
	if req.Email == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Email is required"})
		return
	}

	// Find user by email
	user, err := entities.FindByEmail(c.Request.Context(), req.Email)
	if err != nil {
		// Don't reveal if the email exists or not for security reasons
		c.JSON(http.StatusOK, gin.H{
			"message": "If the email exists, a verification code has been sent",
		})
		return
	}

	// Generate a new verification code
	if err := user.StartVerification(c.Request.Context()); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to generate new verification code"})
		return
	}

	// Send the verification code
	if err := utils.SendVerificationEmail(user.Email, user.VerifyCode); err != nil {
		golog.Error("Failed to send verification email", "error", err, "email", user.Email)
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "If the email exists, a verification code has been sent",
	})
}

// handleSendResetCode handles the send reset code request
func (controller *AuthController) handleSendResetCode(c *gin.Context) {
	var req ResendCodeRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request body"})
		return
	}

	// Basic validation
	if req.Email == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Email is required"})
		return
	}

	// Find user by email
	user, err := entities.FindByEmail(c.Request.Context(), req.Email)
	if err != nil {
		// Don't reveal if the email exists or not for security reasons
		c.JSON(http.StatusOK, gin.H{
			"message": "If the email exists, a reset code has been sent",
		})
		return
	}

	// Generate a new verification code and reset user status
	if err := user.StartPasswordReset(c.Request.Context()); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to generate reset code"})
		return
	}

	// Send the verification code
	if err := utils.SendResetPasswordEmail(user.Email, user.VerifyCode); err != nil {
		// Log the error but don't return it to the client
		golog.Error("Failed to send reset code email", "error", err, "email", user.Email)
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "If the email exists, a reset code has been sent",
	})
}

// handleResetPassword handles the reset password request
func (controller *AuthController) handleResetPassword(c *gin.Context) {
	var req ResetPasswordRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request body"})
		return
	}

	// Basic validation
	if req.Email == "" || req.Code == "" || req.NewPassword == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Email, code and new password are required"})
		return
	}

	// Find user by email
	user, err := entities.FindByEmail(c.Request.Context(), req.Email)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "User not found"})
		return
	}

	// Verify the code
	if err := user.VerifyEmail(c.Request.Context(), req.Code); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid or expired verification code"})
		return
	}

	// Update the password and set verified to true
	if err := user.ResetPassword(c.Request.Context(), req.NewPassword); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update password"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "Password has been reset successfully",
	})
}

// isValidEmail validates email format using regex
func isValidEmail(email string) bool {
	// Email regex pattern
	emailRegex := `^[^\s@]+@[^\s@]+\.[^\s@]+$`
	re := regexp.MustCompile(emailRegex)
	return re.MatchString(email)
}
