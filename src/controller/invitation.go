package controller

import (
	"net/http"
	"rent_report/entities"
	"rent_report/router"
	"rent_report/utils"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/real-rm/golog"
	"github.com/real-rm/gomongo"
	"go.mongodb.org/mongo-driver/bson"
)

type InvitationController struct{}

func init() {
	router.Register(&InvitationController{})
}

func (c *InvitationController) RegisterRoutes(r *gin.Engine) {
	invitationRouter := r.Group("/v1/invitations")
	{
		invitationRouter.POST("", c.handleCreateInvitation)
		invitationRouter.GET("", c.handleListInvitations)
		invitationRouter.GET("/verify", c.handleVerifyInvitation)

		// RESTful status update
		invitationRouter.PUT("/:code/status", c.handleUpdateStatusRESTful)

		// Deprecated route (for backward compatibility)
		invitationRouter.POST("/update_status_by_code", c.handleUpdateStatusByCode)
	}
}

func (c *InvitationController) handleCreateInvitation(ctx *gin.Context) {
	userID, err := utils.GetUserIDFromToken(ctx.Request)
	if err != nil {
		ctx.JSON(http.StatusUnauthorized, gin.H{"error": "Unauthorized"})
		return
	}

	var inv entities.Invitation
	if err := ctx.ShouldBindJSON(&inv); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request body"})
		return
	}

	inv.SenderId = userID
	user, err := entities.GetUserByID(ctx.Request.Context(), userID)
	if err == nil {
		// 如果usrNm为空，使用email作为SenderName
		if user.Username != "" {
			inv.SenderName = user.Username
		} else {
			inv.SenderName = user.Email
		}
		inv.SenderEmail = user.Email
	}

	if err := inv.Create(ctx.Request.Context()); err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	// 创建成功后发送邀请邮件
	if err := utils.SendInvitationEmail(inv.SenderName, inv.ReceiverName, inv.ReceiverEmail, inv.Code); err != nil {
		// 记录错误但不中断流程，因为邀请已创建
		golog.Error("Failed to send invitation email", "error", err, "receiverEmail", inv.ReceiverEmail, "code", inv.Code)
		// 可以考虑添加重试机制或异步队列
	}

	// 根据用户的viewType更新对应的邀请列表
	if user != nil {
		update := bson.M{}
		if user.ViewType == "tenant" {
			update = bson.M{
				"$push": bson.M{
					"tntInvLst": inv.ID,
				},
			}
		} else if user.ViewType == "landlord" {
			update = bson.M{
				"$push": bson.M{
					"lndInvLst": inv.ID,
				},
			}
		}
		if len(update) > 0 {
			userColl := gomongo.Coll("rr", "users")
			if userColl != nil {
				_, err = userColl.UpdateOne(ctx.Request.Context(), bson.M{"_id": userID}, update)
				if err != nil {
					// 记录错误但不中断流程
					golog.Error("Failed to update user invitation list", "error", err, "userID", userID)
				}
			}
		}
	}

	ctx.JSON(http.StatusCreated, inv)
}

func (c *InvitationController) handleListInvitations(ctx *gin.Context) {
	userID, err := utils.GetUserIDFromToken(ctx.Request)
	if err != nil {
		ctx.JSON(http.StatusUnauthorized, gin.H{"error": "Unauthorized"})
		return
	}

	// 获取用户信息
	user, err := entities.GetUserByID(ctx.Request.Context(), userID)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get user info"})
		return
	}

	status := ctx.Query("status")
	typeStr := ctx.Query("type")

	// 构建查询条件
	filter := map[string]interface{}{}
	if status != "" {
		filter["status"] = status
	}
	if typeStr != "" {
		filter["type"] = typeStr
	}

	// 根据用户类型决定查询条件
	if user.ViewType == "tenant" {
		// 如果是租客，从 tntInvLst 中获取邀请
		if len(user.TenantInvitationList) > 0 {
			filter["_id"] = bson.M{"$in": user.TenantInvitationList}
		} else {
			// 如果没有邀请，返回空列表
			ctx.JSON(http.StatusOK, gin.H{"items": []entities.Invitation{}})
			return
		}
	} else {
		// 如果是房东，从 lndInvLst 中获取邀请
		if len(user.LandlordInvitationList) > 0 {
			filter["_id"] = bson.M{"$in": user.LandlordInvitationList}
		} else {
			// 如果没有邀请，返回空列表
			ctx.JSON(http.StatusOK, gin.H{"items": []entities.Invitation{}})
			return
		}
	}

	coll := gomongo.Coll("rr", "invitations")
	if coll == nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": "invitations collection not initialized"})
		return
	}

	cursor, err := coll.Find(ctx.Request.Context(), filter)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	var items []entities.Invitation
	if err := cursor.All(ctx.Request.Context(), &items); err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{"items": items})
}

func (c *InvitationController) handleVerifyInvitation(ctx *gin.Context) {
	code := ctx.Query("code")
	if code == "" {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "Missing code"})
		return
	}

	coll := gomongo.Coll("rr", "invitations")
	var inv entities.Invitation
	err := coll.FindOne(ctx.Request.Context(), map[string]interface{}{"code": code}).Decode(&inv)
	if err != nil {
		ctx.JSON(http.StatusNotFound, gin.H{"error": "Invitation not found"})
		return
	}

	if time.Now().After(inv.ExpiresAt) {
		ctx.JSON(http.StatusGone, gin.H{"error": "Invitation expired"})
		return
	}

	ctx.JSON(http.StatusOK, inv)
}

func (c *InvitationController) handleUpdateStatusByCode(ctx *gin.Context) {
	type Req struct {
		Code    string `json:"code"`
		Status  string `json:"status"`
		LeaseId string `json:"leaseId"`
	}

	var req Req
	if err := ctx.ShouldBindJSON(&req); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request body"})
		return
	}

	if req.Code == "" || req.Status == "" {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "Missing code or status"})
		return
	}

	// 验证状态值
	validStatuses := map[string]bool{
		"pending":   true,
		"active":    true,
		"expired":   true,
		"cancelled": true,
	}
	if !validStatuses[req.Status] {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "Invalid status. Valid values are: pending, active, expired, cancelled"})
		return
	}

	// 获取当前登录用户ID
	userID, err := utils.GetUserIDFromToken(ctx.Request)
	if err != nil {
		ctx.JSON(http.StatusUnauthorized, gin.H{"error": "Unauthorized"})
		return
	}

	coll := gomongo.Coll("rr", "invitations")
	updateFields := map[string]interface{}{"status": req.Status}
	if req.LeaseId != "" {
		updateFields["leaseId"] = req.LeaseId
	}
	update := map[string]interface{}{"$set": updateFields}
	res, err := coll.UpdateOne(ctx.Request.Context(), map[string]interface{}{"code": req.Code}, update)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}
	if res.MatchedCount == 0 {
		ctx.JSON(http.StatusNotFound, gin.H{"error": "Invitation not found"})
		return
	}

	// 如果是通过邀请创建租客，更新当前用户的邀请列表
	if req.Status == "active" && req.LeaseId != "" {
		// 获取邀请信息
		var inv entities.Invitation
		err = coll.FindOne(ctx.Request.Context(), map[string]interface{}{"code": req.Code}).Decode(&inv)
		if err == nil {
			// 更新邀请相关用户的邀请列表
			userColl := gomongo.Coll("rr", "users")
			if userColl != nil {
				if inv.Type == "landlord_to_tenant" {
					// 房东邀请租客：sender=房东，receiver=租客
					// 更新sender(房东)的lndInvLst (使用$addToSet避免重复)
					if inv.SenderId != "" {
						update := bson.M{
							"$addToSet": bson.M{
								"lndInvLst": inv.ID,
							},
						}
						_, err = userColl.UpdateOne(ctx.Request.Context(), bson.M{"_id": inv.SenderId}, update)
						if err != nil {
							golog.Error("Failed to update sender landlord invitation list", "error", err, "senderID", inv.SenderId)
						}
					}

					// 更新receiver(租客，当前用户)的tntInvLst (使用$addToSet避免重复)
					update := bson.M{
						"$addToSet": bson.M{
							"tntInvLst": inv.ID,
						},
					}
					_, err = userColl.UpdateOne(ctx.Request.Context(), bson.M{"_id": userID}, update)
					if err != nil {
						golog.Error("Failed to update receiver tenant invitation list", "error", err, "userID", userID)
					}
				} else if inv.Type == "tenant_to_landlord" {
					// 租客邀请房东：sender=租客，receiver=房东
					// 更新sender(租客)的tntInvLst (使用$addToSet避免重复)
					if inv.SenderId != "" {
						update := bson.M{
							"$addToSet": bson.M{
								"tntInvLst": inv.ID,
							},
						}
						_, err = userColl.UpdateOne(ctx.Request.Context(), bson.M{"_id": inv.SenderId}, update)
						if err != nil {
							golog.Error("Failed to update sender tenant invitation list", "error", err, "senderID", inv.SenderId)
						}
					}

					// 更新receiver(房东，当前用户)的lndInvLst (使用$addToSet避免重复)
					update := bson.M{
						"$addToSet": bson.M{
							"lndInvLst": inv.ID,
						},
					}
					_, err = userColl.UpdateOne(ctx.Request.Context(), bson.M{"_id": userID}, update)
					if err != nil {
						golog.Error("Failed to update receiver landlord invitation list", "error", err, "userID", userID)
					}
				}
			}

			// 新增：激活邀请后，修正 lease.ctnts 里的 tenantId 为当前 userId
			if inv.LeaseId != "" && inv.ReceiverEmail != "" {
				leaseColl := gomongo.Coll("rr", "leases")
				if leaseColl != nil {
					filter := bson.M{"_id": inv.LeaseId, "ctnts.email": inv.ReceiverEmail}
					update := bson.M{"$set": bson.M{
						"ctnts.$.tenantId": userID,
					}}
					_, err := leaseColl.UpdateOne(ctx.Request.Context(), filter, update)
					if err != nil {
						golog.Error("Failed to update lease.ctnts tenantId for invitation", "error", err, "leaseId", inv.LeaseId, "receiverEmail", inv.ReceiverEmail)
					}
				}
			}
		}
	}

	ctx.JSON(http.StatusNoContent, nil)
}

// PUT /v1/invitations/:code/status - RESTful version
func (c *InvitationController) handleUpdateStatusRESTful(ctx *gin.Context) {
	code := ctx.Param("code")
	if code == "" {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "Missing invitation code"})
		return
	}

	type Req struct {
		Status  string `json:"status"`
		LeaseId string `json:"leaseId"`
	}

	var req Req
	if err := ctx.ShouldBindJSON(&req); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request body"})
		return
	}

	if req.Status == "" {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "Missing status"})
		return
	}

	// 验证状态值
	validStatuses := map[string]bool{
		"pending":   true,
		"active":    true,
		"expired":   true,
		"cancelled": true,
	}
	if !validStatuses[req.Status] {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "Invalid status. Valid values are: pending, active, expired, cancelled"})
		return
	}

	// 获取当前登录用户ID
	userID, err := utils.GetUserIDFromToken(ctx.Request)
	if err != nil {
		ctx.JSON(http.StatusUnauthorized, gin.H{"error": "Unauthorized"})
		return
	}

	coll := gomongo.Coll("rr", "invitations")
	updateFields := map[string]interface{}{"status": req.Status}
	if req.LeaseId != "" {
		updateFields["leaseId"] = req.LeaseId
	}
	update := map[string]interface{}{"$set": updateFields}
	res, err := coll.UpdateOne(ctx.Request.Context(), map[string]interface{}{"code": code}, update)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}
	if res.MatchedCount == 0 {
		ctx.JSON(http.StatusNotFound, gin.H{"error": "Invitation not found"})
		return
	}

	// 如果是通过邀请创建租客，更新当前用户的邀请列表
	if req.Status == "active" && req.LeaseId != "" {
		// 获取邀请信息
		var inv entities.Invitation
		err = coll.FindOne(ctx.Request.Context(), map[string]interface{}{"code": code}).Decode(&inv)
		if err == nil {
			// 更新邀请相关用户的邀请列表
			userColl := gomongo.Coll("rr", "users")
			if userColl != nil {
				if inv.Type == "landlord_to_tenant" {
					// 房东邀请租客：sender=房东，receiver=租客
					// 更新sender(房东)的lndInvLst (使用$addToSet避免重复)
					if inv.SenderId != "" {
						update := bson.M{
							"$addToSet": bson.M{
								"lndInvLst": inv.ID,
							},
						}
						_, err = userColl.UpdateOne(ctx.Request.Context(), bson.M{"_id": inv.SenderId}, update)
						if err != nil {
							golog.Error("Failed to update sender landlord invitation list", "error", err, "senderID", inv.SenderId)
						}
					}

					// 更新receiver(租客，当前用户)的tntInvLst (使用$addToSet避免重复)
					update := bson.M{
						"$addToSet": bson.M{
							"tntInvLst": inv.ID,
						},
					}
					_, err = userColl.UpdateOne(ctx.Request.Context(), bson.M{"_id": userID}, update)
					if err != nil {
						golog.Error("Failed to update receiver tenant invitation list", "error", err, "userID", userID)
					}
				} else if inv.Type == "tenant_to_landlord" {
					// 租客邀请房东：sender=租客，receiver=房东
					// 更新sender(租客)的tntInvLst (使用$addToSet避免重复)
					if inv.SenderId != "" {
						update := bson.M{
							"$addToSet": bson.M{
								"tntInvLst": inv.ID,
							},
						}
						_, err = userColl.UpdateOne(ctx.Request.Context(), bson.M{"_id": inv.SenderId}, update)
						if err != nil {
							golog.Error("Failed to update sender tenant invitation list", "error", err, "senderID", inv.SenderId)
						}
					}

					// 更新receiver(房东，当前用户)的lndInvLst (使用$addToSet避免重复)
					update := bson.M{
						"$addToSet": bson.M{
							"lndInvLst": inv.ID,
						},
					}
					_, err = userColl.UpdateOne(ctx.Request.Context(), bson.M{"_id": userID}, update)
					if err != nil {
						golog.Error("Failed to update receiver landlord invitation list", "error", err, "userID", userID)
					}
				}
			}

			// 新增：激活邀请后，修正 lease.ctnts 里的 tenantId 为当前 userId
			if inv.LeaseId != "" && inv.ReceiverEmail != "" {
				leaseColl := gomongo.Coll("rr", "leases")
				if leaseColl != nil {
					filter := bson.M{"_id": inv.LeaseId, "ctnts.email": inv.ReceiverEmail}
					update := bson.M{"$set": bson.M{
						"ctnts.$.tenantId": userID,
					}}
					_, err = leaseColl.UpdateOne(ctx.Request.Context(), filter, update)
					if err != nil {
						golog.Error("Failed to update lease.ctnts tenantId for invitation", "error", err, "leaseId", inv.LeaseId, "receiverEmail", inv.ReceiverEmail)
					}
				}
			}
		}
	}

	ctx.JSON(http.StatusOK, gin.H{"success": true})
}
