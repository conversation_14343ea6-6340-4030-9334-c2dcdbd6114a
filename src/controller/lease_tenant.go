package controller

import (
	"net/http"
	"rent_report/entities"
	"rent_report/middleware"
	"rent_report/router"
	"rent_report/utils"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/real-rm/gomongo"
	"go.mongodb.org/mongo-driver/bson"
)

type TenantController struct{}

func init() {
	router.Register(&TenantController{})
}

func (c *TenantController) RegisterRoutes(r *gin.Engine) {
	// RESTful tenant routes
	tenantRouter := r.Group("/v1/leases/:leaseId/tenants")
	{
		tenantRouter.GET("", c.handleGetTenants)
		tenantRouter.POST("", c.handleCreateTenant)
		tenantRouter.GET("/:tenantId", middleware.SmartResourceAuth("tenant"), c.handleGetTenant)
		// 房东可以更新和删除tenant，使用lease权限检查
		tenantRouter.PUT("/:tenantId", middleware.RequireLandlord(), middleware.SmartResourceAuth("lease"), c.handleUpdateTenant)

		tenantRouter.DELETE("/:tenantId/soft", middleware.RequireLandlord(), middleware.SmartResourceAuth("lease"), c.handleSoftDeleteTenant)
		tenantRouter.PUT("/:tenantId/restore", middleware.RequireLandlord(), middleware.SmartResourceAuth("lease"), c.handleRestoreTenant)
		tenantRouter.DELETE("/:tenantId", middleware.RequireLandlord(), middleware.SmartResourceAuth("lease"), c.handleHardDeleteTenant)
		tenantRouter.PUT("/self", c.handleUpdateTenantSelf)
	}

	// Deprecated routes (for backward compatibility)
	ctntsRouter := r.Group("/v1/leases/:leaseId/ctnts")
	{
		ctntsRouter.PUT("/:tenantId", middleware.RequireLandlord(), middleware.SmartResourceAuth("lease"), c.handleUpdateTenant)
		ctntsRouter.DELETE("/:tenantId/soft", middleware.RequireLandlord(), middleware.SmartResourceAuth("lease"), c.handleSoftDeleteTenant)
		ctntsRouter.PUT("/:tenantId/restore", middleware.RequireLandlord(), middleware.SmartResourceAuth("lease"), c.handleRestoreTenant)
		ctntsRouter.DELETE("/:tenantId", middleware.RequireLandlord(), middleware.SmartResourceAuth("lease"), c.handleHardDeleteTenant)
		ctntsRouter.PUT("/self_id", c.handleUpdateTenantSelf)
	}

	r.GET("/v1/tenants/search", c.handleSearchTenants)
}

func (c *TenantController) handleCreateTenant(ctx *gin.Context) {
	// Get userID from token
	userID, err := utils.GetUserIDFromToken(ctx.Request)
	if err != nil {
		ctx.JSON(http.StatusUnauthorized, gin.H{"error": "Unauthorized"})
		return
	}

	// Get leaseId from URL
	leaseID := ctx.Param("leaseId")

	var tenant entities.Tenant
	if err := ctx.ShouldBindJSON(&tenant); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request body"})
		return
	}

	// Set the required fields
	tenant.ID = utils.GenerateNanoID()
	tenant.UserID = userID
	tenant.LeaseID = leaseID

	if err := tenant.Create(ctx.Request.Context()); err != nil {
		if err.Error() == "lease not found" {
			ctx.JSON(http.StatusNotFound, gin.H{"error": err.Error()})
			return
		}
		ctx.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	ctx.JSON(http.StatusCreated, tenant)
}

func (c *TenantController) handleGetTenant(ctx *gin.Context) {
	// Get userID from token
	userID, err := utils.GetUserIDFromToken(ctx.Request)
	if err != nil {
		ctx.JSON(http.StatusUnauthorized, gin.H{"error": "Unauthorized"})
		return
	}

	// Get leaseId and tenantId from URL
	leaseID := ctx.Param("leaseId")
	tenantID := ctx.Param("tenantId")

	tenant, err := entities.GetTenant(ctx.Request.Context(), tenantID, leaseID, userID)
	if err != nil {
		if err.Error() == "tenant not found" {
			ctx.JSON(http.StatusNotFound, gin.H{"error": err.Error()})
			return
		}
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	ctx.JSON(http.StatusOK, tenant)
}

func (c *TenantController) handleUpdateTenant(ctx *gin.Context) {
	// Get userID from token
	userID, err := utils.GetUserIDFromToken(ctx.Request)
	if err != nil {
		ctx.JSON(http.StatusUnauthorized, gin.H{"error": "Unauthorized"})
		return
	}

	// Get leaseId and tenantId from URL
	leaseID := ctx.Param("leaseId")
	tenantID := ctx.Param("tenantId")

	var tenant entities.Tenant
	if err := ctx.ShouldBindJSON(&tenant); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request body"})
		return
	}

	tenant.ID = tenantID
	tenant.LeaseID = leaseID
	tenant.UserID = userID

	// 使用与原来lease.go相同的方法
	if err := entities.UpdateLeaseTenantAndSync(ctx.Request.Context(), leaseID, userID, tenant); err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	ctx.JSON(http.StatusOK, tenant)
}

func (c *TenantController) handleSoftDeleteTenant(ctx *gin.Context) {
	// Get userID from token
	userID, err := utils.GetUserIDFromToken(ctx.Request)
	if err != nil {
		ctx.JSON(http.StatusUnauthorized, gin.H{"error": "Unauthorized"})
		return
	}

	// Get leaseId and tenantId from URL
	leaseID := ctx.Param("leaseId")
	tenantID := ctx.Param("tenantId")

	// 使用软删除方法（移动到past tenants）
	if err := entities.SoftDeleteLeaseTenant(ctx.Request.Context(), leaseID, userID, tenantID); err != nil {
		if err.Error() == "lease not found" {
			ctx.JSON(http.StatusNotFound, gin.H{"error": err.Error()})
			return
		}
		if err.Error() == "tenant not found in ctnts" {
			ctx.JSON(http.StatusNotFound, gin.H{"error": "Tenant not found in this lease"})
			return
		}
		// 检查是否是保护错误
		if strings.Contains(err.Error(), "cannot delete protected tenant") {
			ctx.JSON(http.StatusForbidden, gin.H{
				"error": err.Error(),
				"code":  "TENANT_PROTECTED",
			})
			return
		}
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	ctx.Status(http.StatusNoContent)
}

func (c *TenantController) handleRestoreTenant(ctx *gin.Context) {
	// Get userID from token
	userID, err := utils.GetUserIDFromToken(ctx.Request)
	if err != nil {
		ctx.JSON(http.StatusUnauthorized, gin.H{"error": "Unauthorized"})
		return
	}

	// Get leaseId and tenantId from URL
	leaseID := ctx.Param("leaseId")
	tenantID := ctx.Param("tenantId")

	// 使用恢复方法（从past tenants移动到current tenants）
	if err := entities.RestoreLeaseTenant(ctx.Request.Context(), leaseID, userID, tenantID); err != nil {
		if err.Error() == "lease not found" {
			ctx.JSON(http.StatusNotFound, gin.H{"error": err.Error()})
			return
		}
		if err.Error() == "tenant not found in past tenants" {
			ctx.JSON(http.StatusNotFound, gin.H{"error": "Tenant not found in past tenants"})
			return
		}
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	ctx.Status(http.StatusNoContent)
}

func (c *TenantController) handleHardDeleteTenant(ctx *gin.Context) {
	// Get userID from token
	userID, err := utils.GetUserIDFromToken(ctx.Request)
	if err != nil {
		ctx.JSON(http.StatusUnauthorized, gin.H{"error": "Unauthorized"})
		return
	}

	// Get leaseId and tenantId from URL
	leaseID := ctx.Param("leaseId")
	tenantID := ctx.Param("tenantId")

	// 使用硬删除方法（从ctnts中删除，同时删除tenant表中的记录）
	if err := entities.HardDeleteLeaseTenant(ctx.Request.Context(), leaseID, userID, tenantID); err != nil {
		if err.Error() == "lease not found" {
			ctx.JSON(http.StatusNotFound, gin.H{"error": err.Error()})
			return
		}
		if err.Error() == "tenant not found in current tenants" {
			ctx.JSON(http.StatusNotFound, gin.H{"error": "Tenant not found in this lease"})
			return
		}
		// 检查是否是保护错误
		if strings.Contains(err.Error(), "cannot delete protected tenant") {
			ctx.JSON(http.StatusForbidden, gin.H{
				"error": err.Error(),
				"code":  "TENANT_PROTECTED",
			})
			return
		}
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	ctx.Status(http.StatusNoContent)
}

func (c *TenantController) handleGetTenants(ctx *gin.Context) {
	// Get userID from token
	userID, err := utils.GetUserIDFromToken(ctx.Request)
	if err != nil {
		ctx.JSON(http.StatusUnauthorized, gin.H{"error": "Unauthorized"})
		return
	}

	// Get leaseId from URL
	leaseID := ctx.Param("leaseId")

	tenants, total, err := entities.GetTenants(ctx.Request.Context(), leaseID, userID)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"items": tenants,
		"total": total,
	})
}

func (c *TenantController) handleSearchTenants(ctx *gin.Context) {
	// Get userID from token
	userID, err := utils.GetUserIDFromToken(ctx.Request)
	if err != nil {
		ctx.JSON(http.StatusUnauthorized, gin.H{"error": "Unauthorized"})
		return
	}

	// Get search query from URL
	query := ctx.Query("q")
	if query == "" {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "Search query is required"})
		return
	}

	// Get tenants from database
	tenants, _, err := entities.SearchTenants(ctx.Request.Context(), query, userID)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"items": tenants,
	})
}

// handleUpdateTenantSelf 处理租客自己更新tenantId的请求
func (c *TenantController) handleUpdateTenantSelf(ctx *gin.Context) {
	userID, err := utils.GetUserIDFromToken(ctx.Request)
	if err != nil {
		ctx.JSON(http.StatusUnauthorized, gin.H{"error": "Unauthorized"})
		return
	}
	leaseId := ctx.Param("leaseId")
	user, _ := entities.GetUserByID(ctx.Request.Context(), userID)
	if user == nil || user.Email == "" {
		ctx.JSON(http.StatusForbidden, gin.H{"error": "No permission"})
		return
	}
	leaseColl := gomongo.Coll("rr", "leases")
	filter := bson.M{"_id": leaseId, "ctnts.email": user.Email}
	update := bson.M{"$set": bson.M{
		"ctnts.$.tenantId": userID,
	}}
	res, err := leaseColl.UpdateOne(ctx.Request.Context(), filter, update)
	if err != nil || res.MatchedCount == 0 {
		ctx.JSON(http.StatusForbidden, gin.H{"error": "No permission or not found"})
		return
	}
	ctx.JSON(http.StatusOK, gin.H{"success": true})
}
