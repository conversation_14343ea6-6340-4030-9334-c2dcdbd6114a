package controller

import (
	"bytes"
	"fmt"
	"io"
	"net/http"
	"rent_report/entities"
	"rent_report/router"
	"rent_report/utils"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/real-rm/golog"
	"github.com/real-rm/gomail"
	"github.com/real-rm/gomongo"
)

type SendMessageRequest struct {
	Recipients             []string `json:"recipients"`
	RecipientNames         []string `json:"recipientNames"`
	RecipientLeaseIds      []string `json:"recipientLeaseIds"`
	RecipientLeaseStatuses []string `json:"recipientLeaseStatuses"`
	Subject                string   `json:"subject"`
	Body                   string   `json:"body"`
}

type MessageController struct{}

func init() {
	router.Register(&MessageController{})
}

func (c *MessageController) RegisterRoutes(r *gin.Engine) {
	r.POST("/v1/messages/send", c.<PERSON>le<PERSON>endMessage)
	r.GET("/v1/messages/sent", c.GetSentMessages)
}

func (c *MessageController) HandleSendMessage(ctx *gin.Context) {
	// 获取当前用户ID和邮箱
	userID, err := utils.GetUserIDFromToken(ctx.Request)
	if err != nil {
		ctx.JSON(http.StatusUnauthorized, gin.H{"error": "Unauthorized"})
		return
	}

	// 重新创建请求体以便解析
	body, _ := io.ReadAll(ctx.Request.Body)
	ctx.Request.Body = io.NopCloser(bytes.NewBuffer(body))

	var req SendMessageRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request body"})
		return
	}

	if len(req.Recipients) == 0 || req.Subject == "" || req.Body == "" {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "Missing recipients, subject, or body"})
		return
	}

	// 获取当前用户信息以获取邮箱
	user, err := entities.GetUserByID(ctx.Request.Context(), userID)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get user info"})
		return
	}

	// Get mailer instance
	mailer, err := gomail.GetSendMailObj()
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get mailer: " + err.Error()})
		return
	}

	// Create email message
	htmlBody := fmt.Sprintf("<pre style='font-family:inherit'>%s</pre>", req.Body)
	msg := &gomail.EmailMessage{
		To:      req.Recipients,
		Subject: req.Subject,
		HTML:    htmlBody,
		Engine:  "gmail", // Use SMTP server that matches SPF record
	}

	// Send email using Gmail SMTP engine
	err = mailer.SendMail("gmail", msg)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to send email: " + err.Error()})
		return
	}

	// 邮件发送成功后，保存到数据库
	messageColl := gomongo.Coll("rr", "messages")
	if messageColl != nil {
		msgDoc := map[string]interface{}{
			"from":                   user.Email, // 使用当前用户的邮箱
			"to":                     req.Recipients,
			"toName":                 req.RecipientNames,         // 添加收件人姓名字段
			"recipientLeaseIds":      req.RecipientLeaseIds,      // 添加收件人leaseId字段
			"recipientLeaseStatuses": req.RecipientLeaseStatuses, // 添加收件人lease状态字段
			"subject":                req.Subject,
			"body":                   req.Body,
			"sentAt":                 time.Now(),
		}
		if _, err := messageColl.InsertOne(ctx.Request.Context(), msgDoc); err != nil {
			// 记录错误但不影响邮件发送的成功响应
			golog.Error("Failed to save message to database", "error", err, "recipients", req.Recipients, "subject", req.Subject)
		}
	}

	ctx.JSON(http.StatusOK, gin.H{"message": "Email sent successfully"})
}

func (c *MessageController) GetSentMessages(ctx *gin.Context) {
	// 获取当前用户ID和邮箱
	userID, err := utils.GetUserIDFromToken(ctx.Request)
	if err != nil {
		ctx.JSON(http.StatusUnauthorized, gin.H{"error": "Unauthorized"})
		return
	}

	// 获取当前用户信息以获取邮箱
	user, err := entities.GetUserByID(ctx.Request.Context(), userID)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get user info"})
		return
	}

	messageColl := gomongo.Coll("rr", "messages")
	if messageColl == nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": "DB not ready"})
		return
	}
	cursor, err := messageColl.Find(ctx.Request.Context(), map[string]interface{}{"from": user.Email})
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": "DB error"})
		return
	}
	var messages []map[string]interface{}
	if err := cursor.All(ctx.Request.Context(), &messages); err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": "DB error"})
		return
	}
	ctx.JSON(http.StatusOK, messages)
}
