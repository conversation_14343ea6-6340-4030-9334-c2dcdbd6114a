package controller

import (
	"net/http"
	"rent_report/entities"
	"rent_report/router"
	"rent_report/utils"
	"strings"

	"github.com/gin-gonic/gin"
)

type PropertyRoomController struct{}

func init() {
	router.Register(&PropertyRoomController{})
}

func (c *PropertyRoomController) RegisterRoutes(r *gin.Engine) {
	roomRouter := r.Group("/v1/properties/:propertyId/rooms")
	{
		roomRouter.POST("", c.handleCreateRoom)
		roomRouter.GET("/:roomId", c.handleGetRoom)
		roomRouter.PUT("/:roomId", c.handleUpdateRoom)
		roomRouter.GET("/:roomId/check-deletion", c.handleCheckRoomDeletion)
		roomRouter.DELETE("/:roomId", c.handleDeleteRoom)
	}
}

func (c *PropertyRoomController) handleCreateRoom(ctx *gin.Context) {
	// Get userID from token
	userID, err := utils.GetUserIDFromToken(ctx.Request)
	if err != nil {
		ctx.JSON(http.StatusUnauthorized, gin.H{"error": "Unauthorized"})
		return
	}

	// Get propertyId from URL
	propertyID := ctx.Param("propertyId")

	// Get the property first
	property, err := entities.GetProperty(ctx.Request.Context(), propertyID, userID)
	if err != nil {
		ctx.JSON(http.StatusNotFound, gin.H{"error": err.Error()})
		return
	}

	var room entities.Room
	if err := ctx.ShouldBindJSON(&room); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request body"})
		return
	}

	// Set the room ID
	room.ID = utils.GenerateNanoID()

	// Add room to property
	if err := property.AddRoom(room); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Update property in database
	if err := property.Update(ctx.Request.Context()); err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	ctx.JSON(http.StatusCreated, room)
}

func (c *PropertyRoomController) handleGetRoom(ctx *gin.Context) {
	// Get userID from token
	userID, err := utils.GetUserIDFromToken(ctx.Request)
	if err != nil {
		ctx.JSON(http.StatusUnauthorized, gin.H{"error": "Unauthorized"})
		return
	}

	// Get propertyId and roomId from URL
	propertyID := ctx.Param("propertyId")
	roomID := ctx.Param("roomId")

	// Get the property first
	property, err := entities.GetProperty(ctx.Request.Context(), propertyID, userID)
	if err != nil {
		ctx.JSON(http.StatusNotFound, gin.H{"error": err.Error()})
		return
	}

	// Get the room from property
	room, err := property.GetRoom(roomID)
	if err != nil {
		ctx.JSON(http.StatusNotFound, gin.H{"error": err.Error()})
		return
	}

	ctx.JSON(http.StatusOK, room)
}

func (c *PropertyRoomController) handleUpdateRoom(ctx *gin.Context) {
	// Get userID from token
	userID, err := utils.GetUserIDFromToken(ctx.Request)
	if err != nil {
		ctx.JSON(http.StatusUnauthorized, gin.H{"error": "Unauthorized"})
		return
	}

	// Get propertyId and roomId from URL
	propertyID := ctx.Param("propertyId")
	roomID := ctx.Param("roomId")

	// Get the property first
	property, err := entities.GetProperty(ctx.Request.Context(), propertyID, userID)
	if err != nil {
		ctx.JSON(http.StatusNotFound, gin.H{"error": err.Error()})
		return
	}

	var room entities.Room
	if err := ctx.ShouldBindJSON(&room); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request body"})
		return
	}

	// Ensure the room ID matches
	room.ID = roomID

	// Update room in property
	if err := property.UpdateRoom(room); err != nil {
		ctx.JSON(http.StatusNotFound, gin.H{"error": err.Error()})
		return
	}

	// Update property in database
	if err := property.Update(ctx.Request.Context()); err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	ctx.JSON(http.StatusOK, room)
}

func (c *PropertyRoomController) handleCheckRoomDeletion(ctx *gin.Context) {
	// Get userID from token
	userID, err := utils.GetUserIDFromToken(ctx.Request)
	if err != nil {
		ctx.JSON(http.StatusUnauthorized, gin.H{"error": "Unauthorized"})
		return
	}

	// Get propertyId and roomId from URL
	propertyID := ctx.Param("propertyId")
	roomID := ctx.Param("roomId")

	// 检查删除约束
	if err := entities.CheckRoomDeletionConstraints(ctx.Request.Context(), propertyID, roomID, userID); err != nil {
		if err.Error() == "property not found" || err.Error() == "room not found" {
			ctx.JSON(http.StatusNotFound, gin.H{"error": err.Error()})
			return
		}
		// 检查是否是约束错误
		if strings.Contains(err.Error(), "cannot delete room") {
			ctx.JSON(http.StatusConflict, gin.H{
				"canDelete": false,
				"error":     err.Error(),
				"code":      "DELETION_CONSTRAINT_VIOLATION",
			})
			return
		}
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	// 没有约束，可以删除
	ctx.JSON(http.StatusOK, gin.H{"canDelete": true})
}

func (c *PropertyRoomController) handleDeleteRoom(ctx *gin.Context) {
	// Get userID from token
	userID, err := utils.GetUserIDFromToken(ctx.Request)
	if err != nil {
		ctx.JSON(http.StatusUnauthorized, gin.H{"error": "Unauthorized"})
		return
	}

	// Get propertyId and roomId from URL
	propertyID := ctx.Param("propertyId")
	roomID := ctx.Param("roomId")

	// Get the property first
	property, err := entities.GetProperty(ctx.Request.Context(), propertyID, userID)
	if err != nil {
		ctx.JSON(http.StatusNotFound, gin.H{"error": err.Error()})
		return
	}

	// Delete room from property
	if err := property.DeleteRoom(ctx.Request.Context(), roomID); err != nil {
		// 检查是否是约束错误
		if strings.Contains(err.Error(), "cannot delete room") {
			ctx.JSON(http.StatusConflict, gin.H{
				"error": err.Error(),
				"code":  "DELETION_CONSTRAINT_VIOLATION",
			})
			return
		}
		ctx.JSON(http.StatusNotFound, gin.H{"error": err.Error()})
		return
	}

	// Update property in database
	if err := property.Update(ctx.Request.Context()); err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	ctx.Status(http.StatusNoContent)
}
