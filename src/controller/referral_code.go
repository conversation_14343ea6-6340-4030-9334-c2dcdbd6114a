package controller

import (
	"net/http"
	"rent_report/entities"
	"rent_report/router"
	"rent_report/utils"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/real-rm/gomongo"
)

// GET /api/referral-codes?creator=xxx
func GetReferralCodes(ctx *gin.Context) {
	// 1. 验证用户身份
	userID, err := utils.GetUserIDFromToken(ctx.Request)
	if err != nil {
		ctx.JSON(http.StatusUnauthorized, gin.H{"error": "Unauthorized"})
		return
	}

	creator := ctx.Query("creator")
	if creator == "" {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "creator required"})
		return
	}

	// 2. 权限验证：只能查询自己的推荐码
	if creator != userID {
		ctx.JSON(http.StatusForbidden, gin.H{"error": "Access denied: can only view your own referral codes"})
		return
	}

	codes, err := entities.GetReferralCodesByCreator(ctx, creator)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}
	ctx.JSON(http.StatusOK, gin.H{"items": codes})
}

// POST /api/referral-codes
func CreateReferralCode(ctx *gin.Context) {
	userID, err := utils.GetUserIDFromToken(ctx.Request)
	if err != nil {
		ctx.JSON(http.StatusUnauthorized, gin.H{"error": "Unauthorized"})
		return
	}

	// 新增：先查找当前用户是否已有推荐码
	codes, err := entities.GetReferralCodesByCreator(ctx, userID)
	if err == nil && len(codes) > 0 {
		// 已有推荐码，直接返回第一个
		ctx.JSON(http.StatusOK, codes[0])
		return
	}

	code := &entities.ReferralCode{
		CrtrUID: userID,
		Status:  "active",
		TtlUse:  10,
		RmnUse:  10,
		Type:    "discount",
		Dscp:    "{\"discount_percent\":10}",
		Exp:     time.Now().Add(365 * 24 * time.Hour),
	}
	if err := code.Create(ctx); err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}
	ctx.JSON(http.StatusOK, code)
}

// POST /v1/referral-codes/apply
func ApplyReferralCode(ctx *gin.Context) {
	type Req struct {
		Code string `json:"code"`
	}
	var req Req
	if err := ctx.ShouldBindJSON(&req); err != nil || req.Code == "" {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "Invalid code"})
		return
	}

	userID, err := utils.GetUserIDFromToken(ctx.Request)
	if err != nil {
		ctx.JSON(http.StatusUnauthorized, gin.H{"error": "Unauthorized"})
		return
	}

	coll := gomongo.Coll("rr", "referral_codes")
	if coll == nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": "referral_codes collection not initialized"})
		return
	}

	// 先查找推荐码，校验creator不是自己
	var code entities.ReferralCode
	err = coll.FindOne(ctx, map[string]interface{}{"code": req.Code, "status": "active", "rmnuse": map[string]interface{}{"$gt": 0}}).Decode(&code)

	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "Invalid or expired code"})
		return
	}
	if code.CrtrUID == userID {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "You cannot use your own referral code"})
		return
	}

	// 检查usage表，是否已用过该code
	usageColl := gomongo.Coll("rr", "referral_code_usages")
	if usageColl == nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": "Usage collection not initialized"})
		return
	}
	count, err := usageColl.CountDocuments(ctx, map[string]interface{}{"uid": userID, "code_id": code.ID})
	if err == nil && count > 0 {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "You have already used this referral code"})
		return
	}

	// 原子减1
	res := coll.FindOneAndUpdate(ctx, map[string]interface{}{"_id": code.ID, "rmnuse": map[string]interface{}{"$gt": 0}}, map[string]interface{}{"$inc": map[string]interface{}{"rmnuse": -1}})
	if res.Err() != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "This referral code has been used up"})
		return
	}

	// 写usage表
	usage := &entities.ReferralCodeUsage{
		UID:    userID,
		CodeID: code.ID,
		Status: "active",
		Actv:   time.Now(),
		Exp:    code.Exp,
	}
	if err := usage.Create(ctx); err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to record usage"})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{"success": true})
}

// POST /v1/referral-codes/validate
func ValidateReferralCode(ctx *gin.Context) {
	type Req struct {
		Code string `json:"code"`
	}
	var req Req
	if err := ctx.ShouldBindJSON(&req); err != nil || req.Code == "" {
		ctx.JSON(http.StatusBadRequest, gin.H{"valid": false, "error": "Invalid code"})
		return
	}

	coll := gomongo.Coll("rr", "referral_codes")
	if coll == nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"valid": false, "error": "Referral code collection not initialized"})
		return
	}

	var code entities.ReferralCode
	err := coll.FindOne(ctx, map[string]interface{}{"code": req.Code, "status": "active", "rmnuse": map[string]interface{}{"$gt": 0}}).Decode(&code)
	if err != nil {
		ctx.JSON(http.StatusOK, gin.H{"valid": false, "error": "Invalid or expired code"})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{"valid": true})
}

// GET /v1/referral-codes/:code/validation - RESTful version
func ValidateReferralCodeRESTful(ctx *gin.Context) {
	code := ctx.Param("code")
	if code == "" {
		ctx.JSON(http.StatusBadRequest, gin.H{"valid": false, "error": "Invalid code"})
		return
	}

	coll := gomongo.Coll("rr", "referral_codes")
	if coll == nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"valid": false, "error": "Referral code collection not initialized"})
		return
	}

	var referralCode entities.ReferralCode
	err := coll.FindOne(ctx, map[string]interface{}{"code": code, "status": "active", "rmnuse": map[string]interface{}{"$gt": 0}}).Decode(&referralCode)
	if err != nil {
		ctx.JSON(http.StatusOK, gin.H{"valid": false, "error": "Invalid or expired code"})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{"valid": true})
}

// 标准controller注册方式
// 定义controller结构体
// 并在init中注册

type ReferralCodeController struct{}

func init() {
	router.Register(&ReferralCodeController{})
}

func (c *ReferralCodeController) RegisterRoutes(r *gin.Engine) {
	r.GET("/v1/referral-codes", GetReferralCodes)
	r.POST("/v1/referral-codes", CreateReferralCode)

	// RESTful routes
	r.POST("/v1/referral-code-applications", ApplyReferralCode)
	r.GET("/v1/referral-codes/:code/validation", ValidateReferralCodeRESTful)

	// Deprecated routes (for backward compatibility)
	r.POST("/v1/referral-codes/apply", ApplyReferralCode)
	r.POST("/v1/referral-codes/validate", ValidateReferralCode)
}
