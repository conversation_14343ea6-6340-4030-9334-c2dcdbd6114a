package controller

import (
	"context"
	"encoding/json"
	"fmt"
	"io/ioutil"
	"net/http"
	"os"
	"time"

	"rent_report/entities"
	"rent_report/models"
	"rent_report/router"
	"rent_report/utils"

	"github.com/gin-gonic/gin"
	"github.com/real-rm/goconfig"
	"github.com/real-rm/golog"
	"github.com/real-rm/gomongo"
	"github.com/stripe/stripe-go/paymentintent"
	"github.com/stripe/stripe-go/v75"
	"github.com/stripe/stripe-go/v75/webhook"
	"go.mongodb.org/mongo-driver/bson"
)

type StripeWebhookController struct{}

func init() {
	router.Register(&StripeWebhookController{})
}

func (c *StripeWebhookController) RegisterRoutes(r *gin.Engine) {
	r.POST("/v1/stripe/webhook", handleStripeWebhook)
}

func saveStripeEventLog(ctx context.Context, event stripe.Event, raw string, status string, pid string) {
	coll := gomongo.Coll("rr", "stripeeventlog")
	if coll == nil {
		return
	}
	log := models.StripeEventLog{
		Eid: utils.GenerateNanoID(),
		Tp:  string(event.Type),
		Sts: status,
		Raw: raw,
		Pid: pid,
		Ts:  time.Now(),
		Mt:  time.Now(),
	}
	_, _ = coll.InsertOne(ctx, log)
}

// handleStripeWebhook 监听并处理Stripe Webhook事件
func handleStripeWebhook(c *gin.Context) {
	const MaxBodyBytes = int64(65536)
	c.Request.Body = http.MaxBytesReader(c.Writer, c.Request.Body, MaxBodyBytes)
	payload, err := ioutil.ReadAll(c.Request.Body)
	if err != nil {
		c.JSON(http.StatusServiceUnavailable, gin.H{"error": "Read error"})
		return
	}

	// Stripe签名校验（必须配置STRIPE_WEBHOOK_SECRET环境变量或配置文件）
	secret := os.Getenv("STRIPE_WEBHOOK_SECRET")
	if secret == "" {
		// 从配置文件读取作为备选
		if configSecret := goconfig.Config("stripe.webhook_secret"); configSecret != nil {
			if secretStr, ok := configSecret.(string); ok && secretStr != "" {
				secret = secretStr
				golog.Debug("Using webhook secret from config file")
			}
		}
	} else {
		golog.Debug("Using webhook secret from environment variable")
	}

	// 强制要求webhook secret，绝不跳过签名验证
	if secret == "" {
		golog.Error("Webhook secret not configured")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Webhook secret not configured"})
		return
	}

	// 验证Stripe签名
	sigHeader := c.GetHeader("Stripe-Signature")
	golog.Debug("Stripe webhook signature verification", "hasSignature", sigHeader != "", "secretLength", len(secret))

	// 使用 ConstructEventWithOptions 忽略API版本不匹配
	event, err := webhook.ConstructEventWithOptions(payload, sigHeader, secret, webhook.ConstructEventOptions{
		IgnoreAPIVersionMismatch: true,
	})
	if err != nil {
		golog.Error("Signature verification failed", "error", err)
		c.JSON(http.StatusBadRequest, gin.H{"error": "Signature verification failed: " + err.Error()})
		return
	}

	golog.Debug("Webhook signature verification successful")
	// if secret != "" {
	// 	sigHeader := c.GetHeader("Stripe-Signature")
	// 	event, err = webhook.ConstructEvent(payload, sigHeader, secret)
	// 	if err != nil {
	// 		c.JSON(http.StatusBadRequest, gin.H{"error": "Signature verification failed"})
	// 		return
	// 	}
	// } else {
	// 	if err := json.Unmarshal(payload, &event); err != nil {
	// 		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid payload"})
	// 		return
	// 	}
	// }

	saveStripeEventLog(c.Request.Context(), event, string(payload), "received", "")

	switch event.Type {
	case "checkout.session.completed":
		var session stripe.CheckoutSession
		if err := json.Unmarshal(event.Data.Raw, &session); err == nil {
			// 1. Get User ID and Customer ID (from your original logic)
			uid := ""
			cusId := ""
			if session.Metadata != nil && session.Metadata["uid"] != "" {
				uid = session.Metadata["uid"]
			}
			if uid == "" && session.Customer != nil {
				user, _ := gomongo.Coll("rr", "users").FindOne(c.Request.Context(), bson.M{"stripeCusIds": session.Customer.ID}).DecodeBytes()
				if user != nil {
					uid, _ = user.Lookup("_id").StringValueOK()
				}
			}
			if session.Customer != nil {
				cusId = session.Customer.ID
			}

			// 2. Create user-customer mapping (from your original logic)
			if uid != "" && cusId != "" {
				usersColl := gomongo.Coll("rr", "users")
				filter := bson.M{"_id": uid}
				update := bson.M{"$addToSet": bson.M{"stripeCusIds": cusId}}
				_, _ = usersColl.UpdateOne(c.Request.Context(), filter, update)
			}

			// 3. Create Order Info Record (from your original logic)
			prdId := ""
			if v, ok := session.Metadata["prdId"]; ok && v != "" {
				prdId = v
			}
			ordColl := gomongo.Coll("rr", "ordinfo")
			ord := bson.M{
				"_id":    utils.GenerateNanoID(),
				"uid":    uid,
				"cusId":  cusId,
				"prdId":  prdId,
				"qty":    1,
				"ttlAmt": session.AmountTotal,
				"cur":    session.Currency,
				"sts":    session.Status,
				"ssnId":  session.ID,
				"ts":     time.Now(),
				"mt":     time.Now(),
			}
			_, _ = ordColl.InsertOne(c.Request.Context(), ord)

			// 4. Create/Update User Subscription Record (my new logic)
			if uid != "" && prdId != "" {
				var subPlan models.SubPlan
				err := gomongo.Coll("rr", "subplan").FindOne(c.Request.Context(), bson.M{"prdId": prdId}).Decode(&subPlan)
				if err == nil {
					userSubColl := gomongo.Coll("rr", "usersub")
					// Invalidate old subscriptions
					_, _ = userSubColl.UpdateMany(
						c.Request.Context(),
						bson.M{"uid": uid, "sts": "active"},
						bson.M{"$set": bson.M{"sts": "inactive", "mt": time.Now()}},
					)
					// Create new subscription
					newUserSub := bson.M{
						"_id":         utils.GenerateNanoID(),
						"uid":         uid,
						"cusId":       cusId,
						"planId":      subPlan.PlanId, // Correctly uses the internal _id
						"stripeSubId": session.Subscription.ID,
						"sts":         "active",
						"ts":          time.Now(),
						"mt":          time.Now(),
					}
					_, _ = userSubColl.InsertOne(c.Request.Context(), newUserSub)
				}
			}

			// 订阅成功后，仅将 accountType 设为 vip，role 不变
			if uid != "" {
				usersColl := gomongo.Coll("rr", "users")
				_, _ = usersColl.UpdateOne(c.Request.Context(), bson.M{"_id": uid}, bson.M{"$set": bson.M{"acctTp": "vip", "mt": time.Now()}})
			}
		}
	case "customer.subscription.created":
		// This event is redundant when using checkout sessions.
		// The main logic is handled in "checkout.session.completed" to ensure
		// we have access to the metadata (like prdId) for creating a correct usersub record.
		// Leaving this block empty prevents the creation of a duplicate, incomplete usersub record.
	case "invoice.paid":
		var inv stripe.Invoice
		if err := json.Unmarshal(event.Data.Raw, &inv); err == nil {
			uid := ""
			if inv.Metadata != nil && inv.Metadata["uid"] != "" {
				uid = inv.Metadata["uid"]
			}
			if uid == "" && inv.Customer != nil {
				user, _ := gomongo.Coll("rr", "users").FindOne(c.Request.Context(), bson.M{"stripeCusIds": inv.Customer.ID}).DecodeBytes()
				if user != nil {
					uid, _ = user.Lookup("_id").StringValueOK()
				}
			}

			pid := ""
			pmId := ""
			crdL4 := ""

			// 方法1: 尝试从 PaymentIntent 获取支付信息
			if inv.PaymentIntent != nil && inv.PaymentIntent.ID != "" {
				golog.Debug("Processing PaymentIntent", "paymentIntentID", inv.PaymentIntent.ID)
				pid = inv.PaymentIntent.ID

				pi, err := paymentintent.Get(inv.PaymentIntent.ID, nil)
				if err != nil {
					golog.Error("Failed to get PaymentIntent", "error", err, "paymentIntentID", inv.PaymentIntent.ID)
				} else {
					if pi.PaymentMethod != nil {
						pmId = pi.PaymentMethod.ID
						golog.Debug("Found payment method", "pmId", pmId)
					}
				}
			} else {
				golog.Debug("PaymentIntent is nil or empty")
			}

			// 方法2: 如果 PaymentIntent 为空，直接从 payinfo 表中查找用户的支付信息
			if pmId == "" && inv.Customer != nil {
				golog.Debug("Searching payinfo by customer ID", "customerID", inv.Customer.ID)
				payinfoColl := gomongo.Coll("rr", "payinfo")
				var payinfo map[string]interface{}
				if err := payinfoColl.FindOne(c.Request.Context(), bson.M{"cusId": inv.Customer.ID}).Decode(&payinfo); err == nil {
					if pm, ok := payinfo["pmId"].(string); ok {
						pmId = pm
						golog.Debug("Found payment method from payinfo", "pmId", pmId)
					}
					if l4, ok := payinfo["crdL4"].(string); ok {
						crdL4 = l4
						golog.Debug("Found card last 4 digits", "pmId", pmId, "crdL4", crdL4)
					}
				} else {
					golog.Debug("Payinfo not found", "customerID", inv.Customer.ID)
				}
			}

			// 方法3: 如果还是没有找到，尝试通过 pmId 查找 payinfo
			if pmId != "" && crdL4 == "" {
				golog.Debug("Searching payinfo by payment method ID", "pmId", pmId)
				payinfoColl := gomongo.Coll("rr", "payinfo")
				var payinfo map[string]interface{}
				if err := payinfoColl.FindOne(c.Request.Context(), bson.M{"pmId": pmId}).Decode(&payinfo); err == nil {
					if l4, ok := payinfo["crdL4"].(string); ok {
						crdL4 = l4
						golog.Debug("Found card last 4 digits by pmId", "pmId", pmId, "crdL4", crdL4)
					}
				} else {
					golog.Debug("Payinfo not found by pmId", "pmId", pmId)
				}
			}

			billColl := gomongo.Coll("rr", "billhst")
			bill := models.BillHst{
				Bid:   utils.GenerateNanoID(),
				Uid:   uid,
				CusId: inv.Customer.ID,
				Amt:   inv.AmountPaid,
				Cur:   string(inv.Currency),
				Sts:   string(inv.Status),
				Pid:   pid,
				PmId:  pmId,
				CrdL4: crdL4,
				InvId: inv.ID,
				Ts:    time.Now(),
				Mt:    time.Now(),
			}
			_, _ = billColl.InsertOne(c.Request.Context(), bill)

			// 发送账单邮件
			// 获取用户邮箱和姓名
			var userEmail, userName string
			if uid != "" {
				if user, err := entities.GetUserByID(c.Request.Context(), uid); err == nil {
					userEmail = user.Email
					userName = user.Username
				}
			}
			if userEmail != "" {
				// 续费日
				renewalDate := ""
				if inv.NextPaymentAttempt != 0 {
					renewalDate = time.Unix(inv.NextPaymentAttempt, 0).Format("Jan 2, 2006")
				} else {
					renewalDate = time.Now().Format("Jan 2, 2006")
				}
				// Rent reports & Total
				rentReports := "$0 (0 additional reports)"
				if inv.Lines.TotalCount > 1 && len(inv.Lines.Data) > 1 {
					rentReports = fmt.Sprintf("$%.2f (%d additional reports)", float64(inv.Lines.Data[1].Amount)/100, inv.Lines.TotalCount-1)
				}
				total := fmt.Sprintf("$%.2f", float64(inv.AmountPaid)/100)
				golog.Debug("Sending invoice email", "userEmail", userEmail, "userName", userName, "renewalDate", renewalDate, "total", total)
				_ = utils.SendInvoiceEmail(userEmail, userName, renewalDate, rentReports, total)
			}

			// 支付成功后，更新 usersub 状态为 active
			subID := ""
			if inv.Subscription != nil && inv.Subscription.ID != "" {
				subID = inv.Subscription.ID
			}
			// 如果没有，尝试从 event.Data.Raw 里解析
			if subID == "" {
				var raw map[string]interface{}
				if err := json.Unmarshal(event.Data.Raw, &raw); err == nil {
					if lines, ok := raw["lines"].(map[string]interface{}); ok {
						if dataArr, ok := lines["data"].([]interface{}); ok && len(dataArr) > 0 {
							if item, ok := dataArr[0].(map[string]interface{}); ok {
								if parent, ok := item["parent"].(map[string]interface{}); ok {
									if sidetails, ok := parent["subscription_item_details"].(map[string]interface{}); ok {
										if sub, ok := sidetails["subscription"].(string); ok {
											subID = sub
										}
									}
									if subdetails, ok := parent["subscription_details"].(map[string]interface{}); ok {
										if sub, ok := subdetails["subscription"].(string); ok {
											subID = sub
										}
									}
								}
							}
						}
					}
				}
			}
			golog.Debug("Parsed subscription ID", "subID", subID)
			if subID != "" {
				userSubColl := gomongo.Coll("rr", "usersub")
				filter := bson.M{"stripeSubId": subID}
				update := bson.M{"$set": bson.M{"sts": "active", "mt": time.Now()}}
				res, err := userSubColl.UpdateOne(c.Request.Context(), filter, update)
				golog.Debug("Updated usersub to active", "subID", subID, "matched", res.MatchedCount, "modified", res.ModifiedCount, "error", err)
			}

		}
	case "payment_method.attached":
		var pm stripe.PaymentMethod
		if err := json.Unmarshal(event.Data.Raw, &pm); err == nil {
			uid := ""
			if pm.Metadata != nil && pm.Metadata["uid"] != "" {
				uid = pm.Metadata["uid"]
			}
			if uid == "" && pm.Customer != nil {
				user, _ := gomongo.Coll("rr", "users").FindOne(c.Request.Context(), bson.M{"stripeCusIds": pm.Customer.ID}).DecodeBytes()
				if user != nil {
					uid, _ = user.Lookup("_id").StringValueOK()
				}
			}
			payColl := gomongo.Coll("rr", "payinfo")
			if pm.Card != nil {
				pay := bson.M{
					"_id":    utils.GenerateNanoID(),
					"uid":    uid,
					"cusId":  pm.Customer.ID,
					"pmId":   pm.ID,
					"crdBrd": pm.Card.Brand,
					"crdL4":  pm.Card.Last4,
					"expMn":  pm.Card.ExpMonth,
					"expYr":  pm.Card.ExpYear,
					"mtdTp":  pm.Type,
					"ts":     time.Now(),
				}

				// 添加账单地址信息
				if pm.BillingDetails != nil && pm.BillingDetails.Address != nil {
					pay["billingAddress"] = bson.M{
						"line1":      pm.BillingDetails.Address.Line1,
						"line2":      pm.BillingDetails.Address.Line2,
						"city":       pm.BillingDetails.Address.City,
						"state":      pm.BillingDetails.Address.State,
						"postalCode": pm.BillingDetails.Address.PostalCode,
						"country":    pm.BillingDetails.Address.Country,
					}

					// 注释掉自动更新用户地址的逻辑，避免覆盖用户在Account Settings中设置的地址
					// Billing Address只保存在payinfo中用于显示，不影响用户的address字段
					// if uid != "" {
					// 	usersColl := gomongo.Coll("rr", "users")
					// 	userAddress := bson.M{
					// 		"street":  pm.BillingDetails.Address.Line1,
					// 		"unit":    pm.BillingDetails.Address.Line2,
					// 		"city":    pm.BillingDetails.Address.City,
					// 		"prov":    pm.BillingDetails.Address.State,
					// 		"country": pm.BillingDetails.Address.Country,
					// 		"zipCode": pm.BillingDetails.Address.PostalCode,
					// 	}
					// 	filter := bson.M{"_id": uid}
					// 	update := bson.M{"$set": bson.M{"address": userAddress}}
					// 	_, _ = usersColl.UpdateOne(c.Request.Context(), filter, update)
					// }
				}

				_, _ = payColl.InsertOne(c.Request.Context(), pay)
			}
		}
	case "customer.subscription.deleted":
		var sub stripe.Subscription
		if err := json.Unmarshal(event.Data.Raw, &sub); err == nil {
			// 取消订阅后，仅将 accountType 设为空，role 不变
			customerId := ""
			if sub.Customer != nil {
				customerId = sub.Customer.ID
			}
			if customerId != "" {
				usersColl := gomongo.Coll("rr", "users")
				_, _ = usersColl.UpdateOne(c.Request.Context(), bson.M{"stripeCusIds": customerId}, bson.M{"$set": bson.M{"acctTp": "", "mt": time.Now()}})
				var userDoc bson.M
				if err := usersColl.FindOne(c.Request.Context(), bson.M{"stripeCusIds": customerId}).Decode(&userDoc); err == nil {
					if uid, ok := userDoc["_id"].(string); ok && uid != "" {
						// 处理租约的 rent reporting 关闭和邮件通知
						err := handleSubscriptionCancellationRentReporting(c.Request.Context(), uid)
						if err != nil {
							golog.Error("Failed to handle rent reporting cancellation", "error", err, "userId", uid)
						}
					}
				}
			}

			// 同步更新相关订单状态
			ordColl := gomongo.Coll("rr", "ordinfo")
			ordFilter := bson.M{}
			// 优先用 metadata 里的 uid/prdId
			if sub.Metadata != nil && sub.Metadata["uid"] != "" && sub.Metadata["prdId"] != "" {
				ordFilter["uid"] = sub.Metadata["uid"]
				ordFilter["prdId"] = sub.Metadata["prdId"]
			} else {
				// 若 metadata 不全，尝试通过 customer id 反查 usersub
				ordFilter["cusId"] = sub.Customer.ID
			}
			ordFilter["sts"] = bson.M{"$ne": "canceled"}
			ordUpdate := bson.M{"$set": bson.M{"sts": "canceled", "mt": time.Now()}}
			_, _ = ordColl.UpdateMany(c.Request.Context(), ordFilter, ordUpdate)
		}
	}

	c.Status(200)
}

// handleSubscriptionCancellationRentReporting 处理订阅取消时的租约 rent reporting 关闭和邮件通知
func handleSubscriptionCancellationRentReporting(ctx context.Context, userID string) error {
	leasesColl := gomongo.Coll("rr", "leases")
	if leasesColl == nil {
		return fmt.Errorf("leases collection not initialized")
	}

	// 查找该用户所有启用了 rent reporting 的租约
	filter := bson.M{
		"usrId":   userID,
		"rentRep": true,
	}

	cursor, err := leasesColl.Find(ctx, filter)
	if err != nil {
		return fmt.Errorf("failed to find leases: %v", err)
	}
	defer cursor.Close(ctx)

	var leasesToUpdate []entities.Lease
	if err = cursor.All(ctx, &leasesToUpdate); err != nil {
		return fmt.Errorf("failed to decode leases: %v", err)
	}

	// 批量更新租约状态
	if len(leasesToUpdate) > 0 {
		updateFilter := bson.M{"usrId": userID, "rentRep": true}
		update := bson.M{"$set": bson.M{"rentRep": false, "mt": time.Now()}}
		res, err := leasesColl.UpdateMany(ctx, updateFilter, update)
		if err != nil {
			return fmt.Errorf("failed to update leases: %v", err)
		}
		golog.Debug("Updated leases rentRep status", "matched", res.MatchedCount, "modified", res.ModifiedCount)

		// 为每个租约发送暂停邮件通知
		for _, originalLease := range leasesToUpdate {
			// 创建更新后的租约对象
			updatedLease := originalLease
			updatedLease.RentReporting = false

			// 发送租金报告暂停通知邮件
			err = sendRentReportingPausedNotificationForLease(ctx, &originalLease, &updatedLease)
			if err != nil {
				// 记录错误但不影响主要操作
				golog.Error("Failed to send rent reporting paused notification emails", "error", err, "leaseId", originalLease.ID)
			}
		}
	}

	return nil
}

// sendRentReportingPausedNotificationForLease 为单个租约发送租金报告暂停通知
func sendRentReportingPausedNotificationForLease(ctx context.Context, originalLease, updatedLease *entities.Lease) error {
	// 获取房东信息
	user, err := entities.GetUserByID(ctx, updatedLease.UserID)
	if err != nil {
		golog.Error("Failed to get landlord user info", "error", err, "userId", updatedLease.UserID)
		return fmt.Errorf("failed to get landlord info: %v", err)
	}

	// 获取属性信息
	property, err := entities.GetProperty(ctx, updatedLease.PropertyID, updatedLease.UserID)
	if err != nil {
		golog.Error("Failed to get property info", "error", err, "propertyId", updatedLease.PropertyID)
		// 不返回错误，继续发送邮件
	}

	// 准备房东信息
	landlordInfo := utils.LandlordInfo{
		FirstName:       user.Username,   // 使用 username 作为名字
		LastName:        "",              // 没有单独的姓氏字段
		PropertyAddress: "your property", // 默认值
	}

	// 如果成功获取到属性信息，格式化属性地址
	if property != nil {
		addr := property.Address
		addressStr := ""
		if addr.Street != "" {
			addressStr += addr.Street
		}
		if addr.City != "" {
			if addressStr != "" {
				addressStr += ", "
			}
			addressStr += addr.City
		}
		if addr.Prov != "" {
			if addressStr != "" {
				addressStr += ", "
			}
			addressStr += addr.Prov
		}
		if addr.ZipCode != "" {
			if addressStr != "" {
				addressStr += " "
			}
			addressStr += addr.ZipCode
		}
		if addr.Country != "" {
			if addressStr != "" {
				addressStr += ", "
			}
			addressStr += addr.Country
		}

		if addressStr != "" {
			landlordInfo.PropertyAddress = addressStr
		}
	}

	// 如果房东姓名为空，使用默认值
	if landlordInfo.FirstName == "" {
		landlordInfo.FirstName = "Your landlord"
	}

	// 准备租户邮件信息
	var tenants []utils.TenantEmailInfo
	for _, tenant := range originalLease.CurrentTenants {
		if tenant.Email != "" && tenant.FirstName != "" {
			tenants = append(tenants, utils.TenantEmailInfo{
				Email:     tenant.Email,
				FirstName: tenant.FirstName,
			})
		}
	}

	if len(tenants) > 0 {
		// 异步发送暂停邮件
		utils.SendRentReportingPausedNotificationEmailsAsync(tenants, landlordInfo)
		golog.Info("Initiated rent reporting paused notification emails (subscription cancelled)",
			"leaseId", updatedLease.ID,
			"tenantCount", len(tenants),
			"landlordName", landlordInfo.FirstName,
			"propertyAddress", landlordInfo.PropertyAddress,
			"propertyId", updatedLease.PropertyID)
	} else {
		golog.Warn("No valid tenant emails found for rent reporting paused notification", "leaseId", updatedLease.ID)
	}

	return nil
}
