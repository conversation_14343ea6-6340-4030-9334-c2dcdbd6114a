package controller

import (
	"encoding/json"
	"net/http"
	"rent_report/entities"
	"rent_report/models"
	"rent_report/router"
	"rent_report/utils"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/real-rm/gomongo"
	"go.mongodb.org/mongo-driver/bson"
)

type SubPlanResponse struct {
	models.SubPlan
	OriginalPrc     int64   `json:"originalPrc,omitempty"`
	DiscountPercent float64 `json:"discountPercent,omitempty"`
}

type SubPlanController struct{}

func init() {
	router.Register(&SubPlanController{})
}

func (c *SubPlanController) RegisterRoutes(r *gin.Engine) {
	r.GET("/v1/subplans", c.handleGetSubPlans)
}

func (c *SubPlanController) handleGetSubPlans(ctx *gin.Context) {
	// Get user from token
	userID, err := utils.GetUserIDFromToken(ctx.Request)
	var discountPercent float64 = 0

	if err == nil && userID != "" {
		// Find active referral code usage for the user
		var usage entities.ReferralCodeUsage
		err := gomongo.Coll("rr", "referral_code_usages").FindOne(ctx.Request.Context(), bson.M{
			"uid":    userID,
			"status": "active",
		}).Decode(&usage)

		if err == nil {
			// Check if usage is not expired
			if usage.Exp.After(time.Now()) {
				// Find the corresponding referral code
				var code entities.ReferralCode
				err := gomongo.Coll("rr", "referral_codes").FindOne(ctx.Request.Context(), bson.M{
					"_id":    usage.CodeID,
					"status": "active",
				}).Decode(&code)

				if err == nil {
					// Check if code is not expired
					if code.Exp.After(time.Now()) {
						type Dscp struct {
							DiscountPercent float64 `json:"discount_percent"`
						}
						var d Dscp
						if json.Unmarshal([]byte(code.Dscp), &d) == nil {
							discountPercent = d.DiscountPercent
						}
					}
				}
			}
		}
	}

	// 从数据库获取所有订阅计划
	cursor, err := gomongo.Coll("rr", "subplan").Find(ctx.Request.Context(), bson.M{"sts": "active"})
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch subscription plans"})
		return
	}
	defer cursor.Close(ctx.Request.Context())

	var plans []models.SubPlan
	if err := cursor.All(ctx.Request.Context(), &plans); err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to decode subscription plans"})
		return
	}

	var responsePlans []SubPlanResponse
	for _, plan := range plans {
		responsePlan := SubPlanResponse{
			SubPlan: plan,
		}
		if discountPercent > 0 {
			responsePlan.OriginalPrc = plan.Prc
			responsePlan.Prc = int64(float64(plan.Prc) * (1 - discountPercent/100.0))
			responsePlan.DiscountPercent = discountPercent
		}
		responsePlans = append(responsePlans, responsePlan)
	}

	ctx.JSON(http.StatusOK, responsePlans)
}
