package controller

import (
	"net/http"
	"rent_report/entities"
	"rent_report/router"
	"rent_report/utils"
	"strings"

	"github.com/gin-gonic/gin"
)

type TenantPaymentController struct{}

func init() {
	router.Register(&TenantPaymentController{})
}

func (c *TenantPaymentController) RegisterRoutes(r *gin.Engine) {
	paymentRouter := r.Group("/v1/leases/:leaseId/tenant-payments")
	{
		paymentRouter.GET("", c.handleGetTenantPayments)
		paymentRouter.POST("", c.handleCreateTenantPayment)
		paymentRouter.GET("/:tenantPaymentId", c.handleGetTenantPayment)
		paymentRouter.PUT("/:tenantPaymentId", c.handleUpdateTenantPayment)
		paymentRouter.DELETE("/:tenantPaymentId", c.handleDeleteTenantPayment)
	}
}

func (c *TenantPaymentController) handleCreateTenantPayment(ctx *gin.Context) {
	// Get userID from token
	userID, err := utils.GetUserIDFromToken(ctx.Request)
	if err != nil {
		ctx.JSON(http.StatusUnauthorized, gin.H{"error": "Unauthorized"})
		return
	}

	// Get leaseId from URL
	leaseID := ctx.Param("leaseId")

	var payment entities.TenantPayment
	if err := ctx.ShouldBindJSON(&payment); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request body"})
		return
	}

	// Set the payment ID and leaseId
	payment.ID = utils.GenerateNanoID()
	payment.LeaseID = leaseID
	payment.UserID = userID

	if err := payment.Create(ctx.Request.Context()); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	ctx.JSON(http.StatusCreated, payment)
}

func (c *TenantPaymentController) handleGetTenantPayment(ctx *gin.Context) {
	// Get userID from token
	userID, err := utils.GetUserIDFromToken(ctx.Request)
	if err != nil {
		ctx.JSON(http.StatusUnauthorized, gin.H{"error": "Unauthorized"})
		return
	}

	// Get paymentId from URL
	paymentID := ctx.Param("tenantPaymentId")

	payment, err := entities.GetTenantPayment(ctx.Request.Context(), paymentID, userID)
	if err != nil {
		if err.Error() == "tenant payment not found" {
			ctx.JSON(http.StatusNotFound, gin.H{"error": err.Error()})
			return
		}
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	ctx.JSON(http.StatusOK, payment)
}

func (c *TenantPaymentController) handleUpdateTenantPayment(ctx *gin.Context) {
	// Get userID from token
	userID, err := utils.GetUserIDFromToken(ctx.Request)
	if err != nil {
		ctx.JSON(http.StatusUnauthorized, gin.H{"error": "Unauthorized"})
		return
	}

	// Get leaseId and paymentId from URL
	leaseID := ctx.Param("leaseId")
	paymentID := ctx.Param("tenantPaymentId")

	var payment entities.TenantPayment
	if err := ctx.ShouldBindJSON(&payment); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request body"})
		return
	}

	// Ensure IDs match
	payment.ID = paymentID
	payment.LeaseID = leaseID
	payment.UserID = userID

	if err := payment.Update(ctx.Request.Context()); err != nil {
		if err.Error() == "tenant payment not found" {
			ctx.JSON(http.StatusNotFound, gin.H{"error": err.Error()})
			return
		}
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	ctx.JSON(http.StatusOK, payment)
}

func (c *TenantPaymentController) handleDeleteTenantPayment(ctx *gin.Context) {
	// Get userID from token
	userID, err := utils.GetUserIDFromToken(ctx.Request)
	if err != nil {
		ctx.JSON(http.StatusUnauthorized, gin.H{"error": "Unauthorized"})
		return
	}

	// Get paymentId from URL
	paymentID := ctx.Param("tenantPaymentId")

	if err := entities.DeleteTenantPayment(ctx.Request.Context(), paymentID, userID); err != nil {
		if err.Error() == "tenant payment not found" {
			ctx.JSON(http.StatusNotFound, gin.H{"error": err.Error()})
			return
		}
		// 检查是否是保护错误
		if strings.Contains(err.Error(), "cannot delete protected payment") {
			ctx.JSON(http.StatusForbidden, gin.H{
				"error": err.Error(),
				"code":  "PAYMENT_PROTECTED",
			})
			return
		}
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	ctx.Status(http.StatusNoContent)
}

func (c *TenantPaymentController) handleGetTenantPayments(ctx *gin.Context) {
	// Get userID from token
	userID, err := utils.GetUserIDFromToken(ctx.Request)
	if err != nil {
		ctx.JSON(http.StatusUnauthorized, gin.H{"error": "Unauthorized"})
		return
	}

	// Get leaseId from URL
	leaseID := ctx.Param("leaseId")

	payments, total, err := entities.GetTenantPayments(ctx.Request.Context(), leaseID, userID)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"items": payments,
		"total": total,
	})
}
