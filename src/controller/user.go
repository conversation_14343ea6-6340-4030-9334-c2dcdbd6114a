package controller

import (
	"context"
	"fmt"
	"net/http"
	"rent_report/config"
	"rent_report/entities"
	"rent_report/middleware"
	"rent_report/router"
	"rent_report/utils"
	"strconv"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/real-rm/goconfig"
	"github.com/real-rm/golog"
	"github.com/real-rm/gomongo"
	"github.com/real-rm/goupload"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

type UserController struct{}

// 初始化MongoDB客户端
var mongoClient *mongo.Client

func init() {
	// 从配置文件获取MongoDB URI
	uri, _ := goconfig.Config("dbs.rr.uri").(string)
	client, err := mongo.Connect(context.Background(), options.Client().ApplyURI(uri))
	if err != nil {
		panic(fmt.Sprintf("Failed to connect to MongoDB: %v", err))
	}
	mongoClient = client

	router.Register(&UserController{})
}

func (c *UserController) RegisterRoutes(r *gin.Engine) {
	userRouter := r.Group("/v1/user")
	{
		userRouter.GET("", c.handleGetCurrentUser)
		userRouter.PUT("", c.handleUpdateUser)
		userRouter.PUT("/password", c.handleUpdatePassword)
		userRouter.POST("/verify", c.handleStartVerification)
		userRouter.DELETE("", c.handleCloseAccount)
		userRouter.GET("/related-objects", c.handleGetRelatedObjects)
		userRouter.PUT("/view-type", c.handleUpdateViewType)
		userRouter.GET("/ordinfo", c.handleGetUserOrdInfo)
		userRouter.GET("/billhst", c.handleGetUserBillHst)
		userRouter.POST("/resend-invoice", c.handleResendInvoice)
		userRouter.GET("/payinfo", c.handleGetPaymentInfo)
	}
	r.POST("/v1/problems", c.handleSubmitProblem)

	adminRouter := r.Group("/v1/admin")
	adminRouter.Use(middleware.RequireAdmin())
	adminRouter.GET("/users/:id/billhst", c.handleGetUserBillHstAdmin)
}

// handleGetCurrentUser returns the current user's information
func (c *UserController) handleGetCurrentUser(ctx *gin.Context) {
	// Get userID from token
	userID, err := utils.GetUserIDFromToken(ctx.Request)
	if err != nil {
		ctx.JSON(http.StatusUnauthorized, gin.H{"error": "Unauthorized"})
		return
	}

	// Get user from database
	user, err := entities.GetUserByID(ctx.Request.Context(), userID)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	// Return user data
	ctx.JSON(http.StatusOK, gin.H{
		"email":       user.Email,
		"name":        user.Username,
		"phoneNumber": user.PhoneNumber,
		"address":     user.Address,
		"accountType": user.AccountType,
		"accountStatus": func() string {
			if user.IsVerified {
				return "Verified"
			}
			return "Unverified"
		}(),
		"viewType": user.ViewType,
	})
}

// handleUpdateUser updates the current user's information
func (c *UserController) handleUpdateUser(ctx *gin.Context) {
	// Get userID from token
	userID, err := utils.GetUserIDFromToken(ctx.Request)
	if err != nil {
		ctx.JSON(http.StatusUnauthorized, gin.H{"error": "Unauthorized"})
		return
	}

	// Get user from database
	user, err := entities.GetUserByID(ctx.Request.Context(), userID)
	if err != nil {
		ctx.JSON(http.StatusNotFound, gin.H{"error": "User not found"})
		return
	}

	// Parse request body
	var updates map[string]interface{}
	if err := ctx.ShouldBindJSON(&updates); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request data"})
		return
	}

	// Update user
	if err := user.UpdateUser(ctx.Request.Context(), updates); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"message": err.Error()})
		return
	}

	ctx.Status(http.StatusOK)
}

// handleUpdatePassword updates the current user's password
func (c *UserController) handleUpdatePassword(ctx *gin.Context) {
	// Get userID from token
	userID, err := utils.GetUserIDFromToken(ctx.Request)
	if err != nil {
		ctx.JSON(http.StatusUnauthorized, gin.H{"error": "Unauthorized"})
		return
	}

	// Get user from database
	user, err := entities.GetUserByID(ctx.Request.Context(), userID)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	// Parse request body
	var passwordData struct {
		CurrentPassword string `json:"currentPassword"`
		NewPassword     string `json:"newPassword"`
	}
	if err := ctx.ShouldBindJSON(&passwordData); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request body"})
		return
	}

	// Update password
	if err := user.UpdatePassword(ctx.Request.Context(), passwordData.CurrentPassword, passwordData.NewPassword); err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	ctx.Status(http.StatusOK)
}

// handleStartVerification initiates the account verification process
func (c *UserController) handleStartVerification(ctx *gin.Context) {
	// Get userID from token
	userID, err := utils.GetUserIDFromToken(ctx.Request)
	if err != nil {
		ctx.JSON(http.StatusUnauthorized, gin.H{"error": "Unauthorized"})
		return
	}

	// Get user from database
	user, err := entities.GetUserByID(ctx.Request.Context(), userID)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	// Start verification process
	if err := user.StartVerification(ctx.Request.Context()); err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	// TODO: Send verification email with code
	// This should be implemented based on your email service

	ctx.Status(http.StatusOK)
}

// handleCloseAccount deletes the current user's account
func (c *UserController) handleCloseAccount(ctx *gin.Context) {
	// Get userID from token
	userID, err := utils.GetUserIDFromToken(ctx.Request)
	if err != nil {
		ctx.JSON(http.StatusUnauthorized, gin.H{"error": "Unauthorized"})
		return
	}

	// Get user from database
	user, err := entities.GetUserByID(ctx.Request.Context(), userID)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	// Close account
	if err := user.CloseAccount(ctx.Request.Context()); err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	ctx.Status(http.StatusOK)
}

// handleGetRelatedObjects returns the user's related objects (properties and leases)
func (c *UserController) handleGetRelatedObjects(ctx *gin.Context) {
	// Get userID from token
	userID, err := utils.GetUserIDFromToken(ctx.Request)
	if err != nil {
		ctx.JSON(http.StatusUnauthorized, gin.H{"error": "Unauthorized"})
		return
	}

	// Get user from database
	user, err := entities.GetUserByID(ctx.Request.Context(), userID)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	// Get properties and leases based on user's view type
	var propertyList []entities.PropertyList
	var leaseList []entities.LeaseList

	if user.ViewType == "tenant" {
		propertyList, _, err = entities.GetProperties(ctx.Request.Context(), 100, map[string]string{"userId": userID})
		if err != nil {
			ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			return
		}
		leaseList, _, err = entities.GetLeases(ctx.Request.Context(), 100, map[string]string{"userId": userID})
		if err != nil {
			ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			return
		}
	} else {
		propertyList, _, err = entities.GetProperties(ctx.Request.Context(), 100, map[string]string{"userId": userID})
		if err != nil {
			ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			return
		}
		leaseList, _, err = entities.GetLeases(ctx.Request.Context(), 100, map[string]string{"userId": userID})
		if err != nil {
			ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			return
		}
	}

	ctx.JSON(http.StatusOK, gin.H{
		"properties": propertyList,
		"leases":     leaseList,
	})
}

// handleSubmitProblem handles problem submissions
func (c *UserController) handleSubmitProblem(ctx *gin.Context) {
	// Get userID from token
	userID, err := utils.GetUserIDFromToken(ctx.Request)
	if err != nil {
		ctx.JSON(http.StatusUnauthorized, gin.H{"error": "Unauthorized"})
		return
	}

	// 获取用户信息以获取邮箱
	user, err := entities.GetUserByID(ctx.Request.Context(), userID)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get user info"})
		return
	}

	// 处理文件上传
	file, header, err := ctx.Request.FormFile("attachment")
	var fileInfo struct {
		Path string
		Name string
		Size int64
		Type string
	}

	if err == nil && file != nil {
		defer file.Close()

		// 加载上传配置
		uploadConfig := config.LoadUploadConfig()

		// 创建统计更新器
		statsColl := gomongo.Coll("tmp", "dir_stats")
		statsUpdater, err := goupload.NewStatsUpdater(uploadConfig.Site, uploadConfig.ProblemReports, statsColl)
		if err != nil {
			ctx.JSON(http.StatusInternalServerError, gin.H{"error": fmt.Sprintf("Failed to create stats updater: %v", err)})
			return
		}

		// 使用 goupload 上传文件
		result, err := goupload.Upload(
			ctx.Request.Context(),
			statsUpdater,                // statsUpdater
			uploadConfig.Site,           // site (从配置文件读取)
			uploadConfig.ProblemReports, // entryName (从配置文件读取)
			userID,                      // uid
			file,                        // reader
			header.Filename,             // originalFilename
			header.Size,                 // clientDeclaredSize
		)
		if err != nil {
			ctx.JSON(http.StatusInternalServerError, gin.H{"error": fmt.Sprintf("Failed to upload file: %v", err)})
			return
		}

		// 获取文件信息
		fileInfo = struct {
			Path string
			Name string
			Size int64
			Type string
		}{
			Path: result.Path,
			Name: header.Filename,
			Size: result.Size,
			Type: result.MimeType,
		}
	}

	// 获取表单数据
	subject := ctx.PostForm("subject")
	if subject == "" {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "Subject is required"})
		return
	}

	problemType := ctx.PostForm("problemType")
	// 映射前端的问题类型到后端的类型
	typeMapping := map[string]string{
		"bug":      "bug",
		"feature":  "feature",
		"feedback": "feedback",
		"other":    "other",
	}
	if mappedType, ok := typeMapping[problemType]; ok {
		problemType = mappedType
	} else {
		problemType = "other"
	}

	description := ctx.PostForm("description")
	if description == "" {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "Description is required"})
		return
	}

	relatedObject := ctx.PostForm("relatedObject")

	// 创建问题记录
	problem := &entities.Problem{
		ID:            utils.GenerateNanoID(),
		UserID:        userID,
		Type:          problemType,
		Subject:       subject,
		Description:   description,
		FilePath:      fileInfo.Path,
		FileName:      fileInfo.Name,
		GeneratedName: fileInfo.Name, // 对于问题报告，生成名和原始名相同
		FileSize:      fileInfo.Size,
		FileType:      fileInfo.Type,
		ContactEmail:  user.Email,
		Status:        "pending",
		CreatedAt:     time.Now().Format(time.RFC3339),
		UpdatedAt:     time.Now().Format(time.RFC3339),
	}

	// 如果有关联对象，设置相应的ID
	if relatedObject != "" {
		parts := strings.Split(relatedObject, "-")
		if len(parts) == 2 {
			switch parts[0] {
			case "lease":
				problem.LeaseID = parts[1]
			case "property":
				problem.PropertyID = parts[1]
			}
		}
	}

	// 保存到数据库
	if err := problem.Create(ctx.Request.Context()); err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	ctx.JSON(http.StatusCreated, gin.H{
		"success": true,
		"message": "report has been submitted",
		"problem": problem,
	})
}

// handleUpdateViewType updates the user's view type
func (c *UserController) handleUpdateViewType(ctx *gin.Context) {
	// Get userID from token
	userID, err := utils.GetUserIDFromToken(ctx.Request)
	if err != nil {
		ctx.JSON(http.StatusUnauthorized, gin.H{"error": "Unauthorized"})
		return
	}

	// Parse request body
	var requestBody struct {
		ViewType string `json:"viewType"`
	}
	if err := ctx.ShouldBindJSON(&requestBody); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request body"})
		return
	}

	// Get user from database
	user, err := entities.GetUserByID(ctx.Request.Context(), userID)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	// Update user's view type
	updates := map[string]interface{}{
		"viewType": requestBody.ViewType,
	}
	if err := user.UpdateUser(ctx.Request.Context(), updates); err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	ctx.Status(http.StatusOK)
}

// 查询当前用户近一年内的所有订单（ordinfo）
func (c *UserController) handleGetUserOrdInfo(ctx *gin.Context) {
	userID, err := utils.GetUserIDFromToken(ctx.Request)
	if err != nil {
		ctx.JSON(401, gin.H{"error": "Unauthorized"})
		return
	}
	year := 1
	if y := ctx.Query("year"); y != "" {
		if n, err := strconv.Atoi(y); err == nil && n > 0 {
			year = n
		}
	}
	startTime := time.Now().AddDate(-year, 0, 0)
	coll := gomongo.Coll("rr", "ordinfo")
	filter := bson.M{
		"uid": userID,
		"ts":  bson.M{"$gte": startTime},
	}
	cur, err := coll.Find(ctx.Request.Context(), filter)
	if err != nil {
		ctx.JSON(500, gin.H{"error": "DB error"})
		return
	}
	defer cur.Close(ctx.Request.Context())
	var result []map[string]interface{}
	for cur.Next(ctx.Request.Context()) {
		var ord map[string]interface{}
		if err := cur.Decode(&ord); err == nil {
			result = append(result, ord)
		}
	}
	ctx.JSON(200, result)
}

// 查询当前用户近一年内的所有账单（billhst）
func (c *UserController) handleGetUserBillHst(ctx *gin.Context) {
	userID, err := utils.GetUserIDFromToken(ctx.Request)
	if err != nil {
		ctx.JSON(401, gin.H{"error": "Unauthorized"})
		return
	}

	result, err := c.getUserBillHistory(ctx, userID)
	if err != nil {
		ctx.JSON(500, gin.H{"error": err.Error()})
		return
	}

	ctx.JSON(200, result)
}

// getUserBillHistory 获取用户账单历史的通用逻辑
func (c *UserController) getUserBillHistory(ctx *gin.Context, userID string) ([]map[string]interface{}, error) {
	year := 1
	if y := ctx.Query("year"); y != "" {
		if n, err := strconv.Atoi(y); err == nil && n > 0 {
			year = n
		}
	}
	startTime := time.Now().AddDate(-year, 0, 0)
	coll := gomongo.Coll("rr", "billhst")
	filter := bson.M{
		"uid": userID,
		"ts":  bson.M{"$gte": startTime},
	}
	cur, err := coll.Find(ctx.Request.Context(), filter)
	if err != nil {
		return nil, fmt.Errorf("DB error")
	}
	defer cur.Close(ctx.Request.Context())
	var result []map[string]interface{}
	for cur.Next(ctx.Request.Context()) {
		var bill map[string]interface{}
		if err := cur.Decode(&bill); err == nil {
			result = append(result, bill)
		}
	}
	return result, nil
}

// 重新发送账单邮件
func (c *UserController) handleResendInvoice(ctx *gin.Context) {
	var req struct {
		BillId string `json:"billId"`
	}
	if err := ctx.ShouldBindJSON(&req); err != nil || req.BillId == "" {
		ctx.JSON(400, gin.H{"error": "billId required"})
		return
	}
	billColl := gomongo.Coll("rr", "billhst")
	var bill map[string]interface{}
	if err := billColl.FindOne(ctx.Request.Context(), bson.M{"_id": req.BillId}).Decode(&bill); err != nil {
		ctx.JSON(404, gin.H{"error": "bill not found"})
		return
	}
	uid, _ := bill["uid"].(string)
	user, err := entities.GetUserByID(ctx.Request.Context(), uid)
	if err != nil {
		ctx.JSON(404, gin.H{"error": "user not found"})
		return
	}
	// 邮件内容参数
	email := user.Email
	name := user.Username
	renewalDate := ""
	golog.Debug("Bill timestamp debug", "type", fmt.Sprintf("%T", bill["ts"]), "value", bill["ts"])
	if ts, ok := bill["ts"].(time.Time); ok {
		renewalDate = ts.Format("Jan 2, 2006")
	} else if tsStr, ok := bill["ts"].(string); ok && tsStr != "" {
		if t, err := time.Parse(time.RFC3339, tsStr); err == nil {
			renewalDate = t.Format("Jan 2, 2006")
		}
	} else if tsPrimitive, ok := bill["ts"].(primitive.DateTime); ok {
		renewalDate = tsPrimitive.Time().Format("Jan 2, 2006")
	}
	if renewalDate == "" {
		renewalDate = "TEST_DATE"
	}
	rentReports := "$0 (0 additional reports)"
	total := "$0.00"
	if amt, ok := bill["amt"].(int64); ok {
		total = fmt.Sprintf("$%.2f", float64(amt)/100)
	}
	// 可根据 bill 结构补充 rentReports 字段
	err = utils.SendInvoiceEmail(email, name, renewalDate, rentReports, total)
	if err != nil {
		ctx.JSON(500, gin.H{"error": "email send failed"})
		return
	}
	ctx.JSON(200, gin.H{"message": "invoice email has been resent"})
}

// handleGetBillingAddress returns the user's billing address
// func (c *UserController) handleGetBillingAddress(ctx *gin.Context) {
// 	// Get userID from token
// 	userID, err := utils.GetUserIDFromToken(ctx.Request)
// 	if err != nil {
// 		ctx.JSON(http.StatusUnauthorized, gin.H{"error": "Unauthorized"})
// 		return
// 	}

// 	// Get user from database
// 	user, err := entities.GetUserByID(ctx.Request.Context(), userID)
// 	if err != nil {
// 		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
// 		return
// 	}

// 	// Return billing address
// 	ctx.JSON(http.StatusOK, gin.H{
// 		"address": user.Address,
// 	})
// }

// handleGetPaymentInfo 获取用户的支付信息
func (c *UserController) handleGetPaymentInfo(ctx *gin.Context) {
	// 从token获取用户ID
	userID, err := utils.GetUserIDFromToken(ctx.Request)
	if err != nil {
		ctx.JSON(http.StatusUnauthorized, gin.H{"error": "unauthorized"})
		return
	}

	// 从payinfo表获取最新的支付信息
	payColl := gomongo.Coll("rr", "payinfo")
	var payinfo map[string]interface{}
	err = payColl.FindOne(
		ctx.Request.Context(),
		bson.M{"uid": userID},
		options.FindOne().SetSort(bson.M{"ts": -1}), // 按时间戳倒序，获取最新的记录
	).Decode(&payinfo)

	if err != nil {
		if err == mongo.ErrNoDocuments {
			ctx.JSON(http.StatusOK, gin.H{
				"billingAddress": nil,
				"paymentMethod":  nil,
			})
			return
		}
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": "failed to get payment info"})
		return
	}

	// 构造返回数据
	response := gin.H{
		"billingAddress": payinfo["billingAddress"],
		"paymentMethod": gin.H{
			"cardBrand": payinfo["crdBrd"],
			"lastFour":  payinfo["crdL4"],
			"expMonth":  payinfo["expMn"],
			"expYear":   payinfo["expYr"],
		},
	}

	ctx.JSON(http.StatusOK, response)
}

func (c *UserController) handleGetUserBillHstAdmin(ctx *gin.Context) {
	userID := ctx.Param("id")
	if userID == "" {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "User ID is required"})
		return
	}

	result, err := c.getUserBillHistory(ctx, userID)
	if err != nil {
		ctx.JSON(500, gin.H{"error": err.Error()})
		return
	}

	ctx.JSON(200, result)
}

// getFileOriginalName 从GridFS metadata中获取原始文件名
// func (c *UserController) getFileOriginalName(fileID string) (string, error) {
// 	db := mongoClient.Database("rr")
// 	bucket, err := gridfs.NewBucket(db)
// 	if err != nil {
// 		return "", fmt.Errorf("failed to create GridFS bucket: %v", err)
// 	}

// 	objectID, err := primitive.ObjectIDFromHex(fileID)
// 	if err != nil {
// 		return "", fmt.Errorf("invalid file ID: %v", err)
// 	}

// 	// 查找文件
// 	filter := bson.M{"_id": objectID}
// 	var fileDoc bson.M
// 	err = bucket.GetFilesCollection().FindOne(context.Background(), filter).Decode(&fileDoc)
// 	if err != nil {
// 		return "", fmt.Errorf("file not found: %v", err)
// 	}

// 	// 从metadata中获取原始文件名
// 	if metadata, ok := fileDoc["metadata"].(bson.M); ok {
// 		if originalName, ok := metadata["originalName"].(string); ok {
// 			return originalName, nil
// 		}
// 	}

// 	// 如果没有找到原始文件名，返回GridFS文件名
// 	if filename, ok := fileDoc["filename"].(string); ok {
// 		return filename, nil
// 	}

// 	return "unknown_file", nil
// }
