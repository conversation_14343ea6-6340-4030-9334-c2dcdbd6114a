package entities

import (
	"context"
	"fmt"
	"rent_report/utils"
	"time"

	"github.com/real-rm/gomongo"
)

type Invitation struct {
	ID              string    `json:"id" bson:"_id"`
	Code            string    `json:"code" bson:"code"`
	SenderId        string    `json:"senderId" bson:"senderId"`
	SenderName      string    `json:"senderName" bson:"senderName"`
	SenderEmail     string    `json:"senderEmail" bson:"senderEmail"`
	ReceiverEmail   string    `json:"receiverEmail" bson:"receiverEmail"`
	ReceiverName    string    `json:"receiverName" bson:"receiverName"`
	ReceiverPhone   string    `json:"receiverPhone" bson:"receiverPhone"`
	PropertyName    string    `json:"propertyName" bson:"propertyName"`
	PropertyAddress string    `json:"propertyAddress" bson:"propertyAddress"`
	FromDate        string    `json:"fromDate" bson:"fromDate"`
	ToDate          string    `json:"toDate" bson:"toDate"`
	RentAmount      float64   `json:"rentAmount" bson:"rentAmount"`
	Status          string    `json:"status" bson:"status"`
	Type            string    `json:"type" bson:"type"`
	OrgId           string    `json:"orgId" bson:"orgId"`
	CreatedAt       time.Time `json:"createdAt" bson:"createdAt"`
	ExpiresAt       time.Time `json:"expiresAt" bson:"expiresAt"`
	LeaseId         string    `json:"leaseId" bson:"leaseId"`
}

func (inv *Invitation) Create(ctx context.Context) error {
	coll := gomongo.Coll("rr", "invitations")
	if coll == nil {
		return fmt.Errorf("invitations collection not initialized")
	}
	if inv.ID == "" {
		inv.ID = utils.GenerateNanoID()
	}
	if inv.Code == "" {
		inv.Code = utils.GenerateNanoID()
	}
	inv.CreatedAt = time.Now()
	if inv.ExpiresAt.IsZero() {
		inv.ExpiresAt = inv.CreatedAt.Add(24 * time.Hour)
	}
	if inv.Status == "" {
		inv.Status = "pending"
	}
	_, err := coll.InsertOne(ctx, inv)
	return err
}
