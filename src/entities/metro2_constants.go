package entities

import (
	"fmt"
	"time"
)

// Metro2ReporterInfo contains the reporter's information for Metro2 reporting
type Metro2ReporterInfo struct {
	Name            string
	Address         string
	TelephoneNumber int64
}

// Metro2BaseSegment contains constants for the base segment of Metro2 reporting
type Metro2BaseSegment struct {
	RecordDescriptorWord   int
	ProcessingIndicator    int
	PortfolioType          string
	AccountType            string
	TermsDuration          string
	AccountStatus          string
	EcoaCode               string
	CountryCode            string
	AddressIndicator       string
	CreditorClassification int
}

// Metro2Constants contains all the constant values used in Metro2 reporting
type Metro2Constants struct {
	ReporterInfo         Metro2ReporterInfo
	IdentificationCode   string
	RecordDescriptorWord int
	BaseSegment          Metro2BaseSegment
	Metro2ReportFileName string
}

// DefaultMetro2Constants provides the default values for Metro2 reporting
var DefaultMetro2Constants = Metro2Constants{
	// TODO: config ReporterInfo and IdentificationCode
	ReporterInfo: Metro2ReporterInfo{
		Name:            "REALMASTER",
		Address:         "50 Acadia Ave #130, Markham, Ontario, Canada L3R 5Z2",
		TelephoneNumber: **********,
	},
	IdentificationCode:   "467RE01193",
	RecordDescriptorWord: 626,
	BaseSegment: Metro2BaseSegment{
		RecordDescriptorWord:   626,
		ProcessingIndicator:    1,
		PortfolioType:          "O",
		AccountType:            "29",
		TermsDuration:          "001",
		AccountStatus:          "11",
		EcoaCode:               "1",
		CountryCode:            "CA",
		AddressIndicator:       "Y",
		CreditorClassification: 9,
	},
	Metro2ReportFileName: "", // Will be generated dynamically with timestamp
}

// GenerateMetro2FileName generates a Metro2 filename with the format: RM-Metro2-YYYYMMDD-HHMMSS.txt
func GenerateMetro2FileName() string {
	now := time.Now()
	return fmt.Sprintf("RM-Metro2-%s.txt", now.Format("********-150405"))
}
