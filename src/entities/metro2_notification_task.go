package entities

import (
	"context"
	"fmt"
	"time"

	"github.com/real-rm/gomongo"
	"go.mongodb.org/mongo-driver/bson"
)

// Metro2NotificationTask Metro2上报通知延迟任务
type Metro2NotificationTask struct {
	ID                 string     `bson:"_id" json:"id"`
	UserID             string     `bson:"usrId" json:"userId"`
	OrganizationID     string     `bson:"orgId,omitempty" json:"organizationId,omitempty"`
	Metro2GenerationID string     `bson:"metro2GenerationId" json:"metro2GenerationId"`         // Metro2GenerationLog的ID
	ReportMonth        string     `bson:"reportMonth" json:"reportMonth"`                       // YYYY-MM format
	ScheduledTime      time.Time  `bson:"scheduledTime" json:"scheduledTime"`                   // 计划执行时间
	CreatedAt          time.Time  `bson:"createdAt" json:"createdAt"`                           // 创建时间
	ExecutedAt         *time.Time `bson:"executedAt,omitempty" json:"executedAt,omitempty"`     // 执行时间
	Completed          bool       `bson:"completed" json:"completed"`                           // 是否已完成
	EmailsSent         int        `bson:"emailsSent" json:"emailsSent"`                         // 发送的邮件数量
	ErrorMessage       string     `bson:"errorMessage,omitempty" json:"errorMessage,omitempty"` // 错误信息
}

// Create 创建Metro2通知任务
func (task *Metro2NotificationTask) Create(ctx context.Context) error {
	coll := gomongo.Coll("rr", "metro2_notification_tasks")
	if coll == nil {
		return fmt.Errorf("metro2_notification_tasks collection not initialized")
	}

	// 获取资源范围
	scope, err := GetResourceScope(ctx, task.UserID)
	if err != nil {
		return fmt.Errorf("failed to get resource scope: %v", err)
	}

	task.OrganizationID = scope.OrganizationID

	// 验证必填字段
	if task.ID == "" || task.Metro2GenerationID == "" || task.ReportMonth == "" {
		return fmt.Errorf("id, metro2GenerationId and reportMonth are required")
	}

	_, err = coll.InsertOne(ctx, task)
	if err != nil {
		return fmt.Errorf("failed to insert metro2 notification task: %v", err)
	}

	return nil
}

// GetPendingTasks 获取待执行的Metro2通知任务
func GetPendingMetro2NotificationTasks(ctx context.Context) ([]Metro2NotificationTask, error) {
	coll := gomongo.Coll("rr", "metro2_notification_tasks")
	if coll == nil {
		return nil, fmt.Errorf("metro2_notification_tasks collection not initialized")
	}

	now := time.Now()
	filter := bson.M{
		"completed":     false,
		"scheduledTime": bson.M{"$lte": now},
	}

	cursor, err := coll.Find(ctx, filter)
	if err != nil {
		return nil, err
	}
	defer cursor.Close(ctx)

	var tasks []Metro2NotificationTask
	if err = cursor.All(ctx, &tasks); err != nil {
		return nil, err
	}

	return tasks, nil
}

// MarkCompleted 标记任务为已完成
func (task *Metro2NotificationTask) MarkCompleted(ctx context.Context, emailsSent int, errorMessage string) error {
	coll := gomongo.Coll("rr", "metro2_notification_tasks")
	if coll == nil {
		return fmt.Errorf("metro2_notification_tasks collection not initialized")
	}

	now := time.Now()
	update := bson.M{
		"$set": bson.M{
			"completed":    true,
			"executedAt":   now,
			"emailsSent":   emailsSent,
			"errorMessage": errorMessage,
		},
	}

	filter := bson.M{"_id": task.ID}
	_, err := coll.UpdateOne(ctx, filter, update)
	return err
}

// CleanupOldTasks 清理旧的Metro2通知任务记录
func CleanupOldMetro2NotificationTasks(ctx context.Context, daysOld int) error {
	coll := gomongo.Coll("rr", "metro2_notification_tasks")
	if coll == nil {
		return fmt.Errorf("metro2_notification_tasks collection not initialized")
	}

	cutoffTime := time.Now().AddDate(0, 0, -daysOld)
	filter := bson.M{
		"completed": true,
		"createdAt": bson.M{"$lt": cutoffTime},
	}

	result, err := coll.DeleteMany(ctx, filter)
	if err != nil {
		return err
	}

	if result.DeletedCount > 0 {
		fmt.Printf("Cleaned up %d old Metro2 notification tasks\n", result.DeletedCount)
	}

	return nil
}

// GetMetro2NotificationTaskByGenerationID 根据Metro2生成ID获取通知任务
func GetMetro2NotificationTaskByGenerationID(ctx context.Context, generationID string) (*Metro2NotificationTask, error) {
	coll := gomongo.Coll("rr", "metro2_notification_tasks")
	if coll == nil {
		return nil, fmt.Errorf("metro2_notification_tasks collection not initialized")
	}

	var task Metro2NotificationTask
	filter := bson.M{"metro2GenerationId": generationID}

	err := coll.FindOne(ctx, filter).Decode(&task)
	if err != nil {
		return nil, err
	}

	return &task, nil
}
