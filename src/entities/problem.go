package entities

import (
	"context"
	"fmt"
	"time"

	"github.com/real-rm/gomongo"
	"go.mongodb.org/mongo-driver/bson"
)

// Problem 表示用户提交的问题报告
type Problem struct {
	ID             string `json:"id" bson:"_id"`
	UserID         string `json:"-" bson:"usrId"`
	OrganizationID string `json:"-" bson:"orgId,omitempty"`
	Type           string `json:"type" bson:"type"` // bug, feature, feedback, other
	Subject        string `json:"subject" bson:"subject"`
	Description    string `json:"description" bson:"desc"`
	// 文件相关字段
	FilePath      string `json:"filePath,omitempty" bson:"filePath,omitempty"`           // goupload返回的文件路径
	FileName      string `json:"fileName,omitempty" bson:"fileName,omitempty"`           // 原始文件名
	GeneratedName string `json:"generatedName,omitempty" bson:"generatedName,omitempty"` // goupload生成的文件名
	FileSize      int64  `json:"fileSize,omitempty" bson:"fileSize,omitempty"`           // 文件大小
	FileType      string `json:"fileType,omitempty" bson:"fileType,omitempty"`           // 文件类型/MIME类型
	ContactEmail  string `json:"contactEmail" bson:"contactEmail"`
	Status        string `json:"status" bson:"stat"` // pending, in_progress, resolved, closed
	LeaseID       string `json:"leaseId,omitempty" bson:"leaseId,omitempty"`
	PropertyID    string `json:"propertyId,omitempty" bson:"propertyId,omitempty"`
	CreatedAt     string `json:"createdAt" bson:"createdAt"`
	UpdatedAt     string `json:"updatedAt" bson:"updatedAt"`
}

// Create 创建新的问题报告
func (p *Problem) Create(ctx context.Context) error {
	var problemColl = gomongo.Coll("rr", "problems")
	if problemColl == nil {
		return fmt.Errorf("problems collection not initialized")
	}

	scope, err := GetResourceScope(ctx, p.UserID)
	if err != nil {
		return fmt.Errorf("failed to get resource scope: %v", err)
	}

	p.OrganizationID = scope.OrganizationID

	// 验证必填字段
	if p.ID == "" {
		return fmt.Errorf("id is required")
	}

	if p.Subject == "" {
		return fmt.Errorf("subject is required")
	}

	// 验证问题类型
	validTypes := map[string]bool{
		"bug":      true,
		"feature":  true,
		"feedback": true,
		"other":    true,
	}
	if !validTypes[p.Type] {
		return fmt.Errorf("invalid problem type: %s", p.Type)
	}

	// 验证状态
	validStatus := map[string]bool{
		"pending":     true,
		"in_progress": true,
		"resolved":    true,
		"closed":      true,
	}
	if !validStatus[p.Status] {
		return fmt.Errorf("invalid status")
	}

	// 设置创建和更新时间
	if p.CreatedAt == "" {
		p.CreatedAt = time.Now().Format(time.RFC3339)
	}
	p.UpdatedAt = time.Now().Format(time.RFC3339)

	_, err = problemColl.InsertOne(ctx, p)
	if err != nil {
		return fmt.Errorf("failed to insert problem: %v", err)
	}

	return nil
}

// GetProblems 获取用户的问题报告列表
func GetProblems(ctx context.Context, userID string, limit int) ([]Problem, error) {
	var problemColl = gomongo.Coll("rr", "problems")
	if problemColl == nil {
		return nil, fmt.Errorf("problems collection not initialized")
	}

	scope, err := GetResourceScope(ctx, userID)
	if err != nil {
		return nil, fmt.Errorf("failed to get resource scope: %v", err)
	}

	filter := scope.ToFilter()

	findOptions := gomongo.QueryOptions{
		Limit: int64(limit),
		Sort:  bson.D{{Key: "createdAt", Value: -1}},
	}

	problems := make([]Problem, 0)
	result, err := problemColl.FindToArray(ctx, filter, findOptions)
	if err != nil {
		return nil, fmt.Errorf("failed to execute find query: %v", err)
	}

	for _, item := range result {
		var problem Problem
		data, err := bson.Marshal(item)
		if err != nil {
			return nil, fmt.Errorf("failed to marshal problem data: %v", err)
		}

		if err := bson.Unmarshal(data, &problem); err != nil {
			return nil, fmt.Errorf("failed to unmarshal problem data: %v", err)
		}

		problems = append(problems, problem)
	}

	return problems, nil
}

// Update 更新问题报告
func (p *Problem) Update(ctx context.Context) error {
	var problemColl = gomongo.Coll("rr", "problems")
	if problemColl == nil {
		return fmt.Errorf("problems collection not initialized")
	}

	scope, err := GetResourceScope(ctx, p.UserID)
	if err != nil {
		return fmt.Errorf("failed to get resource scope: %v", err)
	}

	filter := scope.ToFilter()
	filter["_id"] = p.ID

	// 验证状态
	validStatus := map[string]bool{
		"pending":     true,
		"in_progress": true,
		"resolved":    true,
		"closed":      true,
	}
	if !validStatus[p.Status] {
		return fmt.Errorf("invalid status")
	}

	// 更新更新时间
	p.UpdatedAt = time.Now().Format(time.RFC3339)

	updateDoc, err := gomongo.ToBSONDoc(p)
	if err != nil {
		return fmt.Errorf("failed to create update document: %v", err)
	}

	result, err := problemColl.UpdateOne(ctx, filter, bson.M{"$set": updateDoc})
	if err != nil {
		return fmt.Errorf("failed to update problem: %v", err)
	}

	if result.MatchedCount == 0 {
		return fmt.Errorf("problem not found")
	}

	return nil
}
