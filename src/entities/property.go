package entities

import (
	"context"
	"fmt"
	"rent_report/config"
	"time"

	"github.com/real-rm/golog"
	"github.com/real-rm/gomongo"
	"github.com/real-rm/goupload"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
)

// PropertyDocument 表示房产文档
type PropertyDocument struct {
	ID            string     `json:"id" bson:"id"`                                   // 文档唯一标识
	FilePath      string     `json:"filePath" bson:"filePath"`                       // goupload返回的文件路径
	FileName      string     `json:"fileName" bson:"fileName"`                       // 原始文件名
	GeneratedName string     `json:"generatedName" bson:"generatedName"`             // goupload生成的文件名
	FileSize      int64      `json:"fileSize" bson:"fileSize"`                       // 文件大小
	FileType      string     `json:"fileType" bson:"fileType"`                       // 文件类型/MIME类型
	UploadedAt    time.Time  `json:"uploadedAt" bson:"uploadedAt"`                   // 上传时间
	UploadedBy    string     `json:"uploadedBy" bson:"uploadedBy"`                   // 上传用户ID
	Status        string     `json:"status" bson:"status"`                           // 文件状态: active, deleted, final_deleted
	DeletedAt     *time.Time `json:"deletedAt,omitempty" bson:"deletedAt,omitempty"` // 删除时间
	IsProtected   bool       `json:"isProtected" bson:"isProtected"`                 // 是否受保护，不可删除（如用于Metro2报告）
}

type Address struct {
	Street  string `json:"street" bson:"street"`
	City    string `json:"city" bson:"city"`
	Prov    string `json:"prov" bson:"prov"`
	Country string `json:"country" bson:"country"`
	ZipCode string `json:"zipCode" bson:"zip"`
	Unit    string `json:"unit" bson:"unit"`
}

type Room struct {
	ID           string  `json:"id" bson:"_id"`
	Name         string  `json:"name" bson:"nm"`
	Type         string  `json:"type" bson:"tp"`
	Status       string  `json:"status" bson:"stat"`
	Notes        string  `json:"notes" bson:"notes"`
	OwingBalance float64 `json:"owingBalance" bson:"-"` // 不存储在数据库中，仅用于API响应
	// CurrentLease *struct {
	// 	ID         string `json:"id" bson:"_id"`
	// 	TenantName string `json:"tenantName" bson:"tenantName"`
	// } `json:"currentLease,omitempty" bson:"currentLease,omitempty"`
}

type Property struct {
	ID             string  `json:"id" bson:"_id"`
	Name           string  `json:"name" bson:"nm"`
	Address        Address `json:"address" bson:"addr"`
	PropertyType   string  `json:"propertyType" bson:"propTp"`
	TotalRooms     int     `json:"totalRooms" bson:"totRms"`
	VacantRooms    int     `json:"vacantRooms" bson:"vacRms"`
	Status         string  `json:"status" bson:"stat"`
	Notes          string  `json:"notes" bson:"notes"`
	UserID         string  `json:"userId" bson:"usrId"`
	OrganizationID string  `json:"-" bson:"orgId,omitempty"`
	Rooms          []Room  `json:"rooms" bson:"rooms"`
	InvId          string  `json:"invId" bson:"invId"`
	// 文档字段
	Documents []PropertyDocument `json:"documents" bson:"documents"`
}

type PropertyList struct {
	ID           string             `json:"id" bson:"_id"`
	Name         string             `json:"name" bson:"nm"`
	TotalRooms   int                `json:"totalRooms" bson:"totRms"`
	VacantRooms  int                `json:"vacantRooms" bson:"vacRms"`
	UserID       string             `json:"-" bson:"usrId"`
	OwingBalance float64            `json:"owingBalance" bson:"owingBal"`
	Address      Address            `json:"address" bson:"addr"`
	Status       string             `json:"status" bson:"stat"`
	Ts           primitive.DateTime `json:"_ts" bson:"_ts"`
}

func (property *Property) Create(ctx context.Context) error {
	var propColl = gomongo.Coll("rr", "properties")
	if propColl == nil {
		return fmt.Errorf("properties collection not initialized")
	}

	// Get resource scope for access control
	scope, err := GetResourceScope(ctx, property.UserID)
	if err != nil {
		return fmt.Errorf("failed to get resource scope: %v", err)
	}

	// Set organization based on scope
	property.OrganizationID = scope.OrganizationID

	// 如果status为空，自动赋值为active
	if property.Status == "" {
		property.Status = "active"
	}

	// Validate required fields
	if property.ID == "" {
		return fmt.Errorf("id is required")
	}

	// Validate property type if provided
	if property.PropertyType != "" {
		validTypes := map[string]bool{
			"apartment":  true,
			"house":      true,
			"condo":      true,
			"commercial": true,
		}
		if !validTypes[property.PropertyType] {
			return fmt.Errorf("invalid property type")
		}
	}

	// Validate status if provided
	if property.Status != "" {
		validStatus := map[string]bool{
			"active":      true,
			"inactive":    true,
			"maintenance": true,
			"archived":    true,
			"deleted":     true,
		}
		if !validStatus[property.Status] {
			return fmt.Errorf("invalid status")
		}
	}

	// Ensure TotalRooms is at least 1 if provided
	if property.TotalRooms != 0 && property.TotalRooms < 1 {
		return fmt.Errorf("totalRooms must be at least 1")
	}

	// Validate VacantRooms is not greater than TotalRooms
	if property.TotalRooms != 0 && property.VacantRooms > property.TotalRooms {
		return fmt.Errorf("vacantRooms cannot be greater than totalRooms")
	}

	property.Rooms = []Room{}

	_, err = propColl.InsertOne(ctx, property)
	if err != nil {
		return fmt.Errorf("failed to insert property: %v", err)
	}

	return nil
}

func GetProperties(ctx context.Context, limit int, filters map[string]string) ([]PropertyList, int64, error) {
	var propColl = gomongo.Coll("rr", "properties")
	if propColl == nil {
		return nil, 0, fmt.Errorf("properties collection not initialized")
	}

	scope, err := GetResourceScope(ctx, filters["userId"])
	if err != nil {
		return nil, 0, fmt.Errorf("failed to get resource scope: %v", err)
	}

	// Build query filter
	filter := scope.ToFilter()
	userId := filters["userId"]
	if userId != "" {
		delete(filter, "usrId") // 避免与$or冲突
		// 租客通过lease关联到property，不是直接存储在rooms中
		// 这里只查询房东拥有的properties
		filter["usrId"] = userId
	}

	if name := filters["name"]; name != "" {
		filter["nm"] = bson.M{"$regex": name, "$options": "i"}
	}
	if propertyId := filters["propertyId"]; propertyId != "" {
		filter["_id"] = propertyId
	}
	if status := filters["status"]; status != "" {
		filter["status"] = status
	}

	// Get total count
	total, err := propColl.CountDocuments(ctx, filter)
	if err != nil {
		return nil, 0, fmt.Errorf("failed to get total count: %v", err)
	}

	// Use lib's QueryOptions with projection for PropertyList fields
	findOptions := gomongo.QueryOptions{
		Limit: int64(limit),
		Sort:  bson.D{{Key: "nm", Value: 1}},
		Projection: bson.M{
			"_id":    1,
			"nm":     1,
			"totRms": 1,
			"vacRms": 1,
			"addr":   1,
			"stat":   1,
			"_ts":    1,
		},
	}

	// Initialize properties as empty slice instead of nil
	properties := make([]PropertyList, 0)

	result, err := propColl.FindToArray(ctx, filter, findOptions)
	if err != nil {
		return nil, 0, fmt.Errorf("failed to execute find query: %v", err)
	}

	// Get lease collection for owing balance calculation
	leaseColl := gomongo.Coll("rr", "leases")
	if leaseColl == nil {
		return nil, 0, fmt.Errorf("leases collection not initialized")
	}

	for _, item := range result {
		var prop PropertyList
		data, err := bson.Marshal(item)
		if err != nil {
			return nil, 0, fmt.Errorf("failed to marshal property data: %v", err)
		}

		if err := bson.Unmarshal(data, &prop); err != nil {
			return nil, 0, fmt.Errorf("failed to unmarshal property data: %v", err)
		}

		// Calculate owing balance from active leases for this property
		owingBalance, err := calculatePropertyOwingBalance(ctx, leaseColl, prop.ID, scope)
		if err != nil {
			// Log error but don't fail the entire request
			golog.Warn("Failed to calculate owing balance for property", "propertyID", prop.ID, "error", err)
			owingBalance = 0.0
		}
		prop.OwingBalance = owingBalance

		properties = append(properties, prop)
	}

	return properties, total, nil
}

func GetProperty(ctx context.Context, id string, userID string) (*Property, error) {
	var propColl = gomongo.Coll("rr", "properties")
	if propColl == nil {
		return nil, fmt.Errorf("properties collection not initialized")
	}

	scope, err := GetResourceScope(ctx, userID)
	if err != nil {
		return nil, fmt.Errorf("failed to get resource scope: %v", err)
	}

	filter := scope.ToFilter()
	filter["_id"] = id

	var property Property
	err = propColl.FindOne(ctx, filter).Decode(&property)
	if err != nil {
		// 如果按scope查不到，尝试只按_id查（用于租客/房东只要有lease关联即可查地址）
		err = propColl.FindOne(ctx, bson.M{"_id": id}).Decode(&property)
		if err != nil {
			if err == mongo.ErrNoDocuments {
				return nil, fmt.Errorf("property not found")
			}
			return nil, fmt.Errorf("failed to get property: %v", err)
		}
		// 检查当前用户是否有lease关联该property
		leaseColl := gomongo.Coll("rr", "leases")
		if leaseColl == nil {
			return nil, fmt.Errorf("leases collection not initialized")
		}
		leaseCount, err := leaseColl.CountDocuments(ctx, bson.M{
			"propId": id,
			"$or": []bson.M{
				{"usrId": userID},
				{"ctnts.tenantId": userID},
				{"ptnts.tenantId": userID},
			},
		})
		if err != nil || leaseCount == 0 {
			return nil, fmt.Errorf("property not found")
		}
		// 只返回property的公开信息（如地址、name等），不返回UserID等敏感字段
		property.UserID = ""
		property.OrganizationID = ""
		// 其余字段照常
		return &property, nil
	}

	golog.Debug("Found property", "propertyID", property.ID, "name", property.Name, "roomCount", len(property.Rooms))

	// 获取每个房间的当前lease信息
	var leaseColl = gomongo.Coll("rr", "leases")
	if leaseColl == nil {
		return nil, fmt.Errorf("leases collection not initialized")
	}

	for i, room := range property.Rooms {
		golog.Debug("Processing room", "roomName", room.Name, "status", room.Status, "propertyID", property.ID)

		// 查找该房间的活跃lease
		leaseFilter := bson.M{
			"propId": property.ID,
			"roomId": room.ID,
			"status": "active",
			"usrId":  userID,
		}

		golog.Debug("Searching for lease", "filter", leaseFilter)

		var lease struct {
			ID           string  `bson:"_id"`
			TenantID     string  `bson:"tenantId"`
			OwingBalance float64 `bson:"owingBal"`
		}

		err = leaseColl.FindOne(ctx, leaseFilter).Decode(&lease)
		if err != nil {
			golog.Debug("No lease found for room", "roomName", room.Name, "error", err)
			continue
		}

		golog.Debug("Found lease for room", "roomName", room.Name, "leaseID", lease.ID, "tenantID", lease.TenantID, "owingBalance", lease.OwingBalance)

		// 如果找到活跃lease，获取tenant信息
		var tenantColl = gomongo.Coll("rr", "tenants")
		if tenantColl != nil {
			var tenant struct {
				FirstName string `bson:"firstNm"`
				LastName  string `bson:"lastNm"`
			}
			tenantFilter := bson.M{
				"_id":   lease.TenantID,
				"usrId": userID,
			}

			golog.Debug("Searching for tenant", "filter", tenantFilter)

			err = tenantColl.FindOne(ctx, tenantFilter).Decode(&tenant)
			if err != nil {
				golog.Debug("Failed to find tenant for room", "roomName", room.Name, "error", err)
				continue
			}

			golog.Debug("Found tenant for room", "roomName", room.Name, "firstName", tenant.FirstName, "lastName", tenant.LastName)

			// 设置当前lease信息和owing balance
			// property.Rooms[i].CurrentLease = &struct {
			// 	ID         string `json:"id" bson:"_id"`
			// 	TenantName string `json:"tenantName" bson:"tenantName"`
			// }{
			// 	ID:         lease.ID,
			// 	TenantName: tenant.FirstName + " " + tenant.LastName,
			// }

			// 设置owing balance
			property.Rooms[i].OwingBalance = lease.OwingBalance

			golog.Debug("Updated room with lease info", "roomName", room.Name, "owingBalance", lease.OwingBalance)
		}
	}

	return &property, nil
}

func (property *Property) Update(ctx context.Context) error {
	var propColl = gomongo.Coll("rr", "properties")
	if propColl == nil {
		return fmt.Errorf("properties collection not initialized")
	}

	scope, err := GetResourceScope(ctx, property.UserID)
	if err != nil {
		return fmt.Errorf("failed to get resource scope: %v", err)
	}

	filter := scope.ToFilter()
	filter["_id"] = property.ID

	// Set organization based on scope
	property.OrganizationID = scope.OrganizationID

	// Validate required fields
	if property.ID == "" || property.UserID == "" {
		return fmt.Errorf("id and userId are required")
	}

	// Validate property type if provided
	if property.PropertyType != "" {
		validTypes := map[string]bool{
			"apartment":  true,
			"house":      true,
			"condo":      true,
			"commercial": true,
		}
		if !validTypes[property.PropertyType] {
			return fmt.Errorf("invalid property type")
		}
	}

	// Validate status if provided
	if property.Status != "" {
		validStatus := map[string]bool{
			"active":      true,
			"inactive":    true,
			"maintenance": true,
			"archived":    true,
			"deleted":     true,
		}
		if !validStatus[property.Status] {
			return fmt.Errorf("invalid status")
		}
	}

	// Ensure TotalRooms is at least 1 if provided
	if property.TotalRooms != 0 && property.TotalRooms < 1 {
		return fmt.Errorf("totalRooms must be at least 1")
	}

	// Validate VacantRooms is not greater than TotalRooms
	if property.TotalRooms != 0 && property.VacantRooms > property.TotalRooms {
		return fmt.Errorf("vacantRooms cannot be greater than totalRooms")
	}

	updateDoc, err := gomongo.ToBSONDoc(property)
	if err != nil {
		return fmt.Errorf("failed to create update document: %v", err)
	}

	result, err := propColl.UpdateOne(ctx, filter, bson.M{"$set": updateDoc})
	if err != nil {
		return fmt.Errorf("failed to update property: %v", err)
	}

	if result.MatchedCount == 0 {
		return fmt.Errorf("property not found")
	}

	return nil
}

// CheckPropertyDeletionConstraints 检查property是否可以被删除
// 返回错误信息如果有关联的room
func CheckPropertyDeletionConstraints(ctx context.Context, propertyID string, userID string) error {
	// 检查是否有关联的rooms
	property, err := GetProperty(ctx, propertyID, userID)
	if err != nil {
		return fmt.Errorf("failed to get property: %v", err)
	}

	// 检查是否有rooms
	if len(property.Rooms) > 0 {
		return fmt.Errorf("cannot delete property: property has %d room(s). Please delete all rooms first", len(property.Rooms))
	}

	return nil
}

// deletePropertyFiles 删除property的所有文件（实际文件和数据库记录）
func deletePropertyFiles(ctx context.Context, property *Property) error {
	if len(property.Documents) == 0 {
		return nil // 没有文件需要删除
	}

	// 加载上传配置
	uploadConfig := config.LoadUploadConfig()

	// 创建统计更新器
	statsColl := gomongo.Coll("tmp", "dir_stats")
	statsUpdater, err := goupload.NewStatsUpdater(uploadConfig.Site, uploadConfig.PropertyDocuments, statsColl)
	if err != nil {
		return fmt.Errorf("failed to create stats updater: %v", err)
	}

	// 删除所有文件
	var failedFiles []string
	for _, doc := range property.Documents {
		if doc.FilePath == "" {
			golog.Warn("Property document has empty file path", "propertyId", property.ID, "documentId", doc.ID)
			continue
		}

		// 调用goupload删除文件
		result, err := goupload.Delete(ctx, statsUpdater, uploadConfig.Site, uploadConfig.PropertyDocuments, doc.FilePath)
		if err != nil {
			golog.Error("Failed to delete property file", "error", err, "propertyId", property.ID, "filePath", doc.FilePath)
			failedFiles = append(failedFiles, doc.FilePath)
			continue
		}

		// 记录删除结果
		if result != nil && len(result.FailedPaths) > 0 {
			golog.Warn("Some files failed to delete", "propertyId", property.ID, "failedCount", len(result.FailedPaths))
			for _, fp := range result.FailedPaths {
				failedFiles = append(failedFiles, fp.Path)
			}
		} else {
			golog.Info("Successfully deleted property file", "propertyId", property.ID, "filePath", doc.FilePath)
		}
	}

	// 如果有文件删除失败，返回错误
	if len(failedFiles) > 0 {
		return fmt.Errorf("failed to delete %d file(s): %v", len(failedFiles), failedFiles)
	}

	return nil
}

func DeleteProperty(ctx context.Context, id string, userID string) error {
	// 首先检查删除约束
	if err := CheckPropertyDeletionConstraints(ctx, id, userID); err != nil {
		return err
	}

	// 获取property信息（包含documents）
	property, err := GetProperty(ctx, id, userID)
	if err != nil {
		return fmt.Errorf("failed to get property: %v", err)
	}

	// 先删除所有文件
	if err := deletePropertyFiles(ctx, property); err != nil {
		golog.Error("Failed to delete property files", "error", err, "propertyId", id)
		return fmt.Errorf("failed to delete property files: %v", err)
	}

	// 然后删除property记录
	var propColl = gomongo.Coll("rr", "properties")
	if propColl == nil {
		return fmt.Errorf("properties collection not initialized")
	}

	scope, err := GetResourceScope(ctx, userID)
	if err != nil {
		return fmt.Errorf("failed to get resource scope: %v", err)
	}

	filter := scope.ToFilter()
	filter["_id"] = id

	result, err := propColl.DeleteOne(ctx, filter)
	if err != nil {
		return fmt.Errorf("failed to delete property: %v", err)
	}

	if result.DeletedCount == 0 {
		return fmt.Errorf("property not found")
	}

	golog.Info("Successfully deleted property and all files", "propertyId", id, "fileCount", len(property.Documents))
	return nil
}

// CheckRoomDeletionConstraints 检查room是否可以被删除
// 返回错误信息如果有关联的lease
func CheckRoomDeletionConstraints(ctx context.Context, propertyID string, roomID string, userID string) error {
	// 检查是否有关联的leases
	var leaseColl = gomongo.Coll("rr", "leases")
	if leaseColl == nil {
		return fmt.Errorf("leases collection not initialized")
	}

	scope, err := GetResourceScope(ctx, userID)
	if err != nil {
		return fmt.Errorf("failed to get resource scope: %v", err)
	}

	leaseFilter := scope.ToFilter()
	leaseFilter["propId"] = propertyID
	leaseFilter["roomId"] = roomID

	leaseCount, err := leaseColl.CountDocuments(ctx, leaseFilter)
	if err != nil {
		return fmt.Errorf("failed to check lease associations: %v", err)
	}

	if leaseCount > 0 {
		return fmt.Errorf("cannot delete room: room has %d associated lease(s). Please delete all leases first", leaseCount)
	}

	return nil
}

func (property *Property) AddRoom(room Room) error {
	// Validate room type if provided
	if room.Type != "" {
		validTypes := map[string]bool{
			"studio":     true,
			"oneBed":     true,
			"twoBed":     true,
			"commercial": true,
		}
		if !validTypes[room.Type] {
			return fmt.Errorf("invalid room type")
		}
	}

	// Validate status if provided
	if room.Status != "" {
		validStatus := map[string]bool{
			"vacant":      true,
			"occupied":    true,
			"maintenance": true,
		}
		if !validStatus[room.Status] {
			return fmt.Errorf("invalid status")
		}
	}

	property.Rooms = append(property.Rooms, room)
	property.TotalRooms++

	// Update vacant rooms count
	// TODO: change to enum for status
	if room.Status == "vacant" {
		property.VacantRooms++
	}

	return nil
}

func (property *Property) UpdateRoom(room Room) error {
	for i, existingRoom := range property.Rooms {
		if existingRoom.ID == room.ID {
			// If status is changing from occupied/maintenance to vacant
			if existingRoom.Status != "vacant" && room.Status == "vacant" {
				property.VacantRooms++
			}
			// If status is changing from vacant to occupied/maintenance
			if existingRoom.Status == "vacant" && room.Status != "vacant" {
				property.VacantRooms--
			}
			property.Rooms[i] = room
			return nil
		}
	}
	return fmt.Errorf("room not found")
}

func (property *Property) DeleteRoom(ctx context.Context, roomID string) error {
	// 首先检查删除约束
	if err := CheckRoomDeletionConstraints(ctx, property.ID, roomID, property.UserID); err != nil {
		return err
	}

	for i, room := range property.Rooms {
		if room.ID == roomID {
			// Update counts before removing the room
			property.TotalRooms--
			if room.Status == "vacant" {
				property.VacantRooms--
			}

			// Remove the room
			property.Rooms = append(property.Rooms[:i], property.Rooms[i+1:]...)
			return nil
		}
	}
	return fmt.Errorf("room not found")
}

func (property *Property) GetRoom(roomID string) (*Room, error) {
	for _, r := range property.Rooms {
		if r.ID == roomID {
			return &r, nil
		}
	}
	return nil, fmt.Errorf("room not found")
}

// GetAllProperties 获取所有属性
func GetAllProperties(ctx context.Context) ([]Property, error) {
	var propertyColl = gomongo.Coll("rr", "properties")
	if propertyColl == nil {
		return nil, fmt.Errorf("properties collection not initialized")
	}

	cursor, err := propertyColl.Find(ctx, bson.M{})
	if err != nil {
		return nil, fmt.Errorf("failed to find properties: %v", err)
	}
	defer cursor.Close(ctx)

	var properties []Property
	if err := cursor.All(ctx, &properties); err != nil {
		return nil, fmt.Errorf("failed to decode properties: %v", err)
	}

	return properties, nil
}

// calculatePropertyOwingBalance 计算指定 property 下所有 active 租约的 owingBalance 总和
func calculatePropertyOwingBalance(ctx context.Context, leaseColl *gomongo.MongoCollection, propertyID string, scope *ResourceScope) (float64, error) {
	// 构建查询条件：该 property 下的所有 active 租约
	leaseFilter := scope.ToFilter()
	leaseFilter["propId"] = propertyID
	leaseFilter["status"] = "active"

	// 使用聚合管道计算 owingBalance 总和
	pipeline := []bson.M{
		{"$match": leaseFilter},
		{"$group": bson.M{
			"_id":   nil,
			"total": bson.M{"$sum": "$owingBal"},
		}},
	}

	cursor, err := leaseColl.Aggregate(ctx, pipeline)
	if err != nil {
		return 0.0, fmt.Errorf("failed to aggregate owing balance: %v", err)
	}
	defer cursor.Close(ctx)

	var result struct {
		Total float64 `bson:"total"`
	}

	if cursor.Next(ctx) {
		if err := cursor.Decode(&result); err != nil {
			return 0.0, fmt.Errorf("failed to decode aggregation result: %v", err)
		}
		return result.Total, nil
	}

	// 如果没有找到任何 active 租约，返回 0.0
	return 0.0, nil
}

// GetPropertyByID 根据ID获取属性
func GetPropertyByID(ctx context.Context, propertyID string, userID string) (*Property, error) {
	var propertyColl = gomongo.Coll("rr", "properties")
	if propertyColl == nil {
		return nil, fmt.Errorf("properties collection not initialized")
	}

	scope, err := GetResourceScope(ctx, userID)
	if err != nil {
		return nil, fmt.Errorf("failed to get resource scope: %v", err)
	}

	filter := scope.ToFilter()
	filter["_id"] = propertyID

	var property Property
	err = propertyColl.FindOne(ctx, filter).Decode(&property)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return nil, fmt.Errorf("property not found")
		}
		return nil, fmt.Errorf("failed to get property: %v", err)
	}

	return &property, nil
}

// GetPropertyByIDAdmin 管理员专用：根据ID获取属性（无访问控制）
func GetPropertyByIDAdmin(ctx context.Context, propertyID string) (*Property, error) {
	var propertyColl = gomongo.Coll("rr", "properties")
	if propertyColl == nil {
		return nil, fmt.Errorf("properties collection not initialized")
	}

	var property Property
	err := propertyColl.FindOne(ctx, bson.M{"_id": propertyID}).Decode(&property)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return nil, fmt.Errorf("property not found")
		}
		return nil, fmt.Errorf("failed to get property: %v", err)
	}

	return &property, nil
}

// ClearDocument 清除property的文档信息
func (p *Property) ClearDocument(ctx context.Context) error {
	var propColl = gomongo.Coll("rr", "properties")
	if propColl == nil {
		return fmt.Errorf("properties collection not initialized")
	}

	// 添加权限检查，与Update方法一致
	scope, err := GetResourceScope(ctx, p.UserID)
	if err != nil {
		return fmt.Errorf("failed to get resource scope: %v", err)
	}

	filter := scope.ToFilter()
	filter["_id"] = p.ID

	update := bson.M{
		"$set": bson.M{
			"documents": []PropertyDocument{},
		},
		"$unset": bson.M{
			// 清理可能存在的旧字段
			"fileId":   "",
			"fileName": "",
			"fileSize": "",
			"fileType": "",
		},
	}

	result, err := propColl.UpdateOne(ctx, filter, update)
	if err != nil {
		return fmt.Errorf("failed to clear document fields: %v", err)
	}

	if result.MatchedCount == 0 {
		return fmt.Errorf("property not found")
	}

	// 清除本地对象的文档信息
	p.Documents = []PropertyDocument{}

	return nil
}

// UpdateFields 更新属性信息
func (p *Property) UpdateFields(ctx context.Context, updates map[string]interface{}) error {
	var propertyColl = gomongo.Coll("rr", "properties")
	if propertyColl == nil {
		return fmt.Errorf("properties collection not initialized")
	}

	// 添加访问控制检查
	scope, err := GetResourceScope(ctx, p.UserID)
	if err != nil {
		return fmt.Errorf("failed to get resource scope: %v", err)
	}

	filter := scope.ToFilter()
	filter["_id"] = p.ID

	update := bson.M{
		"$set": updates,
	}

	result, err := propertyColl.UpdateOne(ctx, filter, update)
	if err != nil {
		return fmt.Errorf("failed to update property: %v", err)
	}

	if result.MatchedCount == 0 {
		return fmt.Errorf("property not found or access denied")
	}

	return nil
}

// Delete 删除属性
func (p *Property) Delete(ctx context.Context) error {
	// 首先检查删除约束
	if err := CheckPropertyDeletionConstraints(ctx, p.ID, p.UserID); err != nil {
		return err
	}

	// 先删除所有文件
	if err := deletePropertyFiles(ctx, p); err != nil {
		golog.Error("Failed to delete property files", "error", err, "propertyId", p.ID)
		return fmt.Errorf("failed to delete property files: %v", err)
	}

	var propertyColl = gomongo.Coll("rr", "properties")
	if propertyColl == nil {
		return fmt.Errorf("properties collection not initialized")
	}

	// 添加访问控制检查
	scope, err := GetResourceScope(ctx, p.UserID)
	if err != nil {
		return fmt.Errorf("failed to get resource scope: %v", err)
	}

	filter := scope.ToFilter()
	filter["_id"] = p.ID

	result, err := propertyColl.DeleteOne(ctx, filter)
	if err != nil {
		return fmt.Errorf("failed to delete property: %v", err)
	}

	if result.DeletedCount == 0 {
		return fmt.Errorf("property not found or access denied")
	}

	golog.Info("Successfully deleted property and all files", "propertyId", p.ID, "fileCount", len(p.Documents))
	return nil
}

// DeleteAdmin 管理员专用：删除属性（无访问控制）
func (p *Property) DeleteAdmin(ctx context.Context) error {
	// 先删除所有文件
	if err := deletePropertyFiles(ctx, p); err != nil {
		golog.Error("Failed to delete property files (admin)", "error", err, "propertyId", p.ID)
		return fmt.Errorf("failed to delete property files: %v", err)
	}

	var propertyColl = gomongo.Coll("rr", "properties")
	if propertyColl == nil {
		return fmt.Errorf("properties collection not initialized")
	}

	result, err := propertyColl.DeleteOne(ctx, bson.M{"_id": p.ID})
	if err != nil {
		return fmt.Errorf("failed to delete property: %v", err)
	}

	if result.DeletedCount == 0 {
		return fmt.Errorf("property not found")
	}

	golog.Info("Successfully deleted property and all files (admin)", "propertyId", p.ID, "fileCount", len(p.Documents))
	return nil
}
