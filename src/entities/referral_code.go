package entities

import (
	"context"
	"fmt"
	"rent_report/utils"
	"time"

	"github.com/real-rm/gomongo"
)

// 推荐码表
// 对应字段简写见表格
// id, code, type, ttluse, rmnuse, crtr_uid, dscp, status, exp, ct, mt

// ReferralCode 推荐码结构体
// status: ACTIVE, INACTIVE, EXPIRED
// type: DISCOUNT, TRIAL, ...
type ReferralCode struct {
	ID      string    `json:"id" bson:"_id"`
	Code    string    `json:"code" bson:"code"`
	Type    string    `json:"type" bson:"type"`
	TtlUse  int       `json:"ttluse" bson:"ttluse"`
	RmnUse  int       `json:"rmnuse" bson:"rmnuse"`
	CrtrUID string    `json:"crtr_uid,omitempty" bson:"crtr_uid,omitempty"`
	Dscp    string    `json:"dscp" bson:"dscp"`
	Status  string    `json:"status" bson:"status"`
	Exp     time.Time `json:"exp" bson:"exp"`
}

// 推荐码使用记录表
// id, uid, code_id, status, actv, exp

type ReferralCodeUsage struct {
	ID     string    `json:"id" bson:"_id"`
	UID    string    `json:"uid" bson:"uid"`
	CodeID string    `json:"code_id" bson:"code_id"`
	Status string    `json:"status" bson:"status"`
	Actv   time.Time `json:"actv" bson:"actv"`
	Exp    time.Time `json:"exp" bson:"exp"`
}

// 创建推荐码
func (rc *ReferralCode) Create(ctx context.Context) error {
	coll := gomongo.Coll("rr", "referral_codes")
	if coll == nil {
		return fmt.Errorf("referral_codes collection not initialized")
	}
	if rc.ID == "" {
		rc.ID = utils.GenerateNanoID()
	}
	if rc.Code == "" {
		rc.Code = utils.GenerateCustomNanoID(9)
	}
	if rc.Status == "" {
		rc.Status = "ACTIVE"
	}
	_, err := coll.InsertOne(ctx, rc)
	return err
}

// 创建推荐码使用记录
func (rcu *ReferralCodeUsage) Create(ctx context.Context) error {
	coll := gomongo.Coll("rr", "referral_code_usages")
	if coll == nil {
		return fmt.Errorf("referral_code_usages collection not initialized")
	}
	if rcu.ID == "" {
		rcu.ID = utils.GenerateNanoID()
	}
	_, err := coll.InsertOne(ctx, rcu)
	return err
}

// 通过creator查询推荐码
func GetReferralCodesByCreator(ctx context.Context, creator string) ([]*ReferralCode, error) {
	coll := gomongo.Coll("rr", "referral_codes")
	if coll == nil {
		return nil, fmt.Errorf("referral_codes collection not initialized")
	}
	filter := map[string]interface{}{"crtr_uid": creator}
	var codes []*ReferralCode
	cursor, err := coll.Find(ctx, filter)
	if err != nil {
		return nil, err
	}
	defer cursor.Close(ctx)
	for cursor.Next(ctx) {
		var code ReferralCode
		if err := cursor.Decode(&code); err == nil {
			codes = append(codes, &code)
		}
	}
	return codes, nil
}
