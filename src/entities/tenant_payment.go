package entities

import (
	"context"
	"fmt"
	"time"

	"github.com/real-rm/golog"
	"github.com/real-rm/gomongo"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
)

type TenantPayment struct {
	ID               string    `json:"id" bson:"_id"`
	LeaseID          string    `json:"leaseId" bson:"leaseId"`
	Amount           float64   `json:"amount" bson:"amt"`
	Date             time.Time `json:"date" bson:"dt"`
	Notes            string    `json:"notes" bson:"notes"`
	RemainingBalance float64   `json:"remainingBalance" bson:"remBal"`
	Status           string    `json:"status" bson:"stat"`
	UserID           string    `json:"-" bson:"usrId"`
	OrganizationID   string    `json:"-" bson:"orgId,omitempty"`
	IsProtected      bool      `json:"isProtected" bson:"isProtected"` // 是否受保护，不可删除（如用于Metro2报告）
}

func (tenantPayment *TenantPayment) Create(ctx context.Context) error {
	var paymentColl = gomongo.Coll("rr", "tenant_payments")
	if paymentColl == nil {
		return fmt.Errorf("tenant_payments collection not initialized")
	}

	// Get resource scope for access control
	scope, err := GetResourceScope(ctx, tenantPayment.UserID)
	if err != nil {
		return fmt.Errorf("failed to get resource scope: %v", err)
	}

	// Set organization based on scope
	tenantPayment.OrganizationID = scope.OrganizationID

	// Validate required fields
	if tenantPayment.ID == "" || tenantPayment.LeaseID == "" {
		return fmt.Errorf("id and leaseId are required")
	}

	// Check if lease allows modifications (Scenario 5: status lock)
	err = validateLeaseCanAcceptPayments(ctx, tenantPayment.LeaseID, scope)
	if err != nil {
		return fmt.Errorf("lease validation failed: %v", err)
	}

	// Validate status if provided
	if tenantPayment.Status != "" {
		validStatus := map[string]bool{
			"pending":   true,
			"completed": true,
			"failed":    true,
		}
		if !validStatus[tenantPayment.Status] {
			return fmt.Errorf("invalid status")
		}
	}

	_, err = paymentColl.InsertOne(ctx, tenantPayment)
	if err != nil {
		return fmt.Errorf("failed to insert tenant payment: %v", err)
	}

	// Update lease's lastPmtDt
	var leaseColl = gomongo.Coll("rr", "leases")
	if leaseColl == nil {
		return fmt.Errorf("leases collection not initialized")
	}

	filter := scope.ToFilter()
	filter["_id"] = tenantPayment.LeaseID

	// Scenario 2: Update lease balance when adding payment
	err = updateLeaseBalanceAfterPayment(ctx, tenantPayment.LeaseID, -tenantPayment.Amount, scope)
	if err != nil {
		return fmt.Errorf("failed to update lease balance: %v", err)
	}

	// Update lease's lastPmtDt
	update := bson.M{
		"$set": bson.M{
			"lastPmtDt": tenantPayment.Date.Format("2006-01-02"),
		},
	}

	result, err := leaseColl.UpdateOne(ctx, filter, update)
	if err != nil {
		return fmt.Errorf("failed to update lease last payment date: %v", err)
	}

	if result.MatchedCount == 0 {
		return fmt.Errorf("lease not found")
	}

	return nil
}

func GetTenantPayment(ctx context.Context, id string, userID string) (*TenantPayment, error) {
	var paymentColl = gomongo.Coll("rr", "tenant_payments")
	if paymentColl == nil {
		return nil, fmt.Errorf("tenant_payments collection not initialized")
	}

	scope, err := GetResourceScope(ctx, userID)
	if err != nil {
		return nil, fmt.Errorf("failed to get resource scope: %v", err)
	}

	filter := scope.ToFilter()
	filter["_id"] = id

	result := paymentColl.FindOne(ctx, filter)
	if result.Err() != nil {
		if result.Err() == mongo.ErrNoDocuments {
			return nil, fmt.Errorf("tenant payment not found")
		}
		return nil, fmt.Errorf("failed to get tenant payment: %v", result.Err())
	}

	var payment TenantPayment
	if err := result.Decode(&payment); err != nil {
		return nil, fmt.Errorf("failed to decode tenant payment: %v", err)
	}

	return &payment, nil
}

func (tenantPayment *TenantPayment) Update(ctx context.Context) error {
	golog.Debug("Updating tenant payment", "paymentID", tenantPayment.ID, "amount", tenantPayment.Amount, "status", tenantPayment.Status)
	var paymentColl = gomongo.Coll("rr", "tenant_payments")
	if paymentColl == nil {
		return fmt.Errorf("tenant_payments collection not initialized")
	}

	scope, err := GetResourceScope(ctx, tenantPayment.UserID)
	if err != nil {
		return fmt.Errorf("failed to get resource scope: %v", err)
	}

	filter := scope.ToFilter()
	filter["_id"] = tenantPayment.ID

	// Scenario 3: Get old payment data before update for balance calculation
	var oldPayment TenantPayment
	err = paymentColl.FindOne(ctx, filter).Decode(&oldPayment)
	if err != nil {
		return fmt.Errorf("failed to find existing payment: %v", err)
	}

	// Check if lease allows modifications (Scenario 5: status lock)
	err = validateLeaseCanAcceptPayments(ctx, tenantPayment.LeaseID, scope)
	if err != nil {
		return fmt.Errorf("lease validation failed: %v", err)
	}

	// Set organization based on scope
	tenantPayment.OrganizationID = scope.OrganizationID

	// Validate required fields
	if tenantPayment.ID == "" || tenantPayment.LeaseID == "" {
		return fmt.Errorf("id and leaseId are required")
	}

	// Validate status if provided
	if tenantPayment.Status != "" {
		validStatus := map[string]bool{
			"pending":   true,
			"completed": true,
			"failed":    true,
		}
		if !validStatus[tenantPayment.Status] {
			return fmt.Errorf("invalid status")
		}
	}

	updateDoc, err := gomongo.ToBSONDoc(tenantPayment)
	if err != nil {
		return fmt.Errorf("failed to create update document: %v", err)
	}

	result, err := paymentColl.UpdateOne(ctx, filter, bson.M{"$set": updateDoc})
	if err != nil {
		return fmt.Errorf("failed to update tenant payment: %v", err)
	}

	if result.MatchedCount == 0 {
		return fmt.Errorf("tenant payment not found")
	}

	// Scenario 3: Update lease balance after payment update
	// Calculate balance change: +oldAmount (revert) -newAmount (apply)
	balanceChange := oldPayment.Amount - tenantPayment.Amount
	err = updateLeaseBalanceAfterPayment(ctx, tenantPayment.LeaseID, balanceChange, scope)
	if err != nil {
		return fmt.Errorf("failed to update lease balance: %v", err)
	}

	return nil
}

func DeleteTenantPayment(ctx context.Context, id string, userID string) error {
	var paymentColl = gomongo.Coll("rr", "tenant_payments")
	if paymentColl == nil {
		return fmt.Errorf("tenant_payments collection not initialized")
	}

	scope, err := GetResourceScope(ctx, userID)
	if err != nil {
		return fmt.Errorf("failed to get resource scope: %v", err)
	}

	filter := scope.ToFilter()
	filter["_id"] = id

	// Scenario 4: Get payment data before deletion for balance calculation
	var paymentToDelete TenantPayment
	err = paymentColl.FindOne(ctx, filter).Decode(&paymentToDelete)
	if err != nil {
		return fmt.Errorf("failed to find payment to delete: %v", err)
	}

	// Check if payment is protected
	if paymentToDelete.IsProtected {
		return fmt.Errorf("cannot delete protected payment. This payment is protected due to Metro2 reporting requirements")
	}

	// Check if lease allows modifications (Scenario 5: status lock)
	err = validateLeaseCanAcceptPayments(ctx, paymentToDelete.LeaseID, scope)
	if err != nil {
		return fmt.Errorf("lease validation failed: %v", err)
	}

	result, err := paymentColl.DeleteOne(ctx, filter)
	if err != nil {
		return fmt.Errorf("failed to delete tenant payment: %v", err)
	}

	if result.DeletedCount == 0 {
		return fmt.Errorf("tenant payment not found")
	}

	// Scenario 4: Update lease balance after payment deletion
	// Revert the payment's effect: +deletedAmount (restore balance)
	err = updateLeaseBalanceAfterPayment(ctx, paymentToDelete.LeaseID, paymentToDelete.Amount, scope)
	if err != nil {
		return fmt.Errorf("failed to update lease balance: %v", err)
	}

	return nil
}

func GetTenantPayments(ctx context.Context, leaseID string, userID string) ([]TenantPayment, int64, error) {
	var paymentColl = gomongo.Coll("rr", "tenant_payments")
	if paymentColl == nil {
		return nil, 0, fmt.Errorf("tenant_payments collection not initialized")
	}

	// 只根据leaseId查找，不做userId权限过滤
	filter := bson.M{"leaseId": leaseID}

	// Get total count
	total, err := paymentColl.CountDocuments(ctx, filter)
	if err != nil {
		return nil, 0, fmt.Errorf("failed to get total count: %v", err)
	}

	// Initialize payments as empty slice instead of nil
	payments := make([]TenantPayment, 0)

	result, err := paymentColl.FindToArray(ctx, filter, gomongo.QueryOptions{
		Sort: bson.D{{Key: "dt", Value: -1}},
	})
	if err != nil {
		return nil, 0, fmt.Errorf("failed to get tenant payments: %v", err)
	}

	for _, item := range result {
		var payment TenantPayment
		data, err := bson.Marshal(item)
		if err != nil {
			return nil, 0, fmt.Errorf("failed to marshal payment data: %v", err)
		}

		if err := bson.Unmarshal(data, &payment); err != nil {
			return nil, 0, fmt.Errorf("failed to unmarshal payment data: %v", err)
		}

		payments = append(payments, payment)
	}

	return payments, total, nil
}

// GetTenantPaymentsUpToDate 获取指定日期及之前的payment记录，用于Metro2报告生成
func GetTenantPaymentsUpToDate(ctx context.Context, leaseID string, userID string, endDate time.Time) ([]TenantPayment, int64, error) {
	var paymentColl = gomongo.Coll("rr", "tenant_payments")
	if paymentColl == nil {
		return nil, 0, fmt.Errorf("tenant_payments collection not initialized")
	}

	// 只根据leaseId查找，并添加日期过滤条件
	filter := bson.M{
		"leaseId": leaseID,
		"dt":      bson.M{"$lte": endDate}, // 只获取endDate及之前的记录
	}

	// Get total count
	total, err := paymentColl.CountDocuments(ctx, filter)
	if err != nil {
		return nil, 0, fmt.Errorf("failed to get total count: %v", err)
	}

	// Initialize payments as empty slice instead of nil
	payments := make([]TenantPayment, 0)

	result, err := paymentColl.FindToArray(ctx, filter, gomongo.QueryOptions{
		Sort: bson.D{{Key: "dt", Value: -1}},
	})
	if err != nil {
		return nil, 0, fmt.Errorf("failed to get tenant payments: %v", err)
	}

	for _, item := range result {
		var payment TenantPayment
		data, err := bson.Marshal(item)
		if err != nil {
			return nil, 0, fmt.Errorf("failed to marshal payment data: %v", err)
		}

		if err := bson.Unmarshal(data, &payment); err != nil {
			return nil, 0, fmt.Errorf("failed to unmarshal payment data: %v", err)
		}

		payments = append(payments, payment)
	}

	return payments, total, nil
}

// CreateAutoPayment 为自动支付系统创建支付记录（绕过权限检查）
func (tenantPayment *TenantPayment) CreateAutoPayment(ctx context.Context) error {
	var paymentColl = gomongo.Coll("rr", "tenant_payments")
	if paymentColl == nil {
		return fmt.Errorf("tenant_payments collection not initialized")
	}

	// 自动支付系统不需要权限检查，直接获取用户的组织信息
	userColl := gomongo.Coll("rr", "users")
	if userColl == nil {
		return fmt.Errorf("users collection not initialized")
	}

	// 获取用户信息以设置组织ID
	var user struct {
		OrganizationID string `bson:"orgId"`
	}
	err := userColl.FindOne(ctx, bson.M{"_id": tenantPayment.UserID}).Decode(&user)
	if err != nil {
		// 如果找不到用户或没有组织ID，继续处理（组织ID可以为空）
		tenantPayment.OrganizationID = ""
	} else {
		tenantPayment.OrganizationID = user.OrganizationID
	}

	// Validate required fields
	if tenantPayment.ID == "" || tenantPayment.LeaseID == "" {
		return fmt.Errorf("id and leaseId are required")
	}

	// Validate status if provided
	if tenantPayment.Status != "" {
		validStatus := map[string]bool{
			"pending":   true,
			"completed": true,
			"failed":    true,
		}
		if !validStatus[tenantPayment.Status] {
			return fmt.Errorf("invalid status")
		}
	}

	_, err = paymentColl.InsertOne(ctx, tenantPayment)
	if err != nil {
		return fmt.Errorf("failed to insert tenant payment: %v", err)
	}

	// Update lease's lastPmtDt
	var leaseColl = gomongo.Coll("rr", "leases")
	if leaseColl == nil {
		return fmt.Errorf("leases collection not initialized")
	}

	// 自动支付系统直接更新租约，不需要权限过滤
	filter := bson.M{"_id": tenantPayment.LeaseID}
	update := bson.M{
		"$set": bson.M{
			"lastPmtDt": tenantPayment.Date.Format("2006-01-02"),
		},
	}

	_, err = leaseColl.UpdateOne(ctx, filter, update)
	if err != nil {
		return fmt.Errorf("failed to update lease lastPmtDt: %v", err)
	}

	return nil
}

// updateLeaseBalanceAfterPayment 更新租约余额（支付操作后）
func updateLeaseBalanceAfterPayment(ctx context.Context, leaseID string, balanceChange float64, scope *ResourceScope) error {
	var leaseColl = gomongo.Coll("rr", "leases")
	if leaseColl == nil {
		return fmt.Errorf("leases collection not initialized")
	}

	// 获取当前租约信息
	filter := scope.ToFilter()
	filter["_id"] = leaseID

	var lease Lease
	err := leaseColl.FindOne(ctx, filter).Decode(&lease)
	if err != nil {
		return fmt.Errorf("failed to find lease: %v", err)
	}

	// 计算新余额：当前余额 + 变化量（支付为负数）
	// 允许负数余额，表示多付款
	newBalance := lease.OwingBalance + balanceChange

	// 更新租约余额
	update := bson.M{
		"$set": bson.M{
			"owingBal": newBalance,
		},
	}

	result, err := leaseColl.UpdateOne(ctx, filter, update)
	if err != nil {
		return fmt.Errorf("failed to update lease balance: %v", err)
	}

	if result.MatchedCount == 0 {
		return fmt.Errorf("lease not found for balance update")
	}

	return nil
}

// validateLeaseCanAcceptPayments 验证租约是否可以接受支付操作
func validateLeaseCanAcceptPayments(ctx context.Context, leaseID string, scope *ResourceScope) error {
	var leaseColl = gomongo.Coll("rr", "leases")
	if leaseColl == nil {
		return fmt.Errorf("leases collection not initialized")
	}

	// 获取租约信息
	filter := scope.ToFilter()
	filter["_id"] = leaseID

	var lease Lease
	err := leaseColl.FindOne(ctx, filter).Decode(&lease)
	if err != nil {
		return fmt.Errorf("failed to find lease: %v", err)
	}

	// Scenario 5: 只有active状态的租约才能接受支付
	if lease.Status != "active" {
		return fmt.Errorf("can only add payments to active leases, current status: %s", lease.Status)
	}

	return nil
}
