package entities

import (
	"context"
	"fmt"
	"rent_report/utils"
	"time"

	//nanoid "github.com/matoous/go-nanoid/v2"

	"github.com/real-rm/gomongo"
	"go.mongodb.org/mongo-driver/bson"
	"golang.org/x/crypto/bcrypt"
)

const (
	RoleNormalUser = "normal_user"
	RoleAdmin      = "admin"

	UserStatusDisabled = "disabled"
	UserStatusActive   = "active"
)

type User struct {
	ID             string                 `json:"id" bson:"_id"`
	Email          string                 `json:"email" bson:"email"`
	Username       string                 `json:"username" bson:"usrNm"`
	Password       string                 `json:"-" bson:"pwd,omitempty"`
	AccountType    string                 `json:"accountType" bson:"acctTp"`
	Role           string                 `json:"role" bson:"role"`
	Status         string                 `json:"status" bson:"status"`
	OrganizationID string                 `json:"organizationId" bson:"orgId"`
	IsOAuth        bool                   `json:"isOAuth" bson:"isOAuth"`
	OAuthID        string                 `json:"oauthId,omitempty" bson:"oauthId,omitempty"`
	IsVerified     bool                   `json:"isVerified" bson:"isVerified"`
	VerifyCode     string                 `json:"-" bson:"verifyCode,omitempty"`
	CodeExpiry     time.Time              `json:"-" bson:"codeExpiry,omitempty"`
	PhoneNumber    string                 `json:"phoneNumber" bson:"phoneNumber"`
	Address        map[string]interface{} `json:"address" bson:"address"`
	// Fields for password binding verification
	BindverifyCode  string    `json:"-" bson:"bindverifyCode,omitempty"`
	BindCodeExpiry  time.Time `json:"-" bson:"bindCodeExpiry,omitempty"`
	PendingPassword string    `json:"-" bson:"pendingPassword,omitempty"`
	ViewType        string    `json:"viewType" bson:"viewType"`
	// 推荐码注册临时字段
	RfrlCd string `json:"rfrl_cd,omitempty" bson:"rfrl_cd,omitempty"`
	// Invitation lists
	TenantInvitationList   []string `json:"tenantInvitationList" bson:"tntInvLst"`
	LandlordInvitationList []string `json:"landlordInvitationList" bson:"lndInvLst"`
	StripeCusIds           []string `json:"stripeCusIds,omitempty" bson:"stripeCusIds,omitempty"`
}

// LinkOAuthUser creates a new user from OAuth2 data or binds with existing account
func LinkOAuthUser(ctx context.Context, email, oauthID string) (*User, error) {
	var userColl = gomongo.Coll("rr", "users")
	if userColl == nil {
		return nil, fmt.Errorf("users collection not initialized")
	}

	// First try to find user by OAuth ID
	var existingUser User
	err := userColl.FindOne(ctx, bson.M{"oauthId": oauthID}).Decode(&existingUser)
	if err == nil {
		// User already exists with this OAuth ID - return as is without updating
		return &existingUser, nil
	}

	// Try to find user by email
	err = userColl.FindOne(ctx, bson.M{"email": email}).Decode(&existingUser)
	if err == nil {
		// Only update if the user doesn't have an OAuth ID yet
		if existingUser.OAuthID == "" {
			update := bson.M{
				"$set": bson.M{
					"oauthId":    oauthID,
					"isOAuth":    true,
					"isVerified": true,
				},
			}

			_, err = userColl.UpdateOne(ctx, bson.M{"_id": existingUser.ID}, update)
			if err != nil {
				return nil, fmt.Errorf("failed to bind OAuth account: %v", err)
			}

			existingUser.OAuthID = oauthID
			existingUser.IsOAuth = true
			existingUser.IsVerified = true
		}
		return &existingUser, nil
	}

	// Create new user
	user := &User{
		ID:                     utils.GenerateNanoID(),
		Email:                  email,
		Username:               email, // Default to email, can be updated later
		OAuthID:                oauthID,
		IsOAuth:                true,
		IsVerified:             true,             // OAuth users are considered verified
		Role:                   RoleNormalUser,   // 设置默认角色
		Status:                 UserStatusActive, // OAuth users are active by default
		ViewType:               "landlord",       // OAuth users default to landlord view
		TenantInvitationList:   []string{},
		LandlordInvitationList: []string{},
	}

	_, err = userColl.InsertOne(ctx, user)
	if err != nil {
		return nil, fmt.Errorf("failed to create OAuth user: %v", err)
	}

	return user, nil
}

// Create creates a new regular user with email verification
func (u *User) Create(ctx context.Context, password string) error {
	var userColl = gomongo.Coll("rr", "users")
	if userColl == nil {
		return fmt.Errorf("users collection not initialized")
	}

	// Check if email already exists
	var existingUser User
	err := userColl.FindOne(ctx, bson.M{"email": u.Email}).Decode(&existingUser)
	if err == nil {
		// If the existing user is OAuth-only, require email verification before binding password
		if existingUser.IsOAuth && existingUser.Password == "" {
			verifyCode := GenerateVerificationCode()
			now := time.Now()

			update := bson.M{
				"$set": bson.M{
					"bindverifyCode":  verifyCode,
					"bindCodeExpiry":  now.Add(24 * time.Hour), // Code valid for 24 hours
					"pendingPassword": password,                // Store the password temporarily
					"updatedAt":       now,
				},
			}

			_, err = userColl.UpdateOne(ctx, bson.M{"_id": existingUser.ID}, update)
			if err != nil {
				return fmt.Errorf("failed to set binding verification: %v", err)
			}

			*u = existingUser
			u.BindverifyCode = verifyCode // Set for email sending purposes
			return nil
		}
		return fmt.Errorf("email already exists")
	}

	// Validate password using the same rules as frontend
	if err := validatePassword(password); err != nil {
		return err
	}

	// Hash password
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(password), bcrypt.DefaultCost)
	if err != nil {
		return fmt.Errorf("failed to hash password: %v", err)
	}

	// Generate verification code
	verifyCode := GenerateVerificationCode()
	now := time.Now()

	// 确保设置默认角色和状态
	if u.Role == "" {
		u.Role = RoleNormalUser
	}
	u.Status = UserStatusDisabled // 设置初始状态为disabled

	u.Password = string(hashedPassword)
	u.IsOAuth = false
	u.IsVerified = false
	u.VerifyCode = verifyCode
	u.CodeExpiry = now.Add(24 * time.Hour) // Code valid for 24 hours
	// 新增初始化
	if u.TenantInvitationList == nil {
		u.TenantInvitationList = []string{}
	}
	if u.LandlordInvitationList == nil {
		u.LandlordInvitationList = []string{}
	}
	if u.StripeCusIds == nil {
		u.StripeCusIds = []string{}
	}

	// Insert user
	_, err = userColl.InsertOne(ctx, u)
	if err != nil {
		return fmt.Errorf("failed to create user: %v", err)
	}

	return nil
}

// VerifyEmail verifies the user's email with the provided code
func (u *User) VerifyEmail(ctx context.Context, code string) error {
	var userColl = gomongo.Coll("rr", "users")
	if userColl == nil {
		return fmt.Errorf("users collection not initialized")
	}

	if u.IsVerified {
		return fmt.Errorf("email already verified")
	}

	if u.VerifyCode != code {
		return fmt.Errorf("invalid verification code")
	}

	if time.Now().After(u.CodeExpiry) {
		return fmt.Errorf("verification code expired")
	}

	update := bson.M{
		"$set": bson.M{
			"isVerified": true,
			"status":     UserStatusActive, // 验证成功后激活用户
			"verifyCode": "",
			"codeExpiry": time.Time{},
			"updatedAt":  time.Now(),
		},
	}

	_, err := userColl.UpdateOne(ctx, bson.M{"_id": u.ID}, update)
	if err != nil {
		return fmt.Errorf("failed to verify email: %v", err)
	}

	u.IsVerified = true
	u.Status = UserStatusActive
	return nil
}

// VerifyPasswordBinding verifies the binding code and sets the password for an OAuth user
func (u *User) VerifyPasswordBinding(ctx context.Context, code string) error {
	var userColl = gomongo.Coll("rr", "users")
	if userColl == nil {
		return fmt.Errorf("users collection not initialized")
	}

	// Find user with binding verification code
	var user User
	err := userColl.FindOne(ctx, bson.M{
		"_id":            u.ID,
		"bindverifyCode": code,
	}).Decode(&user)

	if err != nil {
		return fmt.Errorf("invalid verification code")
	}

	if time.Now().After(user.BindCodeExpiry) {
		return fmt.Errorf("verification code expired")
	}

	// Hash the pending password
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(user.PendingPassword), bcrypt.DefaultCost)
	if err != nil {
		return fmt.Errorf("failed to hash password: %v", err)
	}

	// Update user with password and clear verification fields
	update := bson.M{
		"$set": bson.M{
			"pwd":       string(hashedPassword),
			"updatedAt": time.Now(),
		},
		"$unset": bson.M{
			"bindverifyCode":  "",
			"bindCodeExpiry":  "",
			"pendingPassword": "",
		},
	}

	_, err = userColl.UpdateOne(ctx, bson.M{"_id": u.ID}, update)
	if err != nil {
		return fmt.Errorf("failed to bind password: %v", err)
	}

	u.Password = string(hashedPassword)
	return nil
}

// AuthenticateUser authenticates a user with email and password
func AuthenticateUser(ctx context.Context, email, password string) (*User, error) {
	var userColl = gomongo.Coll("rr", "users")
	if userColl == nil {
		return nil, fmt.Errorf("users collection not initialized")
	}

	var user User
	err := userColl.FindOne(ctx, bson.M{"email": email}).Decode(&user)
	if err != nil {
		return nil, fmt.Errorf("Email does not match any account")
	}

	if !user.IsVerified {
		return nil, fmt.Errorf("Email not verified. Please check your email and verify your account")
	}

	if user.Status != UserStatusActive {
		return nil, fmt.Errorf("Account is disabled. Please contact support")
	}

	// If user is OAuth-only (no password set)
	if user.IsOAuth && user.Password == "" {
		return nil, fmt.Errorf("Please use OAuth to login")
	}

	// Compare passwords
	err = bcrypt.CompareHashAndPassword([]byte(user.Password), []byte(password))
	if err != nil {
		return nil, fmt.Errorf("Incorrect password")
	}

	return &user, nil
}

// GenerateVerificationCode generates a verification code
func GenerateVerificationCode() string {
	// For now, return a simple 6-digit code
	// In production, use a more secure method
	return fmt.Sprintf("%06d", time.Now().UnixNano()%1000000)
}

// FindByEmail finds a user by email
func FindByEmail(ctx context.Context, email string) (*User, error) {
	var userColl = gomongo.Coll("rr", "users")
	if userColl == nil {
		return nil, fmt.Errorf("users collection not initialized")
	}

	var user User
	err := userColl.FindOne(ctx, bson.M{"email": email}).Decode(&user)
	if err != nil {
		return nil, fmt.Errorf("user not found")
	}

	return &user, nil
}

// UpdateOAuthInfo updates user information from OAuth provider
func (u *User) UpdateOAuthInfo(ctx context.Context, email, username string) error {
	var userColl = gomongo.Coll("rr", "users")
	if userColl == nil {
		return fmt.Errorf("users collection not initialized")
	}

	update := bson.M{
		"noModifyMt": true,
		"$set": bson.M{
			"email":  email,
			"usrNm":  username,
			"acctTp": "realmaster",
		},
	}

	_, err := userColl.UpdateOne(ctx, bson.M{"_id": u.ID}, update)
	if err != nil {
		return fmt.Errorf("failed to update user with OAuth info: %v", err)
	}

	// Update the current instance
	u.Email = email
	u.Username = username
	u.AccountType = "realmaster"

	return nil
}

// GetUserByID finds a user by their ID
func GetUserByID(ctx context.Context, userID string) (*User, error) {
	var userColl = gomongo.Coll("rr", "users")
	if userColl == nil {
		return nil, fmt.Errorf("users collection not initialized")
	}

	var user User
	err := userColl.FindOne(ctx, bson.M{"_id": userID}).Decode(&user)
	if err != nil {
		return nil, fmt.Errorf("user not found")
	}

	return &user, nil
}

// UpdateUser updates user information
func (u *User) UpdateUser(ctx context.Context, updates map[string]interface{}) error {
	var userColl = gomongo.Coll("rr", "users")
	if userColl == nil {
		return fmt.Errorf("users collection not initialized")
	}

	// Convert the updates map to bson.M
	updateData := bson.M{}
	for key, value := range updates {
		switch key {
		case "name":
			updateData["usrNm"] = value
		case "email":
			// Check if email already exists
			if value != u.Email {
				exists, _ := userColl.CountDocuments(ctx, bson.M{"email": value})
				if exists > 0 {
					return fmt.Errorf("email already exists")
				}
			}
			updateData["email"] = value
		case "phoneNumber":
			updateData["phoneNumber"] = value
		case "address":
			if addr, ok := value.(map[string]interface{}); ok {
				updateData["address"] = addr
			}
		case "viewType":
			updateData["viewType"] = value
		case "status":
			updateData["status"] = value
		}
	}

	if len(updateData) == 0 {
		return fmt.Errorf("no valid fields to update")
	}

	// Add updated timestamp
	updateData["updatedAt"] = time.Now()

	update := bson.M{
		"$set": updateData,
	}

	_, err := userColl.UpdateOne(ctx, bson.M{"_id": u.ID}, update)
	if err != nil {
		return fmt.Errorf("failed to update user: %v", err)
	}

	// Update the current instance with new values
	for key, value := range updates {
		switch key {
		case "name":
			u.Username = value.(string)
		case "email":
			u.Email = value.(string)
		case "phoneNumber":
			u.PhoneNumber = value.(string)
		case "address":
			if addr, ok := value.(map[string]interface{}); ok {
				u.Address = addr
			}
		case "viewType":
			u.ViewType = value.(string)
		case "status":
			u.Status = value.(string)
		}
	}

	return nil
}

// UpdatePassword updates the user's password
func (u *User) UpdatePassword(ctx context.Context, currentPassword, newPassword string) error {
	var userColl = gomongo.Coll("rr", "users")
	if userColl == nil {
		return fmt.Errorf("users collection not initialized")
	}

	// Verify current password
	err := bcrypt.CompareHashAndPassword([]byte(u.Password), []byte(currentPassword))
	if err != nil {
		return fmt.Errorf("invalid current password")
	}

	// Hash new password
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(newPassword), bcrypt.DefaultCost)
	if err != nil {
		return fmt.Errorf("failed to hash password: %v", err)
	}

	update := bson.M{
		"$set": bson.M{
			"pwd":       string(hashedPassword),
			"updatedAt": time.Now(),
		},
	}

	_, err = userColl.UpdateOne(ctx, bson.M{"_id": u.ID}, update)
	if err != nil {
		return fmt.Errorf("failed to update password: %v", err)
	}

	u.Password = string(hashedPassword)
	return nil
}

// CloseAccount deletes the user account
func (u *User) CloseAccount(ctx context.Context) error {
	var userColl = gomongo.Coll("rr", "users")
	if userColl == nil {
		return fmt.Errorf("users collection not initialized")
	}

	_, err := userColl.DeleteOne(ctx, bson.M{"_id": u.ID})
	if err != nil {
		return fmt.Errorf("failed to delete account: %v", err)
	}

	return nil
}

// StartVerification initiates the account verification process
func (u *User) StartVerification(ctx context.Context) error {
	var userColl = gomongo.Coll("rr", "users")
	if userColl == nil {
		return fmt.Errorf("users collection not initialized")
	}

	// Generate new verification code
	verifyCode := GenerateVerificationCode()
	now := time.Now()

	// Update user with new verification code
	update := bson.M{
		"$set": bson.M{
			"verifyCode": verifyCode,
			"codeExpiry": now.Add(24 * time.Hour),
			"updatedAt":  now,
		},
	}

	_, err := userColl.UpdateOne(ctx, bson.M{"_id": u.ID}, update)
	if err != nil {
		return fmt.Errorf("failed to start verification: %v", err)
	}

	u.VerifyCode = verifyCode
	u.CodeExpiry = now.Add(24 * time.Hour)
	return nil
}

// StartPasswordReset initiates the password reset process
func (u *User) StartPasswordReset(ctx context.Context) error {
	var userColl = gomongo.Coll("rr", "users")
	if userColl == nil {
		return fmt.Errorf("users collection not initialized")
	}

	// Generate verification code
	verifyCode := GenerateVerificationCode()
	now := time.Now()

	// Update user with new verification code and reset status
	update := bson.M{
		"$set": bson.M{
			"verifyCode": verifyCode,
			"codeExpiry": now.Add(24 * time.Hour), // Code valid for 24 hours
			"isVerified": false,
			"pwd":        "", // Clear the password
			"updatedAt":  now,
		},
	}

	_, err := userColl.UpdateOne(ctx, bson.M{"_id": u.ID}, update)
	if err != nil {
		return fmt.Errorf("failed to start password reset: %v", err)
	}

	// Update the user object
	u.VerifyCode = verifyCode
	u.CodeExpiry = now.Add(24 * time.Hour)
	u.IsVerified = false
	u.Password = ""

	return nil
}

// ResetPassword resets the user's password and sets verified to true
func (u *User) ResetPassword(ctx context.Context, newPassword string) error {
	var userColl = gomongo.Coll("rr", "users")
	if userColl == nil {
		return fmt.Errorf("users collection not initialized")
	}

	// Validate password using the same rules as signup
	if err := validatePassword(newPassword); err != nil {
		return err
	}

	// Hash password
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(newPassword), bcrypt.DefaultCost)
	if err != nil {
		return fmt.Errorf("failed to hash password: %v", err)
	}

	// Update user with new password and verified status
	update := bson.M{
		"$set": bson.M{
			"pwd":        string(hashedPassword),
			"isVerified": true,
			"updatedAt":  time.Now(),
		},
	}

	_, err = userColl.UpdateOne(ctx, bson.M{"_id": u.ID}, update)
	if err != nil {
		return fmt.Errorf("failed to reset password: %v", err)
	}

	// Update the user object
	u.Password = string(hashedPassword)
	u.IsVerified = true

	return nil
}

// GetAllUsers 获取所有用户
func GetAllUsers(ctx context.Context) ([]User, error) {
	var userColl = gomongo.Coll("rr", "users")
	if userColl == nil {
		return nil, fmt.Errorf("users collection not initialized")
	}

	cursor, err := userColl.Find(ctx, bson.M{})
	if err != nil {
		return nil, fmt.Errorf("failed to find users: %v", err)
	}
	defer cursor.Close(ctx)

	var users []User
	if err := cursor.All(ctx, &users); err != nil {
		return nil, fmt.Errorf("failed to decode users: %v", err)
	}

	return users, nil
}

// validatePassword validates password according to the same rules as frontend
func validatePassword(password string) error {
	// At least 8 characters
	if len(password) < 8 {
		return fmt.Errorf("password must be at least 8 characters")
	}

	// Contains at least 1 alphabetical character (A-Z or a-z)
	hasAlphabetical := false
	for _, r := range password {
		if (r >= 'A' && r <= 'Z') || (r >= 'a' && r <= 'z') {
			hasAlphabetical = true
			break
		}
	}
	if !hasAlphabetical {
		return fmt.Errorf("password must contain at least 1 alphabetical character (A-Z)")
	}

	return nil
}
