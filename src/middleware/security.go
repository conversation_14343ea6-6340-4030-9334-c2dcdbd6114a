package middleware

import (
	"net/http"
	"os"
	"rent_report/config"
	"rent_report/utils"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/real-rm/golog"
)

// SecurityHeaders 添加安全头的中间件
func SecurityHeaders() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 防止点击劫持
		c.<PERSON><PERSON>("X-Frame-Options", "DENY")

		// 防止MIME类型嗅探
		c.Header("X-Content-Type-Options", "nosniff")

		// XSS保护
		c.Header("X-XSS-Protection", "1; mode=block")

		// 引用策略
		c.Header("Referrer-Policy", "strict-origin-when-cross-origin")

		// 内容安全策略
		c.Header("Content-Security-Policy", "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self' data:; connect-src 'self' https:; frame-ancestors 'none';")

		// 权限策略
		c.Header("Permissions-Policy", "geolocation=(), microphone=(), camera=()")

		// 严格传输安全（HTTPS）
		c.Header("Strict-Transport-Security", "max-age=31536000; includeSubDomains")

		c.Next()
	}
}

// CORS 跨域资源共享中间件
func CORS(securityConfig *config.SecurityConfig) gin.HandlerFunc {
	return func(c *gin.Context) {
		origin := c.Request.Header.Get("Origin")

		// 检查是否允许该来源
		allowed := false
		for _, allowedOrigin := range securityConfig.AllowedOrigins {
			if allowedOrigin == "*" || allowedOrigin == origin {
				allowed = true
				break
			}
		}

		if allowed {
			c.Header("Access-Control-Allow-Origin", origin)
		}

		c.Header("Access-Control-Allow-Credentials", "true")
		c.Header("Access-Control-Allow-Methods", strings.Join(securityConfig.AllowedMethods, ", "))
		c.Header("Access-Control-Allow-Headers", strings.Join(securityConfig.AllowedHeaders, ", "))

		// 处理预检请求
		if c.Request.Method == "OPTIONS" {
			c.AbortWithStatus(http.StatusNoContent)
			return
		}

		c.Next()
	}
}

// RateLimit 简单的速率限制中间件
func RateLimit(securityConfig *config.SecurityConfig) gin.HandlerFunc {
	// 使用内存存储请求计数（生产环境建议使用Redis）
	requestCounts := make(map[string]int)

	return func(c *gin.Context) {
		clientIP := c.ClientIP()

		// 获取当前请求计数
		count, exists := requestCounts[clientIP]
		if !exists {
			requestCounts[clientIP] = 1
		} else {
			requestCounts[clientIP] = count + 1
		}

		// 检查是否超过限制
		if requestCounts[clientIP] > securityConfig.RateLimitPerMinute {
			c.JSON(http.StatusTooManyRequests, gin.H{
				"error": "Too many requests",
			})
			c.Abort()
			return
		}

		c.Next()
	}
}

// RequestSizeLimit 请求大小限制中间件
func RequestSizeLimit(securityConfig *config.SecurityConfig) gin.HandlerFunc {
	return func(c *gin.Context) {
		// 设置请求体大小限制
		c.Request.Body = http.MaxBytesReader(c.Writer, c.Request.Body, securityConfig.MaxRequestSize)

		c.Next()
	}
}

// NoCache 禁用缓存中间件
func NoCache() gin.HandlerFunc {
	return func(c *gin.Context) {
		c.Header("Cache-Control", "no-cache, no-store, must-revalidate")
		c.Header("Pragma", "no-cache")
		c.Header("Expires", "0")

		c.Next()
	}
}

// isProduction 检查是否为生产环境
func isProduction() bool {
	env := strings.ToLower(os.Getenv("GIN_MODE"))
	if env == "release" {
		return true
	}

	// 检查其他生产环境标识
	if strings.ToLower(os.Getenv("ENVIRONMENT")) == "production" {
		return true
	}

	return false
}

// isHTTPS 检查请求是否使用HTTPS
func isHTTPS(c *gin.Context) bool {
	// 检查直接的HTTPS连接
	if c.Request.TLS != nil {
		return true
	}

	// 检查代理头（如Nginx、CloudFlare等）
	if c.GetHeader("X-Forwarded-Proto") == "https" {
		return true
	}

	if c.GetHeader("X-Forwarded-Ssl") == "on" {
		return true
	}

	return false
}

// safeLog 安全的日志函数，避免在测试环境中panic
func safeLog(level string, msg string, args ...interface{}) {
	// 在测试环境中，golog可能未初始化，使用标准库日志
	if gin.Mode() == gin.TestMode {
		// 在测试模式下不记录日志，避免干扰测试输出
		return
	}

	// 在非测试环境中使用golog
	switch level {
	case "debug":
		golog.Debug(msg, args...)
	case "info":
		golog.Info(msg, args...)
	case "warn":
		golog.Warn(msg, args...)
	case "error":
		golog.Error(msg, args...)
	default:
		golog.Info(msg, args...)
	}
}

// SecureCookies 安全Cookie中间件 - 拦截并修改Cookie设置
func SecureCookies() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 创建一个自定义的ResponseWriter来拦截Set-Cookie头
		writer := &secureCookieWriter{
			ResponseWriter: c.Writer,
			context:        c,
		}
		c.Writer = writer

		c.Next()
	}
}

// secureCookieWriter 包装ResponseWriter以拦截和修改Cookie
type secureCookieWriter struct {
	gin.ResponseWriter
	context *gin.Context
}

// Header 拦截Header方法来修改Set-Cookie
func (w *secureCookieWriter) Header() http.Header {
	return w.ResponseWriter.Header()
}

// Write 拦截Write方法
func (w *secureCookieWriter) Write(data []byte) (int, error) {
	// 在写入响应前，修改所有的Set-Cookie头
	w.modifySetCookieHeaders()
	return w.ResponseWriter.Write(data)
}

// WriteHeader 拦截WriteHeader方法
func (w *secureCookieWriter) WriteHeader(statusCode int) {
	// 在写入状态码前，修改所有的Set-Cookie头
	w.modifySetCookieHeaders()
	w.ResponseWriter.WriteHeader(statusCode)
}

// modifySetCookieHeaders 修改所有Set-Cookie头以添加安全属性
func (w *secureCookieWriter) modifySetCookieHeaders() {
	headers := w.Header()
	cookies := headers["Set-Cookie"]

	if len(cookies) == 0 {
		return
	}

	secure := isHTTPS(w.context) || isProduction()

	// 修改每个cookie
	for i, cookie := range cookies {
		cookies[i] = w.addSecurityAttributes(cookie, secure)
	}

	// 更新headers
	headers["Set-Cookie"] = cookies
}

// addSecurityAttributes 为cookie字符串添加安全属性
func (w *secureCookieWriter) addSecurityAttributes(cookie string, secure bool) string {
	// 如果已经有HttpOnly，不重复添加
	if !strings.Contains(cookie, "HttpOnly") {
		cookie += "; HttpOnly"
	}

	// 如果是HTTPS或生产环境，添加Secure
	if secure && !strings.Contains(cookie, "Secure") {
		cookie += "; Secure"
	}

	// 添加SameSite属性
	if !strings.Contains(cookie, "SameSite") {
		if isProduction() {
			cookie += "; SameSite=Strict"
		} else {
			cookie += "; SameSite=Lax"
		}
	}

	return cookie
}

// InputValidation 输入验证中间件
func InputValidation() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 检查请求头中的内容类型
		contentType := c.GetHeader("Content-Type")
		if c.Request.Method == "POST" || c.Request.Method == "PUT" {
			if !strings.Contains(contentType, "application/json") &&
				!strings.Contains(contentType, "multipart/form-data") {
				c.JSON(http.StatusBadRequest, gin.H{
					"error": "Unsupported content type",
				})
				c.Abort()
				return
			}
		}

		c.Next()
	}
}

// Logging 安全日志中间件 - 记录请求和安全事件
func Logging() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 记录开始时间
		startTime := time.Now()

		// 获取请求信息
		method := c.Request.Method
		path := c.Request.URL.Path
		clientIP := c.ClientIP()
		userAgent := c.GetHeader("User-Agent")

		// 获取用户ID（如果已认证）
		userID := ""
		if uid, err := utils.GetUserIDFromToken(c.Request); err == nil {
			userID = uid
		}

		// 记录敏感操作的开始
		if isSensitiveOperation(method, path) {
			safeLog("info", "Sensitive operation started",
				"method", method,
				"path", path,
				"clientIP", clientIP,
				"userID", userID,
				"userAgent", userAgent,
				"timestamp", startTime.Format(time.RFC3339))
		}

		// 处理请求
		c.Next()

		// 计算处理时间
		duration := time.Since(startTime)
		status := c.Writer.Status()
		responseSize := c.Writer.Size()

		// 构建基础日志字段
		logFields := []interface{}{
			"method", method,
			"path", path,
			"status", status,
			"duration", duration.String(),
			"clientIP", clientIP,
			"responseSize", responseSize,
		}

		// 添加用户ID（如果存在）
		if userID != "" {
			logFields = append(logFields, "userID", userID)
		}

		// 根据状态码和操作类型记录不同级别的日志
		switch {
		case status >= 500:
			// 服务器错误 - 错误级别
			safeLog("error", "Server error", logFields...)

		case status >= 400:
			// 客户端错误 - 警告级别
			safeLog("warn", "Client error", logFields...)

		case isSensitiveOperation(method, path):
			// 敏感操作 - 信息级别
			safeLog("info", "Sensitive operation completed", logFields...)

		case duration > 5*time.Second:
			// 慢请求 - 警告级别
			safeLog("warn", "Slow request", logFields...)

		default:
			// 正常请求 - 调试级别
			safeLog("debug", "Request completed", logFields...)
		}

		// 记录特殊的安全事件
		logSecurityEvents(c, method, path, status, userID, clientIP)
	}
}

// logSecurityEvents 记录特殊的安全事件
func logSecurityEvents(c *gin.Context, method, path string, status int, userID, clientIP string) {
	// 记录认证失败
	if status == 401 {
		safeLog("warn", "Authentication failed",
			"method", method,
			"path", path,
			"clientIP", clientIP,
			"userAgent", c.GetHeader("User-Agent"))
	}

	// 记录权限拒绝
	if status == 403 {
		safeLog("warn", "Access denied",
			"method", method,
			"path", path,
			"userID", userID,
			"clientIP", clientIP)
	}

	// 记录可疑的多次失败尝试（简单实现）
	if status >= 400 && strings.Contains(path, "/auth/") {
		safeLog("warn", "Authentication endpoint error",
			"method", method,
			"path", path,
			"status", status,
			"clientIP", clientIP,
			"timestamp", time.Now().Format(time.RFC3339))
	}

	// 记录管理员操作
	if strings.Contains(path, "/admin/") && status < 400 {
		safeLog("info", "Admin operation",
			"method", method,
			"path", path,
			"userID", userID,
			"clientIP", clientIP,
			"status", status)
	}
}

// isSensitiveOperation 检查是否为敏感操作
func isSensitiveOperation(method, path string) bool {
	sensitivePatterns := []string{
		"/v1/admin/",
		"/v1/user/password",
		"/v1/user/verify",
		"/v1/auth/",
	}

	for _, pattern := range sensitivePatterns {
		if strings.Contains(path, pattern) {
			return true
		}
	}

	return method == "DELETE" || strings.Contains(path, "/delete")
}
