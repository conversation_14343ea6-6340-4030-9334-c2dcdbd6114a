package middleware

import (
	"context"
	"fmt"
	"net/http"
	"rent_report/entities"
	"rent_report/utils"

	"github.com/gin-gonic/gin"
	"github.com/real-rm/golog"
	"github.com/real-rm/gomongo"
	"go.mongodb.org/mongo-driver/bson"
)

// SmartResourceAuth 智能资源权限验证中间件
// 基于现有业务逻辑，支持多种资源类型的权限验证
func SmartResourceAuth(resourceType string) gin.HandlerFunc {
	return func(c *gin.Context) {
		// 1. 获取用户信息
		userID, err := utils.GetUserIDFromToken(c.Request)
		if err != nil {
			c.JSON(http.StatusUnauthorized, gin.H{"error": "Unauthorized"})
			c.Abort()
			return
		}

		user, err := entities.GetUserByID(c.Request.Context(), userID)
		if err != nil {
			c.<PERSON>(http.StatusUnauthorized, gin.H{"error": "User not found"})
			c.Abort()
			return
		}

		// 2. 管理员拥有所有权限
		if user.Role == entities.RoleAdmin {
			c.Next()
			return
		}

		// 3. 获取资源ID
		var resourceID string
		switch resourceType {
		case "property":
			resourceID = c.Param("propertyId")
		case "lease":
			resourceID = c.Param("leaseId")
		case "tenant":
			resourceID = c.Param("tenantId")
		case "user":
			resourceID = c.Param("id")
		default:
			resourceID = c.Param("id")
		}

		if resourceID == "" {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Resource ID is required"})
			c.Abort()
			return
		}

		// 4. 验证用户是否有权限访问该资源
		hasAccess, err := checkResourceAccess(c.Request.Context(), resourceType, resourceID, user)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to verify access"})
			c.Abort()
			return
		}

		if !hasAccess {
			c.JSON(http.StatusForbidden, gin.H{"error": "Access denied"})
			c.Abort()
			return
		}

		c.Next()
	}
}

// checkResourceAccess 检查用户是否有权限访问特定资源
func checkResourceAccess(ctx context.Context, resourceType, resourceID string, user *entities.User) (bool, error) {
	switch resourceType {
	case "property":
		return checkPropertyAccess(ctx, resourceID, user)
	case "lease":
		return checkLeaseAccess(ctx, resourceID, user)
	case "tenant":
		return checkTenantAccess(ctx, resourceID, user)
	case "user":
		return checkUserAccess(ctx, resourceID, user)
	default:
		return false, fmt.Errorf("unsupported resource type: %s", resourceType)
	}
}

// checkPropertyAccess 检查房产访问权限
func checkPropertyAccess(ctx context.Context, propertyID string, user *entities.User) (bool, error) {
	// 1. 检查用户是否是房产的所有者
	propColl := gomongo.Coll("rr", "properties")
	if propColl == nil {
		return false, fmt.Errorf("properties collection not initialized")
	}

	// 获取资源范围
	scope, err := entities.GetResourceScope(ctx, user.ID)
	if err != nil {
		return false, err
	}

	// 构建查询条件
	filter := scope.ToFilter()
	filter["_id"] = propertyID

	count, err := propColl.CountDocuments(ctx, filter)
	if err != nil {
		return false, err
	}

	if count > 0 {
		return true, nil
	}

	// 2. 检查用户是否通过租约关联到该房产
	leaseColl := gomongo.Coll("rr", "leases")
	if leaseColl == nil {
		return false, fmt.Errorf("leases collection not initialized")
	}

	leaseFilter := bson.M{
		"propId": propertyID,
		"$or": []bson.M{
			{"usrId": user.ID},    // 房东
			{"tenantId": user.ID}, // 租客
		},
	}

	leaseCount, err := leaseColl.CountDocuments(ctx, leaseFilter)
	if err != nil {
		return false, err
	}

	return leaseCount > 0, nil
}

// checkLeaseAccess 检查租约访问权限
func checkLeaseAccess(ctx context.Context, leaseID string, user *entities.User) (bool, error) {
	leaseColl := gomongo.Coll("rr", "leases")
	if leaseColl == nil {
		golog.Error("checkLeaseAccess: leases collection not initialized")
		return false, fmt.Errorf("leases collection not initialized")
	}

	var lease struct {
		UserID   string `bson:"usrId"`
		TenantID string `bson:"tenantId"`
		Ctnts    []struct {
			ID       string `bson:"_id"`
			TenantID string `bson:"tenantId"`
		} `bson:"ctnts"`
		Ptnts []struct {
			ID       string `bson:"_id"`
			TenantID string `bson:"tenantId"`
		} `bson:"ptnts"`
	}
	err := leaseColl.FindOne(ctx, bson.M{"_id": leaseID}).Decode(&lease)
	if err != nil {
		golog.Debug("checkLeaseAccess: lease not found", "leaseID", leaseID, "error", err)
		return false, nil // 不存在直接拒绝
	}

	golog.Debug("checkLeaseAccess: checking permissions",
		"userID", user.ID,
		"leaseID", leaseID,
		"leaseUserID", lease.UserID,
		"leaseTenantID", lease.TenantID,
		"ctntsCount", len(lease.Ctnts),
		"ptntsCount", len(lease.Ptnts))

	if lease.UserID == user.ID {
		golog.Debug("checkLeaseAccess: user is landlord, access granted", "userID", user.ID, "leaseID", leaseID)
		return true, nil // landlord
	}
	for _, t := range lease.Ctnts {
		if t.ID == user.ID || t.TenantID == user.ID {
			golog.Debug("checkLeaseAccess: user is in ctnts, access granted", "userID", user.ID, "leaseID", leaseID)
			return true, nil // 当前租客
		}
	}
	for _, t := range lease.Ptnts {
		if t.ID == user.ID || t.TenantID == user.ID {
			golog.Debug("checkLeaseAccess: user is in ptnts, access granted", "userID", user.ID, "leaseID", leaseID)
			return true, nil // 历史租客
		}
	}
	golog.Debug("checkLeaseAccess: access denied", "userID", user.ID, "leaseID", leaseID)
	return false, nil
}

// checkTenantAccess 检查租客访问权限
func checkTenantAccess(ctx context.Context, tenantID string, user *entities.User) (bool, error) {
	tenantColl := gomongo.Coll("rr", "tenants")
	if leaseColl := gomongo.Coll("rr", "leases"); leaseColl != nil {
		// 检查用户是否是该租客关联租约的房东
		filter := bson.M{
			"_id":   tenantID,
			"usrId": user.ID,
		}

		count, err := tenantColl.CountDocuments(ctx, filter)
		if err != nil {
			return false, err
		}

		if count > 0 {
			return true, nil
		}

		// 检查用户是否是该租客的租客（自己访问自己）
		tenantFilter := bson.M{
			"_id": tenantID,
		}

		var tenant struct {
			LeaseID string `bson:"leaseId"`
		}

		err = tenantColl.FindOne(ctx, tenantFilter).Decode(&tenant)
		if err != nil {
			return false, err
		}

		// 检查租约是否属于该用户
		leaseFilter := bson.M{
			"_id": tenant.LeaseID,
			"$or": []bson.M{
				{"ctnts.tenantId": user.ID},
				{"ptnts.tenantId": user.ID},
			},
		}

		leaseCount, err := leaseColl.CountDocuments(ctx, leaseFilter)
		if err != nil {
			return false, err
		}

		return leaseCount > 0, nil
	}

	return false, fmt.Errorf("tenants collection not initialized")
}

// checkUserAccess 检查用户访问权限
func checkUserAccess(ctx context.Context, targetUserID string, currentUser *entities.User) (bool, error) {
	// 用户只能访问自己的信息
	return currentUser.ID == targetUserID, nil
}

// RequireLandlord 要求房东权限的中间件
func RequireLandlord() gin.HandlerFunc {
	return func(c *gin.Context) {
		userID, err := utils.GetUserIDFromToken(c.Request)
		if err != nil {
			c.JSON(http.StatusUnauthorized, gin.H{"error": "Unauthorized"})
			c.Abort()
			return
		}

		user, err := entities.GetUserByID(c.Request.Context(), userID)
		if err != nil {
			c.JSON(http.StatusUnauthorized, gin.H{"error": "User not found"})
			c.Abort()
			return
		}

		// 管理员和房东都可以访问
		if user.Role != entities.RoleAdmin && user.ViewType != "landlord" {
			c.JSON(http.StatusForbidden, gin.H{"error": "Landlord access required"})
			c.Abort()
			return
		}

		c.Next()
	}
}

// RequireTenant 要求租客权限的中间件
func RequireTenant() gin.HandlerFunc {
	return func(c *gin.Context) {
		userID, err := utils.GetUserIDFromToken(c.Request)
		if err != nil {
			c.JSON(http.StatusUnauthorized, gin.H{"error": "Unauthorized"})
			c.Abort()
			return
		}

		user, err := entities.GetUserByID(c.Request.Context(), userID)
		if err != nil {
			c.JSON(http.StatusUnauthorized, gin.H{"error": "User not found"})
			c.Abort()
			return
		}

		// 管理员和租客都可以访问
		if user.Role != entities.RoleAdmin && user.ViewType != "tenant" {
			c.JSON(http.StatusForbidden, gin.H{"error": "Tenant access required"})
			c.Abort()
			return
		}

		c.Next()
	}
}
