package services

import (
	"context"
	"fmt"
	"rent_report/entities"
	"rent_report/utils"
	"time"

	"github.com/real-rm/golog"
	"github.com/real-rm/gomongo"
	"go.mongodb.org/mongo-driver/bson"
)

// processAutoPayments 处理自动支付
func processAutoPayments() error {
	golog.Info("Processing auto payments...")

	ctx := context.Background()
	today := time.Now()
	currentDay := today.Day()

	// 查找所有需要处理的活跃租约
	leases, err := getActiveLeasesForProcessing(ctx)
	if err != nil {
		golog.Error("Failed to get active auto pay leases", "error", err)
		return fmt.Errorf("failed to get active auto pay leases: %v", err)
	}

	golog.Info(fmt.Sprintf("Found %d active leases for processing (status=active, within valid date range)", len(leases)))

	var errors []string
	successCount := 0

	for _, lease := range leases {
		// 检查今天是否是该租约的租金到期日
		if lease.RentDueDay == currentDay {
			if lease.AutoPay {
				// Scenario 10: AutoPay=true，创建支付记录（现有逻辑）
				err := createAutoPayment(ctx, lease, today)
				if err != nil {
					errorMsg := fmt.Sprintf("Failed to create auto payment for lease %s: %v", lease.ID, err)
					golog.Error(errorMsg)
					errors = append(errors, errorMsg)
				} else {
					golog.Info("Auto payment created successfully",
						"leaseId", lease.ID,
						"amount", lease.RentAmount)
					successCount++
				}
			} else {
				// Scenario 10: AutoPay=false，只更新余额（新逻辑）
				err := updateBalanceOnDueDay(ctx, lease)
				if err != nil {
					errorMsg := fmt.Sprintf("Failed to update balance on due day for lease %s: %v", lease.ID, err)
					golog.Error(errorMsg)
					errors = append(errors, errorMsg)
				} else {
					golog.Info("Balance updated on due day",
						"leaseId", lease.ID,
						"monthlyRent", lease.RentAmount+lease.AdditionalMonthlyFees)
					successCount++
				}
			}
		}
	}

	golog.Info("Auto payment processing completed",
		"successCount", successCount,
		"errorCount", len(errors))

	if len(errors) > 0 {
		return fmt.Errorf("auto payment processing completed with %d errors: %v", len(errors), errors)
	}

	return nil
}

// RunAutoPaymentSchedulerCLI 通过命令行运行自动支付调度器（用于cron job）
func RunAutoPaymentSchedulerCLI() error {
	golog.Info("Starting auto payment scheduler CLI execution...")
	return processAutoPayments()
}

// getActiveLeasesForProcessing 获取所有需要处理的活跃租约（包括自动支付和余额更新）
func getActiveLeasesForProcessing(ctx context.Context) ([]entities.Lease, error) {
	leaseColl := gomongo.Coll("rr", "leases")
	if leaseColl == nil {
		return nil, fmt.Errorf("leases collection not initialized")
	}

	now := time.Now()
	today := now.Format("2006-01-02")

	// 查询条件：状态为active且在有效期内（包括自动支付和手动支付的租约）
	filter := bson.M{
		"status": "active",
		// 检查开始日期：租约已开始
		"startDt": bson.M{"$lte": today},
		// 检查结束日期：无结束日期或结束日期在今天或未来
		"$or": []bson.M{
			{"endDt": ""},                    // 无结束日期
			{"endDt": bson.M{"$gte": today}}, // 结束日期在今天或未来
		},
	}

	result, err := leaseColl.FindToArray(ctx, filter, gomongo.QueryOptions{})
	if err != nil {
		return nil, fmt.Errorf("failed to query leases: %v", err)
	}

	var leases []entities.Lease
	for _, item := range result {
		var lease entities.Lease
		data, err := bson.Marshal(item)
		if err != nil {
			golog.Error("Failed to marshal lease data", "error", err)
			continue
		}

		if err := bson.Unmarshal(data, &lease); err != nil {
			golog.Error("Failed to unmarshal lease data", "error", err)
			continue
		}

		leases = append(leases, lease)
	}

	return leases, nil
}

// isLeaseActiveOnDate 检查租约在指定日期是否有效
func isLeaseActiveOnDate(lease entities.Lease, date time.Time) bool {
	dateStr := date.Format("2006-01-02")

	// 检查开始日期：租约必须已开始
	if lease.StartDate != "" && lease.StartDate > dateStr {
		return false
	}

	// 检查结束日期：如果有结束日期，必须在结束日期之前或当天
	if lease.EndDate != "" && lease.EndDate < dateStr {
		return false
	}

	return true
}

// createAutoPayment 为指定租约创建自动支付记录
func createAutoPayment(ctx context.Context, lease entities.Lease, paymentDate time.Time) error {
	// 双重检查：确保租约在支付日期时仍然有效
	if !isLeaseActiveOnDate(lease, paymentDate) {
		golog.Info("Lease is not active on payment date, skipping auto payment",
			"leaseId", lease.ID,
			"paymentDate", paymentDate.Format("2006-01-02"),
			"startDate", lease.StartDate,
			"endDate", lease.EndDate)
		return nil
	}

	// 检查当月是否已经存在支付记录
	exists, err := checkPaymentExists(ctx, lease.ID, paymentDate)
	if err != nil {
		return fmt.Errorf("failed to check existing payment: %v", err)
	}

	if exists {
		golog.Info("Payment already exists for this month",
			"leaseId", lease.ID,
			"month", paymentDate.Format("2006-01"))
		return nil
	}

	// 创建支付记录
	payment := entities.TenantPayment{
		ID:               utils.GenerateNanoID(),
		LeaseID:          lease.ID,
		Amount:           lease.RentAmount,
		Date:             paymentDate,
		Notes:            "Auto payment",
		RemainingBalance: 0,
		Status:           "", // 根据你的需求，状态为空
		UserID:           lease.UserID,
	}

	err = payment.CreateAutoPayment(ctx)
	if err != nil {
		return fmt.Errorf("failed to create payment: %v", err)
	}

	return nil
}

// checkPaymentExists 检查指定租约在指定月份是否已存在支付记录
func checkPaymentExists(ctx context.Context, leaseID string, paymentDate time.Time) (bool, error) {
	paymentColl := gomongo.Coll("rr", "tenant_payments")
	if paymentColl == nil {
		return false, fmt.Errorf("tenant_payments collection not initialized")
	}

	// 计算当月的开始和结束时间
	year, month, _ := paymentDate.Date()
	monthStart := time.Date(year, month, 1, 0, 0, 0, 0, paymentDate.Location())
	monthEnd := monthStart.AddDate(0, 1, 0).Add(-time.Nanosecond)

	filter := bson.M{
		"leaseId": leaseID,
		"dt": bson.M{
			"$gte": monthStart,
			"$lte": monthEnd,
		},
	}

	count, err := paymentColl.CountDocuments(ctx, filter)
	if err != nil {
		return false, fmt.Errorf("failed to count payments: %v", err)
	}

	return count > 0, nil
}

// updateBalanceOnDueDay 在到期日更新余额（不创建支付记录）- Scenario 10
func updateBalanceOnDueDay(ctx context.Context, lease entities.Lease) error {
	// 检查当月是否已经处理过（避免重复处理）
	exists, err := checkBalanceUpdateExists(ctx, lease.ID, time.Now())
	if err != nil {
		return fmt.Errorf("failed to check existing balance update: %v", err)
	}

	if exists {
		golog.Info("Balance already updated for this month",
			"leaseId", lease.ID,
			"month", time.Now().Format("2006-01"))
		return nil
	}

	// 计算月租金（租金 + 附加费用）
	monthlyRent := lease.RentAmount + lease.AdditionalMonthlyFees

	// 创建资源范围（复用现有逻辑）
	scope := &entities.ResourceScope{
		OrganizationID: lease.OrganizationID,
		UserID:         lease.UserID,
	}

	// 增加余额（因为又欠了一个月的租金）
	err = updateLeaseBalanceForDueDay(ctx, lease.ID, monthlyRent, scope)
	if err != nil {
		return fmt.Errorf("failed to update lease balance: %v", err)
	}

	// 更新租约的最后处理日期（复用 lastPmtDt 字段来避免重复处理）
	err = updateLastProcessDate(ctx, lease.ID, time.Now(), scope)
	if err != nil {
		golog.Warn("Failed to update last process date", "error", err)
		// 不返回错误，因为余额已经更新成功
	}

	return nil
}

// checkBalanceUpdateExists 检查当月是否已经处理过余额更新
func checkBalanceUpdateExists(ctx context.Context, leaseID string, date time.Time) (bool, error) {
	// 检查当月是否已有支付记录
	paymentExists, err := checkPaymentExists(ctx, leaseID, date)
	if err != nil {
		return false, err
	}

	if paymentExists {
		return true, nil // 有支付记录，说明已经处理过了
	}

	// 检查是否已经更新过余额（通过 lastPmtDt 字段）
	balanceUpdateExists, err := checkLastProcessDate(ctx, leaseID, date)
	if err != nil {
		return false, err
	}

	return balanceUpdateExists, nil
}

// checkLastProcessDate 检查是否在指定日期已经处理过
func checkLastProcessDate(ctx context.Context, leaseID string, date time.Time) (bool, error) {
	leaseColl := gomongo.Coll("rr", "leases")
	if leaseColl == nil {
		return false, fmt.Errorf("leases collection not initialized")
	}

	filter := bson.M{"_id": leaseID}

	var lease entities.Lease
	err := leaseColl.FindOne(ctx, filter).Decode(&lease)
	if err != nil {
		return false, fmt.Errorf("failed to find lease: %v", err)
	}

	// 检查 lastPmtDt 是否是今天
	if lease.LastPaymentDate != "" {
		lastProcessDate, err := time.Parse("2006-01-02", lease.LastPaymentDate)
		if err == nil {
			// 如果最后处理日期是今天，说明已经处理过了
			if lastProcessDate.Format("2006-01-02") == date.Format("2006-01-02") {
				return true, nil
			}
		}
	}

	return false, nil
}

// updateLastProcessDate 更新最后处理日期
func updateLastProcessDate(ctx context.Context, leaseID string, date time.Time, scope *entities.ResourceScope) error {
	leaseColl := gomongo.Coll("rr", "leases")
	if leaseColl == nil {
		return fmt.Errorf("leases collection not initialized")
	}

	filter := scope.ToFilter()
	filter["_id"] = leaseID

	update := bson.M{
		"$set": bson.M{
			"lastPmtDt": date.Format("2006-01-02"),
		},
	}

	result, err := leaseColl.UpdateOne(ctx, filter, update)
	if err != nil {
		return fmt.Errorf("failed to update last process date: %v", err)
	}

	if result.MatchedCount == 0 {
		return fmt.Errorf("lease not found")
	}

	return nil
}

// updateLeaseBalanceForDueDay 更新租约余额（到期日处理）
func updateLeaseBalanceForDueDay(ctx context.Context, leaseID string, balanceChange float64, scope *entities.ResourceScope) error {
	leaseColl := gomongo.Coll("rr", "leases")
	if leaseColl == nil {
		return fmt.Errorf("leases collection not initialized")
	}

	// 获取当前租约信息
	filter := scope.ToFilter()
	filter["_id"] = leaseID

	var lease entities.Lease
	err := leaseColl.FindOne(ctx, filter).Decode(&lease)
	if err != nil {
		return fmt.Errorf("failed to find lease: %v", err)
	}

	// 计算新余额：当前余额 + 变化量（到期日增加租金）
	// 允许负数余额，表示多付款
	newBalance := lease.OwingBalance + balanceChange

	// 更新租约余额
	update := bson.M{
		"$set": bson.M{
			"owingBal": newBalance,
		},
	}

	result, err := leaseColl.UpdateOne(ctx, filter, update)
	if err != nil {
		return fmt.Errorf("failed to update lease balance: %v", err)
	}

	if result.MatchedCount == 0 {
		return fmt.Errorf("lease not found for balance update")
	}

	return nil
}
