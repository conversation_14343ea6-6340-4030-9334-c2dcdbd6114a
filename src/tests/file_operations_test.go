package tests

import (
	"os"
	"path/filepath"
	"rent_report/config"
	"rent_report/entities"
	"testing"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

// TestFileOperations 测试文件操作
func TestFileOperations(t *testing.T) {
	gin.SetMode(gin.TestMode)

	// 初始化 goupload
	err := config.InitializeGoupload()
	require.NoError(t, err, "Failed to initialize goupload")

	t.Run("TestFileCreationAndReading", testFileCreationAndReading)
	t.Run("TestFilePathGeneration", testFilePathGeneration)
}

// testFileCreationAndReading 测试文件创建和读取
func testFileCreationAndReading(t *testing.T) {
	// 测试数据
	testContent := []byte("This is a test file for goupload integration")
	testFileName := "test_lease_document.pdf"

	// 创建测试文件路径
	testFilePath := filepath.Join("uploads/lease_documents", testFileName)

	// 写入测试文件
	err := os.WriteFile(testFilePath, testContent, 0644)
	require.NoError(t, err, "Failed to create test file")
	defer os.Remove(testFilePath) // 清理

	// 验证文件存在
	assert.FileExists(t, testFilePath, "Test file should exist")

	// 读取文件内容
	readContent, err := os.ReadFile(testFilePath)
	require.NoError(t, err, "Failed to read test file")

	// 验证内容
	assert.Equal(t, testContent, readContent, "File content should match")

	// 获取文件信息
	fileInfo, err := os.Stat(testFilePath)
	require.NoError(t, err, "Failed to get file info")

	assert.Equal(t, testFileName, fileInfo.Name(), "File name should match")
	assert.Equal(t, int64(len(testContent)), fileInfo.Size(), "File size should match")

	t.Log("File creation and reading test completed successfully")
}

// testFilePathGeneration 测试文件路径生成
func testFilePathGeneration(t *testing.T) {
	// 测试不同类型的文件路径
	testCases := []struct {
		fileType string
		basePath string
	}{
		{"lease_documents", "uploads/lease_documents"},
		{"property_documents", "uploads/property_documents"},
		{"problem_reports", "uploads/problem_reports"},
	}

	for _, tc := range testCases {
		t.Run(tc.fileType, func(t *testing.T) {
			// 验证基础路径存在
			assert.DirExists(t, tc.basePath, "Base path should exist for %s", tc.fileType)

			// 测试在该路径下创建文件
			testFile := filepath.Join(tc.basePath, "test_"+tc.fileType+".txt")
			testContent := []byte("test content for " + tc.fileType)

			err := os.WriteFile(testFile, testContent, 0644)
			require.NoError(t, err, "Should be able to create file in %s", tc.basePath)
			defer os.Remove(testFile)

			// 验证文件可读
			readContent, err := os.ReadFile(testFile)
			require.NoError(t, err, "Should be able to read file from %s", tc.basePath)
			assert.Equal(t, testContent, readContent, "Content should match for %s", tc.fileType)
		})
	}

	t.Log("File path generation test completed successfully")
}

// TestFileSystemSecurity 测试文件系统安全性
func TestFileSystemSecurity(t *testing.T) {
	gin.SetMode(gin.TestMode)

	err := config.InitializeGoupload()
	require.NoError(t, err, "Failed to initialize goupload")

	t.Run("TestPathTraversalPrevention", testPathTraversalPrevention)
}

// testPathTraversalPrevention 测试路径遍历攻击防护
func testPathTraversalPrevention(t *testing.T) {
	// 测试危险的文件路径
	dangerousPaths := []string{
		"../../../etc/passwd",
		"..\\..\\..\\windows\\system32\\config\\sam",
		"/etc/shadow",
		"C:\\Windows\\System32\\config\\SAM",
	}

	basePath := "uploads/lease_documents"

	for _, dangerousPath := range dangerousPaths {
		// 构建完整路径
		fullPath := filepath.Join(basePath, dangerousPath)

		// 清理路径
		cleanPath := filepath.Clean(fullPath)

		// 验证清理后的路径仍在预期的基础目录内
		relPath, err := filepath.Rel(basePath, cleanPath)
		if err != nil || filepath.IsAbs(relPath) || len(relPath) > 0 && relPath[0] == '.' {
			t.Logf("Dangerous path correctly rejected: %s -> %s", dangerousPath, cleanPath)
		} else {
			// 这是一个安全的相对路径
			t.Logf("Path is safe: %s -> %s", dangerousPath, cleanPath)
		}
	}

	t.Log("Path traversal prevention test completed successfully")
}

// TestLargeFileHandling 测试大文件处理
func TestLargeFileHandling(t *testing.T) {
	gin.SetMode(gin.TestMode)

	err := config.InitializeGoupload()
	require.NoError(t, err, "Failed to initialize goupload")

	// 创建一个相对较大的测试文件 (1MB)
	largeContent := make([]byte, 1024*1024) // 1MB
	for i := range largeContent {
		largeContent[i] = byte(i % 256)
	}

	testFilePath := "uploads/lease_documents/large_test_file.bin"

	// 写入大文件
	err = os.WriteFile(testFilePath, largeContent, 0644)
	require.NoError(t, err, "Failed to create large test file")
	defer os.Remove(testFilePath)

	// 验证文件存在且大小正确
	fileInfo, err := os.Stat(testFilePath)
	require.NoError(t, err, "Failed to get large file info")
	assert.Equal(t, int64(len(largeContent)), fileInfo.Size(), "Large file size should match")

	// 读取并验证内容
	readContent, err := os.ReadFile(testFilePath)
	require.NoError(t, err, "Failed to read large test file")
	assert.Equal(t, largeContent, readContent, "Large file content should match")

	t.Log("Large file handling test completed successfully")
}

// TestDataStructureOperations 测试数据结构操作
func TestDataStructureOperations(t *testing.T) {
	gin.SetMode(gin.TestMode)

	err := config.InitializeGoupload()
	require.NoError(t, err, "Failed to initialize goupload")

	t.Run("TestLeaseDocumentStructure", testLeaseDocumentStructure)
	t.Run("TestPropertyDocumentStructure", testPropertyDocumentStructure)
	t.Run("TestProblemStructure", testProblemStructure)
}

// testLeaseDocumentStructure 测试租赁文档数据结构
func testLeaseDocumentStructure(t *testing.T) {
	// 创建测试文档结构
	doc := entities.LeaseDocument{
		ID:            "test_lease_doc_123",
		FilePath:      "/uploads/lease_documents/test_file.pdf",
		FileName:      "original_document.pdf",
		GeneratedName: "test_file.pdf",
		FileSize:      1024,
		FileType:      "application/pdf",
		UploadedAt:    time.Now(),
		UploadedBy:    "test_user_123",
	}

	// 验证字段
	assert.Equal(t, "test_lease_doc_123", doc.ID, "Document ID should match")
	assert.Equal(t, "/uploads/lease_documents/test_file.pdf", doc.FilePath, "File path should match")
	assert.Equal(t, "original_document.pdf", doc.FileName, "Original filename should match")
	assert.Equal(t, "test_file.pdf", doc.GeneratedName, "Generated filename should match")
	assert.Equal(t, int64(1024), doc.FileSize, "File size should match")
	assert.Equal(t, "application/pdf", doc.FileType, "File type should match")
	assert.Equal(t, "test_user_123", doc.UploadedBy, "Uploaded by should match")

	t.Log("Lease document structure test completed successfully")
}

// testPropertyDocumentStructure 测试物业文档数据结构
func testPropertyDocumentStructure(t *testing.T) {
	// 创建测试文档结构
	doc := entities.PropertyDocument{
		ID:            "test_property_doc_456",
		FilePath:      "/uploads/property_documents/property_file.pdf",
		FileName:      "property_document.pdf",
		GeneratedName: "property_file.pdf",
		FileSize:      2048,
		FileType:      "application/pdf",
		UploadedAt:    time.Now(),
		UploadedBy:    "test_user_456",
	}

	// 验证字段
	assert.Equal(t, "test_property_doc_456", doc.ID, "Document ID should match")
	assert.Equal(t, "/uploads/property_documents/property_file.pdf", doc.FilePath, "File path should match")
	assert.Equal(t, "property_document.pdf", doc.FileName, "Original filename should match")
	assert.Equal(t, "property_file.pdf", doc.GeneratedName, "Generated filename should match")
	assert.Equal(t, int64(2048), doc.FileSize, "File size should match")
	assert.Equal(t, "application/pdf", doc.FileType, "File type should match")
	assert.Equal(t, "test_user_456", doc.UploadedBy, "Uploaded by should match")

	t.Log("Property document structure test completed successfully")
}

// testProblemStructure 测试问题报告数据结构
func testProblemStructure(t *testing.T) {
	// 创建测试问题结构
	problem := entities.Problem{
		ID:            "test_problem_789",
		UserID:        "test_user_789",
		Type:          "bug",
		Subject:       "Test Problem",
		Description:   "This is a test problem description",
		FilePath:      "/uploads/problem_reports/problem_file.jpg",
		FileName:      "screenshot.jpg",
		GeneratedName: "problem_file.jpg",
		FileSize:      4096,
		FileType:      "image/jpeg",
		ContactEmail:  "<EMAIL>",
		Status:        "pending",
		CreatedAt:     time.Now().Format(time.RFC3339),
		UpdatedAt:     time.Now().Format(time.RFC3339),
	}

	// 验证字段
	assert.Equal(t, "test_problem_789", problem.ID, "Problem ID should match")
	assert.Equal(t, "test_user_789", problem.UserID, "User ID should match")
	assert.Equal(t, "bug", problem.Type, "Problem type should match")
	assert.Equal(t, "Test Problem", problem.Subject, "Subject should match")
	assert.Equal(t, "/uploads/problem_reports/problem_file.jpg", problem.FilePath, "File path should match")
	assert.Equal(t, "screenshot.jpg", problem.FileName, "Original filename should match")
	assert.Equal(t, "problem_file.jpg", problem.GeneratedName, "Generated filename should match")
	assert.Equal(t, int64(4096), problem.FileSize, "File size should match")
	assert.Equal(t, "image/jpeg", problem.FileType, "File type should match")
	assert.Equal(t, "pending", problem.Status, "Status should match")

	t.Log("Problem structure test completed successfully")
}
