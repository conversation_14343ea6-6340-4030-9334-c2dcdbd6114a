package main

import (
	"context"
	"fmt"
	"time"

	"github.com/real-rm/goconfig"
	"github.com/real-rm/golog"
	"github.com/real-rm/gomongo"
)

func main() {
	fmt.Println("🔍 实时监控租户通知表变化")
	fmt.Println("==================================================")

	// 初始化环境
	if err := initializeEnvironment(); err != nil {
		fmt.Printf("❌ 初始化环境失败: %v\n", err)
		return
	}

	ctx := context.Background()

	// 记录初始状态
	initialSnapshots := countSnapshots(ctx)
	initialQueue := countQueue(ctx)

	fmt.Printf("📊 初始状态:\n")
	fmt.Printf("  tenant_status_snapshots: %d 条记录\n", initialSnapshots)
	fmt.Printf("  email_notification_queue: %d 条记录\n", initialQueue)
	fmt.Printf("\n🔄 开始监控... (按 Ctrl+C 退出)\n")
	fmt.Printf("==================================================\n")

	// 持续监控
	ticker := time.NewTicker(2 * time.Second)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			currentSnapshots := countSnapshots(ctx)
			currentQueue := countQueue(ctx)

			// 检查是否有变化
			if currentSnapshots != initialSnapshots || currentQueue != initialQueue {
				fmt.Printf("\n🚨 检测到变化! [%s]\n", time.Now().Format("15:04:05"))
				
				if currentSnapshots != initialSnapshots {
					fmt.Printf("  📈 tenant_status_snapshots: %d -> %d (+%d)\n", 
						initialSnapshots, currentSnapshots, currentSnapshots-initialSnapshots)
					
					// 显示最新的快照记录
					showLatestSnapshots(ctx, 3)
				}
				
				if currentQueue != initialQueue {
					fmt.Printf("  📈 email_notification_queue: %d -> %d (+%d)\n", 
						initialQueue, currentQueue, currentQueue-initialQueue)
					
					// 显示最新的队列记录
					showLatestQueue(ctx, 3)
				}
				
				fmt.Printf("==================================================\n")
				
				// 更新基准值
				initialSnapshots = currentSnapshots
				initialQueue = currentQueue
			}
		}
	}
}

// initializeEnvironment 初始化环境
func initializeEnvironment() error {
	if err := goconfig.LoadConfig(); err != nil {
		return fmt.Errorf("failed to load config: %v", err)
	}

	if err := golog.InitLog(); err != nil {
		return fmt.Errorf("failed to initialize logging: %v", err)
	}

	if err := gomongo.InitMongoDB(); err != nil {
		return fmt.Errorf("failed to initialize MongoDB: %v", err)
	}

	return nil
}

// countSnapshots 统计快照表记录数
func countSnapshots(ctx context.Context) int64 {
	coll := gomongo.Coll("rr", "tenant_status_snapshots")
	if coll == nil {
		return 0
	}

	count, err := coll.CountDocuments(ctx, map[string]interface{}{})
	if err != nil {
		return 0
	}

	return count
}

// countQueue 统计队列表记录数
func countQueue(ctx context.Context) int64 {
	coll := gomongo.Coll("rr", "email_notification_queue")
	if coll == nil {
		return 0
	}

	count, err := coll.CountDocuments(ctx, map[string]interface{}{})
	if err != nil {
		return 0
	}

	return count
}

// showLatestSnapshots 显示最新的快照记录
func showLatestSnapshots(ctx context.Context, limit int) {
	coll := gomongo.Coll("rr", "tenant_status_snapshots")
	if coll == nil {
		return
	}

	cursor, err := coll.Find(ctx, map[string]interface{}{}, map[string]interface{}{
		"sort":  map[string]interface{}{"createdAt": -1},
		"limit": limit,
	})
	if err != nil {
		return
	}
	defer cursor.Close(ctx)

	var snapshots []map[string]interface{}
	if err = cursor.All(ctx, &snapshots); err != nil {
		return
	}

	fmt.Printf("    📋 最新快照记录:\n")
	for i, snapshot := range snapshots {
		tenantEmail, _ := snapshot["tenantEmail"].(string)
		leaseId, _ := snapshot["leaseId"].(string)
		previousStatus, _ := snapshot["previousStatus"].(string)
		currentStatus, _ := snapshot["currentStatus"].(string)
		processed, _ := snapshot["processed"].(bool)
		
		fmt.Printf("      %d. Email: %s, LeaseID: %s, %s -> %s, Processed: %v\n", 
			i+1, tenantEmail, leaseId, previousStatus, currentStatus, processed)
	}
}

// showLatestQueue 显示最新的队列记录
func showLatestQueue(ctx context.Context, limit int) {
	coll := gomongo.Coll("rr", "email_notification_queue")
	if coll == nil {
		return
	}

	cursor, err := coll.Find(ctx, map[string]interface{}{}, map[string]interface{}{
		"sort":  map[string]interface{}{"createdAt": -1},
		"limit": limit,
	})
	if err != nil {
		return
	}
	defer cursor.Close(ctx)

	var notifications []map[string]interface{}
	if err = cursor.All(ctx, &notifications); err != nil {
		return
	}

	fmt.Printf("    📋 最新队列记录:\n")
	for i, notif := range notifications {
		tenantEmail, _ := notif["tenantEmail"].(string)
		leaseId, _ := notif["leaseId"].(string)
		notificationType, _ := notif["notificationType"].(string)
		processed, _ := notif["processed"].(bool)
		
		var scheduledTime string
		if st, ok := notif["scheduledTime"]; ok {
			if t, ok := st.(time.Time); ok {
				scheduledTime = t.Format("15:04:05")
			}
		}
		
		fmt.Printf("      %d. Email: %s, LeaseID: %s, Type: %s, Scheduled: %s, Processed: %v\n", 
			i+1, tenantEmail, leaseId, notificationType, scheduledTime, processed)
	}
}
