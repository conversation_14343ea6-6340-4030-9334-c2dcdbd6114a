package main

import (
	"fmt"
	"sync"
	"time"
)

// 模拟租户邮件信息结构
type TenantEmailInfo struct {
	Email     string
	FirstName string
}

// LandlordInfo 房东信息结构
type LandlordInfo struct {
	FirstName       string
	LastName        string
	PropertyAddress string
}

// 模拟邮件发送函数
func mockSendRentReportingNotificationEmail(tenantEmail, tenantFirstName, landlordFirstName, landlordLastName, propertyAddress string) error {
	// 模拟邮件发送延迟
	time.Sleep(100 * time.Millisecond)

	// 构建房东全名
	landlordFullName := fmt.Sprintf("%s %s", landlordFirstName, landlordLastName)
	if landlordLastName == "" {
		landlordFullName = landlordFirstName
	}

	// 模拟获取基础URL
	baseURL := "http://localhost:8089"
	signInURL := fmt.Sprintf("%s/pages/signup/", baseURL)

	fmt.Printf("📧 发送邮件到: %s (%s)\n", tenantFirstName, tenantEmail)
	fmt.Printf("   主题: Rent reporting has been enabled for your lease\n")
	fmt.Printf("   内容: Hi %s, Your landlord %s at %s has enabled rent reporting...\n", tenantFirstName, landlordFullName, propertyAddress)
	fmt.Printf("   登录链接: %s\n", signInURL)

	return nil
}

// 模拟异步邮件发送功能
func mockSendRentReportingNotificationEmailsAsync(tenants []TenantEmailInfo, landlordInfo LandlordInfo) {
	if len(tenants) == 0 {
		return
	}

	// 使用 goroutine 异步发送邮件
	go func() {
		var wg sync.WaitGroup

		// 限制并发数量，避免过多的并发邮件发送
		semaphore := make(chan struct{}, 5) // 最多5个并发邮件发送

		fmt.Printf("🚀 开始异步发送 %d 封邮件给租户...\n", len(tenants))

		for _, tenant := range tenants {
			wg.Add(1)
			go func(t TenantEmailInfo) {
				defer wg.Done()

				// 获取信号量
				semaphore <- struct{}{}
				defer func() { <-semaphore }()

				// 发送邮件
				err := mockSendRentReportingNotificationEmail(t.Email, t.FirstName, landlordInfo.FirstName, landlordInfo.LastName, landlordInfo.PropertyAddress)
				if err != nil {
					// 记录错误但不影响其他邮件发送
					fmt.Printf("❌ 发送邮件失败 %s (%s): %v\n", t.FirstName, t.Email, err)
				} else {
					fmt.Printf("✅ 邮件发送成功 %s (%s)\n", t.FirstName, t.Email)
				}
			}(tenant)
		}

		// 等待所有邮件发送完成
		wg.Wait()
		fmt.Printf("🎉 完成发送 %d 封租金报告通知邮件 (房东: %s %s, 属性地址: %s)\n",
			len(tenants), landlordInfo.FirstName, landlordInfo.LastName, landlordInfo.PropertyAddress)
	}()
}

// 测试租金报告状态变化逻辑
func testRentReportingLogic() {
	fmt.Println("=== 测试租金报告状态变化逻辑 ===")

	scenarios := []struct {
		name     string
		original bool
		updated  bool
		expected bool // 是否应该发送邮件
	}{
		{"关闭 -> 开启", false, true, true},
		{"开启 -> 关闭", true, false, false},
		{"关闭 -> 关闭", false, false, false},
		{"开启 -> 开启", true, true, false},
	}

	for _, scenario := range scenarios {
		fmt.Printf("\n场景: %s\n", scenario.name)
		fmt.Printf("  原始状态: %v\n", scenario.original)
		fmt.Printf("  更新状态: %v\n", scenario.updated)

		shouldSend := !scenario.original && scenario.updated
		fmt.Printf("  应该发送邮件: %v\n", shouldSend)

		if shouldSend == scenario.expected {
			fmt.Printf("  ✅ 逻辑正确\n")
		} else {
			fmt.Printf("  ❌ 逻辑错误\n")
		}
	}
}

// 测试异步邮件发送
func testAsyncEmailSending() {
	fmt.Println("\n=== 测试异步邮件发送 ===")

	// 创建测试租户数据
	tenants := []TenantEmailInfo{
		{Email: "<EMAIL>", FirstName: "Alice"},
		{Email: "<EMAIL>", FirstName: "Bob"},
		{Email: "<EMAIL>", FirstName: "Charlie"},
		{Email: "<EMAIL>", FirstName: "Diana"},
		{Email: "<EMAIL>", FirstName: "Eve"},
	}

	landlordInfo := LandlordInfo{
		FirstName:       "John",
		LastName:        "Smith",
		PropertyAddress: "123 Main Street, Toronto, ON M5V 3A8, Canada",
	}

	fmt.Printf("租户数量: %d\n", len(tenants))
	fmt.Printf("房东姓名: %s %s\n", landlordInfo.FirstName, landlordInfo.LastName)
	fmt.Printf("属性地址: %s\n", landlordInfo.PropertyAddress)

	// 开始异步发送
	mockSendRentReportingNotificationEmailsAsync(tenants, landlordInfo)

	// 等待异步操作完成
	fmt.Println("⏳ 等待异步邮件发送完成...")
	time.Sleep(2 * time.Second)
}

// 测试邮件模板内容
func testEmailTemplate() {
	fmt.Println("\n=== 测试邮件模板内容 ===")

	tenantEmail := "<EMAIL>"
	tenantFirstName := "Alice"
	landlordFirstName := "John"
	landlordLastName := "Smith"
	propertyAddress := "123 Main Street, Toronto, ON M5V 3A8, Canada"

	fmt.Printf("测试邮件参数:\n")
	fmt.Printf("  租户邮箱: %s\n", tenantEmail)
	fmt.Printf("  租户姓名: %s\n", tenantFirstName)
	fmt.Printf("  房东姓名: %s %s\n", landlordFirstName, landlordLastName)
	fmt.Printf("  属性地址: %s\n", propertyAddress)

	fmt.Printf("\n预期邮件内容:\n")
	fmt.Printf("  主题: Rent reporting has been enabled for your lease\n")
	fmt.Printf("  正文: Hi %s, Your landlord %s %s at %s has enabled rent reporting for your lease...\n",
		tenantFirstName, landlordFirstName, landlordLastName, propertyAddress)

	// 发送测试邮件
	err := mockSendRentReportingNotificationEmail(tenantEmail, tenantFirstName, landlordFirstName, landlordLastName, propertyAddress)
	if err != nil {
		fmt.Printf("❌ 邮件发送失败: %v\n", err)
	} else {
		fmt.Printf("✅ 邮件模板测试成功\n")
	}
}

func main() {
	fmt.Println("🚀 开始租金报告邮件通知功能测试")
	fmt.Println("==================================================")

	testRentReportingLogic()
	testEmailTemplate()
	testAsyncEmailSending()

	fmt.Println("\n==================================================")
	fmt.Println("🎉 所有测试完成")
}
