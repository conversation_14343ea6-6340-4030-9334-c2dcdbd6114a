package main

import (
	"fmt"
	"time"
)

// 完整的租约结构体
type CompleteLease struct {
	ID                    string
	Status                string
	AutoPay               bool
	StartDate             string
	EndDate               string
	RentDueDay            int
	RentAmount            float64
	AdditionalMonthlyFees float64
	OwingBalance          float64
	LastPaymentDate       string
	UserID                string
	OrganizationID        string
}

// 支付记录结构体
type PaymentRecord struct {
	ID      string
	LeaseID string
	Amount  float64
	Date    string
	Type    string // "auto_payment" 或 "balance_update"
}

// 模拟数据存储
var allLeases []CompleteLease
var allPayments []PaymentRecord

// 初始化测试数据
func initTestData() {
	today := time.Now()
	currentDay := today.Day()

	allLeases = []CompleteLease{
		{
			ID:                    "lease-autopay-due-today",
			Status:                "active",
			AutoPay:               true,
			StartDate:             "2025-01-01",
			EndDate:               "",
			RentDueDay:            currentDay,
			RentAmount:            1000.0,
			AdditionalMonthlyFees: 200.0,
			OwingBalance:          1200.0,
			LastPaymentDate:       "",
			UserID:                "user-1",
			OrganizationID:        "org-1",
		},
		{
			ID:                    "lease-manual-due-today",
			Status:                "active",
			AutoPay:               false,
			StartDate:             "2025-02-01",
			EndDate:               "",
			RentDueDay:            currentDay,
			RentAmount:            800.0,
			AdditionalMonthlyFees: 150.0,
			OwingBalance:          950.0,
			LastPaymentDate:       "",
			UserID:                "user-2",
			OrganizationID:        "org-1",
		},
		{
			ID:                    "lease-autopay-not-due",
			Status:                "active",
			AutoPay:               true,
			StartDate:             "2025-01-01",
			EndDate:               "",
			RentDueDay:            (currentDay % 28) + 1,
			RentAmount:            1200.0,
			AdditionalMonthlyFees: 300.0,
			OwingBalance:          1500.0,
			LastPaymentDate:       "",
			UserID:                "user-3",
			OrganizationID:        "org-1",
		},
		{
			ID:                    "lease-ended",
			Status:                "ended",
			AutoPay:               true,
			StartDate:             "2024-01-01",
			EndDate:               "2024-12-31",
			RentDueDay:            currentDay,
			RentAmount:            900.0,
			AdditionalMonthlyFees: 100.0,
			OwingBalance:          0.0,
			LastPaymentDate:       "",
			UserID:                "user-4",
			OrganizationID:        "org-1",
		},
		{
			ID:                    "lease-already-processed",
			Status:                "active",
			AutoPay:               false,
			StartDate:             "2025-01-01",
			EndDate:               "",
			RentDueDay:            currentDay,
			RentAmount:            700.0,
			AdditionalMonthlyFees: 100.0,
			OwingBalance:          800.0,
			LastPaymentDate:       today.Format("2006-01-02"), // 今天已处理
			UserID:                "user-5",
			OrganizationID:        "org-1",
		},
	}

	allPayments = []PaymentRecord{}
}

// 模拟完整的处理流程
func processAllLeasesForToday(today time.Time) map[string]interface{} {
	currentDay := today.Day()

	stats := map[string]interface{}{
		"totalLeases":        len(allLeases),
		"eligibleLeases":     0,
		"autoPaymentCreated": 0,
		"balanceUpdated":     0,
		"alreadyProcessed":   0,
		"skippedNotDue":      0,
		"skippedInactive":    0,
		"errors":             []string{},
	}

	for i := range allLeases {
		lease := &allLeases[i]

		// 检查租约状态
		if lease.Status != "active" {
			stats["skippedInactive"] = stats["skippedInactive"].(int) + 1
			continue
		}

		// 检查是否在有效期内
		if !isLeaseValidOnDate(*lease, today) {
			stats["skippedInactive"] = stats["skippedInactive"].(int) + 1
			continue
		}

		stats["eligibleLeases"] = stats["eligibleLeases"].(int) + 1

		// 检查是否是到期日
		if lease.RentDueDay != currentDay {
			stats["skippedNotDue"] = stats["skippedNotDue"].(int) + 1
			continue
		}

		// 检查是否已经处理过
		if isAlreadyProcessed(*lease, today) {
			stats["alreadyProcessed"] = stats["alreadyProcessed"].(int) + 1
			continue
		}

		// 根据 AutoPay 设置进行处理
		if lease.AutoPay {
			// 创建自动支付
			payment := PaymentRecord{
				ID:      fmt.Sprintf("payment-%s-%s", lease.ID, today.Format("2006-01")),
				LeaseID: lease.ID,
				Amount:  lease.RentAmount + lease.AdditionalMonthlyFees,
				Date:    today.Format("2006-01-02"),
				Type:    "auto_payment",
			}

			allPayments = append(allPayments, payment)

			// 更新余额
			lease.OwingBalance -= payment.Amount
			// 允许负数余额，表示多付款

			// 更新最后处理日期
			lease.LastPaymentDate = today.Format("2006-01-02")

			stats["autoPaymentCreated"] = stats["autoPaymentCreated"].(int) + 1
		} else {
			// 只更新余额
			monthlyRent := lease.RentAmount + lease.AdditionalMonthlyFees
			lease.OwingBalance += monthlyRent

			// 更新最后处理日期
			lease.LastPaymentDate = today.Format("2006-01-02")

			stats["balanceUpdated"] = stats["balanceUpdated"].(int) + 1
		}
	}

	return stats
}

// 检查租约是否在指定日期有效
func isLeaseValidOnDate(lease CompleteLease, date time.Time) bool {
	dateStr := date.Format("2006-01-02")

	// 检查开始日期
	if lease.StartDate != "" && lease.StartDate > dateStr {
		return false
	}

	// 检查结束日期
	if lease.EndDate != "" && lease.EndDate < dateStr {
		return false
	}

	return true
}

// 检查是否已经处理过
func isAlreadyProcessed(lease CompleteLease, date time.Time) bool {
	// 检查是否有当月的支付记录
	targetMonth := date.Format("2006-01")
	for _, payment := range allPayments {
		if payment.LeaseID == lease.ID {
			paymentDate, err := time.Parse("2006-01-02", payment.Date)
			if err == nil && paymentDate.Format("2006-01") == targetMonth {
				return true
			}
		}
	}

	// 检查最后处理日期
	if lease.LastPaymentDate != "" {
		lastProcessDate, err := time.Parse("2006-01-02", lease.LastPaymentDate)
		if err == nil && lastProcessDate.Format("2006-01-02") == date.Format("2006-01-02") {
			return true
		}
	}

	return false
}

// TestScenario10Complete 完整的 Scenario 10 测试
func TestScenario10Complete() {
	fmt.Println("=== Scenario 10 完整集成测试 ===")

	today := time.Now()
	fmt.Printf("测试日期: %s (第%d天)\n\n", today.Format("2006-01-02"), today.Day())

	// 初始化测试数据
	initTestData()

	fmt.Printf("=== 初始数据 ===\n")
	fmt.Printf("总租约数: %d\n", len(allLeases))

	for i, lease := range allLeases {
		fmt.Printf("租约 %d: %s\n", i+1, lease.ID)
		fmt.Printf("  状态: %s, AutoPay: %t, 到期日: %d号\n",
			lease.Status, lease.AutoPay, lease.RentDueDay)
		fmt.Printf("  余额: $%.2f, 月租: $%.2f\n",
			lease.OwingBalance, lease.RentAmount+lease.AdditionalMonthlyFees)
		fmt.Printf("  最后处理: %s\n", lease.LastPaymentDate)
	}

	// 执行处理
	fmt.Printf("\n=== 执行自动处理 ===\n")
	stats := processAllLeasesForToday(today)

	// 显示处理结果
	fmt.Printf("处理统计:\n")
	fmt.Printf("  总租约数: %d\n", stats["totalLeases"])
	fmt.Printf("  符合条件: %d\n", stats["eligibleLeases"])
	fmt.Printf("  创建自动支付: %d\n", stats["autoPaymentCreated"])
	fmt.Printf("  更新余额: %d\n", stats["balanceUpdated"])
	fmt.Printf("  已处理过: %d\n", stats["alreadyProcessed"])
	fmt.Printf("  未到期: %d\n", stats["skippedNotDue"])
	fmt.Printf("  非活跃: %d\n", stats["skippedInactive"])

	// 显示处理后的状态
	fmt.Printf("\n=== 处理后状态 ===\n")
	for i, lease := range allLeases {
		fmt.Printf("租约 %d: %s\n", i+1, lease.ID)
		fmt.Printf("  处理后余额: $%.2f\n", lease.OwingBalance)
		fmt.Printf("  最后处理日期: %s\n", lease.LastPaymentDate)
	}

	// 显示支付记录
	fmt.Printf("\n=== 生成的支付记录 ===\n")
	fmt.Printf("支付记录数: %d\n", len(allPayments))
	for i, payment := range allPayments {
		fmt.Printf("支付 %d: %s\n", i+1, payment.ID)
		fmt.Printf("  租约: %s, 金额: $%.2f\n", payment.LeaseID, payment.Amount)
		fmt.Printf("  日期: %s, 类型: %s\n", payment.Date, payment.Type)
	}

	// 验证业务逻辑
	fmt.Printf("\n=== 业务逻辑验证 ===\n")

	// 验证1：AutoPay=true 的租约应该创建支付记录
	autoPayLeaseProcessed := false
	for _, lease := range allLeases {
		if lease.ID == "lease-autopay-due-today" {
			// 检查是否有对应的支付记录
			hasPayment := false
			for _, payment := range allPayments {
				if payment.LeaseID == lease.ID {
					hasPayment = true
					break
				}
			}

			if hasPayment && lease.LastPaymentDate == today.Format("2006-01-02") {
				fmt.Printf("✅ AutoPay=true 租约正确创建支付记录\n")
				autoPayLeaseProcessed = true
			} else {
				fmt.Printf("❌ AutoPay=true 租约未正确处理\n")
			}
			break
		}
	}

	// 验证2：AutoPay=false 的租约应该只更新余额
	manualPayLeaseProcessed := false
	for _, lease := range allLeases {
		if lease.ID == "lease-manual-due-today" {
			// 检查是否没有支付记录但余额增加了
			hasPayment := false
			for _, payment := range allPayments {
				if payment.LeaseID == lease.ID {
					hasPayment = true
					break
				}
			}

			expectedBalance := 950.0 + 950.0 // 原余额 + 月租
			if !hasPayment && lease.OwingBalance == expectedBalance &&
				lease.LastPaymentDate == today.Format("2006-01-02") {
				fmt.Printf("✅ AutoPay=false 租约正确更新余额\n")
				manualPayLeaseProcessed = true
			} else {
				fmt.Printf("❌ AutoPay=false 租约未正确处理\n")
			}
			break
		}
	}

	// 验证3：已处理的租约应该被跳过
	alreadyProcessedSkipped := false
	for _, lease := range allLeases {
		if lease.ID == "lease-already-processed" {
			// 余额应该没有变化
			if lease.OwingBalance == 800.0 {
				fmt.Printf("✅ 已处理租约正确跳过\n")
				alreadyProcessedSkipped = true
			} else {
				fmt.Printf("❌ 已处理租约未正确跳过\n")
			}
			break
		}
	}

	// 验证4：非活跃租约应该被跳过
	inactiveLeaseSkipped := false
	for _, lease := range allLeases {
		if lease.ID == "lease-ended" {
			// 余额应该没有变化
			if lease.OwingBalance == 0.0 && lease.LastPaymentDate == "" {
				fmt.Printf("✅ 非活跃租约正确跳过\n")
				inactiveLeaseSkipped = true
			} else {
				fmt.Printf("❌ 非活跃租约未正确跳过\n")
			}
			break
		}
	}

	// 总体验证
	fmt.Printf("\n=== 总体验证结果 ===\n")
	allTestsPassed := autoPayLeaseProcessed && manualPayLeaseProcessed &&
		alreadyProcessedSkipped && inactiveLeaseSkipped

	if allTestsPassed {
		fmt.Printf("🎉 所有测试通过！Scenario 10 实施成功！\n")
	} else {
		fmt.Printf("❌ 部分测试失败，需要检查实施\n")
	}

	fmt.Printf("\n=== Scenario 10 完整测试完成 ===\n")
}

func main() {
	TestScenario10Complete()
}
