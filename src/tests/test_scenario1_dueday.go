package main

import (
	"fmt"
	"time"
)

// 简化的租约结构体用于测试
type TestLease struct {
	RentAmount            float64
	AdditionalMonthlyFees float64
	StartDate             string
	RentDueDay            int
	OwingBalance          float64
}

// 模拟基于到期日的月数计算
func calculateMonthsBasedOnDueDay(startDate, currentDate time.Time, rentDueDay int) int {
	// 确保 start 在 current 之前
	if startDate.After(currentDate) {
		return 0
	}

	// 如果没有设置到期日，默认为1号
	if rentDueDay < 1 || rentDueDay > 31 {
		rentDueDay = 1
	}

	months := 0
	
	// 从开始月份逐月检查到当前月份
	checkDate := time.Date(startDate.Year(), startDate.Month(), 1, 0, 0, 0, 0, startDate.Location())
	currentMonth := time.Date(currentDate.Year(), currentDate.Month(), 1, 0, 0, 0, 0, currentDate.Location())
	
	for !checkDate.After(currentMonth) {
		// 计算这个月的租金到期日
		dueDate := time.Date(checkDate.Year(), checkDate.Month(), rentDueDay, 0, 0, 0, 0, checkDate.Location())
		
		// 如果到期日超过了该月的最后一天，调整为该月最后一天
		lastDayOfMonth := time.Date(checkDate.Year(), checkDate.Month()+1, 0, 0, 0, 0, 0, checkDate.Location()).Day()
		if rentDueDay > lastDayOfMonth {
			dueDate = time.Date(checkDate.Year(), checkDate.Month(), lastDayOfMonth, 0, 0, 0, 0, checkDate.Location())
		}
		
		// 如果当前日期已经过了这个月的租金到期日，则计算这个月的租金
		if currentDate.After(dueDate) || currentDate.Equal(dueDate) {
			months++
		}
		
		// 移动到下一个月
		checkDate = checkDate.AddDate(0, 1, 0)
	}
	
	return months
}

// 模拟 calculateInitialOwingBalance 函数
func calculateInitialOwingBalance(lease *TestLease) float64 {
	// 解析开始日期
	startDate, err := time.Parse("2006-01-02", lease.StartDate)
	if err != nil {
		// 如果日期解析失败，返回一个月的租金
		return lease.RentAmount + lease.AdditionalMonthlyFees
	}

	now := time.Now()
	monthlyRent := lease.RentAmount + lease.AdditionalMonthlyFees

	// 如果开始日期在未来，余额为0（还没开始）
	if startDate.After(now) {
		return 0.0
	}

	// 计算从开始日期到当前日期的月数（基于租金到期日）
	months := calculateMonthsBasedOnDueDay(startDate, now, lease.RentDueDay)
	
	// 至少计算1个月（如果租约已开始）
	if months < 1 {
		months = 1
	}

	totalBalance := float64(months) * monthlyRent
	
	return totalBalance
}

// TestScenario1DueDay 测试基于到期日的余额计算
func TestScenario1DueDay() {
	fmt.Println("=== 测试 Scenario 1：基于租金到期日的余额计算 ===")
	
	now := time.Now()
	fmt.Printf("当前时间: %s\n\n", now.Format("2006-01-02"))

	// 测试案例：不同的到期日和开始日期
	testCases := []struct {
		name        string
		startDate   string
		rentDueDay  int
		rent        float64
		fees        float64
		description string
	}{
		{
			name:        "当月开始，5号到期，今天3号",
			startDate:   "2025-07-01",
			rentDueDay:  5,
			rent:        1000.0,
			fees:        200.0,
			description: "租约7月1日开始，每月5号到期，今天7月2日（还没到期）",
		},
		{
			name:        "当月开始，1号到期，今天2号",
			startDate:   "2025-07-01",
			rentDueDay:  1,
			rent:        1000.0,
			fees:        200.0,
			description: "租约7月1日开始，每月1号到期，今天7月2日（已过期）",
		},
		{
			name:        "上月开始，5号到期",
			startDate:   "2025-06-01",
			rentDueDay:  5,
			rent:        1000.0,
			fees:        200.0,
			description: "租约6月1日开始，每月5号到期",
		},
		{
			name:        "3个月前开始，15号到期",
			startDate:   "2025-04-01",
			rentDueDay:  15,
			rent:        800.0,
			fees:        150.0,
			description: "租约4月1日开始，每月15号到期",
		},
		{
			name:        "2月开始，31号到期（2月没31号）",
			startDate:   "2025-02-01",
			rentDueDay:  31,
			rent:        1200.0,
			fees:        300.0,
			description: "租约2月1日开始，每月31号到期（2月调整为28号）",
		},
	}

	for i, tc := range testCases {
		fmt.Printf("测试案例 %d: %s\n", i+1, tc.name)
		fmt.Printf("  %s\n", tc.description)
		
		lease := &TestLease{
			RentAmount:            tc.rent,
			AdditionalMonthlyFees: tc.fees,
			StartDate:             tc.startDate,
			RentDueDay:            tc.rentDueDay,
		}

		fmt.Printf("  开始日期: %s\n", tc.startDate)
		fmt.Printf("  租金到期日: 每月%d号\n", tc.rentDueDay)
		fmt.Printf("  月租金: $%.2f + 附加费: $%.2f = $%.2f/月\n", 
			tc.rent, tc.fees, tc.rent+tc.fees)

		// 计算月数
		startDate, _ := time.Parse("2006-01-02", tc.startDate)
		months := calculateMonthsBasedOnDueDay(startDate, now, tc.rentDueDay)

		// 应用新的 Scenario 1 逻辑
		lease.OwingBalance = calculateInitialOwingBalance(lease)

		fmt.Printf("  应付月数: %d 个月\n", months)
		fmt.Printf("  初始余额: $%.2f\n", lease.OwingBalance)

		// 详细显示每个月的到期情况
		fmt.Printf("  详细计算:\n")
		checkDate := time.Date(startDate.Year(), startDate.Month(), 1, 0, 0, 0, 0, startDate.Location())
		currentMonth := time.Date(now.Year(), now.Month(), 1, 0, 0, 0, 0, now.Location())
		
		for !checkDate.After(currentMonth) {
			dueDay := tc.rentDueDay
			lastDayOfMonth := time.Date(checkDate.Year(), checkDate.Month()+1, 0, 0, 0, 0, 0, checkDate.Location()).Day()
			if dueDay > lastDayOfMonth {
				dueDay = lastDayOfMonth
			}
			
			dueDate := time.Date(checkDate.Year(), checkDate.Month(), dueDay, 0, 0, 0, 0, checkDate.Location())
			isPaid := now.After(dueDate) || now.Equal(dueDate)
			
			fmt.Printf("    %s: 到期日%d号 -> %s\n", 
				checkDate.Format("2006-01"), dueDay, 
				map[bool]string{true: "✅ 需支付", false: "⏳ 未到期"}[isPaid])
			
			checkDate = checkDate.AddDate(0, 1, 0)
		}

		fmt.Println()
	}

	fmt.Printf("=== Scenario 1 基于到期日测试完成 ===\n")
}

func main() {
	TestScenario1DueDay()
}
