package main

import (
	"fmt"
	"time"
)

// 简化的租约结构体用于测试
type TestLease struct {
	RentAmount            float64
	AdditionalMonthlyFees float64
	StartDate             string
	OwingBalance          float64
}

// 模拟 calculateInitialOwingBalance 函数
func calculateInitialOwingBalance(lease *TestLease) float64 {
	// 解析开始日期
	startDate, err := time.Parse("2006-01-02", lease.StartDate)
	if err != nil {
		// 如果日期解析失败，返回一个月的租金
		return lease.RentAmount + lease.AdditionalMonthlyFees
	}

	now := time.Now()
	monthlyRent := lease.RentAmount + lease.AdditionalMonthlyFees

	// 如果开始日期在未来，余额为0（还没开始）
	if startDate.After(now) {
		return 0.0
	}

	// 计算从开始日期到当前日期的月数
	months := calculateMonthsBetween(startDate, now)

	// 至少计算1个月（当前月）
	if months < 1 {
		months = 1
	}

	totalBalance := float64(months) * monthlyRent

	return totalBalance
}

// 计算两个日期之间的月数
func calculateMonthsBetween(start, end time.Time) int {
	// 确保 start 在 end 之前
	if start.After(end) {
		return 0
	}

	years := end.Year() - start.Year()
	months := int(end.Month()) - int(start.Month())

	// 计算总月数
	totalMonths := years*12 + months + 1 // +1 包含当前月

	// 至少返回1个月
	if totalMonths < 1 {
		totalMonths = 1
	}

	return totalMonths
}

// TestScenario1Updated 测试更新后的 Scenario 1
func TestScenario1Updated() {
	fmt.Println("=== 测试 Scenario 1 更新版：基于时间的初始余额计算 ===")

	now := time.Now()
	fmt.Printf("当前时间: %s\n\n", now.Format("2006-01-02"))

	// 测试案例
	testCases := []struct {
		name        string
		startDate   string
		rent        float64
		fees        float64
		description string
	}{
		{
			name:        "当月开始",
			startDate:   now.Format("2006-01-02"),
			rent:        1000.0,
			fees:        200.0,
			description: "租约从今天开始",
		},
		{
			name:        "上月开始",
			startDate:   now.AddDate(0, -1, 0).Format("2006-01-02"),
			rent:        1000.0,
			fees:        200.0,
			description: "租约从上个月开始",
		},
		{
			name:        "3个月前开始",
			startDate:   now.AddDate(0, -3, 0).Format("2006-01-02"),
			rent:        800.0,
			fees:        150.0,
			description: "租约从3个月前开始",
		},
		{
			name:        "6个月前开始",
			startDate:   now.AddDate(0, -6, 0).Format("2006-01-02"),
			rent:        1200.0,
			fees:        300.0,
			description: "租约从6个月前开始",
		},
		{
			name:        "未来开始",
			startDate:   now.AddDate(0, 1, 0).Format("2006-01-02"),
			rent:        1000.0,
			fees:        200.0,
			description: "租约从下个月开始",
		},
	}

	for i, tc := range testCases {
		fmt.Printf("测试案例 %d: %s\n", i+1, tc.name)
		fmt.Printf("  %s\n", tc.description)

		lease := &TestLease{
			RentAmount:            tc.rent,
			AdditionalMonthlyFees: tc.fees,
			StartDate:             tc.startDate,
		}

		fmt.Printf("  开始日期: %s\n", tc.startDate)
		fmt.Printf("  月租金: $%.2f + 附加费: $%.2f = $%.2f/月\n",
			tc.rent, tc.fees, tc.rent+tc.fees)

		// 计算月数
		startDate, _ := time.Parse("2006-01-02", tc.startDate)
		months := calculateMonthsBetween(startDate, now)
		if startDate.After(now) {
			months = 0
		}

		// 应用新的 Scenario 1 逻辑
		lease.OwingBalance = calculateInitialOwingBalance(lease)

		fmt.Printf("  计算月数: %d 个月\n", months)
		fmt.Printf("  初始余额: $%.2f\n", lease.OwingBalance)

		// 验证计算
		expectedBalance := 0.0
		if !startDate.After(now) {
			if months < 1 {
				months = 1
			}
			expectedBalance = float64(months) * (tc.rent + tc.fees)
		}

		if lease.OwingBalance == expectedBalance {
			fmt.Printf("  ✅ 计算正确\n")
		} else {
			fmt.Printf("  ❌ 计算错误 (预期: $%.2f)\n", expectedBalance)
		}
		fmt.Println()
	}

	// 特殊边界测试
	fmt.Printf("=== 边界情况测试 ===\n")

	// 测试日期解析错误
	invalidLease := &TestLease{
		RentAmount:            1000.0,
		AdditionalMonthlyFees: 200.0,
		StartDate:             "invalid-date",
	}

	fmt.Printf("无效日期测试:\n")
	fmt.Printf("  开始日期: %s\n", invalidLease.StartDate)
	invalidLease.OwingBalance = calculateInitialOwingBalance(invalidLease)
	fmt.Printf("  余额: $%.2f (应该是一个月租金)\n", invalidLease.OwingBalance)

	if invalidLease.OwingBalance == 1200.0 {
		fmt.Printf("  ✅ 无效日期处理正确\n")
	} else {
		fmt.Printf("  ❌ 无效日期处理错误\n")
	}

	fmt.Printf("\n=== Scenario 1 更新版测试完成 ===\n")
}

func main() {
	TestScenario1Updated()
}
