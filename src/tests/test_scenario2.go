package main

import (
	"fmt"
)

// 简化的租约结构体用于测试
type TestLease struct {
	RentAmount            float64
	AdditionalMonthlyFees float64
	OwingBalance          float64
}

// 简化的支付结构体用于测试
type TestPayment struct {
	Amount float64
}

// TestScenario2 测试添加支付时的余额更新
func TestScenario2() {
	fmt.Println("=== 测试 Scenario 2: Add Tenant Payment 余额更新 ===")

	// 创建测试租约（已有初始余额）
	lease := &TestLease{
		RentAmount:            1000.0,
		AdditionalMonthlyFees: 200.0,
		OwingBalance:          1200.0, // 初始余额
	}

	fmt.Printf("初始状态:\n")
	fmt.Printf("  租金: $%.2f\n", lease.RentAmount)
	fmt.Printf("  附加费: $%.2f\n", lease.AdditionalMonthlyFees)
	fmt.Printf("  当前余额: $%.2f\n", lease.OwingBalance)

	// 测试不同的支付场景
	testCases := []struct {
		name            string
		paymentAmount   float64
		expectedBalance float64
		description     string
	}{
		{
			name:            "完全支付",
			paymentAmount:   1200.0,
			expectedBalance: 0.0,
			description:     "支付全部余额",
		},
		{
			name:            "部分支付",
			paymentAmount:   500.0,
			expectedBalance: 700.0,
			description:     "支付部分余额",
		},
		{
			name:            "超额支付",
			paymentAmount:   1500.0,
			expectedBalance: -300.0,
			description:     "支付超过余额（允许负数余额，表示多付款）",
		},
		{
			name:            "小额支付",
			paymentAmount:   100.0,
			expectedBalance: 1100.0,
			description:     "小额支付",
		},
	}

	fmt.Printf("\n=== 测试不同支付场景 ===\n")
	for i, tc := range testCases {
		// 重置租约余额为初始状态
		testLease := &TestLease{
			RentAmount:            1000.0,
			AdditionalMonthlyFees: 200.0,
			OwingBalance:          1200.0,
		}

		payment := &TestPayment{
			Amount: tc.paymentAmount,
		}

		fmt.Printf("测试案例 %d: %s\n", i+1, tc.name)
		fmt.Printf("  %s\n", tc.description)
		fmt.Printf("  支付前余额: $%.2f\n", testLease.OwingBalance)
		fmt.Printf("  支付金额: $%.2f\n", payment.Amount)

		// 应用 Scenario 2 逻辑
		newBalance := testLease.OwingBalance - payment.Amount
		// 允许负数余额，表示多付款
		testLease.OwingBalance = newBalance

		fmt.Printf("  支付后余额: $%.2f\n", testLease.OwingBalance)
		fmt.Printf("  预期余额: $%.2f\n", tc.expectedBalance)

		if testLease.OwingBalance == tc.expectedBalance {
			fmt.Printf("  ✅ 通过\n")
		} else {
			fmt.Printf("  ❌ 失败\n")
		}
		fmt.Println()
	}

	// 测试连续支付
	fmt.Printf("=== 测试连续支付 ===\n")
	continuousLease := &TestLease{
		RentAmount:            1000.0,
		AdditionalMonthlyFees: 200.0,
		OwingBalance:          1200.0,
	}

	payments := []float64{300.0, 400.0, 500.0}
	expectedBalances := []float64{900.0, 500.0, 0.0}

	fmt.Printf("初始余额: $%.2f\n", continuousLease.OwingBalance)

	for i, paymentAmount := range payments {
		fmt.Printf("支付 %d: $%.2f\n", i+1, paymentAmount)

		// 应用支付逻辑
		newBalance := continuousLease.OwingBalance - paymentAmount
		// 允许负数余额，表示多付款
		continuousLease.OwingBalance = newBalance

		fmt.Printf("  余额变为: $%.2f (预期: $%.2f)\n",
			continuousLease.OwingBalance, expectedBalances[i])

		if continuousLease.OwingBalance == expectedBalances[i] {
			fmt.Printf("  ✅ 通过\n")
		} else {
			fmt.Printf("  ❌ 失败\n")
		}
	}

	fmt.Printf("\n=== Scenario 2 测试完成 ===\n")
}

func main() {
	TestScenario2()
}
