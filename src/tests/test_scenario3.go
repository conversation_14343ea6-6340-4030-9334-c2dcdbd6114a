package main

import (
	"fmt"
)

// 简化的租约结构体用于测试
type TestLease struct {
	ID           string
	OwingBalance float64
	Status       string
}

// 简化的支付结构体用于测试
type TestPayment struct {
	ID      string
	LeaseID string
	Amount  float64
}

// 模拟 Scenario 3 的完整逻辑
func simulateUpdatePayment(lease *TestLease, oldPayment, newPayment *TestPayment) error {
	// Scenario 5: 检查租约状态
	if lease.Status != "active" {
		return fmt.Errorf("can only update payments for active leases, current status: %s", lease.Status)
	}

	// Scenario 3: 更新余额
	// 计算余额变化：+旧金额（撤回）-新金额（应用）
	balanceChange := oldPayment.Amount - newPayment.Amount
	newBalance := lease.OwingBalance + balanceChange

	// 余额不能为负
	if newBalance < 0 {
		newBalance = 0
	}

	lease.OwingBalance = newBalance
	return nil
}

// TestScenario3 测试更新支付时的余额重新计算
func TestScenario3() {
	fmt.Println("=== 测试 Scenario 3: Update Tenant Payment 余额重新计算 ===")

	// 测试案例
	testCases := []struct {
		name            string
		initialBalance  float64
		oldAmount       float64
		newAmount       float64
		expectedBalance float64
		description     string
	}{
		{
			name:            "增加支付金额",
			initialBalance:  1000.0,
			oldAmount:       300.0,
			newAmount:       500.0,
			expectedBalance: 800.0,
			description:     "支付从$300增加到$500，余额应该减少$200",
		},
		{
			name:            "减少支付金额",
			initialBalance:  500.0,
			oldAmount:       400.0,
			newAmount:       200.0,
			expectedBalance: 700.0,
			description:     "支付从$400减少到$200，余额应该增加$200",
		},
		{
			name:            "支付金额不变",
			initialBalance:  800.0,
			oldAmount:       300.0,
			newAmount:       300.0,
			expectedBalance: 800.0,
			description:     "支付金额不变，余额应该保持不变",
		},
		{
			name:            "大幅增加支付（超过余额）",
			initialBalance:  600.0,
			oldAmount:       100.0,
			newAmount:       800.0,
			expectedBalance: 0.0,
			description:     "支付从$100增加到$800，余额应该变为$0（不能为负）",
		},
		{
			name:            "从零支付变为有支付",
			initialBalance:  1200.0,
			oldAmount:       0.0,
			newAmount:       400.0,
			expectedBalance: 800.0,
			description:     "支付从$0增加到$400，余额应该减少$400",
		},
		{
			name:            "从有支付变为零支付",
			initialBalance:  500.0,
			oldAmount:       300.0,
			newAmount:       0.0,
			expectedBalance: 800.0,
			description:     "支付从$300减少到$0，余额应该增加$300",
		},
	}

	for i, tc := range testCases {
		fmt.Printf("测试案例 %d: %s\n", i+1, tc.name)
		fmt.Printf("  %s\n", tc.description)

		// 创建测试数据
		lease := &TestLease{
			ID:           "lease-1",
			OwingBalance: tc.initialBalance,
			Status:       "active",
		}

		oldPayment := &TestPayment{
			ID:      "payment-1",
			LeaseID: "lease-1",
			Amount:  tc.oldAmount,
		}

		newPayment := &TestPayment{
			ID:      "payment-1",
			LeaseID: "lease-1",
			Amount:  tc.newAmount,
		}

		fmt.Printf("  初始余额: $%.2f\n", lease.OwingBalance)
		fmt.Printf("  旧支付金额: $%.2f\n", oldPayment.Amount)
		fmt.Printf("  新支付金额: $%.2f\n", newPayment.Amount)

		// 计算预期的余额变化
		balanceChange := oldPayment.Amount - newPayment.Amount
		fmt.Printf("  余额变化: $%.2f (撤回$%.2f，应用$%.2f)\n", 
			balanceChange, oldPayment.Amount, newPayment.Amount)

		// 应用 Scenario 3 逻辑
		err := simulateUpdatePayment(lease, oldPayment, newPayment)
		if err != nil {
			fmt.Printf("  ❌ 更新失败: %v\n", err)
		} else {
			fmt.Printf("  更新后余额: $%.2f\n", lease.OwingBalance)
			fmt.Printf("  预期余额: $%.2f\n", tc.expectedBalance)

			if lease.OwingBalance == tc.expectedBalance {
				fmt.Printf("  ✅ 通过\n")
			} else {
				fmt.Printf("  ❌ 失败\n")
			}
		}
		fmt.Println()
	}

	// 测试状态锁定
	fmt.Printf("=== 测试状态锁定 ===\n")
	endedLease := &TestLease{
		ID:           "lease-2",
		OwingBalance: 1000.0,
		Status:       "ended",
	}

	oldPayment := &TestPayment{Amount: 300.0}
	newPayment := &TestPayment{Amount: 500.0}

	fmt.Printf("租约状态: %s\n", endedLease.Status)
	fmt.Printf("尝试更新支付: $%.2f → $%.2f\n", oldPayment.Amount, newPayment.Amount)

	err := simulateUpdatePayment(endedLease, oldPayment, newPayment)
	if err != nil {
		fmt.Printf("✅ 正确拒绝: %v\n", err)
	} else {
		fmt.Printf("❌ 应该拒绝已结束租约的支付更新\n")
	}

	// 测试边界情况
	fmt.Printf("\n=== 边界情况测试 ===\n")
	
	// 精确清零
	fmt.Printf("1. 精确清零测试:\n")
	zeroLease := &TestLease{
		ID:           "lease-3",
		OwingBalance: 200.0,
		Status:       "active",
	}
	
	oldPmt := &TestPayment{Amount: 100.0}
	newPmt := &TestPayment{Amount: 300.0}
	
	fmt.Printf("  余额: $%.2f, 支付: $%.2f → $%.2f\n", 
		zeroLease.OwingBalance, oldPmt.Amount, newPmt.Amount)
	
	err = simulateUpdatePayment(zeroLease, oldPmt, newPmt)
	if err == nil {
		fmt.Printf("  结果余额: $%.2f\n", zeroLease.OwingBalance)
		if zeroLease.OwingBalance == 0.0 {
			fmt.Printf("  ✅ 精确清零成功\n")
		} else {
			fmt.Printf("  ❌ 精确清零失败\n")
		}
	}

	fmt.Printf("\n=== Scenario 3 测试完成 ===\n")
}

func main() {
	TestScenario3()
}
