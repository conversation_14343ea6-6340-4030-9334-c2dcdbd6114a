package utils

import (
	"crypto/rand"
	"fmt"
	"net/http"
	"strings"
	"time"

	"github.com/golang-jwt/jwt"
	"github.com/real-rm/goconfig"
	"github.com/real-rm/golog"
)

type TokenType string

const (
	AccessToken  TokenType = "access_token"
	RefreshToken TokenType = "refresh_token"
)

type Claims struct {
	UserID    string    `json:"sub"`
	TokenType TokenType `json:"token_type"`
	jwt.StandardClaims
}

type TokenPair struct {
	AccessToken  string `json:"access_token"`
	RefreshToken string `json:"refresh_token"`
}

func getJWTSecret() []byte {
	secret := goconfig.Config("auth.jwtSecret").(string)
	if secret != "" {
		return []byte(secret)
	}

	// Generate random secret
	randomBytes := make([]byte, 32) // 256 bits
	if _, err := rand.Read(randomBytes); err != nil {
		panic(fmt.Sprintf("failed to generate random secret: %v", err))
	}
	golog.Warn("Using randomly generated JWT secret. Please configure auth.jwtSecret in production.")
	return randomBytes
}

func GenerateTokenPair(userID string) (*TokenPair, error) {
	// Generate access token
	accessToken, err := generateToken(userID, AccessToken, time.Hour) // Access token expires in 1 hour
	if err != nil {
		return nil, fmt.Errorf("failed to generate access token: %v", err)
	}

	// Generate refresh token
	refreshToken, err := generateToken(userID, RefreshToken, time.Hour*24*7) // Refresh token expires in 7 days
	if err != nil {
		return nil, fmt.Errorf("failed to generate refresh token: %v", err)
	}

	return &TokenPair{
		AccessToken:  accessToken,
		RefreshToken: refreshToken,
	}, nil
}

func generateToken(userID string, tokenType TokenType, expiration time.Duration) (string, error) {
	claims := &Claims{
		UserID:    userID,
		TokenType: tokenType,
		StandardClaims: jwt.StandardClaims{
			ExpiresAt: time.Now().Add(expiration).Unix(),
			IssuedAt:  time.Now().Unix(),
		},
	}

	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	return token.SignedString(getJWTSecret())
}

func VerifyToken(tokenString string, expectedType TokenType) (*Claims, error) {
	claims := &Claims{}
	token, err := jwt.ParseWithClaims(tokenString, claims, func(token *jwt.Token) (interface{}, error) {
		if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
			return nil, fmt.Errorf("unexpected signing method")
		}
		return getJWTSecret(), nil
	})

	if err != nil {
		return nil, fmt.Errorf("invalid token: %v", err)
	}

	if !token.Valid {
		return nil, fmt.Errorf("token is not valid")
	}

	if claims.TokenType != expectedType {
		return nil, fmt.Errorf("invalid token type: expected %s, got %s", expectedType, claims.TokenType)
	}

	return claims, nil
}

// GetUserIDFromToken checks if the user is authenticated by validating their tokens
// It performs the following:
// 1. Validates the access token from Authorization header if present
// 2. If access token is invalid/missing, tries to use refresh token from cookies
// 3. If refresh token is valid, generates a new access token
// Returns userID if authentication is successful, error otherwise

// TODO: refactor to split auth check

func GetUserIDFromToken(r *http.Request) (string, error) {
	// First try Authorization header (this is where access token should be)
	authHeader := r.Header.Get("Authorization")
	if authHeader != "" {
		tokenString := strings.TrimPrefix(authHeader, "Bearer ")
		claims, err := VerifyToken(tokenString, AccessToken)
		if err == nil {
			return claims.UserID, nil
		}
		// If access token is invalid, continue to try refresh token
	}

	// If access token is invalid or missing, try refresh token from cookie
	refreshCookie, err := r.Cookie("refresh_token")
	if err != nil {
		return "", fmt.Errorf("no valid token found")
	}

	// Verify refresh token and generate new access token
	refreshClaims, err := VerifyToken(refreshCookie.Value, RefreshToken)
	if err != nil {
		return "", fmt.Errorf("invalid refresh token")
	}

	// Generate new access token
	newAccessToken, err := generateToken(refreshClaims.UserID, AccessToken, time.Hour)
	if err != nil {
		return "", fmt.Errorf("failed to generate new access token")
	}

	// Add the new access token to response headers only
	// Frontend should capture this and update its stored access token
	if w := r.Context().Value("ResponseWriter"); w != nil {
		if responseWriter, ok := w.(http.ResponseWriter); ok {
			responseWriter.Header().Set("X-New-Access-Token", newAccessToken)
		}
	}

	return refreshClaims.UserID, nil
}

func RefreshAccessToken(refreshToken string) (string, error) {
	claims, err := VerifyToken(refreshToken, RefreshToken)
	if err != nil {
		return "", fmt.Errorf("invalid refresh token: %v", err)
	}

	// Generate new access token
	accessToken, err := generateToken(claims.UserID, AccessToken, time.Hour)
	if err != nil {
		return "", fmt.Errorf("failed to generate new access token: %v", err)
	}

	return accessToken, nil
}

func VerifyRecaptcha(r *http.Request) error {
	// For development, always pass verification
	return nil

	// TODO: Implement proper reCAPTCHA verification for production
	// Get reCAPTCHA token from header
	token := r.Header.Get("X-Recaptcha-Token")
	if token == "" {
		return fmt.Errorf("no reCAPTCHA token")
	}
	return nil
}

// CheckAuth validates the authentication token from the request
// It checks both Authorization header and refresh token cookie
func CheckAuth(r *http.Request) error {
	// First try Authorization header (this is where access token should be)
	authHeader := r.Header.Get("Authorization")
	if authHeader != "" {
		tokenString := strings.TrimPrefix(authHeader, "Bearer ")
		_, err := VerifyToken(tokenString, AccessToken)
		if err == nil {
			return nil // Token is valid
		}
		// If access token is invalid, continue to try refresh token
	}

	// If access token is invalid or missing, try refresh token from cookie
	refreshCookie, err := r.Cookie("refresh_token")
	if err != nil {
		return fmt.Errorf("no valid token found")
	}

	// Verify refresh token
	_, err = VerifyToken(refreshCookie.Value, RefreshToken)
	if err != nil {
		return fmt.Errorf("invalid refresh token")
	}

	return nil // Authentication successful
}

// SplitAndTrim 按分隔符分割字符串并去除每项首尾空格
func SplitAndTrim(s, sep string) []string {
	parts := strings.Split(s, sep)
	var out []string
	for _, p := range parts {
		p = strings.TrimSpace(p)
		if p != "" {
			out = append(out, p)
		}
	}
	return out
}
