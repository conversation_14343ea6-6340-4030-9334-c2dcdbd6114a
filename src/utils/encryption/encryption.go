package encryption

import (
	"crypto/aes"
	"crypto/cipher"
	"crypto/rand"
	"encoding/base64"
	"fmt"

	"github.com/real-rm/goconfig"
	"github.com/real-rm/golog"
)

var encryptionKey []byte

// InitEncryption initializes the encryption key from config
func InitEncryption() error {
	configValue := goconfig.Config("encryption.key")
	if configValue == nil {
		return fmt.Errorf("encryption key not found in config. Please ensure your INI file has [encryption] section with 'key = your32bytesecretkeygoesrighthere'")
	}

	key, ok := configValue.(string)
	if !ok {
		return fmt.Errorf("encryption.key must be a string value")
	}
	if key == "" {
		return fmt.Errorf("encryption.key must not be empty")
	}

	// Validate key length
	if len(key) != 32 {
		return fmt.Errorf("encryption.key must be exactly 32 bytes long for AES-256")
	}

	encryptionKey = []byte(key)
	golog.Info("Encryption initialized", "keyLength", len(encryptionKey))
	return nil
}

// EncryptString encrypts a string and returns a base64 encoded string
func EncryptString(plaintext string) (string, error) {
	if plaintext == "" {
		return "", nil
	}

	golog.Debug("Encrypting text", "length", len(plaintext))

	block, err := aes.NewCipher(encryptionKey)
	if err != nil {
		return "", fmt.Errorf("failed to create cipher: %v", err)
	}

	// Create a new GCM
	gcm, err := cipher.NewGCM(block)
	if err != nil {
		return "", fmt.Errorf("failed to create GCM: %v", err)
	}

	// Create a nonce
	nonce := make([]byte, gcm.NonceSize())
	if _, err := rand.Read(nonce); err != nil {
		return "", fmt.Errorf("failed to create nonce: %v", err)
	}

	// Encrypt and prepend nonce
	ciphertext := gcm.Seal(nonce, nonce, []byte(plaintext), nil)

	// Encode to base64
	result := base64.StdEncoding.EncodeToString(ciphertext)
	golog.Debug("Encrypted text", "resultLength", len(result))
	return result, nil
}

// DecryptString decrypts a base64 encoded encrypted string
func DecryptString(ciphertext string) (string, error) {
	if ciphertext == "" {
		return "", nil
	}

	// Decode base64
	data, err := base64.StdEncoding.DecodeString(ciphertext)
	if err != nil {
		return "", fmt.Errorf("failed to decode base64: %v", err)
	}

	block, err := aes.NewCipher(encryptionKey)
	if err != nil {
		return "", fmt.Errorf("failed to create cipher: %v", err)
	}

	// Create a new GCM
	gcm, err := cipher.NewGCM(block)
	if err != nil {
		return "", fmt.Errorf("failed to create GCM: %v", err)
	}

	// Get nonce size
	nonceSize := gcm.NonceSize()
	if len(data) < nonceSize {
		return "", fmt.Errorf("ciphertext too short")
	}

	// Extract nonce and ciphertext
	nonce := data[:nonceSize]
	ciphertextBytes := data[nonceSize:]

	// Decrypt
	plaintext, err := gcm.Open(nil, nonce, ciphertextBytes, nil)
	if err != nil {
		return "", fmt.Errorf("failed to decrypt: %v", err)
	}

	return string(plaintext), nil
}
