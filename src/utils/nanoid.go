package utils

import (
	nanoid "github.com/matoous/go-nanoid/v2"
)

// GenerateNanoID 生成标准长度的nanoId (11字符，只包含大小写字母和数字)
func GenerateNanoID() string {
	alphabet := "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz"
	return nanoid.MustGenerate(alphabet, 11)
}

// GenerateCustomNanoID 生成自定义长度的nanoId (只包含大小写字母和数字)
func GenerateCustomNanoID(length int) string {
	alphabet := "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz"
	return nanoid.MustGenerate(alphabet, length)
}

// GenerateShortNanoID 生成短nanoId (12字符，适用于某些特殊场景)
// 已废弃：请使用 GenerateNanoID() 或 GenerateCustomNanoID(12)
func GenerateShortNanoID() string {
	alphabet := "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz"
	return nanoid.MustGenerate(alphabet, 12)
}
