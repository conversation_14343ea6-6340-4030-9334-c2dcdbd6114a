package utils

import (
	"bytes"
	"crypto/hmac"
	"crypto/sha256"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"sync"
	"time"

	"github.com/real-rm/goconfig"
	"github.com/real-rm/golog"
)

var (
	oauthConfig *Config
	configOnce  sync.Once
	// Store states with expiry
	// TODO: Use MongoDB to store states
	states = make(map[string]time.Time)
)

func init() {
	// Start a goroutine to clean up expired states
	go func() {
		for {
			time.Sleep(time.Minute)
			now := time.Now()
			for state, expiry := range states {
				if now.After(expiry) {
					delete(states, state)
				}
			}
		}
	}()
}

type Config struct {
	ClientID     string
	ClientSecret string
	AuthURL      string
	TokenURL     string
	RedirectURL  string
	UserInfoURL  string
}

type TokenResponse struct {
	AccessToken  string `json:"access_token"`
	RefreshToken string `json:"refresh_token"`
}

// UserInfo is the response from the RealMaster provider/userinfo endpoint
type UserInfo struct {
	ID    string `json:"id"`
	Email string `json:"email"`
	Name  string `json:"name"`
	Phone string `json:"phone"`
}

func GetOAuthConfig() *Config {
	configOnce.Do(func() {
		oauthConfig = &Config{
			ClientID:     goconfig.Config("oauthClient.clientId").(string),
			ClientSecret: goconfig.Config("oauthClient.clientSecret").(string),
			AuthURL:      goconfig.Config("oauthClient.authUrl").(string),
			TokenURL:     goconfig.Config("oauthClient.tokenUrl").(string),
			RedirectURL:  goconfig.Config("oauthClient.redirectUrl").(string),
			UserInfoURL:  goconfig.Config("oauthClient.userInfoUrl").(string),
		}
	})
	return oauthConfig
}

func GenerateState() string {
	// Generate a random state string
	state := fmt.Sprintf("%x", sha256.Sum256([]byte(fmt.Sprintf("%d", time.Now().UnixNano()))))[:32]
	// Store with 5-minute expiry
	states[state] = time.Now().Add(5 * time.Minute)
	return state
}

func SaveState(state string) error {
	// Store the state with expiry
	states[state] = time.Now().Add(5 * time.Minute)
	return nil
}

func ValidateState(state string) bool {
	expiry, exists := states[state]
	if !exists {
		golog.Debug("OAuth state not found", "state", state)
		return false
	}

	// Check if state has expired
	if time.Now().After(expiry) {
		golog.Debug("OAuth state expired", "state", state)
		delete(states, state)
		return false
	}

	// Remove the used state
	delete(states, state)
	return true
}

// generateHMACSignature generates an HMAC signature for OAuth request
func generateHMACSignature(clientID string, timestamp int64, clientSecret string) string {
	message := fmt.Sprintf("%s:%d", clientID, timestamp)
	hmacHash := hmac.New(sha256.New, []byte(clientSecret))
	hmacHash.Write([]byte(message))
	signature := base64.StdEncoding.EncodeToString(hmacHash.Sum(nil))
	return signature
}

func ExchangeCodeForToken(code string) (*TokenResponse, error) {
	config := GetOAuthConfig()

	// Generate timestamp for the request
	timestamp := time.Now().Unix()

	// Generate HMAC signature
	signature := generateHMACSignature(config.ClientID, timestamp, config.ClientSecret)

	tokenData := map[string]interface{}{
		"code":         code,
		"client_id":    config.ClientID,
		"redirect_uri": config.RedirectURL,
		"timestamp":    timestamp,
		"signature":    signature,
	}

	tokenBody, err := json.Marshal(tokenData)
	if err != nil {
		return nil, err
	}

	golog.Debug("Requesting OAuth token", "tokenURL", config.TokenURL, "dataLength", len(tokenBody))

	resp, err := http.Post(config.TokenURL, "application/json", bytes.NewBuffer(tokenBody))
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	golog.Debug("OAuth token response", "status", resp.StatusCode)

	// Read raw response first
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read token response body: %v", err)
	}
	golog.Debug("OAuth token response body", "length", len(body))

	// Create new reader for JSON decoding
	resp.Body = io.NopCloser(bytes.NewBuffer(body))

	var token TokenResponse
	if err := json.NewDecoder(resp.Body).Decode(&token); err != nil {
		return nil, err
	}

	golog.Debug("OAuth token decoded successfully", "hasAccessToken", token.AccessToken != "", "hasRefreshToken", token.RefreshToken != "")
	return &token, nil
}

func GetUserInfo(token *TokenResponse) (*UserInfo, error) {
	config := GetOAuthConfig()

	// Create GET request
	req, err := http.NewRequest("GET", config.UserInfoURL, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %v", err)
	}

	// Set Authorization header with Bearer token
	authHeader := fmt.Sprintf("Bearer %s", token.AccessToken)
	req.Header.Set("Authorization", authHeader)

	golog.Debug("Getting OAuth user info", "userInfoURL", config.UserInfoURL)

	// Make the request
	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to get user info: %v", err)
	}
	defer resp.Body.Close()

	golog.Debug("OAuth user info response", "status", resp.StatusCode)

	// Read the raw response body
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read response body: %v", err)
	}
	golog.Debug("OAuth user info response body", "length", len(body))

	// Create a new reader with the same body for json.Decode
	resp.Body = io.NopCloser(bytes.NewBuffer(body))

	var userInfo UserInfo
	if err := json.NewDecoder(resp.Body).Decode(&userInfo); err != nil {
		return nil, fmt.Errorf("failed to decode user info: %v", err)
	}

	if userInfo.Email == "" {
		return nil, fmt.Errorf("user info email is empty")
	}

	golog.Debug("OAuth user info decoded successfully", "email", userInfo.Email, "name", userInfo.Name)
	return &userInfo, nil
}

// ExchangeRefreshTokenForAccessToken exchanges a refresh token for a new access token
func ExchangeRefreshTokenForAccessToken(refreshToken string) (*TokenResponse, error) {
	config := GetOAuthConfig()
	// Generate timestamp for the request
	timestamp := time.Now().Unix()

	// Generate HMAC signature
	signature := generateHMACSignature(config.ClientID, timestamp, config.ClientSecret)

	tokenData := map[string]interface{}{
		"refresh_token": refreshToken,
		"grant_type":    "refresh_token",
		"client_id":     config.ClientID,
		"timestamp":     timestamp,
		"signature":     signature,
	}

	tokenBody, err := json.Marshal(tokenData)
	if err != nil {
		return nil, err
	}

	golog.Debug("Requesting OAuth token refresh", "tokenURL", config.TokenURL, "dataLength", len(tokenBody))

	resp, err := http.Post(config.TokenURL, "application/json", bytes.NewBuffer(tokenBody))
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	golog.Debug("OAuth token refresh response", "status", resp.StatusCode)

	// Read raw response first
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read token refresh response body: %v", err)
	}
	golog.Debug("OAuth token refresh response body", "length", len(body))

	// Create new reader for JSON decoding
	resp.Body = io.NopCloser(bytes.NewBuffer(body))

	var token TokenResponse
	if err := json.NewDecoder(resp.Body).Decode(&token); err != nil {
		return nil, err
	}

	golog.Debug("OAuth token refresh decoded successfully", "hasAccessToken", token.AccessToken != "", "hasRefreshToken", token.RefreshToken != "")
	return &token, nil
}
