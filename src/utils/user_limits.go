package utils

import (
	"context"
	"fmt"

	"github.com/real-rm/gomongo"
	"go.mongodb.org/mongo-driver/bson"
)

// IsVIPUser 检查用户是否为VIP用户（有活跃订阅）
func IsVIPUser(ctx context.Context, userID string) bool {
	if userID == "" {
		return false
	}

	// 检查用户是否有活跃订阅
	var userSub bson.M
	err := gomongo.Coll("rr", "usersub").FindOne(ctx, bson.M{
		"uid": userID,
		"sts": "active",
	}).Decode(&userSub)

	return err == nil
}

// CountUserNonDeletedProperties 统计用户的非deleted状态的properties数量
func CountUserNonDeletedProperties(ctx context.Context, userID string) (int, error) {
	if userID == "" {
		return 0, fmt.Errorf("userID cannot be empty")
	}

	coll := gomongo.Coll("rr", "properties")
	if coll == nil {
		return 0, fmt.Errorf("properties collection not initialized")
	}

	count, err := coll.CountDocuments(ctx, bson.M{
		"usrId": userID,
		"stat":  bson.M{"$ne": "deleted"}, // 排除deleted状态
	})
	if err != nil {
		return 0, fmt.Errorf("failed to count properties: %v", err)
	}

	return int(count), nil
}

// CountUserNonDeletedLeases 统计用户的非deleted状态的leases数量
func CountUserNonDeletedLeases(ctx context.Context, userID string) (int, error) {
	if userID == "" {
		return 0, fmt.Errorf("userID cannot be empty")
	}

	coll := gomongo.Coll("rr", "leases")
	if coll == nil {
		return 0, fmt.Errorf("leases collection not initialized")
	}

	count, err := coll.CountDocuments(ctx, bson.M{
		"usrId":  userID,
		"status": bson.M{"$ne": "deleted"}, // 排除deleted状态
	})
	if err != nil {
		return 0, fmt.Errorf("failed to count leases: %v", err)
	}

	return int(count), nil
}

// UserLimitError 用户限制错误结构
type UserLimitError struct {
	Type    string `json:"type"`    // "property" 或 "lease"
	Current int    `json:"current"` // 当前数量
	Limit   int    `json:"limit"`   // 限制数量
	Message string `json:"message"` // 错误消息
}

func (e *UserLimitError) Error() string {
	return e.Message
}

// CheckUserPropertyLimit 检查用户property创建限制
func CheckUserPropertyLimit(ctx context.Context, userID string, maxProperties int) error {
	// VIP用户无限制
	if IsVIPUser(ctx, userID) {
		return nil
	}

	// 统计当前properties数量
	count, err := CountUserNonDeletedProperties(ctx, userID)
	if err != nil {
		return fmt.Errorf("failed to check property limit: %v", err)
	}

	// 检查是否超出限制
	if count >= maxProperties {
		return &UserLimitError{
			Type:    "property",
			Current: count,
			Limit:   maxProperties,
			Message: fmt.Sprintf("You've reached your property limit (%d/%d)", count, maxProperties),
		}
	}

	return nil
}

// CheckUserLeaseLimit 检查用户lease创建限制
func CheckUserLeaseLimit(ctx context.Context, userID string, maxLeases int) error {
	// VIP用户无限制
	if IsVIPUser(ctx, userID) {
		return nil
	}

	// 统计当前leases数量
	count, err := CountUserNonDeletedLeases(ctx, userID)
	if err != nil {
		return fmt.Errorf("failed to check lease limit: %v", err)
	}

	// 检查是否超出限制
	if count >= maxLeases {
		return &UserLimitError{
			Type:    "lease",
			Current: count,
			Limit:   maxLeases,
			Message: fmt.Sprintf("You've reached your lease limit (%d/%d)", count, maxLeases),
		}
	}

	return nil
}
