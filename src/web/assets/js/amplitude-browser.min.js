!function(){"use strict";var e=function(t,n){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])},e(t,n)};function t(t,n){if("function"!=typeof n&&null!==n)throw new TypeError("Class extends value "+String(n)+" is not a constructor or null");function r(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(r.prototype=n.prototype,new r)}var n=function(){return n=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var i in t=arguments[n])Object.prototype.hasOwnProperty.call(t,i)&&(e[i]=t[i]);return e},n.apply(this,arguments)};function r(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var i=0;for(r=Object.getOwnPropertySymbols(e);i<r.length;i++)t.indexOf(r[i])<0&&Object.prototype.propertyIsEnumerable.call(e,r[i])&&(n[r[i]]=e[r[i]])}return n}function i(e,t,n,r){return new(n||(n=Promise))((function(i,o){function s(e){try{a(r.next(e))}catch(e){o(e)}}function u(e){try{a(r.throw(e))}catch(e){o(e)}}function a(e){var t;e.done?i(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(s,u)}a((r=r.apply(e,t||[])).next())}))}function o(e,t){var n,r,i,o,s={label:0,sent:function(){if(1&i[0])throw i[1];return i[1]},trys:[],ops:[]};return o={next:u(0),throw:u(1),return:u(2)},"function"==typeof Symbol&&(o[Symbol.iterator]=function(){return this}),o;function u(u){return function(a){return function(u){if(n)throw new TypeError("Generator is already executing.");for(;o&&(o=0,u[0]&&(s=0)),s;)try{if(n=1,r&&(i=2&u[0]?r.return:u[0]?r.throw||((i=r.return)&&i.call(r),0):r.next)&&!(i=i.call(r,u[1])).done)return i;switch(r=0,i&&(u=[2&u[0],i.value]),u[0]){case 0:case 1:i=u;break;case 4:return s.label++,{value:u[1],done:!1};case 5:s.label++,r=u[1],u=[0];continue;case 7:u=s.ops.pop(),s.trys.pop();continue;default:if(!(i=s.trys,(i=i.length>0&&i[i.length-1])||6!==u[0]&&2!==u[0])){s=0;continue}if(3===u[0]&&(!i||u[1]>i[0]&&u[1]<i[3])){s.label=u[1];break}if(6===u[0]&&s.label<i[1]){s.label=i[1],i=u;break}if(i&&s.label<i[2]){s.label=i[2],s.ops.push(u);break}i[2]&&s.ops.pop(),s.trys.pop();continue}u=t.call(e,s)}catch(e){u=[6,e],r=0}finally{n=i=0}if(5&u[0])throw u[1];return{value:u[0]?u[1]:void 0,done:!0}}([u,a])}}}function s(e){var t="function"==typeof Symbol&&Symbol.iterator,n=t&&e[t],r=0;if(n)return n.call(e);if(e&&"number"==typeof e.length)return{next:function(){return e&&r>=e.length&&(e=void 0),{value:e&&e[r++],done:!e}}};throw new TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")}function u(e,t){var n="function"==typeof Symbol&&e[Symbol.iterator];if(!n)return e;var r,i,o=n.call(e),s=[];try{for(;(void 0===t||t-- >0)&&!(r=o.next()).done;)s.push(r.value)}catch(e){i={error:e}}finally{try{r&&!r.done&&(n=o.return)&&n.call(o)}finally{if(i)throw i.error}}return s}function a(e,t,n){if(n||2===arguments.length)for(var r,i=0,o=t.length;i<o;i++)!r&&i in t||(r||(r=Array.prototype.slice.call(t,0,i)),r[i]=t[i]);return e.concat(r||Array.prototype.slice.call(t))}function c(e){return this instanceof c?(this.v=e,this):new c(e)}function l(e,t,n){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var r,i=n.apply(e,t||[]),o=[];return r={},s("next"),s("throw"),s("return"),r[Symbol.asyncIterator]=function(){return this},r;function s(e){i[e]&&(r[e]=function(t){return new Promise((function(n,r){o.push([e,t,n,r])>1||u(e,t)}))})}function u(e,t){try{(n=i[e](t)).value instanceof c?Promise.resolve(n.value.v).then(a,l):d(o[0][2],n)}catch(e){d(o[0][3],e)}var n}function a(e){u("next",e)}function l(e){u("throw",e)}function d(e,t){e(t),o.shift(),o.length&&u(o[0][0],o[0][1])}}function d(e){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var t,n=e[Symbol.asyncIterator];return n?n.call(e):(e=s(e),t={},r("next"),r("throw"),r("return"),t[Symbol.asyncIterator]=function(){return this},t);function r(n){t[n]=e[n]&&function(t){return new Promise((function(r,i){(function(e,t,n,r){Promise.resolve(r).then((function(t){e({value:t,done:n})}),t)})(r,i,(t=e[n](t)).done,t.value)}))}}}var f,h,p,v,g,y,m=function(){var e="ampIntegrationContext";return"undefined"!=typeof globalThis&&void 0!==globalThis[e]?globalThis[e]:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof self?self:"undefined"!=typeof global?global:void 0},b=function(){var e,t=m();return(null===(e=null==t?void 0:t.location)||void 0===e?void 0:e.search)?t.location.search.substring(1).split("&").filter(Boolean).reduce((function(e,t){var n=t.split("=",2),r=w(n[0]),i=w(n[1]);return i?(e[r]=i,e):e}),{}):{}},w=function(e){void 0===e&&(e="");try{return decodeURIComponent(e)}catch(e){return""}},_="dclid",S="fbclid",E="gbraid",I="gclid",T="ko_click_id",k="li_fat_id",O="msclkid",P="rtd_cid",x="ttclid",A="twclid",C="wbraid",N={utm_campaign:void 0,utm_content:void 0,utm_id:void 0,utm_medium:void 0,utm_source:void 0,utm_term:void 0,referrer:void 0,referring_domain:void 0,dclid:void 0,gbraid:void 0,gclid:void 0,fbclid:void 0,ko_click_id:void 0,li_fat_id:void 0,msclkid:void 0,rtd_cid:void 0,ttclid:void 0,twclid:void 0,wbraid:void 0},R=function(){function e(){}return e.prototype.parse=function(){return i(this,void 0,void 0,(function(){return o(this,(function(e){return[2,n(n(n(n({},N),this.getUtmParam()),this.getReferrer()),this.getClickIds())]}))}))},e.prototype.getUtmParam=function(){var e=b();return{utm_campaign:e.utm_campaign,utm_content:e.utm_content,utm_id:e.utm_id,utm_medium:e.utm_medium,utm_source:e.utm_source,utm_term:e.utm_term}},e.prototype.getReferrer=function(){var e,t,n={referrer:void 0,referring_domain:void 0};try{n.referrer=document.referrer||void 0,n.referring_domain=null!==(t=null===(e=n.referrer)||void 0===e?void 0:e.split("/")[2])&&void 0!==t?t:void 0}catch(e){}return n},e.prototype.getClickIds=function(){var e,t=b();return(e={})[_]=t[_],e[S]=t[S],e[E]=t[E],e[I]=t[I],e[T]=t[T],e[k]=t[k],e[O]=t[O],e[P]=t[P],e[x]=t[x],e[A]=t[A],e[C]=t[C],e},e}();!function(e){e.SET="$set",e.SET_ONCE="$setOnce",e.ADD="$add",e.APPEND="$append",e.PREPEND="$prepend",e.REMOVE="$remove",e.PREINSERT="$preInsert",e.POSTINSERT="$postInsert",e.UNSET="$unset",e.CLEAR_ALL="$clearAll"}(f||(f={})),function(e){e.REVENUE_PRODUCT_ID="$productId",e.REVENUE_QUANTITY="$quantity",e.REVENUE_PRICE="$price",e.REVENUE_TYPE="$revenueType",e.REVENUE="$revenue"}(h||(h={})),function(e){e.IDENTIFY="$identify",e.GROUP_IDENTIFY="$groupidentify",e.REVENUE="revenue_amount"}(p||(p={})),function(e){e[e.None=0]="None",e[e.Error=1]="Error",e[e.Warn=2]="Warn",e[e.Verbose=3]="Verbose",e[e.Debug=4]="Debug"}(v||(v={})),function(e){e.US="US",e.EU="EU"}(g||(g={})),function(e){e.Unknown="unknown",e.Skipped="skipped",e.Success="success",e.RateLimit="rate_limit",e.PayloadTooLarge="payload_too_large",e.Invalid="invalid",e.Failed="failed",e.Timeout="Timeout",e.SystemError="SystemError"}(y||(y={}));var L=Object.freeze({__proto__:null,get SpecialEventType(){return p},get IdentifyOperation(){return f},get RevenueProperty(){return h},get LogLevel(){return v},get ServerZone(){return g},get Status(){return y},OfflineDisabled:null,DEFAULT_CSS_SELECTOR_ALLOWLIST:["a","button","input","select","textarea","label","[data-amp-default-track]",".amp-default-track"],DEFAULT_DATA_ATTRIBUTE_PREFIX:"data-amp-track-"}),q=function(e,t,n){return void 0===t&&(t=0),void 0===n&&(n=y.Unknown),{event:e,code:t,message:n}},U=function(e){return e?(e^16*Math.random()>>e/4).toString(16):(String(1e7)+String(-1e3)+String(-4e3)+String(-8e3)+String(-1e11)).replace(/[018]/g,U)},j=function(){function e(e){this.client=e,this.queue=[],this.applying=!1,this.plugins=[]}return e.prototype.register=function(e,t){var n,r;return i(this,void 0,void 0,(function(){return o(this,(function(i){switch(i.label){case 0:return this.plugins.some((function(t){return t.name===e.name}))?(t.loggerProvider.warn("Plugin with name ".concat(e.name," already exists, skipping registration")),[2]):(void 0===e.name&&(e.name=U(),t.loggerProvider.warn("Plugin name is undefined. \n      Generating a random UUID for plugin name: ".concat(e.name,". \n      Set a name for the plugin to prevent it from being added multiple times."))),e.type=null!==(n=e.type)&&void 0!==n?n:"enrichment",[4,null===(r=e.setup)||void 0===r?void 0:r.call(e,t,this.client)]);case 1:return i.sent(),this.plugins.push(e),[2]}}))}))},e.prototype.deregister=function(e){var t;return i(this,void 0,void 0,(function(){var n,r;return o(this,(function(i){switch(i.label){case 0:return n=this.plugins.findIndex((function(t){return t.name===e})),r=this.plugins[n],this.plugins.splice(n,1),[4,null===(t=r.teardown)||void 0===t?void 0:t.call(r)];case 1:return i.sent(),[2]}}))}))},e.prototype.reset=function(e){this.applying=!1,this.plugins.map((function(e){var t;return null===(t=e.teardown)||void 0===t?void 0:t.call(e)})),this.plugins=[],this.client=e},e.prototype.push=function(e){var t=this;return new Promise((function(n){t.queue.push([e,n]),t.scheduleApply(0)}))},e.prototype.scheduleApply=function(e){var t=this;this.applying||(this.applying=!0,setTimeout((function(){t.apply(t.queue.shift()).then((function(){t.applying=!1,t.queue.length>0&&t.scheduleApply(0)}))}),e))},e.prototype.apply=function(e){return i(this,void 0,void 0,(function(){var t,r,i,a,c,l,d,f,h,p,v,g,y,m,b,w,_,S,E,I;return o(this,(function(o){switch(o.label){case 0:if(!e)return[2];t=u(e,1),r=t[0],i=u(e,2),a=i[1],c=this.plugins.filter((function(e){return"before"===e.type})),o.label=1;case 1:o.trys.push([1,6,7,8]),l=s(c),d=l.next(),o.label=2;case 2:return d.done?[3,5]:(g=d.value).execute?[4,g.execute(n({},r))]:[3,4];case 3:if(null===(y=o.sent()))return a({event:r,code:0,message:""}),[2];r=y,o.label=4;case 4:return d=l.next(),[3,2];case 5:return[3,8];case 6:return f=o.sent(),_={error:f},[3,8];case 7:try{d&&!d.done&&(S=l.return)&&S.call(l)}finally{if(_)throw _.error}return[7];case 8:h=this.plugins.filter((function(e){return"enrichment"===e.type||void 0===e.type})),o.label=9;case 9:o.trys.push([9,14,15,16]),p=s(h),v=p.next(),o.label=10;case 10:return v.done?[3,13]:(g=v.value).execute?[4,g.execute(n({},r))]:[3,12];case 11:if(null===(y=o.sent()))return a({event:r,code:0,message:""}),[2];r=y,o.label=12;case 12:return v=p.next(),[3,10];case 13:return[3,16];case 14:return m=o.sent(),E={error:m},[3,16];case 15:try{v&&!v.done&&(I=p.return)&&I.call(p)}finally{if(E)throw E.error}return[7];case 16:return b=this.plugins.filter((function(e){return"destination"===e.type})),w=b.map((function(e){var t=n({},r);return e.execute(t).catch((function(e){return q(t,0,String(e))}))})),Promise.all(w).then((function(e){var t=u(e,1)[0]||q(r,100,"Event not tracked, no destination plugins on the instance");a(t)})),[2]}}))}))},e.prototype.flush=function(){return i(this,void 0,void 0,(function(){var e,t,n,r=this;return o(this,(function(i){switch(i.label){case 0:return e=this.queue,this.queue=[],[4,Promise.all(e.map((function(e){return r.apply(e)})))];case 1:return i.sent(),t=this.plugins.filter((function(e){return"destination"===e.type})),n=t.map((function(e){return e.flush&&e.flush()})),[4,Promise.all(n)];case 2:return i.sent(),[2]}}))}))},e}(),D="AMP",F="".concat(D,"_unsent"),M="https://api2.amplitude.com/2/httpapi",V=function(e){if(Object.keys(e).length>1e3)return!1;for(var t in e){var n=e[t];if(!z(t,n))return!1}return!0},z=function(e,t){var n,r;if("string"!=typeof e)return!1;if(Array.isArray(t)){var i=!0;try{for(var o=s(t),u=o.next();!u.done;u=o.next()){var a=u.value;if(Array.isArray(a))return!1;if("object"==typeof a)i=i&&V(a);else if(!["number","string"].includes(typeof a))return!1;if(!i)return!1}}catch(e){n={error:e}}finally{try{u&&!u.done&&(r=o.return)&&r.call(o)}finally{if(n)throw n.error}}}else{if(null==t)return!1;if("object"==typeof t)return V(t);if(!["number","string","boolean"].includes(typeof t))return!1}return!0},K=function(){function e(){this._propertySet=new Set,this._properties={}}return e.prototype.getUserProperties=function(){return n({},this._properties)},e.prototype.set=function(e,t){return this._safeSet(f.SET,e,t),this},e.prototype.setOnce=function(e,t){return this._safeSet(f.SET_ONCE,e,t),this},e.prototype.append=function(e,t){return this._safeSet(f.APPEND,e,t),this},e.prototype.prepend=function(e,t){return this._safeSet(f.PREPEND,e,t),this},e.prototype.postInsert=function(e,t){return this._safeSet(f.POSTINSERT,e,t),this},e.prototype.preInsert=function(e,t){return this._safeSet(f.PREINSERT,e,t),this},e.prototype.remove=function(e,t){return this._safeSet(f.REMOVE,e,t),this},e.prototype.add=function(e,t){return this._safeSet(f.ADD,e,t),this},e.prototype.unset=function(e){return this._safeSet(f.UNSET,e,"-"),this},e.prototype.clearAll=function(){return this._properties={},this._properties[f.CLEAR_ALL]="-",this},e.prototype._safeSet=function(e,t,n){if(this._validate(e,t,n)){var r=this._properties[e];return void 0===r&&(r={},this._properties[e]=r),r[t]=n,this._propertySet.add(t),!0}return!1},e.prototype._validate=function(e,t,n){return void 0===this._properties[f.CLEAR_ALL]&&(!this._propertySet.has(t)&&(e===f.ADD?"number"==typeof n:e===f.UNSET||e===f.REMOVE||z(t,n)))},e}(),B=function(e,t){return n(n({},t),{event_type:p.IDENTIFY,user_properties:e.getUserProperties()})},W=function(e){return{promise:e||Promise.resolve()}},$=function(){function e(e){void 0===e&&(e="$default"),this.initializing=!1,this.isReady=!1,this.q=[],this.dispatchQ=[],this.logEvent=this.track.bind(this),this.timeline=new j(this),this.name=e}return e.prototype._init=function(e){return i(this,void 0,void 0,(function(){return o(this,(function(t){switch(t.label){case 0:return this.config=e,this.timeline.reset(this),[4,this.runQueuedFunctions("q")];case 1:return t.sent(),this.isReady=!0,[2]}}))}))},e.prototype.runQueuedFunctions=function(e){return i(this,void 0,void 0,(function(){var t,n,r,i,u,a,c,l;return o(this,(function(o){switch(o.label){case 0:t=this[e],this[e]=[],o.label=1;case 1:o.trys.push([1,8,9,10]),n=s(t),r=n.next(),o.label=2;case 2:return r.done?[3,7]:(i=r.value,(u=i())&&"promise"in u?[4,u.promise]:[3,4]);case 3:return o.sent(),[3,6];case 4:return[4,u];case 5:o.sent(),o.label=6;case 6:return r=n.next(),[3,2];case 7:return[3,10];case 8:return a=o.sent(),c={error:a},[3,10];case 9:try{r&&!r.done&&(l=n.return)&&l.call(n)}finally{if(c)throw c.error}return[7];case 10:return this[e].length?[4,this.runQueuedFunctions(e)]:[3,12];case 11:o.sent(),o.label=12;case 12:return[2]}}))}))},e.prototype.track=function(e,t,r){var i=function(e,t,r){return n(n(n({},"string"==typeof e?{event_type:e}:e),r),t&&{event_properties:t})}(e,t,r);return W(this.dispatch(i))},e.prototype.identify=function(e,t){var n=B(e,t);return W(this.dispatch(n))},e.prototype.groupIdentify=function(e,t,r,i){var o=function(e,t,r,i){var o;return n(n({},i),{event_type:p.GROUP_IDENTIFY,group_properties:r.getUserProperties(),groups:(o={},o[e]=t,o)})}(e,t,r,i);return W(this.dispatch(o))},e.prototype.setGroup=function(e,t,r){var i=function(e,t,r){var i,o=new K;return o.set(e,t),n(n({},r),{event_type:p.IDENTIFY,user_properties:o.getUserProperties(),groups:(i={},i[e]=t,i)})}(e,t,r);return W(this.dispatch(i))},e.prototype.revenue=function(e,t){var r=function(e,t){return n(n({},t),{event_type:p.REVENUE,event_properties:e.getEventProperties()})}(e,t);return W(this.dispatch(r))},e.prototype.add=function(e){return this.isReady?this._addPlugin(e):(this.q.push(this._addPlugin.bind(this,e)),W())},e.prototype._addPlugin=function(e){return W(this.timeline.register(e,this.config))},e.prototype.remove=function(e){return this.isReady?this._removePlugin(e):(this.q.push(this._removePlugin.bind(this,e)),W())},e.prototype._removePlugin=function(e){return W(this.timeline.deregister(e))},e.prototype.dispatchWithCallback=function(e,t){if(!this.isReady)return t(q(e,0,"Client not initialized"));this.process(e).then(t)},e.prototype.dispatch=function(e){return i(this,void 0,void 0,(function(){var t=this;return o(this,(function(n){return this.isReady?[2,this.process(e)]:[2,new Promise((function(n){t.dispatchQ.push(t.dispatchWithCallback.bind(t,e,n))}))]}))}))},e.prototype.process=function(e){return i(this,void 0,void 0,(function(){var t,n,r;return o(this,(function(i){switch(i.label){case 0:return i.trys.push([0,2,,3]),this.config.optOut?[2,q(e,0,"Event skipped due to optOut config")]:[4,this.timeline.push(e)];case 1:return 200===(r=i.sent()).code?this.config.loggerProvider.log(r.message):100===r.code?this.config.loggerProvider.warn(r.message):this.config.loggerProvider.error(r.message),[2,r];case 2:return t=i.sent(),n=String(t),this.config.loggerProvider.error(n),[2,r=q(e,0,n)];case 3:return[2]}}))}))},e.prototype.setOptOut=function(e){this.isReady?this._setOptOut(e):this.q.push(this._setOptOut.bind(this,Boolean(e)))},e.prototype._setOptOut=function(e){this.config.optOut=Boolean(e)},e.prototype.flush=function(){return W(this.timeline.flush())},e}(),J=function(){function e(){this.productId="",this.quantity=1,this.price=0}return e.prototype.setProductId=function(e){return this.productId=e,this},e.prototype.setQuantity=function(e){return e>0&&(this.quantity=e),this},e.prototype.setPrice=function(e){return this.price=e,this},e.prototype.setRevenueType=function(e){return this.revenueType=e,this},e.prototype.setRevenue=function(e){return this.revenue=e,this},e.prototype.setEventProperties=function(e){return V(e)&&(this.properties=e),this},e.prototype.getEventProperties=function(){var e=this.properties?n({},this.properties):{};return e[h.REVENUE_PRODUCT_ID]=this.productId,e[h.REVENUE_QUANTITY]=this.quantity,e[h.REVENUE_PRICE]=this.price,e[h.REVENUE_TYPE]=this.revenueType,e[h.REVENUE]=this.revenue,e},e}(),Q="Amplitude Logger ",Y=function(){function e(){this.logLevel=v.None}return e.prototype.disable=function(){this.logLevel=v.None},e.prototype.enable=function(e){void 0===e&&(e=v.Warn),this.logLevel=e},e.prototype.log=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];this.logLevel<v.Verbose||console.log("".concat(Q,"[Log]: ").concat(e.join(" ")))},e.prototype.warn=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];this.logLevel<v.Warn||console.warn("".concat(Q,"[Warn]: ").concat(e.join(" ")))},e.prototype.error=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];this.logLevel<v.Error||console.error("".concat(Q,"[Error]: ").concat(e.join(" ")))},e.prototype.debug=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];this.logLevel<v.Debug||console.log("".concat(Q,"[Debug]: ").concat(e.join(" ")))},e}(),H=function(){return{flushMaxRetries:12,flushQueueSize:200,flushIntervalMillis:1e4,instanceName:"$default_instance",logLevel:v.Warn,loggerProvider:new Y,offline:!1,optOut:!1,serverUrl:M,serverZone:"US",useBatch:!1}},Z=function(){function e(e){var t,n,r,i;this._optOut=!1;var o=H();this.apiKey=e.apiKey,this.flushIntervalMillis=null!==(t=e.flushIntervalMillis)&&void 0!==t?t:o.flushIntervalMillis,this.flushMaxRetries=e.flushMaxRetries||o.flushMaxRetries,this.flushQueueSize=e.flushQueueSize||o.flushQueueSize,this.instanceName=e.instanceName||o.instanceName,this.loggerProvider=e.loggerProvider||o.loggerProvider,this.logLevel=null!==(n=e.logLevel)&&void 0!==n?n:o.logLevel,this.minIdLength=e.minIdLength,this.plan=e.plan,this.ingestionMetadata=e.ingestionMetadata,this.offline=void 0!==e.offline?e.offline:o.offline,this.optOut=null!==(r=e.optOut)&&void 0!==r?r:o.optOut,this.serverUrl=e.serverUrl,this.serverZone=e.serverZone||o.serverZone,this.storageProvider=e.storageProvider,this.transportProvider=e.transportProvider,this.useBatch=null!==(i=e.useBatch)&&void 0!==i?i:o.useBatch,this.loggerProvider.enable(this.logLevel);var s=X(e.serverUrl,e.serverZone,e.useBatch);this.serverZone=s.serverZone,this.serverUrl=s.serverUrl}return Object.defineProperty(e.prototype,"optOut",{get:function(){return this._optOut},set:function(e){this._optOut=e},enumerable:!1,configurable:!0}),e}(),G=function(e,t){return"EU"===e?t?"https://api.eu.amplitude.com/batch":"https://api.eu.amplitude.com/2/httpapi":t?"https://api2.amplitude.com/batch":M},X=function(e,t,n){if(void 0===e&&(e=""),void 0===t&&(t=H().serverZone),void 0===n&&(n=H().useBatch),e)return{serverUrl:e,serverZone:void 0};var r=["US","EU"].includes(t)?t:H().serverZone;return{serverZone:r,serverUrl:G(r,n)}},ee=function(){function e(){this.sdk={metrics:{histogram:{}}}}return e.prototype.recordHistogram=function(e,t){this.sdk.metrics.histogram[e]=t},e}();function te(e){var t="";try{"body"in e&&(t=JSON.stringify(e.body,null,2))}catch(e){}return t}var ne=function(){function e(){this.name="amplitude",this.type="destination",this.retryTimeout=1e3,this.throttleTimeout=3e4,this.storageKey="",this.scheduled=null,this.queue=[]}return e.prototype.setup=function(e){var t;return i(this,void 0,void 0,(function(){var n,r=this;return o(this,(function(i){switch(i.label){case 0:return this.config=e,this.storageKey="".concat(F,"_").concat(this.config.apiKey.substring(0,10)),[4,null===(t=this.config.storageProvider)||void 0===t?void 0:t.get(this.storageKey)];case 1:return(n=i.sent())&&n.length>0&&Promise.all(n.map((function(e){return r.execute(e)}))).catch(),[2,Promise.resolve(void 0)]}}))}))},e.prototype.execute=function(e){var t=this;return e.insert_id||(e.insert_id=U()),new Promise((function(n){var r={event:e,attempts:0,callback:function(e){return n(e)},timeout:0};t.addToQueue(r)}))},e.prototype.getTryableList=function(e){var t=this;return e.filter((function(e){return e.attempts<t.config.flushMaxRetries?(e.attempts+=1,!0):(t.fulfillRequest([e],500,"Event rejected due to exceeded retry count"),!1)}))},e.prototype.scheduleTryable=function(e,t){var n=this;void 0===t&&(t=!1),e.forEach((function(e){t&&(n.queue=n.queue.concat(e)),0!==e.timeout?setTimeout((function(){e.timeout=0,n.schedule(0)}),e.timeout):n.schedule(n.config.flushIntervalMillis)}))},e.prototype.addToQueue=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];var n=this.getTryableList(e);this.scheduleTryable(n,!0),this.saveEvents()},e.prototype.schedule=function(e){var t=this;this.scheduled||this.config.offline||(this.scheduled=setTimeout((function(){t.flush(!0).then((function(){t.queue.length>0&&t.schedule(e)}))}),e))},e.prototype.flush=function(e){return void 0===e&&(e=!1),i(this,void 0,void 0,(function(){var t,n,r,i=this;return o(this,(function(o){switch(o.label){case 0:return this.config.offline?(this.config.loggerProvider.debug("Skipping flush while offline."),[2]):(t=[],n=[],this.queue.forEach((function(e){return 0===e.timeout?t.push(e):n.push(e)})),this.scheduled&&(clearTimeout(this.scheduled),this.scheduled=null),s=t,u=this.config.flushQueueSize,a=Math.max(u,1),r=s.reduce((function(e,t,n){var r=Math.floor(n/a);return e[r]||(e[r]=[]),e[r].push(t),e}),[]),[4,Promise.all(r.map((function(t){return i.send(t,e)})))]);case 1:return o.sent(),this.scheduleTryable(n),[2]}var s,u,a}))}))},e.prototype.send=function(e,t){return void 0===t&&(t=!0),i(this,void 0,void 0,(function(){var n,i,s,u,a;return o(this,(function(o){switch(o.label){case 0:if(!this.config.apiKey)return[2,this.fulfillRequest(e,400,"Event rejected due to missing API key")];n={api_key:this.config.apiKey,events:e.map((function(e){var t=e.event;return t.extra,r(t,["extra"])})),options:{min_id_length:this.config.minIdLength},client_upload_time:(new Date).toISOString(),request_metadata:this.config.requestMetadata},this.config.requestMetadata=new ee,o.label=1;case 1:return o.trys.push([1,3,,4]),i=X(this.config.serverUrl,this.config.serverZone,this.config.useBatch).serverUrl,[4,this.config.transportProvider.send(i,n)];case 2:return null===(s=o.sent())?(this.fulfillRequest(e,0,"Unexpected error occurred"),[2]):t?(this.handleResponse(s,e),[3,4]):("body"in s?this.fulfillRequest(e,s.statusCode,"".concat(s.status,": ").concat(te(s))):this.fulfillRequest(e,s.statusCode,s.status),[2]);case 3:return u=o.sent(),a=(c=u)instanceof Error?c.message:String(c),this.config.loggerProvider.error(a),this.handleResponse({status:y.Failed,statusCode:0},e),[3,4];case 4:return[2]}var c}))}))},e.prototype.handleResponse=function(e,t){var n=e.status;switch(n){case y.Success:this.handleSuccessResponse(e,t);break;case y.Invalid:this.handleInvalidResponse(e,t);break;case y.PayloadTooLarge:this.handlePayloadTooLargeResponse(e,t);break;case y.RateLimit:this.handleRateLimitResponse(e,t);break;default:this.config.loggerProvider.warn("{code: 0, error: \"Status '".concat(n,"' provided for ").concat(t.length,' events"}')),this.handleOtherResponse(t)}},e.prototype.handleSuccessResponse=function(e,t){this.fulfillRequest(t,e.statusCode,"Event tracked successfully")},e.prototype.handleInvalidResponse=function(e,t){var n=this;if(e.body.missingField||e.body.error.startsWith("Invalid API key"))this.fulfillRequest(t,e.statusCode,e.body.error);else{var r=a(a(a(a([],u(Object.values(e.body.eventsWithInvalidFields)),!1),u(Object.values(e.body.eventsWithMissingFields)),!1),u(Object.values(e.body.eventsWithInvalidIdLengths)),!1),u(e.body.silencedEvents),!1).flat(),i=new Set(r),o=t.filter((function(t,r){if(!i.has(r))return!0;n.fulfillRequest([t],e.statusCode,e.body.error)}));o.length>0&&this.config.loggerProvider.warn(te(e));var s=this.getTryableList(o);this.scheduleTryable(s)}},e.prototype.handlePayloadTooLargeResponse=function(e,t){if(1!==t.length){this.config.loggerProvider.warn(te(e)),this.config.flushQueueSize/=2;var n=this.getTryableList(t);this.scheduleTryable(n)}else this.fulfillRequest(t,e.statusCode,e.body.error)},e.prototype.handleRateLimitResponse=function(e,t){var n=this,r=Object.keys(e.body.exceededDailyQuotaUsers),i=Object.keys(e.body.exceededDailyQuotaDevices),o=e.body.throttledEvents,s=new Set(r),u=new Set(i),a=new Set(o),c=t.filter((function(t,r){if(!(t.event.user_id&&s.has(t.event.user_id)||t.event.device_id&&u.has(t.event.device_id)))return a.has(r)&&(t.timeout=n.throttleTimeout),!0;n.fulfillRequest([t],e.statusCode,e.body.error)}));c.length>0&&this.config.loggerProvider.warn(te(e));var l=this.getTryableList(c);this.scheduleTryable(l)},e.prototype.handleOtherResponse=function(e){var t=this,n=e.map((function(e){return e.timeout=e.attempts*t.retryTimeout,e})),r=this.getTryableList(n);this.scheduleTryable(r)},e.prototype.fulfillRequest=function(e,t,n){this.removeEvents(e),e.forEach((function(e){return e.callback(q(e.event,t,n))}))},e.prototype.saveEvents=function(){if(this.config.storageProvider){var e=this.queue.map((function(e){return e.event}));this.config.storageProvider.set(this.storageKey,e)}},e.prototype.removeEvents=function(e){this.queue=this.queue.filter((function(t){return!e.some((function(e){return e.event.insert_id===t.event.insert_id}))})),this.saveEvents()},e}(),re=function(e){return void 0===e&&(e=0),((new Error).stack||"").split("\n").slice(2+e).map((function(e){return e.trim()}))},ie=function(e){return function(){var t=n({},e.config);return{logger:t.loggerProvider,logLevel:t.logLevel}}},oe=function(e,t){var n,r;t=(t=t.replace(/\[(\w+)\]/g,".$1")).replace(/^\./,"");try{for(var i=s(t.split(".")),o=i.next();!o.done;o=i.next()){var u=o.value;if(!(u in e))return;e=e[u]}}catch(e){n={error:e}}finally{try{o&&!o.done&&(r=i.return)&&r.call(i)}finally{if(n)throw n.error}}return e},se=function(e,t){return function(){var n,r,i={};try{for(var o=s(t),u=o.next();!u.done;u=o.next()){var a=u.value;i[a]=oe(e,a)}}catch(e){n={error:e}}finally{try{u&&!u.done&&(r=o.return)&&r.call(o)}finally{if(n)throw n.error}}return i}},ue=function(e,t,n,r,i){return void 0===i&&(i=null),function(){for(var o=[],s=0;s<arguments.length;s++)o[s]=arguments[s];var u=n(),a=u.logger,c=u.logLevel;if(c&&c<v.Debug||!c||!a)return e.apply(i,o);var l={type:"invoke public method",name:t,args:o,stacktrace:re(1),time:{start:(new Date).toISOString()},states:{}};r&&l.states&&(l.states.before=r());var d=e.apply(i,o);return d&&d.promise?d.promise.then((function(){r&&l.states&&(l.states.after=r()),l.time&&(l.time.end=(new Date).toISOString()),a.debug(JSON.stringify(l,null,2))})):(r&&l.states&&(l.states.after=r()),l.time&&(l.time.end=(new Date).toISOString()),a.debug(JSON.stringify(l,null,2))),d}},ae=function(){function e(){this.memoryStorage=new Map}return e.prototype.isEnabled=function(){return i(this,void 0,void 0,(function(){return o(this,(function(e){return[2,!0]}))}))},e.prototype.get=function(e){return i(this,void 0,void 0,(function(){return o(this,(function(t){return[2,this.memoryStorage.get(e)]}))}))},e.prototype.getRaw=function(e){return i(this,void 0,void 0,(function(){var t;return o(this,(function(n){switch(n.label){case 0:return[4,this.get(e)];case 1:return[2,(t=n.sent())?JSON.stringify(t):void 0]}}))}))},e.prototype.set=function(e,t){return i(this,void 0,void 0,(function(){return o(this,(function(n){return this.memoryStorage.set(e,t),[2]}))}))},e.prototype.remove=function(e){return i(this,void 0,void 0,(function(){return o(this,(function(t){return this.memoryStorage.delete(e),[2]}))}))},e.prototype.reset=function(){return i(this,void 0,void 0,(function(){return o(this,(function(e){return this.memoryStorage.clear(),[2]}))}))},e}(),ce=function(){function e(){}return e.prototype.send=function(e,t){return Promise.resolve(null)},e.prototype.buildResponse=function(e){var t,n,r,i,o,s,u,a,c,l,d,f,h,p,v,g,m,b,w,_,S,E;if("object"!=typeof e)return null;var I=e.code||0,T=this.buildStatus(I);switch(T){case y.Success:return{status:T,statusCode:I,body:{eventsIngested:null!==(t=e.events_ingested)&&void 0!==t?t:0,payloadSizeBytes:null!==(n=e.payload_size_bytes)&&void 0!==n?n:0,serverUploadTime:null!==(r=e.server_upload_time)&&void 0!==r?r:0}};case y.Invalid:return{status:T,statusCode:I,body:{error:null!==(i=e.error)&&void 0!==i?i:"",missingField:null!==(o=e.missing_field)&&void 0!==o?o:"",eventsWithInvalidFields:null!==(s=e.events_with_invalid_fields)&&void 0!==s?s:{},eventsWithMissingFields:null!==(u=e.events_with_missing_fields)&&void 0!==u?u:{},eventsWithInvalidIdLengths:null!==(a=e.events_with_invalid_id_lengths)&&void 0!==a?a:{},epsThreshold:null!==(c=e.eps_threshold)&&void 0!==c?c:0,exceededDailyQuotaDevices:null!==(l=e.exceeded_daily_quota_devices)&&void 0!==l?l:{},silencedDevices:null!==(d=e.silenced_devices)&&void 0!==d?d:[],silencedEvents:null!==(f=e.silenced_events)&&void 0!==f?f:[],throttledDevices:null!==(h=e.throttled_devices)&&void 0!==h?h:{},throttledEvents:null!==(p=e.throttled_events)&&void 0!==p?p:[]}};case y.PayloadTooLarge:return{status:T,statusCode:I,body:{error:null!==(v=e.error)&&void 0!==v?v:""}};case y.RateLimit:return{status:T,statusCode:I,body:{error:null!==(g=e.error)&&void 0!==g?g:"",epsThreshold:null!==(m=e.eps_threshold)&&void 0!==m?m:0,throttledDevices:null!==(b=e.throttled_devices)&&void 0!==b?b:{},throttledUsers:null!==(w=e.throttled_users)&&void 0!==w?w:{},exceededDailyQuotaDevices:null!==(_=e.exceeded_daily_quota_devices)&&void 0!==_?_:{},exceededDailyQuotaUsers:null!==(S=e.exceeded_daily_quota_users)&&void 0!==S?S:{},throttledEvents:null!==(E=e.throttled_events)&&void 0!==E?E:[]}};case y.Timeout:default:return{status:T,statusCode:I}}},e.prototype.buildStatus=function(e){return e>=200&&e<300?y.Success:429===e?y.RateLimit:413===e?y.PayloadTooLarge:408===e?y.Timeout:e>=400&&e<500?y.Invalid:e>=500?y.Failed:y.Unknown},e}(),le=function(e,t,n){return void 0===t&&(t=""),void 0===n&&(n=10),[D,t,e.substring(0,n)].filter(Boolean).join("_")},de=function(e){var t=e.split(".");return t.length<=2?e:t.slice(t.length-2,t.length).join(".")},fe=function(e,t,n,i){void 0===i&&(i=!0),e.referrer;var o=e.referring_domain,s=r(e,["referrer","referring_domain"]),u=t||{};u.referrer;var a=u.referring_domain,c=r(u,["referrer","referring_domain"]);if(he(n.excludeReferrers,e.referring_domain))return!1;if(!i&&function(e){return Object.values(e).every((function(e){return!e}))}(e)&&t)return!1;var l=JSON.stringify(s)!==JSON.stringify(c),d=de(o||"")!==de(a||"");return!t||l||d},he=function(e,t){return void 0===e&&(e=[]),void 0===t&&(t=""),e.some((function(e){return e instanceof RegExp?e.test(t):e===t}))},pe=function(e,t){return void 0===t&&(t=Date.now()),Date.now()-t>e},ve=function(){function e(e,t){var r,i,o,s,u,a;this.shouldTrackNewCampaign=!1,this.options=n({initialEmptyValue:"EMPTY",resetSessionOnNewCampaign:!1,excludeReferrers:(i=null===(r=t.cookieOptions)||void 0===r?void 0:r.domain,o=i,o?(o.startsWith(".")&&(o=o.substring(1)),[new RegExp("".concat(o.replace(".","\\."),"$"))]):[])},e),this.storage=t.cookieStorage,this.storageKey=(s=t.apiKey,void 0===(u="MKTG")&&(u=""),void 0===a&&(a=10),[D,u,s.substring(0,a)].filter(Boolean).join("_")),this.currentCampaign=N,this.sessionTimeout=t.sessionTimeout,this.lastEventTime=t.lastEventTime,t.loggerProvider.log("Installing web attribution tracking.")}return e.prototype.init=function(){return i(this,void 0,void 0,(function(){var e,t;return o(this,(function(n){switch(n.label){case 0:return[4,this.fetchCampaign()];case 1:return t=u.apply(void 0,[n.sent(),2]),this.currentCampaign=t[0],this.previousCampaign=t[1],e=!this.lastEventTime||pe(this.sessionTimeout,this.lastEventTime),fe(this.currentCampaign,this.previousCampaign,this.options,e)?(this.shouldTrackNewCampaign=!0,[4,this.storage.set(this.storageKey,this.currentCampaign)]):[3,3];case 2:n.sent(),n.label=3;case 3:return[2]}}))}))},e.prototype.fetchCampaign=function(){return i(this,void 0,void 0,(function(){return o(this,(function(e){switch(e.label){case 0:return[4,Promise.all([(new R).parse(),this.storage.get(this.storageKey)])];case 1:return[2,e.sent()]}}))}))},e.prototype.generateCampaignEvent=function(e){this.shouldTrackNewCampaign=!1;var t,r,i,o,s=(t=this.currentCampaign,r=this.options,i=n(n({},N),t),o=Object.entries(i).reduce((function(e,t){var n,i=u(t,2),o=i[0],s=i[1];return e.setOnce("initial_".concat(o),null!==(n=null!=s?s:r.initialEmptyValue)&&void 0!==n?n:"EMPTY"),s?e.set(o,s):e.unset(o)}),new K),B(o));return e&&(s.event_id=e),s},e.prototype.shouldSetSessionIdOnNewCampaign=function(){return this.shouldTrackNewCampaign&&!!this.options.resetSessionOnNewCampaign},e}(),ge=function(){function e(e){this.options=n({},e)}return e.prototype.isEnabled=function(){return i(this,void 0,void 0,(function(){var t,n;return o(this,(function(r){switch(r.label){case 0:if(!m())return[2,!1];e.testValue=String(Date.now()),t=new e(this.options),n="AMP_TEST",r.label=1;case 1:return r.trys.push([1,4,5,7]),[4,t.set(n,e.testValue)];case 2:return r.sent(),[4,t.get(n)];case 3:return[2,r.sent()===e.testValue];case 4:return r.sent(),[2,!1];case 5:return[4,t.remove(n)];case 6:return r.sent(),[7];case 7:return[2]}}))}))},e.prototype.get=function(e){var t;return i(this,void 0,void 0,(function(){var n,r;return o(this,(function(i){switch(i.label){case 0:return[4,this.getRaw(e)];case 1:if(!(n=i.sent()))return[2,void 0];try{return void 0===(r=null!==(t=ye(n))&&void 0!==t?t:me(n))?(console.error("Amplitude Logger [Error]: Failed to decode cookie value for key: ".concat(e,", value: ").concat(n)),[2,void 0]):[2,JSON.parse(r)]}catch(t){return console.error("Amplitude Logger [Error]: Failed to parse cookie value for key: ".concat(e,", value: ").concat(n)),[2,void 0]}return[2]}}))}))},e.prototype.getRaw=function(e){var t,n;return i(this,void 0,void 0,(function(){var r,i,s;return o(this,(function(o){return r=m(),i=null!==(n=null===(t=null==r?void 0:r.document)||void 0===t?void 0:t.cookie.split("; "))&&void 0!==n?n:[],(s=i.find((function(t){return 0===t.indexOf(e+"=")})))?[2,s.substring(e.length+1)]:[2,void 0]}))}))},e.prototype.set=function(e,t){var n;return i(this,void 0,void 0,(function(){var r,i,s,u,a,c,l;return o(this,(function(o){try{r=null!==(n=this.options.expirationDays)&&void 0!==n?n:0,s=void 0,(i=null!==t?r:-1)&&((u=new Date).setTime(u.getTime()+24*i*60*60*1e3),s=u),a="".concat(e,"=").concat(btoa(encodeURIComponent(JSON.stringify(t)))),s&&(a+="; expires=".concat(s.toUTCString())),a+="; path=/",this.options.domain&&(a+="; domain=".concat(this.options.domain)),this.options.secure&&(a+="; Secure"),this.options.sameSite&&(a+="; SameSite=".concat(this.options.sameSite)),(c=m())&&(c.document.cookie=a)}catch(t){l=t instanceof Error?t.message:String(t),console.error("Amplitude Logger [Error]: Failed to set cookie for key: ".concat(e,". Error: ").concat(l))}return[2]}))}))},e.prototype.remove=function(e){return i(this,void 0,void 0,(function(){return o(this,(function(t){switch(t.label){case 0:return[4,this.set(e,null)];case 1:return t.sent(),[2]}}))}))},e.prototype.reset=function(){return i(this,void 0,void 0,(function(){return o(this,(function(e){return[2]}))}))},e}(),ye=function(e){try{return decodeURIComponent(atob(e))}catch(e){return}},me=function(e){try{return decodeURIComponent(atob(decodeURIComponent(e)))}catch(e){return}},be=function(e){function n(){return null!==e&&e.apply(this,arguments)||this}return t(n,e),n.prototype.send=function(e,t){return i(this,void 0,void 0,(function(){var n,r,i;return o(this,(function(o){switch(o.label){case 0:if("undefined"==typeof fetch)throw new Error("FetchTransport is not supported");return n={headers:{"Content-Type":"application/json",Accept:"*/*"},body:JSON.stringify(t),method:"POST"},[4,fetch(e,n)];case 1:return[4,(r=o.sent()).text()];case 2:i=o.sent();try{return[2,this.buildResponse(JSON.parse(i))]}catch(e){return[2,this.buildResponse({code:r.status})]}return[2]}}))}))},n}(ce),we=function(){function e(){}return e.prototype.getApplicationContext=function(){return{versionName:this.versionName,language:_e(),platform:"Web",os:void 0,deviceModel:void 0}},e}(),_e=function(){return"undefined"!=typeof navigator&&(navigator.languages&&navigator.languages[0]||navigator.language)||""},Se=function(){function e(){this.queue=[]}return e.prototype.logEvent=function(e){this.receiver?this.receiver(e):this.queue.length<512&&this.queue.push(e)},e.prototype.setEventReceiver=function(e){this.receiver=e,this.queue.length>0&&(this.queue.forEach((function(t){e(t)})),this.queue=[])},e}(),Ee=function(){return Ee=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var i in t=arguments[n])Object.prototype.hasOwnProperty.call(t,i)&&(e[i]=t[i]);return e},Ee.apply(this,arguments)},Ie=function(e,t){var n=typeof e;if(n!==typeof t)return!1;for(var r=0,i=["string","number","boolean","undefined"];r<i.length;r++){if(i[r]===n)return e===t}if(null==e&&null==t)return!0;if(null==e||null==t)return!1;if(e.length!==t.length)return!1;var o=Array.isArray(e),s=Array.isArray(t);if(o!==s)return!1;if(!o||!s){var u=Object.keys(e).sort(),a=Object.keys(t).sort();if(!Ie(u,a))return!1;var c=!0;return Object.keys(e).forEach((function(n){Ie(e[n],t[n])||(c=!1)})),c}for(var l=0;l<e.length;l++)if(!Ie(e[l],t[l]))return!1;return!0};Object.entries||(Object.entries=function(e){for(var t=Object.keys(e),n=t.length,r=new Array(n);n--;)r[n]=[t[n],e[t[n]]];return r});var Te=function(){function e(){this.identity={userProperties:{}},this.listeners=new Set}return e.prototype.editIdentity=function(){var e=this,t=Ee({},this.identity.userProperties),n=Ee(Ee({},this.identity),{userProperties:t});return{setUserId:function(e){return n.userId=e,this},setDeviceId:function(e){return n.deviceId=e,this},setUserProperties:function(e){return n.userProperties=e,this},updateUserProperties:function(e){for(var t=n.userProperties||{},r=0,i=Object.entries(e);r<i.length;r++){var o=i[r],s=o[0],u=o[1];switch(s){case"$set":for(var a=0,c=Object.entries(u);a<c.length;a++){var l=c[a],d=l[0],f=l[1];t[d]=f}break;case"$unset":for(var h=0,p=Object.keys(u);h<p.length;h++){delete t[d=p[h]]}break;case"$clearAll":t={}}}return n.userProperties=t,this},commit:function(){return e.setIdentity(n),this}}},e.prototype.getIdentity=function(){return Ee({},this.identity)},e.prototype.setIdentity=function(e){var t=Ee({},this.identity);this.identity=Ee({},e),Ie(t,this.identity)||this.listeners.forEach((function(t){t(e)}))},e.prototype.addIdentityListener=function(e){this.listeners.add(e)},e.prototype.removeIdentityListener=function(e){this.listeners.delete(e)},e}(),ke="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof global?global:self,Oe=function(){function e(){this.identityStore=new Te,this.eventBridge=new Se,this.applicationContextProvider=new we}return e.getInstance=function(t){return ke.analyticsConnectorInstances||(ke.analyticsConnectorInstances={}),ke.analyticsConnectorInstances[t]||(ke.analyticsConnectorInstances[t]=new e),ke.analyticsConnectorInstances[t]},e}(),Pe=function(e){return void 0===e&&(e="$default_instance"),Oe.getInstance(e)},xe=function(){function e(){this.name="identity",this.type="before",this.identityStore=Pe().identityStore}return e.prototype.execute=function(e){return i(this,void 0,void 0,(function(){var t;return o(this,(function(n){return(t=e.user_properties)&&this.identityStore.editIdentity().updateUserProperties(t).commit(),[2,e]}))}))},e.prototype.setup=function(e){return i(this,void 0,void 0,(function(){return o(this,(function(t){return e.instanceName&&(this.identityStore=Pe(e.instanceName).identityStore),[2]}))}))},e}(),Ae=function(){var e,t,n,r;if("undefined"==typeof navigator)return"";var i=navigator.userLanguage;return null!==(r=null!==(n=null!==(t=null===(e=navigator.languages)||void 0===e?void 0:e[0])&&void 0!==t?t:navigator.language)&&void 0!==n?n:i)&&void 0!==r?r:""},Ce=function(e,t){return"boolean"==typeof e?e:!1!==(null==e?void 0:e[t])},Ne=function(e){return Ce(e,"attribution")},Re=function(e){return Ce(e,"pageViews")},Le=function(e){return Ce(e,"sessions")},qe=function(e){return"boolean"==typeof e?e:"object"==typeof e&&(!0===e.elementInteractions||"object"==typeof e.elementInteractions)},Ue=function(e){if(qe(e.autocapture)&&"object"==typeof e.autocapture&&"object"==typeof e.autocapture.elementInteractions)return e.autocapture.elementInteractions},je=function(e){var t,n=function(){return!1},r=void 0,i=e.pageCounter;return Re(e.defaultTracking)&&(n=void 0,t=void 0,e.defaultTracking&&"object"==typeof e.defaultTracking&&e.defaultTracking.pageViews&&"object"==typeof e.defaultTracking.pageViews&&("trackOn"in e.defaultTracking.pageViews&&(n=e.defaultTracking.pageViews.trackOn),"trackHistoryChanges"in e.defaultTracking.pageViews&&(r=e.defaultTracking.pageViews.trackHistoryChanges),"eventType"in e.defaultTracking.pageViews&&e.defaultTracking.pageViews.eventType&&(t=e.defaultTracking.pageViews.eventType))),{trackOn:n,trackHistoryChanges:r,eventType:t,pageCounter:i}},De=function(e,t){Fe(e,t)},Fe=function(e,t){for(var n=0;n<t.length;n++){var r=t[n],i=r.name,o=r.args,s=r.resolve,u=e&&e[i];if("function"==typeof u){var a=u.apply(e,o);"function"==typeof s&&s(null==a?void 0:a.promise)}}return e},Me=function(e){return e&&void 0!==e._q},Ve=function(){function e(){this.name="@amplitude/plugin-context-browser",this.type="before",this.library="amplitude-ts/".concat("2.11.1"),"undefined"!=typeof navigator&&(this.userAgent=navigator.userAgent)}return e.prototype.setup=function(e){return this.config=e,Promise.resolve(void 0)},e.prototype.execute=function(e){var t,r;return i(this,void 0,void 0,(function(){var i,s,u;return o(this,(function(o){return i=(new Date).getTime(),s=null!==(t=this.config.lastEventId)&&void 0!==t?t:-1,u=null!==(r=e.event_id)&&void 0!==r?r:s+1,this.config.lastEventId=u,e.time||(this.config.lastEventTime=i),[2,n(n(n(n(n(n(n(n({user_id:this.config.userId,device_id:this.config.deviceId,session_id:this.config.sessionId,time:i},this.config.appVersion&&{app_version:this.config.appVersion}),this.config.trackingOptions.platform&&{platform:"Web"}),this.config.trackingOptions.language&&{language:Ae()}),this.config.trackingOptions.ipAddress&&{ip:"$remote"}),{insert_id:U(),partner_id:this.config.partnerId,plan:this.config.plan}),this.config.ingestionMetadata&&{ingestion_metadata:{source_name:this.config.ingestionMetadata.sourceName,source_version:this.config.ingestionMetadata.sourceVersion}}),e),{event_id:u,library:this.library,user_agent:this.userAgent})]}))}))},e}(),ze=function(){function e(e){this.storage=e}return e.prototype.isEnabled=function(){return i(this,void 0,void 0,(function(){var t,n,r;return o(this,(function(i){switch(i.label){case 0:if(!this.storage)return[2,!1];t=String(Date.now()),n=new e(this.storage),r="AMP_TEST",i.label=1;case 1:return i.trys.push([1,4,5,7]),[4,n.set(r,t)];case 2:return i.sent(),[4,n.get(r)];case 3:return[2,i.sent()===t];case 4:return i.sent(),[2,!1];case 5:return[4,n.remove(r)];case 6:return i.sent(),[7];case 7:return[2]}}))}))},e.prototype.get=function(e){return i(this,void 0,void 0,(function(){var t;return o(this,(function(n){switch(n.label){case 0:return n.trys.push([0,2,,3]),[4,this.getRaw(e)];case 1:return(t=n.sent())?[2,JSON.parse(t)]:[2,void 0];case 2:return n.sent(),console.error("[Amplitude] Error: Could not get value from storage"),[2,void 0];case 3:return[2]}}))}))},e.prototype.getRaw=function(e){var t;return i(this,void 0,void 0,(function(){return o(this,(function(n){return[2,(null===(t=this.storage)||void 0===t?void 0:t.getItem(e))||void 0]}))}))},e.prototype.set=function(e,t){var n;return i(this,void 0,void 0,(function(){return o(this,(function(r){try{null===(n=this.storage)||void 0===n||n.setItem(e,JSON.stringify(t))}catch(e){}return[2]}))}))},e.prototype.remove=function(e){var t;return i(this,void 0,void 0,(function(){return o(this,(function(n){try{null===(t=this.storage)||void 0===t||t.removeItem(e)}catch(e){}return[2]}))}))},e.prototype.reset=function(){var e;return i(this,void 0,void 0,(function(){return o(this,(function(t){try{null===(e=this.storage)||void 0===e||e.clear()}catch(e){}return[2]}))}))},e}(),Ke=1e3,Be=function(e){function n(t){var n,r=this;return(r=e.call(this,null===(n=m())||void 0===n?void 0:n.localStorage)||this).loggerProvider=null==t?void 0:t.loggerProvider,r}return t(n,e),n.prototype.set=function(t,n){var r;return i(this,void 0,void 0,(function(){var i;return o(this,(function(o){switch(o.label){case 0:return Array.isArray(n)&&n.length>Ke?(i=n.length-Ke,[4,e.prototype.set.call(this,t,n.slice(0,Ke))]):[3,2];case 1:return o.sent(),null===(r=this.loggerProvider)||void 0===r||r.error("Failed to save ".concat(i," events because the queue length exceeded ").concat(Ke,".")),[3,4];case 2:return[4,e.prototype.set.call(this,t,n)];case 3:o.sent(),o.label=4;case 4:return[2]}}))}))},n}(ze),We=function(e){function n(){var t;return e.call(this,null===(t=m())||void 0===t?void 0:t.sessionStorage)||this}return t(n,e),n}(ze),$e=function(e){function n(){var t=null!==e&&e.apply(this,arguments)||this;return t.state={done:4},t}return t(n,e),n.prototype.send=function(e,t){return i(this,void 0,void 0,(function(){var n=this;return o(this,(function(r){return[2,new Promise((function(r,i){"undefined"==typeof XMLHttpRequest&&i(new Error("XHRTransport is not supported."));var o=new XMLHttpRequest;o.open("POST",e,!0),o.onreadystatechange=function(){if(o.readyState===n.state.done){var e=o.responseText;try{r(n.buildResponse(JSON.parse(e)))}catch(e){r(n.buildResponse({code:o.status}))}}},o.setRequestHeader("Content-Type","application/json"),o.setRequestHeader("Accept","*/*"),o.send(JSON.stringify(t))}))]}))}))},n}(ce),Je=function(e){function n(){return null!==e&&e.apply(this,arguments)||this}return t(n,e),n.prototype.send=function(e,t){return i(this,void 0,void 0,(function(){var n=this;return o(this,(function(r){return[2,new Promise((function(r,i){var o=m();if(!(null==o?void 0:o.navigator.sendBeacon))throw new Error("SendBeaconTransport is not supported");try{var s=JSON.stringify(t);return r(o.navigator.sendBeacon(e,JSON.stringify(t))?n.buildResponse({code:200,events_ingested:t.events.length,payload_size_bytes:s.length,server_upload_time:Date.now()}):n.buildResponse({code:500}))}catch(e){i(e)}}))]}))}))},n}(ce),Qe=function(e,t,n){return void 0===n&&(n=!0),i(void 0,void 0,void 0,(function(){var r,i,s,a,c,l,d,f,h;return o(this,(function(o){switch(o.label){case 0:return r=function(e){return"".concat(D.toLowerCase(),"_").concat(e.substring(0,6))}(e),[4,t.getRaw(r)];case 1:return(i=o.sent())?n?[4,t.remove(r)]:[3,3]:[2,{optOut:!1}];case 2:o.sent(),o.label=3;case 3:return s=u(i.split("."),6),a=s[0],c=s[1],l=s[2],d=s[3],f=s[4],h=s[5],[2,{deviceId:a,userId:He(c),sessionId:Ye(d),lastEventId:Ye(h),lastEventTime:Ye(f),optOut:Boolean(l)}]}}))}))},Ye=function(e){var t=parseInt(e,32);if(!isNaN(t))return t},He=function(e){if(atob&&escape&&e)try{return decodeURIComponent(escape(atob(e)))}catch(e){return}},Ze="[Amplitude]",Ge="".concat(Ze," Form Started"),Xe="".concat(Ze," Form Submitted"),et="".concat(Ze," File Downloaded"),tt="session_start",nt="session_end",rt="".concat(Ze," File Extension"),it="".concat(Ze," File Name"),ot="".concat(Ze," Link ID"),st="".concat(Ze," Link Text"),ut="".concat(Ze," Link URL"),at="".concat(Ze," Form ID"),ct="".concat(Ze," Form Name"),lt="".concat(Ze," Form Destination"),dt="cookie",ft=function(e){function n(t,n,r,i,o,s,u,a,c,l,d,f,h,p,g,y,m,b,w,_,S,E,I,T,k,O,P,x,A,C,N,R,L,q){void 0===r&&(r=new ae),void 0===i&&(i={domain:"",expiration:365,sameSite:"Lax",secure:!1,upgrade:!0}),void 0===a&&(a=1e3),void 0===c&&(c=5),void 0===l&&(l=30),void 0===d&&(d=dt),void 0===y&&(y=new Y),void 0===m&&(m=v.Warn),void 0===w&&(w=!1),void 0===_&&(_=!1),void 0===I&&(I=""),void 0===T&&(T="US"),void 0===O&&(O=18e5),void 0===P&&(P=new Be({loggerProvider:y})),void 0===x&&(x={ipAddress:!0,language:!0,platform:!0}),void 0===A&&(A="fetch"),void 0===C&&(C=!1),void 0===N&&(N=!1);var U=e.call(this,{apiKey:t,storageProvider:P,transportProvider:vt(A)})||this;return U.apiKey=t,U.appVersion=n,U.cookieOptions=i,U.defaultTracking=o,U.autocapture=s,U.flushIntervalMillis=a,U.flushMaxRetries=c,U.flushQueueSize=l,U.identityStorage=d,U.ingestionMetadata=f,U.instanceName=h,U.loggerProvider=y,U.logLevel=m,U.minIdLength=b,U.offline=w,U.partnerId=S,U.plan=E,U.serverUrl=I,U.serverZone=T,U.sessionTimeout=O,U.storageProvider=P,U.trackingOptions=x,U.transport=A,U.useBatch=C,U.fetchRemoteConfig=N,U._optOut=!1,U._cookieStorage=r,U.deviceId=u,U.lastEventId=p,U.lastEventTime=g,U.optOut=_,U.sessionId=k,U.pageCounter=L,U.userId=R,U.debugLogsEnabled=q,U.loggerProvider.enable(q?v.Debug:U.logLevel),U}return t(n,e),Object.defineProperty(n.prototype,"cookieStorage",{get:function(){return this._cookieStorage},set:function(e){this._cookieStorage!==e&&(this._cookieStorage=e,this.updateStorage())},enumerable:!1,configurable:!0}),Object.defineProperty(n.prototype,"deviceId",{get:function(){return this._deviceId},set:function(e){this._deviceId!==e&&(this._deviceId=e,this.updateStorage())},enumerable:!1,configurable:!0}),Object.defineProperty(n.prototype,"userId",{get:function(){return this._userId},set:function(e){this._userId!==e&&(this._userId=e,this.updateStorage())},enumerable:!1,configurable:!0}),Object.defineProperty(n.prototype,"sessionId",{get:function(){return this._sessionId},set:function(e){this._sessionId!==e&&(this._sessionId=e,this.updateStorage())},enumerable:!1,configurable:!0}),Object.defineProperty(n.prototype,"optOut",{get:function(){return this._optOut},set:function(e){this._optOut!==e&&(this._optOut=e,this.updateStorage())},enumerable:!1,configurable:!0}),Object.defineProperty(n.prototype,"lastEventTime",{get:function(){return this._lastEventTime},set:function(e){this._lastEventTime!==e&&(this._lastEventTime=e,this.updateStorage())},enumerable:!1,configurable:!0}),Object.defineProperty(n.prototype,"lastEventId",{get:function(){return this._lastEventId},set:function(e){this._lastEventId!==e&&(this._lastEventId=e,this.updateStorage())},enumerable:!1,configurable:!0}),Object.defineProperty(n.prototype,"pageCounter",{get:function(){return this._pageCounter},set:function(e){this._pageCounter!==e&&(this._pageCounter=e,this.updateStorage())},enumerable:!1,configurable:!0}),Object.defineProperty(n.prototype,"debugLogsEnabled",{set:function(e){this._debugLogsEnabled!==e&&(this._debugLogsEnabled=e,this.updateStorage())},enumerable:!1,configurable:!0}),n.prototype.updateStorage=function(){var e={deviceId:this._deviceId,userId:this._userId,sessionId:this._sessionId,optOut:this._optOut,lastEventTime:this._lastEventTime,lastEventId:this._lastEventId,pageCounter:this._pageCounter,debugLogsEnabled:this._debugLogsEnabled};this.cookieStorage.set(le(this.apiKey),e)},n}(Z),ht=function(e,t,r){return void 0===t&&(t={}),i(void 0,void 0,void 0,(function(){var i,s,u,a,c,l,d,f,h,p,v,g,y,m,w,_,S,E,I,T,k,O,P,x,A,C,N,R,L,q,j,D,F,M,V,z,K,B,W,$,J,Q,Y;return o(this,(function(o){switch(o.label){case 0:return i=t.identityStorage||dt,I={},i===dt?[3,1]:(u="",[3,5]);case 1:return null===(k=null===(T=t.cookieOptions)||void 0===T?void 0:T.domain)||void 0===k?[3,2]:(a=k,[3,4]);case 2:return[4,gt()];case 3:a=o.sent(),o.label=4;case 4:u=a,o.label=5;case 5:return s=n.apply(void 0,[(I.domain=u,I.expiration=365,I.sameSite="Lax",I.secure=!1,I.upgrade=!0,I),t.cookieOptions]),c=pt(t.identityStorage,s),[4,Qe(e,c,null===(P=null===(O=t.cookieOptions)||void 0===O?void 0:O.upgrade)||void 0===P||P)];case 6:return l=o.sent(),[4,c.get(le(e))];case 7:return d=o.sent(),f=b(),h=null!==(R=null!==(N=null!==(C=null!==(A=null!==(x=t.deviceId)&&void 0!==x?x:f.ampDeviceId)&&void 0!==A?A:f.deviceId)&&void 0!==C?C:null==d?void 0:d.deviceId)&&void 0!==N?N:l.deviceId)&&void 0!==R?R:U(),p=null!==(L=null==d?void 0:d.lastEventId)&&void 0!==L?L:l.lastEventId,v=null!==(q=null==d?void 0:d.lastEventTime)&&void 0!==q?q:l.lastEventTime,g=null!==(D=null!==(j=t.optOut)&&void 0!==j?j:null==d?void 0:d.optOut)&&void 0!==D?D:l.optOut,y=null!==(F=null==d?void 0:d.sessionId)&&void 0!==F?F:l.sessionId,m=null!==(V=null!==(M=t.userId)&&void 0!==M?M:null==d?void 0:d.userId)&&void 0!==V?V:l.userId,r.previousSessionDeviceId=null!==(z=null==d?void 0:d.deviceId)&&void 0!==z?z:l.deviceId,r.previousSessionUserId=null!==(K=null==d?void 0:d.userId)&&void 0!==K?K:l.userId,w={ipAddress:null===(W=null===(B=t.trackingOptions)||void 0===B?void 0:B.ipAddress)||void 0===W||W,language:null===(J=null===($=t.trackingOptions)||void 0===$?void 0:$.language)||void 0===J||J,platform:null===(Y=null===(Q=t.trackingOptions)||void 0===Q?void 0:Q.platform)||void 0===Y||Y},_=null==d?void 0:d.pageCounter,S=null==d?void 0:d.debugLogsEnabled,void 0!==t.autocapture&&(t.defaultTracking=t.autocapture),[4,(E=new ft(e,t.appVersion,c,s,t.defaultTracking,t.autocapture,h,t.flushIntervalMillis,t.flushMaxRetries,t.flushQueueSize,i,t.ingestionMetadata,t.instanceName,p,v,t.loggerProvider,t.logLevel,t.minIdLength,t.offline,g,t.partnerId,t.plan,t.serverUrl,t.serverZone,y,t.sessionTimeout,t.storageProvider,w,t.transport,t.useBatch,t.fetchRemoteConfig,m,_,S)).storageProvider.isEnabled()];case 8:return o.sent()||(E.loggerProvider.warn("Storage provider ".concat(E.storageProvider.constructor.name," is not enabled. Falling back to MemoryStorage.")),E.storageProvider=new ae),[2,E]}}))}))},pt=function(e,t){switch(void 0===e&&(e=dt),void 0===t&&(t={}),e){case"localStorage":return new Be;case"sessionStorage":return new We;case"none":return new ae;default:return new ge(n(n({},t),{expirationDays:t.expiration}))}},vt=function(e){return"xhr"===e?new $e:"beacon"===e?new Je:new be},gt=function(e){return i(void 0,void 0,void 0,(function(){var t,n,r,i,s,u,a;return o(this,(function(o){switch(o.label){case 0:return[4,(new ge).isEnabled()];case 1:if(!o.sent()||!e&&("undefined"==typeof location||!location.hostname))return[2,""];for(t=null!=e?e:location.hostname,n=t.split("."),r=[],i="AMP_TLDTEST",s=n.length-2;s>=0;--s)r.push(n.slice(s).join("."));s=0,o.label=2;case 2:return s<r.length?(u=r[s],[4,(a=new ge({domain:"."+u})).set(i,1)]):[3,7];case 3:return o.sent(),[4,a.get(i)];case 4:return o.sent()?[4,a.remove(i)]:[3,6];case 5:return o.sent(),[2,"."+u];case 6:return s++,[3,2];case 7:return[2,""]}}))}))},yt=function(e){var t={};for(var n in e){var r=e[n];r&&(t[n]=r)}return t},mt=function(e){var t;void 0===e&&(e={});var r,s,a=m(),c=void 0,l=e.trackOn,d=e.trackHistoryChanges,f=e.eventType,h=void 0===f?"[Amplitude] Page Viewed":f,p=function(){return i(void 0,void 0,void 0,(function(){var e,t;return o(this,(function(r){switch(r.label){case 0:return t={event_type:h},e=[{}],[4,bt()];case 1:return[2,(t.event_properties=n.apply(void 0,[n.apply(void 0,e.concat([r.sent()])),{"[Amplitude] Page Domain":"undefined"!=typeof location&&location.hostname||"","[Amplitude] Page Location":"undefined"!=typeof location&&location.href||"","[Amplitude] Page Path":"undefined"!=typeof location&&location.pathname||"","[Amplitude] Page Title":"undefined"!=typeof document&&document.title||"","[Amplitude] Page URL":"undefined"!=typeof location&&location.href.split("?")[0]||""}]),t)]}}))}))},v=function(){return void 0===l||"function"==typeof l&&l()},g="undefined"!=typeof location?location.href:null,y=function(){return i(void 0,void 0,void 0,(function(){var e,n,r,i;return o(this,(function(o){switch(o.label){case 0:return e=location.href,n=_t(d,e,g||"")&&v(),g=e,n?(null==c||c.log("Tracking page view event"),null!=t?[3,1]:[3,3]):[3,4];case 1:return i=(r=t).track,[4,p()];case 2:i.apply(r,[o.sent()]),o.label=3;case 3:o.label=4;case 4:return[2]}}))}))},b=function(){y()},w={name:"@amplitude/plugin-page-view-tracking-browser",type:"enrichment",setup:function(e,n){return i(void 0,void 0,void 0,(function(){var i,l;return o(this,(function(o){switch(o.label){case 0:return t=n,s=e,(c=e.loggerProvider).log("Installing @amplitude/plugin-page-view-tracking-browser"),a&&(a.addEventListener("popstate",b),r=a.history.pushState,a.history.pushState=new Proxy(a.history.pushState,{apply:function(e,t,n){var r=u(n,3),i=r[0],o=r[1],s=r[2];e.apply(t,[i,o,s]),y()}})),v()?(c.log("Tracking page view event"),l=(i=t).track,[4,p()]):[3,2];case 1:l.apply(i,[o.sent()]),o.label=2;case 2:return[2]}}))}))},execute:function(e){return i(void 0,void 0,void 0,(function(){var t;return o(this,(function(r){switch(r.label){case 0:return"attribution"===l&&wt(e)?(null==c||c.log("Enriching campaign event to page view event with campaign parameters"),[4,p()]):[3,2];case 1:t=r.sent(),e.event_type=t.event_type,e.event_properties=n(n({},e.event_properties),t.event_properties),r.label=2;case 2:return s&&e.event_type===h&&(s.pageCounter=s.pageCounter?s.pageCounter+1:1,e.event_properties=n(n({},e.event_properties),{"[Amplitude] Page Counter":s.pageCounter})),[2,e]}}))}))},teardown:function(){return i(void 0,void 0,void 0,(function(){return o(this,(function(e){return a&&(a.removeEventListener("popstate",b),r&&(a.history.pushState=r)),[2]}))}))}};return w},bt=function(){return i(void 0,void 0,void 0,(function(){var e;return o(this,(function(t){switch(t.label){case 0:return e=yt,[4,(new R).parse()];case 1:return[2,e.apply(void 0,[t.sent()])]}}))}))},wt=function(e){if("$identify"===e.event_type&&e.user_properties){var t=e.user_properties,n=t[f.SET]||{},r=t[f.UNSET]||{},i=a(a([],u(Object.keys(n)),!1),u(Object.keys(r)),!1);return Object.keys(N).every((function(e){return i.includes(e)}))}return!1},_t=function(e,t,n){return"pathOnly"===e?t.split("?")[0]!==n.split("?")[0]:t!==n},St=function(){var e,t=[],n=function(e,n,r){e.addEventListener(n,r),t.push({element:e,type:n,handler:r})};return{name:"@amplitude/plugin-form-interaction-tracking-browser",type:"enrichment",setup:function(t,r){return i(void 0,void 0,void 0,(function(){var i;return o(this,(function(o){return null===(i=m())||void 0===i||i.addEventListener("load",(function(){if(r){if("undefined"!=typeof document){var i=function(e){var t=!1;n(e,"change",(function(){var n;t||r.track(Ge,((n={})[at]=Et(e.id),n[ct]=Et(e.name),n[lt]=e.action,n)),t=!0})),n(e,"submit",(function(){var n,i;t||r.track(Ge,((n={})[at]=Et(e.id),n[ct]=Et(e.name),n[lt]=e.action,n)),r.track(Xe,((i={})[at]=Et(e.id),i[ct]=Et(e.name),i[lt]=e.action,i)),t=!1}))};Array.from(document.getElementsByTagName("form")).forEach(i),"undefined"!=typeof MutationObserver&&(e=new MutationObserver((function(e){e.forEach((function(e){e.addedNodes.forEach((function(e){"FORM"===e.nodeName&&i(e),"querySelectorAll"in e&&"function"==typeof e.querySelectorAll&&Array.from(e.querySelectorAll("form")).map(i)}))}))}))).observe(document.body,{subtree:!0,childList:!0})}}else t.loggerProvider.warn("Form interaction tracking requires a later version of @amplitude/analytics-browser. Form interaction events are not tracked.")})),[2]}))}))},execute:function(e){return i(void 0,void 0,void 0,(function(){return o(this,(function(t){return[2,e]}))}))},teardown:function(){return i(void 0,void 0,void 0,(function(){return o(this,(function(n){return null==e||e.disconnect(),t.forEach((function(e){var t=e.element,n=e.type,r=e.handler;null==t||t.removeEventListener(n,r)})),t=[],[2]}))}))}}},Et=function(e){if("string"==typeof e)return e},It=function(){var e,t=[];return{name:"@amplitude/plugin-file-download-tracking-browser",type:"enrichment",setup:function(n,r){return i(void 0,void 0,void 0,(function(){var i;return o(this,(function(o){return null===(i=m())||void 0===i||i.addEventListener("load",(function(){if(r){if("undefined"!=typeof document){var i=function(e){var n;try{n=new URL(e.href,window.location.href)}catch(e){return}var i=o.exec(n.href),s=null==i?void 0:i[1];s&&function(e,n,r){e.addEventListener(n,r),t.push({element:e,type:n,handler:r})}(e,"click",(function(){var t;s&&r.track(et,((t={})[rt]=s,t[it]=n.pathname,t[ot]=e.id,t[st]=e.text,t[ut]=e.href,t))}))},o=/\.(pdf|xlsx?|docx?|txt|rtf|csv|exe|key|pp(s|t|tx)|7z|pkg|rar|gz|zip|avi|mov|mp4|mpe?g|wmv|midi?|mp3|wav|wma)$/;Array.from(document.getElementsByTagName("a")).forEach(i),"undefined"!=typeof MutationObserver&&(e=new MutationObserver((function(e){e.forEach((function(e){e.addedNodes.forEach((function(e){"A"===e.nodeName&&i(e),"querySelectorAll"in e&&"function"==typeof e.querySelectorAll&&Array.from(e.querySelectorAll("a")).map(i)}))}))}))).observe(document.body,{subtree:!0,childList:!0})}}else n.loggerProvider.warn("File download tracking requires a later version of @amplitude/analytics-browser. File download events are not tracked.")})),[2]}))}))},execute:function(e){return i(void 0,void 0,void 0,(function(){return o(this,(function(t){return[2,e]}))}))},teardown:function(){return i(void 0,void 0,void 0,(function(){return o(this,(function(n){return null==e||e.disconnect(),t.forEach((function(e){var t=e.element,n=e.type,r=e.handler;null==t||t.removeEventListener(n,r)})),t=[],[2]}))}))}}},Tt=!1,kt=function(){var e=m(),t=[],n=function(n,r){e&&(e.addEventListener(n,r),t.push({type:n,handler:r}))};return{name:"@amplitude/plugin-network-checker-browser",type:"before",setup:function(e,t){return i(void 0,void 0,void 0,(function(){return o(this,(function(r){return e.offline=!navigator.onLine,n("online",(function(){e.loggerProvider.debug("Network connectivity changed to online."),e.offline=!1,setTimeout((function(){t.flush()}),e.flushIntervalMillis)})),n("offline",(function(){e.loggerProvider.debug("Network connectivity changed to offline."),e.offline=!0})),[2]}))}))},teardown:function(){return i(void 0,void 0,void 0,(function(){return o(this,(function(n){return t.forEach((function(t){var n=t.type,r=t.handler;e&&e.removeEventListener(n,r)})),t=[],[2]}))}))}}},Ot="Remote config fetch rejected due to timeout after 5 seconds",Pt=function(){function e(e){var t=e.localConfig,r=e.configKeys,u=this;this.retryTimeout=1e3,this.attempts=0,this.sessionTargetingMatch=!1,this.metrics={},this.getRemoteConfig=function(e,t,n){return i(u,void 0,void 0,(function(){var r,i,s;return o(this,(function(o){switch(o.label){case 0:return r=Date.now(),[4,this.fetchWithTimeout(n)];case 1:return(i=o.sent())&&(s=i.configs&&i.configs[e])?(this.metrics.fetchTimeAPISuccess=Date.now()-r,[2,s[t]]):(this.metrics.fetchTimeAPIFail=Date.now()-r,[2,void 0])}}))}))},this.fetchWithTimeout=function(e){return i(u,void 0,void 0,(function(){var t,n,r;return o(this,(function(i){switch(i.label){case 0:return t=new AbortController,n=setTimeout((function(){return t.abort()}),5e3),[4,this.fetchRemoteConfig(t.signal,e)];case 1:return r=i.sent(),clearTimeout(n),[2,r]}}))}))},this.fetchRemoteConfig=function(e,t){return i(u,void 0,void 0,(function(){var r,i,u,a,c,l,d,f,h,p,v;return o(this,(function(o){switch(o.label){case 0:if(t===this.lastFetchedSessionId&&this.attempts>=this.localConfig.flushMaxRetries)return[2,this.completeRequest({err:"Remote config fetch rejected due to exceeded retry count"})];if(e.aborted)return[2,this.completeRequest({err:Ot})];t!==this.lastFetchedSessionId&&(this.lastFetchedSessionId=t,this.attempts=0),o.label=1;case 1:o.trys.push([1,3,,4]),r=new URLSearchParams({api_key:this.localConfig.apiKey});try{for(i=s(this.configKeys),u=i.next();!u.done;u=i.next())a=u.value,r.append("config_keys",a)}catch(e){p={error:e}}finally{try{u&&!u.done&&(v=i.return)&&v.call(i)}finally{if(p)throw p.error}}return t&&r.set("session_id",String(t)),c={headers:{Accept:"*/*"},method:"GET"},l="".concat(this.getServerUrl(),"?").concat(r.toString()),this.attempts+=1,[4,fetch(l,n(n({},c),{signal:e}))];case 2:if(null===(d=o.sent()))return[2,this.completeRequest({err:"Unexpected error occurred"})];switch((new ce).buildStatus(d.status)){case y.Success:return this.attempts=0,[2,this.parseAndStoreConfig(d)];case y.Failed:return[2,this.retryFetch(e,t)];default:return[2,this.completeRequest({err:"Network error occurred, remote config fetch failed"})]}case 3:return f=o.sent(),h=f,e.aborted?[2,this.completeRequest({err:Ot})]:[2,this.completeRequest({err:h.message})];case 4:return[2]}}))}))},this.retryFetch=function(e,t){return i(u,void 0,void 0,(function(){var n=this;return o(this,(function(r){switch(r.label){case 0:return[4,new Promise((function(e){return setTimeout(e,n.attempts*n.retryTimeout)}))];case 1:return r.sent(),[2,this.fetchRemoteConfig(e,t)]}}))}))},this.parseAndStoreConfig=function(e){return i(u,void 0,void 0,(function(){var t;return o(this,(function(n){switch(n.label){case 0:return[4,e.json()];case 1:return t=n.sent(),this.completeRequest({success:"Remote config successfully fetched"}),[2,t]}}))}))},this.localConfig=t,this.configKeys=r}return e.prototype.getServerUrl=function(){return this.localConfig.serverZone===g.STAGING?"https://sr-client-cfg.stag2.amplitude.com/config":this.localConfig.serverZone===g.EU?"https://sr-client-cfg.eu.amplitude.com/config":"https://sr-client-cfg.amplitude.com/config"},e.prototype.completeRequest=function(e){var t=e.err,n=e.success;if(t)throw new Error(t);n&&this.localConfig.loggerProvider.log(n)},e}(),xt=function(e){var t=e.localConfig,n=e.configKeys;return i(void 0,void 0,void 0,(function(){return o(this,(function(e){return[2,new Pt({localConfig:t,configKeys:n})]}))}))},At=function(){function e(e){this.config=e,this.config.loggerProvider.debug("Local configuration before merging with remote config",JSON.stringify(this.config,null,2))}return e.prototype.initialize=function(){return i(this,void 0,void 0,(function(){var e;return o(this,(function(t){switch(t.label){case 0:return e=this,[4,xt({localConfig:this.config,configKeys:["analyticsSDK"]})];case 1:return e.remoteConfigFetch=t.sent(),[2]}}))}))},e.prototype.generateJoinedConfig=function(){var e,t,r,s;return i(this,void 0,void 0,(function(){var i,u,a;return o(this,(function(o){switch(o.label){case 0:return o.trys.push([0,3,,4]),(u=this.remoteConfigFetch)?[4,this.remoteConfigFetch.getRemoteConfig("analyticsSDK","browserSDK",this.config.sessionId)]:[3,2];case 1:u=o.sent(),o.label=2;case 2:return i=u,this.config.loggerProvider.debug("Remote configuration:",JSON.stringify(i,null,2)),i&&"autocapture"in i&&("boolean"==typeof i.autocapture&&(this.config.autocapture=i.autocapture),"object"==typeof i.autocapture&&(void 0===this.config.autocapture&&(this.config.autocapture=i.autocapture),"boolean"==typeof this.config.autocapture&&(this.config.autocapture=n({attribution:this.config.autocapture,fileDownloads:this.config.autocapture,formInteractions:this.config.autocapture,pageViews:this.config.autocapture,sessions:this.config.autocapture,elementInteractions:this.config.autocapture},i.autocapture)),"object"==typeof this.config.autocapture&&(this.config.autocapture=n(n({},this.config.autocapture),i.autocapture))),this.config.defaultTracking=this.config.autocapture),this.config.loggerProvider.debug("Joined configuration: ",JSON.stringify(this.config,null,2)),null!==(e=(s=this.config).requestMetadata)&&void 0!==e||(s.requestMetadata=new ee),(null===(t=this.remoteConfigFetch)||void 0===t?void 0:t.metrics.fetchTimeAPISuccess)&&this.config.requestMetadata.recordHistogram("remote_config_fetch_time_API_success",this.remoteConfigFetch.metrics.fetchTimeAPISuccess),(null===(r=this.remoteConfigFetch)||void 0===r?void 0:r.metrics.fetchTimeAPIFail)&&this.config.requestMetadata.recordHistogram("remote_config_fetch_time_API_fail",this.remoteConfigFetch.metrics.fetchTimeAPIFail),[3,4];case 3:return a=o.sent(),this.config.loggerProvider.error("Failed to fetch remote configuration because of error: ",a),[3,4];case 4:return[2,this.config]}}))}))},e}(),Ct="[Amplitude] Element Clicked",Nt="[Amplitude] Element Tag",Rt="[Amplitude] Element Text",Lt="[Amplitude] Element Selector",qt="[Amplitude] Page URL",Ut="https://app.amplitude.com",jt={US:Ut,EU:"https://app.eu.amplitude.com",STAGING:"https://apps.stag2.amplitude.com"},Dt="amp-visual-tagging-selector-highlight";function Ft(e){return"function"==typeof e}function Mt(e){var t=e((function(e){Error.call(e),e.stack=(new Error).stack}));return t.prototype=Object.create(Error.prototype),t.prototype.constructor=t,t}var Vt=Mt((function(e){return function(t){e(this),this.message=t?t.length+" errors occurred during unsubscription:\n"+t.map((function(e,t){return t+1+") "+e.toString()})).join("\n  "):"",this.name="UnsubscriptionError",this.errors=t}}));function zt(e,t){if(e){var n=e.indexOf(t);0<=n&&e.splice(n,1)}}var Kt=function(){function e(e){this.initialTeardown=e,this.closed=!1,this._parentage=null,this._finalizers=null}var t;return e.prototype.unsubscribe=function(){var e,t,n,r,i;if(!this.closed){this.closed=!0;var o=this._parentage;if(o)if(this._parentage=null,Array.isArray(o))try{for(var c=s(o),l=c.next();!l.done;l=c.next()){l.value.remove(this)}}catch(t){e={error:t}}finally{try{l&&!l.done&&(t=c.return)&&t.call(c)}finally{if(e)throw e.error}}else o.remove(this);var d=this.initialTeardown;if(Ft(d))try{d()}catch(e){i=e instanceof Vt?e.errors:[e]}var f=this._finalizers;if(f){this._finalizers=null;try{for(var h=s(f),p=h.next();!p.done;p=h.next()){var v=p.value;try{$t(v)}catch(e){i=null!=i?i:[],e instanceof Vt?i=a(a([],u(i)),u(e.errors)):i.push(e)}}}catch(e){n={error:e}}finally{try{p&&!p.done&&(r=h.return)&&r.call(h)}finally{if(n)throw n.error}}}if(i)throw new Vt(i)}},e.prototype.add=function(t){var n;if(t&&t!==this)if(this.closed)$t(t);else{if(t instanceof e){if(t.closed||t._hasParent(this))return;t._addParent(this)}(this._finalizers=null!==(n=this._finalizers)&&void 0!==n?n:[]).push(t)}},e.prototype._hasParent=function(e){var t=this._parentage;return t===e||Array.isArray(t)&&t.includes(e)},e.prototype._addParent=function(e){var t=this._parentage;this._parentage=Array.isArray(t)?(t.push(e),t):t?[t,e]:e},e.prototype._removeParent=function(e){var t=this._parentage;t===e?this._parentage=null:Array.isArray(t)&&zt(t,e)},e.prototype.remove=function(t){var n=this._finalizers;n&&zt(n,t),t instanceof e&&t._removeParent(this)},e.EMPTY=((t=new e).closed=!0,t),e}(),Bt=Kt.EMPTY;function Wt(e){return e instanceof Kt||e&&"closed"in e&&Ft(e.remove)&&Ft(e.add)&&Ft(e.unsubscribe)}function $t(e){Ft(e)?e():e.unsubscribe()}var Jt={onUnhandledError:null,onStoppedNotification:null,Promise:void 0,useDeprecatedSynchronousErrorHandling:!1,useDeprecatedNextContext:!1},Qt=function(e,t){for(var n=[],r=2;r<arguments.length;r++)n[r-2]=arguments[r];return setTimeout.apply(void 0,a([e,t],u(n)))};function Yt(e){Qt((function(){throw e}))}function Ht(){}function Zt(e){e()}var Gt=function(e){function n(t){var n=e.call(this)||this;return n.isStopped=!1,t?(n.destination=t,Wt(t)&&t.add(n)):n.destination=on,n}return t(n,e),n.create=function(e,t,n){return new nn(e,t,n)},n.prototype.next=function(e){this.isStopped||this._next(e)},n.prototype.error=function(e){this.isStopped||(this.isStopped=!0,this._error(e))},n.prototype.complete=function(){this.isStopped||(this.isStopped=!0,this._complete())},n.prototype.unsubscribe=function(){this.closed||(this.isStopped=!0,e.prototype.unsubscribe.call(this),this.destination=null)},n.prototype._next=function(e){this.destination.next(e)},n.prototype._error=function(e){try{this.destination.error(e)}finally{this.unsubscribe()}},n.prototype._complete=function(){try{this.destination.complete()}finally{this.unsubscribe()}},n}(Kt),Xt=Function.prototype.bind;function en(e,t){return Xt.call(e,t)}var tn=function(){function e(e){this.partialObserver=e}return e.prototype.next=function(e){var t=this.partialObserver;if(t.next)try{t.next(e)}catch(e){rn(e)}},e.prototype.error=function(e){var t=this.partialObserver;if(t.error)try{t.error(e)}catch(e){rn(e)}else rn(e)},e.prototype.complete=function(){var e=this.partialObserver;if(e.complete)try{e.complete()}catch(e){rn(e)}},e}(),nn=function(e){function n(t,n,r){var i,o,s=e.call(this)||this;Ft(t)||!t?i={next:null!=t?t:void 0,error:null!=n?n:void 0,complete:null!=r?r:void 0}:s&&Jt.useDeprecatedNextContext?((o=Object.create(t)).unsubscribe=function(){return s.unsubscribe()},i={next:t.next&&en(t.next,o),error:t.error&&en(t.error,o),complete:t.complete&&en(t.complete,o)}):i=t;return s.destination=new tn(i),s}return t(n,e),n}(Gt);function rn(e){Yt(e)}var on={closed:!0,next:Ht,error:function(e){throw e},complete:Ht},sn="function"==typeof Symbol&&Symbol.observable||"@@observable";function un(e){return e}function an(e){return 0===e.length?un:1===e.length?e[0]:function(t){return e.reduce((function(e,t){return t(e)}),t)}}var cn=function(){function e(e){e&&(this._subscribe=e)}return e.prototype.lift=function(t){var n=new e;return n.source=this,n.operator=t,n},e.prototype.subscribe=function(e,t,n){var r,i=this,o=(r=e)&&r instanceof Gt||function(e){return e&&Ft(e.next)&&Ft(e.error)&&Ft(e.complete)}(r)&&Wt(r)?e:new nn(e,t,n);return Zt((function(){var e=i,t=e.operator,n=e.source;o.add(t?t.call(o,n):n?i._subscribe(o):i._trySubscribe(o))})),o},e.prototype._trySubscribe=function(e){try{return this._subscribe(e)}catch(t){e.error(t)}},e.prototype.forEach=function(e,t){var n=this;return new(t=ln(t))((function(t,r){var i=new nn({next:function(t){try{e(t)}catch(e){r(e),i.unsubscribe()}},error:r,complete:t});n.subscribe(i)}))},e.prototype._subscribe=function(e){var t;return null===(t=this.source)||void 0===t?void 0:t.subscribe(e)},e.prototype[sn]=function(){return this},e.prototype.pipe=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return an(e)(this)},e.prototype.toPromise=function(e){var t=this;return new(e=ln(e))((function(e,n){var r;t.subscribe((function(e){return r=e}),(function(e){return n(e)}),(function(){return e(r)}))}))},e.create=function(t){return new e(t)},e}();function ln(e){var t;return null!==(t=null!=e?e:Jt.Promise)&&void 0!==t?t:Promise}function dn(e){return Ft(null==e?void 0:e.lift)}function fn(e){return function(t){if(dn(t))return t.lift((function(t){try{return e(t,this)}catch(e){this.error(e)}}));throw new TypeError("Unable to lift unknown Observable type")}}function hn(e,t,n,r,i){return new pn(e,t,n,r,i)}var pn=function(e){function n(t,n,r,i,o,s){var u=e.call(this,t)||this;return u.onFinalize=o,u.shouldUnsubscribe=s,u._next=n?function(e){try{n(e)}catch(e){t.error(e)}}:e.prototype._next,u._error=i?function(e){try{i(e)}catch(e){t.error(e)}finally{this.unsubscribe()}}:e.prototype._error,u._complete=r?function(){try{r()}catch(e){t.error(e)}finally{this.unsubscribe()}}:e.prototype._complete,u}return t(n,e),n.prototype.unsubscribe=function(){var t;if(!this.shouldUnsubscribe||this.shouldUnsubscribe()){var n=this.closed;e.prototype.unsubscribe.call(this),!n&&(null===(t=this.onFinalize)||void 0===t||t.call(this))}},n}(Gt);!function(e){function n(t,n){var r=e.call(this)||this;return r.source=t,r.subjectFactory=n,r._subject=null,r._refCount=0,r._connection=null,dn(t)&&(r.lift=t.lift),r}t(n,e),n.prototype._subscribe=function(e){return this.getSubject().subscribe(e)},n.prototype.getSubject=function(){var e=this._subject;return e&&!e.isStopped||(this._subject=this.subjectFactory()),this._subject},n.prototype._teardown=function(){this._refCount=0;var e=this._connection;this._subject=this._connection=null,null==e||e.unsubscribe()},n.prototype.connect=function(){var e=this,t=this._connection;if(!t){t=this._connection=new Kt;var n=this.getSubject();t.add(this.source.subscribe(hn(n,void 0,(function(){e._teardown(),n.complete()}),(function(t){e._teardown(),n.error(t)}),(function(){return e._teardown()})))),t.closed&&(this._connection=null,t=Kt.EMPTY)}return t},n.prototype.refCount=function(){return fn((function(e,t){var n=null;e._refCount++;var r=hn(t,void 0,void 0,void 0,(function(){if(!e||e._refCount<=0||0<--e._refCount)n=null;else{var r=e._connection,i=n;n=null,!r||i&&r!==i||r.unsubscribe(),t.unsubscribe()}}));e.subscribe(r),r.closed||(n=e.connect())}))(this)}}(cn);var vn,gn={now:function(){return(gn.delegate||performance).now()},delegate:void 0},yn={schedule:function(e){var t=requestAnimationFrame,n=cancelAnimationFrame,r=t((function(t){n=void 0,e(t)}));return new Kt((function(){return null==n?void 0:n(r)}))},requestAnimationFrame:function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];var n=yn.delegate;return((null==n?void 0:n.requestAnimationFrame)||requestAnimationFrame).apply(void 0,a([],u(e)))},cancelAnimationFrame:function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return cancelAnimationFrame.apply(void 0,a([],u(e)))},delegate:void 0};new cn((function(e){var t=vn||gn,n=t.now(),r=0,i=function(){e.closed||(r=yn.requestAnimationFrame((function(o){r=0;var s=t.now();e.next({timestamp:vn?s:o,elapsed:s-n}),i()})))};return i(),function(){r&&yn.cancelAnimationFrame(r)}}));var mn=Mt((function(e){return function(){e(this),this.name="ObjectUnsubscribedError",this.message="object unsubscribed"}})),bn=function(e){function n(){var t=e.call(this)||this;return t.closed=!1,t.currentObservers=null,t.observers=[],t.isStopped=!1,t.hasError=!1,t.thrownError=null,t}return t(n,e),n.prototype.lift=function(e){var t=new wn(this,this);return t.operator=e,t},n.prototype._throwIfClosed=function(){if(this.closed)throw new mn},n.prototype.next=function(e){var t=this;Zt((function(){var n,r;if(t._throwIfClosed(),!t.isStopped){t.currentObservers||(t.currentObservers=Array.from(t.observers));try{for(var i=s(t.currentObservers),o=i.next();!o.done;o=i.next()){o.value.next(e)}}catch(e){n={error:e}}finally{try{o&&!o.done&&(r=i.return)&&r.call(i)}finally{if(n)throw n.error}}}}))},n.prototype.error=function(e){var t=this;Zt((function(){if(t._throwIfClosed(),!t.isStopped){t.hasError=t.isStopped=!0,t.thrownError=e;for(var n=t.observers;n.length;)n.shift().error(e)}}))},n.prototype.complete=function(){var e=this;Zt((function(){if(e._throwIfClosed(),!e.isStopped){e.isStopped=!0;for(var t=e.observers;t.length;)t.shift().complete()}}))},n.prototype.unsubscribe=function(){this.isStopped=this.closed=!0,this.observers=this.currentObservers=null},Object.defineProperty(n.prototype,"observed",{get:function(){var e;return(null===(e=this.observers)||void 0===e?void 0:e.length)>0},enumerable:!1,configurable:!0}),n.prototype._trySubscribe=function(t){return this._throwIfClosed(),e.prototype._trySubscribe.call(this,t)},n.prototype._subscribe=function(e){return this._throwIfClosed(),this._checkFinalizedStatuses(e),this._innerSubscribe(e)},n.prototype._innerSubscribe=function(e){var t=this,n=this,r=n.hasError,i=n.isStopped,o=n.observers;return r||i?Bt:(this.currentObservers=null,o.push(e),new Kt((function(){t.currentObservers=null,zt(o,e)})))},n.prototype._checkFinalizedStatuses=function(e){var t=this,n=t.hasError,r=t.thrownError,i=t.isStopped;n?e.error(r):i&&e.complete()},n.prototype.asObservable=function(){var e=new cn;return e.source=this,e},n.create=function(e,t){return new wn(e,t)},n}(cn),wn=function(e){function n(t,n){var r=e.call(this)||this;return r.destination=t,r.source=n,r}return t(n,e),n.prototype.next=function(e){var t,n;null===(n=null===(t=this.destination)||void 0===t?void 0:t.next)||void 0===n||n.call(t,e)},n.prototype.error=function(e){var t,n;null===(n=null===(t=this.destination)||void 0===t?void 0:t.error)||void 0===n||n.call(t,e)},n.prototype.complete=function(){var e,t;null===(t=null===(e=this.destination)||void 0===e?void 0:e.complete)||void 0===t||t.call(e)},n.prototype._subscribe=function(e){var t,n;return null!==(n=null===(t=this.source)||void 0===t?void 0:t.subscribe(e))&&void 0!==n?n:Bt},n}(bn);!function(e){function n(t){var n=e.call(this)||this;return n._value=t,n}t(n,e),Object.defineProperty(n.prototype,"value",{get:function(){return this.getValue()},enumerable:!1,configurable:!0}),n.prototype._subscribe=function(t){var n=e.prototype._subscribe.call(this,t);return!n.closed&&t.next(this._value),n},n.prototype.getValue=function(){var e=this,t=e.hasError,n=e.thrownError,r=e._value;if(t)throw n;return this._throwIfClosed(),r},n.prototype.next=function(t){e.prototype.next.call(this,this._value=t)}}(bn);var _n={now:function(){return(_n.delegate||Date).now()},delegate:void 0};!function(e){function n(t,n,r){void 0===t&&(t=1/0),void 0===n&&(n=1/0),void 0===r&&(r=_n);var i=e.call(this)||this;return i._bufferSize=t,i._windowTime=n,i._timestampProvider=r,i._buffer=[],i._infiniteTimeWindow=!0,i._infiniteTimeWindow=n===1/0,i._bufferSize=Math.max(1,t),i._windowTime=Math.max(1,n),i}t(n,e),n.prototype.next=function(t){var n=this,r=n.isStopped,i=n._buffer,o=n._infiniteTimeWindow,s=n._timestampProvider,u=n._windowTime;r||(i.push(t),!o&&i.push(s.now()+u)),this._trimBuffer(),e.prototype.next.call(this,t)},n.prototype._subscribe=function(e){this._throwIfClosed(),this._trimBuffer();for(var t=this._innerSubscribe(e),n=this._infiniteTimeWindow,r=this._buffer.slice(),i=0;i<r.length&&!e.closed;i+=n?1:2)e.next(r[i]);return this._checkFinalizedStatuses(e),t},n.prototype._trimBuffer=function(){var e=this,t=e._bufferSize,n=e._timestampProvider,r=e._buffer,i=e._infiniteTimeWindow,o=(i?1:2)*t;if(t<1/0&&o<r.length&&r.splice(0,r.length-o),!i){for(var s=n.now(),u=0,a=1;a<r.length&&r[a]<=s;a+=2)u=a;u&&r.splice(0,u+1)}}}(bn),function(e){function n(){var t=null!==e&&e.apply(this,arguments)||this;return t._value=null,t._hasValue=!1,t._isComplete=!1,t}t(n,e),n.prototype._checkFinalizedStatuses=function(e){var t=this,n=t.hasError,r=t._hasValue,i=t._value,o=t.thrownError,s=t.isStopped,u=t._isComplete;n?e.error(o):(s||u)&&(r&&e.next(i),e.complete())},n.prototype.next=function(e){this.isStopped||(this._value=e,this._hasValue=!0)},n.prototype.complete=function(){var t=this,n=t._hasValue,r=t._value;t._isComplete||(this._isComplete=!0,n&&e.prototype.next.call(this,r),e.prototype.complete.call(this))}}(bn);var Sn,En=function(e){function n(t,n){return e.call(this)||this}return t(n,e),n.prototype.schedule=function(e,t){return this},n}(Kt),In=function(e,t){for(var n=[],r=2;r<arguments.length;r++)n[r-2]=arguments[r];return setInterval.apply(void 0,a([e,t],u(n)))},Tn=function(e){return clearInterval(e)},kn=function(e){function n(t,n){var r=e.call(this,t,n)||this;return r.scheduler=t,r.work=n,r.pending=!1,r}return t(n,e),n.prototype.schedule=function(e,t){var n;if(void 0===t&&(t=0),this.closed)return this;this.state=e;var r=this.id,i=this.scheduler;return null!=r&&(this.id=this.recycleAsyncId(i,r,t)),this.pending=!0,this.delay=t,this.id=null!==(n=this.id)&&void 0!==n?n:this.requestAsyncId(i,this.id,t),this},n.prototype.requestAsyncId=function(e,t,n){return void 0===n&&(n=0),In(e.flush.bind(e,this),n)},n.prototype.recycleAsyncId=function(e,t,n){if(void 0===n&&(n=0),null!=n&&this.delay===n&&!1===this.pending)return t;null!=t&&Tn(t)},n.prototype.execute=function(e,t){if(this.closed)return new Error("executing a cancelled action");this.pending=!1;var n=this._execute(e,t);if(n)return n;!1===this.pending&&null!=this.id&&(this.id=this.recycleAsyncId(this.scheduler,this.id,null))},n.prototype._execute=function(e,t){var n,r=!1;try{this.work(e)}catch(e){r=!0,n=e||new Error("Scheduled action threw falsy error")}if(r)return this.unsubscribe(),n},n.prototype.unsubscribe=function(){if(!this.closed){var t=this.id,n=this.scheduler,r=n.actions;this.work=this.state=this.scheduler=null,this.pending=!1,zt(r,this),null!=t&&(this.id=this.recycleAsyncId(n,t,null)),this.delay=null,e.prototype.unsubscribe.call(this)}},n}(En),On=1,Pn={};function xn(e){return e in Pn&&(delete Pn[e],!0)}var An=function(e){var t=On++;return Pn[t]=!0,Sn||(Sn=Promise.resolve()),Sn.then((function(){return xn(t)&&e()})),t},Cn=function(e){xn(e)},Nn={setImmediate:function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];var n=Nn.delegate;return((null==n?void 0:n.setImmediate)||An).apply(void 0,a([],u(e)))},clearImmediate:function(e){return Cn(e)},delegate:void 0},Rn=function(e){function n(t,n){var r=e.call(this,t,n)||this;return r.scheduler=t,r.work=n,r}return t(n,e),n.prototype.requestAsyncId=function(t,n,r){return void 0===r&&(r=0),null!==r&&r>0?e.prototype.requestAsyncId.call(this,t,n,r):(t.actions.push(this),t._scheduled||(t._scheduled=Nn.setImmediate(t.flush.bind(t,void 0))))},n.prototype.recycleAsyncId=function(t,n,r){var i;if(void 0===r&&(r=0),null!=r?r>0:this.delay>0)return e.prototype.recycleAsyncId.call(this,t,n,r);var o=t.actions;null!=n&&(null===(i=o[o.length-1])||void 0===i?void 0:i.id)!==n&&(Nn.clearImmediate(n),t._scheduled===n&&(t._scheduled=void 0))},n}(kn),Ln=function(){function e(t,n){void 0===n&&(n=e.now),this.schedulerActionCtor=t,this.now=n}return e.prototype.schedule=function(e,t,n){return void 0===t&&(t=0),new this.schedulerActionCtor(this,e).schedule(n,t)},e.now=_n.now,e}(),qn=function(e){function n(t,n){void 0===n&&(n=Ln.now);var r=e.call(this,t,n)||this;return r.actions=[],r._active=!1,r}return t(n,e),n.prototype.flush=function(e){var t=this.actions;if(this._active)t.push(e);else{var n;this._active=!0;do{if(n=e.execute(e.state,e.delay))break}while(e=t.shift());if(this._active=!1,n){for(;e=t.shift();)e.unsubscribe();throw n}}},n}(Ln),Un=function(e){function n(){return null!==e&&e.apply(this,arguments)||this}return t(n,e),n.prototype.flush=function(e){this._active=!0;var t=this._scheduled;this._scheduled=void 0;var n,r=this.actions;e=e||r.shift();do{if(n=e.execute(e.state,e.delay))break}while((e=r[0])&&e.id===t&&r.shift());if(this._active=!1,n){for(;(e=r[0])&&e.id===t&&r.shift();)e.unsubscribe();throw n}},n}(qn);new Un(Rn);var jn=new qn(kn),Dn=jn,Fn=function(e){function n(t,n){var r=e.call(this,t,n)||this;return r.scheduler=t,r.work=n,r}return t(n,e),n.prototype.schedule=function(t,n){return void 0===n&&(n=0),n>0?e.prototype.schedule.call(this,t,n):(this.delay=n,this.state=t,this.scheduler.flush(this),this)},n.prototype.execute=function(t,n){return n>0||this.closed?e.prototype.execute.call(this,t,n):this._execute(t,n)},n.prototype.requestAsyncId=function(t,n,r){return void 0===r&&(r=0),null!=r&&r>0||null==r&&this.delay>0?e.prototype.requestAsyncId.call(this,t,n,r):(t.flush(this),0)},n}(kn),Mn=function(e){function n(){return null!==e&&e.apply(this,arguments)||this}return t(n,e),n}(qn);new Mn(Fn);var Vn=function(e){function n(t,n){var r=e.call(this,t,n)||this;return r.scheduler=t,r.work=n,r}return t(n,e),n.prototype.requestAsyncId=function(t,n,r){return void 0===r&&(r=0),null!==r&&r>0?e.prototype.requestAsyncId.call(this,t,n,r):(t.actions.push(this),t._scheduled||(t._scheduled=yn.requestAnimationFrame((function(){return t.flush(void 0)}))))},n.prototype.recycleAsyncId=function(t,n,r){var i;if(void 0===r&&(r=0),null!=r?r>0:this.delay>0)return e.prototype.recycleAsyncId.call(this,t,n,r);var o=t.actions;null!=n&&(null===(i=o[o.length-1])||void 0===i?void 0:i.id)!==n&&(yn.cancelAnimationFrame(n),t._scheduled=void 0)},n}(kn),zn=function(e){function n(){return null!==e&&e.apply(this,arguments)||this}return t(n,e),n.prototype.flush=function(e){this._active=!0;var t=this._scheduled;this._scheduled=void 0;var n,r=this.actions;e=e||r.shift();do{if(n=e.execute(e.state,e.delay))break}while((e=r[0])&&e.id===t&&r.shift());if(this._active=!1,n){for(;(e=r[0])&&e.id===t&&r.shift();)e.unsubscribe();throw n}},n}(qn);new zn(Vn),function(e){function n(t,n){void 0===t&&(t=Kn),void 0===n&&(n=1/0);var r=e.call(this,t,(function(){return r.frame}))||this;return r.maxFrames=n,r.frame=0,r.index=-1,r}t(n,e),n.prototype.flush=function(){for(var e,t,n=this.actions,r=this.maxFrames;(t=n[0])&&t.delay<=r&&(n.shift(),this.frame=t.delay,!(e=t.execute(t.state,t.delay))););if(e){for(;t=n.shift();)t.unsubscribe();throw e}},n.frameTimeFactor=10}(qn);var Kn=function(e){function n(t,n,r){void 0===r&&(r=t.index+=1);var i=e.call(this,t,n)||this;return i.scheduler=t,i.work=n,i.index=r,i.active=!0,i.index=t.index=r,i}return t(n,e),n.prototype.schedule=function(t,r){if(void 0===r&&(r=0),Number.isFinite(r)){if(!this.id)return e.prototype.schedule.call(this,t,r);this.active=!1;var i=new n(this.scheduler,this.work);return this.add(i),i.schedule(t,r)}return Kt.EMPTY},n.prototype.requestAsyncId=function(e,t,r){void 0===r&&(r=0),this.delay=e.frame+r;var i=e.actions;return i.push(this),i.sort(n.sortActions),1},n.prototype.recycleAsyncId=function(e,t,n){},n.prototype._execute=function(t,n){if(!0===this.active)return e.prototype._execute.call(this,t,n)},n.sortActions=function(e,t){return e.delay===t.delay?e.index===t.index?0:e.index>t.index?1:-1:e.delay>t.delay?1:-1},n}(kn),Bn=new cn((function(e){return e.complete()}));function Wn(e){return e&&Ft(e.schedule)}function $n(e){return e[e.length-1]}function Jn(e){return Wn($n(e))?e.pop():void 0}function Qn(e,t){return"number"==typeof $n(e)?e.pop():t}var Yn=function(e){return e&&"number"==typeof e.length&&"function"!=typeof e};function Hn(e){return Ft(null==e?void 0:e.then)}function Zn(e){return Ft(e[sn])}function Gn(e){return Symbol.asyncIterator&&Ft(null==e?void 0:e[Symbol.asyncIterator])}function Xn(e){return new TypeError("You provided "+(null!==e&&"object"==typeof e?"an invalid object":"'"+e+"'")+" where a stream was expected. You can provide an Observable, Promise, ReadableStream, Array, AsyncIterable, or Iterable.")}var er,tr="function"==typeof Symbol&&Symbol.iterator?Symbol.iterator:"@@iterator";function nr(e){return Ft(null==e?void 0:e[tr])}function rr(e){return l(this,arguments,(function(){var t,n,r;return o(this,(function(i){switch(i.label){case 0:t=e.getReader(),i.label=1;case 1:i.trys.push([1,,9,10]),i.label=2;case 2:return[4,c(t.read())];case 3:return n=i.sent(),r=n.value,n.done?[4,c(void 0)]:[3,5];case 4:return[2,i.sent()];case 5:return[4,c(r)];case 6:return[4,i.sent()];case 7:return i.sent(),[3,2];case 8:return[3,10];case 9:return t.releaseLock(),[7];case 10:return[2]}}))}))}function ir(e){return Ft(null==e?void 0:e.getReader)}function or(e){if(e instanceof cn)return e;if(null!=e){if(Zn(e))return i=e,new cn((function(e){var t=i[sn]();if(Ft(t.subscribe))return t.subscribe(e);throw new TypeError("Provided object does not correctly implement Symbol.observable")}));if(Yn(e))return r=e,new cn((function(e){for(var t=0;t<r.length&&!e.closed;t++)e.next(r[t]);e.complete()}));if(Hn(e))return n=e,new cn((function(e){n.then((function(t){e.closed||(e.next(t),e.complete())}),(function(t){return e.error(t)})).then(null,Yt)}));if(Gn(e))return sr(e);if(nr(e))return t=e,new cn((function(e){var n,r;try{for(var i=s(t),o=i.next();!o.done;o=i.next()){var u=o.value;if(e.next(u),e.closed)return}}catch(e){n={error:e}}finally{try{o&&!o.done&&(r=i.return)&&r.call(i)}finally{if(n)throw n.error}}e.complete()}));if(ir(e))return sr(rr(e))}var t,n,r,i;throw Xn(e)}function sr(e){return new cn((function(t){(function(e,t){var n,r,s,u;return i(this,void 0,void 0,(function(){var i,a;return o(this,(function(o){switch(o.label){case 0:o.trys.push([0,5,6,11]),n=d(e),o.label=1;case 1:return[4,n.next()];case 2:if((r=o.sent()).done)return[3,4];if(i=r.value,t.next(i),t.closed)return[2];o.label=3;case 3:return[3,1];case 4:return[3,11];case 5:return a=o.sent(),s={error:a},[3,11];case 6:return o.trys.push([6,,9,10]),r&&!r.done&&(u=n.return)?[4,u.call(n)]:[3,8];case 7:o.sent(),o.label=8;case 8:return[3,10];case 9:if(s)throw s.error;return[7];case 10:return[7];case 11:return t.complete(),[2]}}))}))})(e,t).catch((function(e){return t.error(e)}))}))}function ur(e,t,n,r,i){void 0===r&&(r=0),void 0===i&&(i=!1);var o=t.schedule((function(){n(),i?e.add(this.schedule(null,r)):this.unsubscribe()}),r);if(e.add(o),!i)return o}function ar(e,t){return void 0===t&&(t=0),fn((function(n,r){n.subscribe(hn(r,(function(n){return ur(r,e,(function(){return r.next(n)}),t)}),(function(){return ur(r,e,(function(){return r.complete()}),t)}),(function(n){return ur(r,e,(function(){return r.error(n)}),t)})))}))}function cr(e,t){return void 0===t&&(t=0),fn((function(n,r){r.add(e.schedule((function(){return n.subscribe(r)}),t))}))}function lr(e,t){if(!e)throw new Error("Iterable cannot be null");return new cn((function(n){ur(n,t,(function(){var r=e[Symbol.asyncIterator]();ur(n,t,(function(){r.next().then((function(e){e.done?n.complete():n.next(e.value)}))}),0,!0)}))}))}function dr(e,t){if(null!=e){if(Zn(e))return function(e,t){return or(e).pipe(cr(t),ar(t))}(e,t);if(Yn(e))return function(e,t){return new cn((function(n){var r=0;return t.schedule((function(){r===e.length?n.complete():(n.next(e[r++]),n.closed||this.schedule())}))}))}(e,t);if(Hn(e))return function(e,t){return or(e).pipe(cr(t),ar(t))}(e,t);if(Gn(e))return lr(e,t);if(nr(e))return function(e,t){return new cn((function(n){var r;return ur(n,t,(function(){r=e[tr](),ur(n,t,(function(){var e,t,i;try{t=(e=r.next()).value,i=e.done}catch(e){return void n.error(e)}i?n.complete():n.next(t)}),0,!0)})),function(){return Ft(null==r?void 0:r.return)&&r.return()}}))}(e,t);if(ir(e))return function(e,t){return lr(rr(e),t)}(e,t)}throw Xn(e)}function fr(e,t){return t?dr(e,t):or(e)}function hr(e){return e instanceof Date&&!isNaN(e)}!function(e){e.NEXT="N",e.ERROR="E",e.COMPLETE="C"}(er||(er={})),Mt((function(e){return function(){e(this),this.name="EmptyError",this.message="no elements in sequence"}})),Mt((function(e){return function(){e(this),this.name="ArgumentOutOfRangeError",this.message="argument out of range"}})),Mt((function(e){return function(t){e(this),this.name="NotFoundError",this.message=t}})),Mt((function(e){return function(t){e(this),this.name="SequenceError",this.message=t}}));var pr=Mt((function(e){return function(t){void 0===t&&(t=null),e(this),this.message="Timeout has occurred",this.name="TimeoutError",this.info=t}}));function vr(e){throw new pr(e)}function gr(e,t){return fn((function(n,r){var i=0;n.subscribe(hn(r,(function(n){r.next(e.call(t,n,i++))})))}))}var yr=Array.isArray;function mr(e){return gr((function(t){return function(e,t){return yr(t)?e.apply(void 0,a([],u(t))):e(t)}(e,t)}))}function br(e,t,n){return void 0===n&&(n=1/0),Ft(t)?br((function(n,r){return gr((function(e,i){return t(n,e,r,i)}))(or(e(n,r)))}),n):("number"==typeof t&&(n=t),fn((function(t,r){return function(e,t,n,r,i,o,s,u){var a=[],c=0,l=0,d=!1,f=function(){!d||a.length||c||t.complete()},h=function(e){return c<r?p(e):a.push(e)},p=function(e){o&&t.next(e),c++;var u=!1;or(n(e,l++)).subscribe(hn(t,(function(e){null==i||i(e),o?h(e):t.next(e)}),(function(){u=!0}),void 0,(function(){if(u)try{c--;for(var e=function(){var e=a.shift();s?ur(t,s,(function(){return p(e)})):p(e)};a.length&&c<r;)e();f()}catch(e){t.error(e)}})))};return e.subscribe(hn(t,h,(function(){d=!0,f()}))),function(){null==u||u()}}(t,r,e,n)})))}function wr(e){return void 0===e&&(e=1/0),br(un,e)}function _r(){return wr(1)}var Sr,Er,Ir=["addListener","removeListener"],Tr=["addEventListener","removeEventListener"],kr=["on","off"];function Or(e,t,n,r){if(Ft(n)&&(r=n,n=void 0),r)return Or(e,t,n).pipe(mr(r));var i=u(function(e){return Ft(e.addEventListener)&&Ft(e.removeEventListener)}(e)?Tr.map((function(r){return function(i){return e[r](t,i,n)}})):function(e){return Ft(e.addListener)&&Ft(e.removeListener)}(e)?Ir.map(Pr(e,t)):function(e){return Ft(e.on)&&Ft(e.off)}(e)?kr.map(Pr(e,t)):[],2),o=i[0],s=i[1];if(!o&&Yn(e))return br((function(e){return Or(e,t,n)}))(or(e));if(!o)throw new TypeError("Invalid event target");return new cn((function(e){var t=function(){for(var t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];return e.next(1<t.length?t:t[0])};return o(t),function(){return s(t)}}))}function Pr(e,t){return function(n){return function(r){return e[n](t,r)}}}function xr(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];var n=Jn(e),r=Qn(e,1/0),i=e;return i.length?1===i.length?or(i[0]):wr(r)(fr(i,n)):Bn}function Ar(e,t){return fn((function(n,r){var i=0;n.subscribe(hn(r,(function(n){return e.call(t,n,i++)&&r.next(n)})))}))}function Cr(e){return e<=0?function(){return Bn}:fn((function(t,n){var r=0;t.subscribe(hn(n,(function(t){++r<=e&&(n.next(t),e<=r&&n.complete())})))}))}function Nr(e,t){return t?function(n){return function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return _r()(fr(e,Jn(e)))}(t.pipe(Cr(1),fn((function(e,t){e.subscribe(hn(t,Ht))}))),n.pipe(Nr(e)))}:br((function(t,n){return or(e(t,n)).pipe(Cr(1),function(e){return gr((function(){return e}))}(t))}))}function Rr(e,t){void 0===t&&(t=jn);var n=function(e,t,n){void 0===e&&(e=0),void 0===n&&(n=Dn);var r=-1;return null!=t&&(Wn(t)?n=t:r=t),new cn((function(t){var i=hr(e)?+e-n.now():e;i<0&&(i=0);var o=0;return n.schedule((function(){t.closed||(t.next(o++),0<=r?this.schedule(void 0,r):t.complete())}),i)}))}(e,t);return Nr((function(){return n}))}function Lr(e,t){if(e.nodeType!==Node.ELEMENT_NODE)throw new Error("Can't generate CSS selector for non-element node type.");if("html"===e.tagName.toLowerCase())return"html";var r={root:document.body,idName:function(e){return!0},className:function(e){return!0},tagName:function(e){return!0},attr:function(e,t){return!1},seedMinLength:1,optimizedMinLength:2,threshold:1e3,maxNumberOfTries:1e4};Sr=n(n({},r),t),Er=function(e,t){if(e.nodeType===Node.DOCUMENT_NODE)return e;if(e===t.root)return e.ownerDocument;return e}(Sr.root,r);var i=qr(e,"all",(function(){return qr(e,"two",(function(){return qr(e,"one",(function(){return qr(e,"none")}))}))}));if(i){var o=Wr($r(i,e));return o.length>0&&(i=o[0]),jr(i)}throw new Error("Selector was not found.")}function qr(e,t,n){for(var r=null,i=[],o=e,c=0,l=function(){var e,l,d=zr(function(e){var t=e.getAttribute("id");if(t&&Sr.idName(t))return{name:"#"+CSS.escape(t),penalty:0};return null}(o))||zr.apply(void 0,a([],u(function(e){var t=Array.from(e.attributes).filter((function(e){return Sr.attr(e.name,e.value)}));return t.map((function(e){return{name:"[".concat(CSS.escape(e.name),'="').concat(CSS.escape(e.value),'"]'),penalty:.5}}))}(o)),!1))||zr.apply(void 0,a([],u(function(e){var t=Array.from(e.classList).filter(Sr.className);return t.map((function(e){return{name:"."+CSS.escape(e),penalty:1}}))}(o)),!1))||zr(function(e){var t=e.tagName.toLowerCase();if(Sr.tagName(t))return{name:t,penalty:2};return null}(o))||[{name:"*",penalty:3}],f=function(e){var t=e.parentNode;if(!t)return null;var n=t.firstChild;if(!n)return null;var r=0;for(;n&&(n.nodeType===Node.ELEMENT_NODE&&r++,n!==e);)n=n.nextSibling;return r}(o);if("all"==t)f&&(d=d.concat(d.filter(Vr).map((function(e){return Mr(e,f)}))));else if("two"==t)d=d.slice(0,1),f&&(d=d.concat(d.filter(Vr).map((function(e){return Mr(e,f)}))));else if("one"==t){var h=u(d=d.slice(0,1),1)[0];f&&Vr(h)&&(d=[Mr(h,f)])}else"none"==t&&(d=[{name:"*",penalty:3}],f&&(d=[Mr(d[0],f)]));try{for(var p=(e=void 0,s(d)),v=p.next();!v.done;v=p.next()){(h=v.value).level=c}}catch(t){e={error:t}}finally{try{v&&!v.done&&(l=p.return)&&l.call(p)}finally{if(e)throw e.error}}if(i.push(d),i.length>=Sr.seedMinLength&&(r=Ur(i,n)))return"break";o=o.parentElement,c++};o;){if("break"===l())break}return r||(r=Ur(i,n)),!r&&n?n():r}function Ur(e,t){var n,r,i=Wr(Br(e));if(i.length>Sr.threshold)return t?t():null;try{for(var o=s(i),u=o.next();!u.done;u=o.next()){var a=u.value;if(Fr(a))return a}}catch(e){n={error:e}}finally{try{u&&!u.done&&(r=o.return)&&r.call(o)}finally{if(n)throw n.error}}return null}function jr(e){for(var t=e[0],n=t.name,r=1;r<e.length;r++){var i=e[r].level||0;n=t.level===i-1?"".concat(e[r].name," > ").concat(n):"".concat(e[r].name," ").concat(n),t=e[r]}return n}function Dr(e){return e.map((function(e){return e.penalty})).reduce((function(e,t){return e+t}),0)}function Fr(e){var t=jr(e);switch(Er.querySelectorAll(t).length){case 0:throw new Error("Can't select any node with this selector: ".concat(t));case 1:return!0;default:return!1}}function Mr(e,t){return{name:e.name+":nth-child(".concat(t,")"),penalty:e.penalty+1}}function Vr(e){return"html"!==e.name&&!e.name.startsWith("#")}function zr(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];var n=e.filter(Kr);return n.length>0?n:null}function Kr(e){return null!=e}function Br(e,t){var n,r,i,u,a,c;return void 0===t&&(t=[]),o(this,(function(o){switch(o.label){case 0:if(!(e.length>0))return[3,9];o.label=1;case 1:o.trys.push([1,6,7,8]),n=s(e[0]),r=n.next(),o.label=2;case 2:return r.done?[3,5]:(i=r.value,[5,s(Br(e.slice(1,e.length),t.concat(i)))]);case 3:o.sent(),o.label=4;case 4:return r=n.next(),[3,2];case 5:return[3,8];case 6:return u=o.sent(),a={error:u},[3,8];case 7:try{r&&!r.done&&(c=n.return)&&c.call(n)}finally{if(a)throw a.error}return[7];case 8:return[3,11];case 9:return[4,t];case 10:o.sent(),o.label=11;case 11:return[2]}}))}function Wr(e){return a([],u(e),!1).sort((function(e,t){return Dr(e)-Dr(t)}))}function $r(e,t,n){var r,i,c;return void 0===n&&(n={counter:0,visited:new Map}),o(this,(function(o){switch(o.label){case 0:if(!(e.length>2&&e.length>Sr.optimizedMinLength))return[3,5];r=1,o.label=1;case 1:return r<e.length-1?n.counter>Sr.maxNumberOfTries?[2]:(n.counter+=1,(i=a([],u(e),!1)).splice(r,1),c=jr(i),n.visited.has(c)?[2]:Fr(i)&&function(e,t){return Er.querySelector(jr(e))===t}(i,t)?[4,i]:[3,4]):[3,5];case 2:return o.sent(),n.visited.set(c,!0),[5,s($r(i,t,n))];case 3:o.sent(),o.label=4;case 4:return r++,[3,1];case 5:return[2]}}))}new cn(Ht);var Jr=["input","select","textarea"],Qr=function(e,t){return function(n,r){var i,o,s,u=e.pageUrlAllowlist,a=e.shouldTrackEventResolver,c=null===(o=null===(i=null==r?void 0:r.tagName)||void 0===i?void 0:i.toLowerCase)||void 0===o?void 0:o.call(i);if(!c)return!1;if(a)return a(n,r);if(!Xr(window.location.href,u))return!1;var l=(null==r?void 0:r.type)||"";if("string"==typeof l)switch(l.toLowerCase()){case"hidden":case"password":return!1}if(t){var d=t.some((function(e){var t;return!!(null===(t=null==r?void 0:r.matches)||void 0===t?void 0:t.call(r,e))}));if(!d)return!1}switch(c){case"input":case"select":case"textarea":return"change"===n||"click"===n;default:var f=null===(s=null===window||void 0===window?void 0:window.getComputedStyle)||void 0===s?void 0:s.call(window,r);return!(!f||"pointer"!==f.getPropertyValue("cursor")||"click"!==n)||"click"===n}}},Yr=function(e){if(null==e)return!1;if("string"==typeof e){if(/^(?:(4[0-9]{12}(?:[0-9]{3})?)|(5[1-5][0-9]{14})|(6(?:011|5[0-9]{2})[0-9]{12})|(3[47][0-9]{13})|(3(?:0[0-5]|[68][0-9])[0-9]{11})|((?:2131|1800|35[0-9]{3})[0-9]{11}))$/.test((e||"").replace(/[- ]/g,"")))return!1;if(/(^\d{3}-?\d{2}-?\d{4}$)/.test(e))return!1}return!0},Hr=function(e){var t,n,r,i=null===(n=null===(t=null==e?void 0:e.tagName)||void 0===t?void 0:t.toLowerCase)||void 0===n?void 0:n.call(t),o=e instanceof HTMLElement&&"true"===(null===(r=e.getAttribute("contenteditable"))||void 0===r?void 0:r.toLowerCase());return!Jr.includes(i)&&!o},Zr=function(e){var t="";return Hr(e)&&e.childNodes&&e.childNodes.length&&e.childNodes.forEach((function(e){var n,r="";(n=e)&&3===n.nodeType?e.textContent&&(r=e.textContent):r=Zr(e),t+=r.split(/(\s+)/).filter(Yr).join("").replace(/[\r\n]/g," ").replace(/[ ]+/g," ").substring(0,255)})),t},Gr=function(e,t){var n,r,i="";try{return i=Lr(e,{className:function(e){return e!==Dt}})}catch(e){if(t){var o=e;t.warn("Failed to get selector with finder, use fallback strategy instead: ".concat(o.toString()))}}var s=null===(r=null===(n=null==e?void 0:e.tagName)||void 0===n?void 0:n.toLowerCase)||void 0===r?void 0:r.call(n);if(s&&(i=s),e.id)i="#".concat(e.id);else if(e.className){var u=e.className.split(" ").filter((function(e){return e!==Dt})).join(".");u&&(i="".concat(i,".").concat(u))}return i},Xr=function(e,t){return!t||!t.length||t.some((function(t){return"string"==typeof t?e===t:e.match(t)}))},ei=function(e){return Object.keys(e).reduce((function(t,n){var r=e[n];return function(e){return null==e||"object"==typeof e&&0===Object.keys(e).length||"string"==typeof e&&0===e.trim().length}(r)||(t[n]=r),t}),{})},ti=function(e){var t=e.parentElement;if(!t)return"";var n=t.querySelector(":scope>span,h1,h2,h3,h4,h5,h6");if(n){var r=n.textContent||"";return Yr(r)?r:""}return ti(t)},ni=function(e,t){return e?t.some((function(t){var n;return null===(n=null==e?void 0:e.matches)||void 0===n?void 0:n.call(e,t)}))?e:ni(null==e?void 0:e.parentElement,t):null},ri=function(e,t){var n,r,i;if(!e)return{};var o=null===(i=null===(r=null==e?void 0:e.tagName)||void 0===r?void 0:r.toLowerCase)||void 0===i?void 0:i.call(r),s=Gr(e,t),u=((n={})[Nt]=o,n[Rt]=Zr(e),n[Lt]=s,n[qt]=window.location.href.split("?")[0],n);return ei(u)};var ii=function(e){return!(null===e.event.target||!e.closestTrackedAncestor)},oi=function(){function e(e){var t=(void 0===e?{}:e).origin,n=void 0===t?Ut:t,r=this;this.endpoint=Ut,this.requestCallbacks={},this.onSelect=function(e){r.notify({action:"element-selected",data:e})},this.onTrack=function(e,t){"selector-mode-changed"===e?r.notify({action:"track-selector-mode-changed",data:t}):"selector-moved"===e&&r.notify({action:"track-selector-moved",data:t})},this.endpoint=n}return e.prototype.notify=function(e){var t,n,r,i;null===(n=null===(t=this.logger)||void 0===t?void 0:t.debug)||void 0===n||n.call(t,"Message sent: ",JSON.stringify(e)),null===(i=null===(r=window.opener)||void 0===r?void 0:r.postMessage)||void 0===i||i.call(r,e,this.endpoint)},e.prototype.sendRequest=function(e,t,n){var r=this;void 0===n&&(n={timeout:15e3});var i="".concat(Date.now(),"-").concat(Math.random().toString(36).substr(2,9)),o={id:i,action:e,args:t};return new Promise((function(t,s){r.requestCallbacks[i]={resolve:t,reject:s},r.notify(o),(null==n?void 0:n.timeout)>0&&setTimeout((function(){s(new Error("".concat(e," timed out (id: ").concat(i,")"))),delete r.requestCallbacks[i]}),n.timeout)}))},e.prototype.handleResponse=function(e){var t;this.requestCallbacks[e.id]?(this.requestCallbacks[e.id].resolve(e.responseData),delete this.requestCallbacks[e.id]):null===(t=this.logger)||void 0===t||t.warn("No callback found for request id: ".concat(e.id))},e.prototype.setup=function(e){var t=this,n=void 0===e?{}:e,r=n.logger,i=n.endpoint,o=n.isElementSelectable,s=n.cssSelectorAllowlist,u=n.actionClickAllowlist;this.logger=r,i&&this.endpoint===Ut&&(this.endpoint=i);var a=null;window.addEventListener("message",(function(e){var n,r,i,c,l;if(null===(r=null===(n=t.logger)||void 0===n?void 0:n.debug)||void 0===r||r.call(n,"Message received: ",JSON.stringify(e)),t.endpoint===e.origin){var d,f=null==e?void 0:e.data,h=null==f?void 0:f.action;if(h)if("id"in f)null===(c=null===(i=t.logger)||void 0===i?void 0:i.debug)||void 0===c||c.call(i,"Received Response to previous request: ",JSON.stringify(e)),t.handleResponse(f);else if("ping"===h)t.notify({action:"pong"});else if("initialize-visual-tagging-selector"===h){var p=null==f?void 0:f.data;(d="https://cdn.amplitude.com/libs/visual-tagging-selector-1.0.0-alpha.js.gz",new Promise((function(e,t){var n;try{var r=document.createElement("script");r.type="text/javascript",r.async=!0,r.src=d,r.addEventListener("load",(function(){e({status:!0})}),{once:!0}),r.addEventListener("error",(function(){t({status:!1,message:"Failed to load the script ".concat(d)})})),null===(n=document.head)||void 0===n||n.appendChild(r)}catch(e){t(e)}}))).then((function(){var e;a=null===(e=null===window||void 0===window?void 0:window.amplitudeVisualTaggingSelector)||void 0===e?void 0:e.call(window,{getEventTagProps:ri,isElementSelectable:function(e){return!o||o((null==p?void 0:p.actionType)||"click",e)},onTrack:t.onTrack,onSelect:t.onSelect,visualHighlightClass:Dt,messenger:t,cssSelectorAllowlist:s,actionClickAllowlist:u}),t.notify({action:"selector-loaded"})})).catch((function(){var e;null===(e=t.logger)||void 0===e||e.warn("Failed to initialize visual tagging selector")}))}else"close-visual-tagging-selector"===h&&(null===(l=null==a?void 0:a.close)||void 0===l||l.call(a))}})),this.notify({action:"page-loaded"})},e}(),si=["id","class","style","value","onclick","onchange","oninput","onblur","onsubmit","onfocus","onkeydown","onkeyup","onkeypress","data-reactid","data-react-checksum","data-reactroot"],ui=["type"],ai=["svg","path","g"],ci=["password","hidden"];var li=function(e){var t;return e?(t=function(e,t){for(var n=0,r=0;r<e.length;r++){var i=e[r];if(null===i)n+=4;else{var o=di(i);n+=o?Array.from(o).length:4}if(n>t)return e.slice(0,r)}return e}(function(e){var t=[];if(!e)return t;t.push(e);for(var n=e.parentElement;n&&"HTML"!==n.tagName;)t.push(n),n=n.parentElement;return t}(e).map((function(e){return function(e){var t,n,r,i,o,u;if(null===e)return null;var a=e.tagName.toLowerCase(),c={tag:a},l=Array.from(null!==(i=null===(r=e.parentElement)||void 0===r?void 0:r.children)&&void 0!==i?i:[]);l.length&&(c.index=l.indexOf(e),c.indexOfType=l.filter((function(t){return t.tagName===e.tagName})).indexOf(e));var d=null===(u=null===(o=e.previousElementSibling)||void 0===o?void 0:o.tagName)||void 0===u?void 0:u.toLowerCase();d&&(c.prevSib=d);var f=e.id;f&&(c.id=f);var h=Array.from(e.classList);h.length&&(c.classes=h);var p={},v=Array.from(e.attributes).filter((function(e){return!si.includes(e.name)})),g=!Hr(e);if(!ci.includes(e.type)&&!ai.includes(a))try{for(var y=s(v),m=y.next();!m.done;m=y.next()){var b=m.value;g&&!ui.includes(b.name)||(p[b.name]=String(b.value).substring(0,128))}}catch(e){t={error:e}}finally{try{m&&!m.done&&(n=y.return)&&n.call(y)}finally{if(t)throw t.error}}return Object.keys(p).length&&(c.attrs=p),c}(e)})),1024),t):[]};function di(e,t){void 0===t&&(t=!1);try{if(null==e)return t?"None":null;if("string"==typeof e)return t?(e=e.replace(/\\/g,"\\\\").replace(/\n/g,"\\n").replace(/\t/g,"\\t").replace(/\r/g,"\\r")).includes('"')?"'".concat(e,"'"):e.includes("'")?'"'.concat(e.replace(/'/g,"\\'"),'"'):"'".concat(e,"'"):e;if("boolean"==typeof e)return e?"True":"False";if(Array.isArray(e)){var n=e.map((function(e){return di(e,!0)}));return"[".concat(n.join(", "),"]")}if("object"==typeof e){var r=Object.entries(e).filter((function(e){return null!=u(e,1)[0]})).map((function(e){var t=u(e,2),n=t[0],r=t[1];return"".concat(String(di(n,!0)),": ").concat(String(di(r,!0)))})),i="{".concat(r.join(", "),"}");return i.includes("\\'")&&(i=i.replace(/'/g,"'").replace(/'/g,"\\'")),i}return e.toString()}catch(e){return null}}function fi(e){var t,n,r,i=e.amplitude,o=e.allObservables,a=e.options,c=e.shouldTrackEvent,l=o.clickObservable,d=l.pipe(fn((function(e,t){var n,r=!1;e.subscribe(hn(t,(function(e){var i=n;n=e,r&&t.next([i,e]),r=!0})))})),Ar((function(e){var t=u(e,2),n=t[0],r=t[1],i=n.event.target!==r.event.target,o=Math.abs(r.event.screenX-n.event.screenX)<=20&&Math.abs(r.event.screenY-n.event.screenY)<=20;return i&&!o}))),f=xr(d,l.pipe((t=a.debounceTime,void 0===n&&(n=jn),fn((function(e,r){var i=null,o=null,s=null,u=function(){if(i){i.unsubscribe(),i=null;var e=o;o=null,r.next(e)}};function a(){var e=s+t,o=n.now();if(o<e)return i=this.schedule(void 0,e-o),void r.add(i);u()}e.subscribe(hn(r,(function(e){o=e,s=n.now(),i||(i=n.schedule(a,t),r.add(i))}),(function(){u(),r.complete()}),void 0,(function(){o=i=null})))}))),gr((function(){return"timeout"}))));return l.pipe(Rr(0),Ar(ii),Ar((function(e){return c("click",e.closestTrackedAncestor)})),(r=f,fn((function(e,t){var n=[];return e.subscribe(hn(t,(function(e){return n.push(e)}),(function(){t.next(n),t.complete()}))),or(r).subscribe(hn(t,(function(){var e=n;n=[],t.next(e)}),Ht)),function(){n=null}})))).subscribe((function(e){var t,n,r=(e.length,Ct);try{for(var o=s(e),u=o.next();!u.done;u=o.next()){var a=u.value;null==i||i.track(r,a.targetElementProperties,{time:a.timestamp})}}catch(e){t={error:e}}finally{try{u&&!u.done&&(n=o.return)&&n.call(o)}finally{if(t)throw t.error}}}))}function hi(e){var t=e.amplitude,n=e.allObservables,r=e.options,i=e.getEventProperties,o=e.shouldTrackEvent,s=e.shouldTrackActionClick,c=n.clickObservable,l=n.mutationObservable,d=n.navigateObservable,f=c.pipe(Ar((function(e){return!o("click",e.closestTrackedAncestor)})),gr((function(e){var t=ni(e.event.target,r.actionClickAllowlist);return e.closestTrackedAncestor=t,null!==e.closestTrackedAncestor&&(e.targetElementProperties=i(e.type,e.closestTrackedAncestor)),e})),Ar(ii),Ar((function(e){return s("click",e.closestTrackedAncestor)}))),h=[l];d&&h.push(d);var p,v,g=xr.apply(void 0,a([],u(h),!1)),y=f.pipe((p=function(e){return g.pipe(Cr(1),function(e,t){var n=hr(e)?{first:e}:"number"==typeof e?{each:e}:e,r=n.first,i=n.each,o=n.with,s=void 0===o?vr:o,u=n.scheduler,a=void 0===u?null!=t?t:jn:u,c=n.meta,l=void 0===c?null:c;if(null==r&&null==i)throw new TypeError("No timeout provided.");return fn((function(e,t){var n,o,u=null,c=0,d=function(e){o=ur(t,a,(function(){try{n.unsubscribe(),or(s({meta:l,lastValue:u,seen:c})).subscribe(t)}catch(e){t.error(e)}}),e)};n=e.subscribe(hn(t,(function(e){null==o||o.unsubscribe(),c++,t.next(u=e),i>0&&d(i)}),void 0,void 0,(function(){(null==o?void 0:o.closed)||null==o||o.unsubscribe(),u=null}))),!c&&d(null!=r?"number"==typeof r?r:+r-a.now():i)}))}({first:500,with:function(){return Bn}}),gr((function(){return e})))},fn((function(e,t){var n=null,r=0,i=!1,o=function(){return i&&!n&&t.complete()};e.subscribe(hn(t,(function(e){null==n||n.unsubscribe();var i=0,s=r++;or(p(e,s)).subscribe(n=hn(t,(function(n){return t.next(v?v(e,n,s,i++):n)}),(function(){n=null,o()})))}),(function(){i=!0,o()})))}))));return y.subscribe((function(e){null==t||t.track(Ct,i("click",e.closestTrackedAncestor),{time:e.timestamp})}))}var pi,vi=["a","button","input","select","textarea","label","video","audio",'[contenteditable="true" i]',"[data-amp-default-track]",".amp-default-track"],gi=["div","span","h1","h2","h3","h4","h5","h6"];!function(e){e.ClickObservable="clickObservable",e.ChangeObservable="changeObservable",e.NavigateObservable="navigateObservable",e.MutationObservable="mutationObservable"}(pi||(pi={}));var yi=function(e){var t,r,c;void 0===e&&(e={});var l=e.dataAttributePrefix,d=void 0===l?"data-amp-track-":l,f=e.visualTaggingOptions,h=void 0===f?{enabled:!0,messenger:new oi}:f;e.cssSelectorAllowlist=null!==(t=e.cssSelectorAllowlist)&&void 0!==t?t:vi,e.actionClickAllowlist=null!==(r=e.actionClickAllowlist)&&void 0!==r?r:gi,e.debounceTime=null!==(c=e.debounceTime)&&void 0!==c?c:0;var p="@amplitude/plugin-autocapture-browser",v=[],g=void 0,y=function(e,t){var n,r,i,o=null===(i=null===(r=null==t?void 0:t.tagName)||void 0===r?void 0:r.toLowerCase)||void 0===i?void 0:i.call(r),s="function"==typeof t.getBoundingClientRect?t.getBoundingClientRect():{left:null,top:null},u=t.getAttribute("aria-label"),a=function(e,t){return e.getAttributeNames().reduce((function(n,r){if(r.startsWith(t)){var i=r.replace(t,""),o=e.getAttribute(r);i&&(n[i]=o||"")}return n}),{})}(t,d),c=ti(t),l=Gr(t,g),f=((n={})["[Amplitude] Element ID"]=t.id,n["[Amplitude] Element Class"]=t.className,n["[Amplitude] Element Hierarchy"]=li(t),n[Nt]=o,n[Rt]=Zr(t),n["[Amplitude] Element Position Left"]=null==s.left?null:Math.round(s.left),n["[Amplitude] Element Position Top"]=null==s.top?null:Math.round(s.top),n["[Amplitude] Element Aria Label"]=u,n["[Amplitude] Element Attributes"]=a,n[Lt]=l,n["[Amplitude] Element Parent Label"]=c,n[qt]=window.location.href.split("?")[0],n["[Amplitude] Page Title"]="undefined"!=typeof document&&document.title||"",n["[Amplitude] Viewport Height"]=window.innerHeight,n["[Amplitude] Viewport Width"]=window.innerWidth,n);return"a"===o&&"click"===e&&t instanceof HTMLAnchorElement&&(f["[Amplitude] Element Href"]=t.href),ei(f)},m=function(t,n){var r={event:t,timestamp:Date.now(),type:n};if(function(e){return"click"===e.type||"change"===e.type}(r)&&null!==r.event.target){var i=ni(r.event.target,e.cssSelectorAllowlist);return i&&(r.closestTrackedAncestor=i,r.targetElementProperties=y(r.type,i)),r}return r};return{name:p,type:"enrichment",setup:function(t,r){return i(void 0,void 0,void 0,(function(){var i,s,c,l,d,f,b,w,_,S,E;return o(this,(function(o){return r?(g=t.loggerProvider,"undefined"==typeof document||(i=Qr(e,e.cssSelectorAllowlist),s=Qr(e,e.actionClickAllowlist),c=function(){var e,t,n=Or(document,"click",{capture:!0}).pipe(gr((function(e){return m(e,"click")}))),r=Or(document,"change",{capture:!0}).pipe(gr((function(e){return m(e,"change")})));window.navigation&&(t=Or(window.navigation,"navigate").pipe(gr((function(e){return m(e,"navigate")}))));var i=new cn((function(e){var t=new MutationObserver((function(t){e.next(t)}));return t.observe(document.body,{childList:!0,attributes:!0,characterData:!0,subtree:!0}),function(){return t.disconnect()}})).pipe(gr((function(e){return m(e,"mutation")})));return(e={})[pi.ClickObservable]=n,e[pi.ChangeObservable]=r,e[pi.NavigateObservable]=t,e[pi.MutationObservable]=i,e}(),l=fi({allObservables:c,options:e,amplitude:r,shouldTrackEvent:i}),v.push(l),d=function(e){var t=e.amplitude,n=e.allObservables,r=e.getEventProperties,i=e.shouldTrackEvent;return n.changeObservable.pipe(Ar(ii),Ar((function(e){return i("change",e.closestTrackedAncestor)}))).subscribe((function(e){null==t||t.track("[Amplitude] Element Changed",r("change",e.closestTrackedAncestor))}))}({allObservables:c,getEventProperties:y,amplitude:r,shouldTrackEvent:i}),v.push(d),f=hi({allObservables:c,options:e,getEventProperties:y,amplitude:r,shouldTrackEvent:i,shouldTrackActionClick:s}),v.push(f),null===(S=null==t?void 0:t.loggerProvider)||void 0===S||S.log("".concat(p," has been successfully added.")),window.opener&&h.enabled&&(b=e.cssSelectorAllowlist,w=e.actionClickAllowlist,null===(E=h.messenger)||void 0===E||E.setup(n(n({logger:null==t?void 0:t.loggerProvider},(null==t?void 0:t.serverZone)&&{endpoint:jt[t.serverZone]}),{isElementSelectable:Qr(e,a(a([],u(b),!1),u(w),!1)),cssSelectorAllowlist:b,actionClickAllowlist:w})))),[2]):(null===(_=null==t?void 0:t.loggerProvider)||void 0===_||_.warn("".concat(p," plugin requires a later version of @amplitude/analytics-browser. Events are not tracked.")),[2])}))}))},execute:function(e){return i(void 0,void 0,void 0,(function(){return o(this,(function(t){return[2,e]}))}))},teardown:function(){return i(void 0,void 0,void 0,(function(){var e,t,n,r;return o(this,(function(i){try{for(e=s(v),t=e.next();!t.done;t=e.next())t.value.unsubscribe()}catch(e){n={error:e}}finally{try{t&&!t.done&&(r=e.return)&&r.call(e)}finally{if(n)throw n.error}}return[2]}))}))}}},mi=function(e){function r(){return null!==e&&e.apply(this,arguments)||this}return t(r,e),r.prototype.init=function(e,t,r){var i,o;return void 0===e&&(e=""),arguments.length>2?(i=t,o=r):"string"==typeof t?(i=t,o=void 0):(i=null==t?void 0:t.userId,o=t),W(this._init(n(n({},o),{userId:i,apiKey:e})))},r.prototype._init=function(t){var r,s,u;return i(this,void 0,void 0,(function(){var a,c,l,d,f,h=this;return o(this,(function(p){switch(p.label){case 0:return this.initializing?[2]:(this.initializing=!0,[4,ht(t.apiKey,t,this)]);case 1:return a=p.sent(),t.fetchRemoteConfig?[4,(g=a,i(void 0,void 0,void 0,(function(){var e;return o(this,(function(t){switch(t.label){case 0:return[4,(e=new At(g)).initialize()];case 1:return t.sent(),[2,e]}}))})))]:[3,4];case 2:return[4,p.sent().generateJoinedConfig()];case 3:a=p.sent(),p.label=4;case 4:return[4,e.prototype._init.call(this,a)];case 5:return p.sent(),this.logBrowserOptions(a),Ne(this.config.defaultTracking)?(c=function(e){return Ne(e.defaultTracking)&&e.defaultTracking&&"object"==typeof e.defaultTracking&&e.defaultTracking.attribution&&"object"==typeof e.defaultTracking.attribution?n({},e.defaultTracking.attribution):{}}(this.config),this.webAttribution=new ve(c,this.config),[4,this.webAttribution.init()]):[3,7];case 6:p.sent(),p.label=7;case 7:return l=b(),d=Number.isNaN(Number(l.ampSessionId))?void 0:Number(l.ampSessionId),this.setSessionId(null!==(u=null!==(s=null!==(r=t.sessionId)&&void 0!==r?r:d)&&void 0!==s?s:this.config.sessionId)&&void 0!==u?u:Date.now()),(f=Pe(t.instanceName)).identityStore.setIdentity({userId:this.config.userId,deviceId:this.config.deviceId}),null===this.config.offline?[3,9]:[4,this.add(kt()).promise];case 8:p.sent(),p.label=9;case 9:return[4,this.add(new ne).promise];case 10:return p.sent(),[4,this.add(new Ve).promise];case 11:return p.sent(),[4,this.add(new xe).promise];case 12:return p.sent(),function(e){Tt||void 0!==e.defaultTracking||(e.loggerProvider.warn("`options.defaultTracking` is set to undefined. This implicitly configures your Amplitude instance to track Page Views, Sessions, File Downloads, and Form Interactions. You can suppress this warning by explicitly setting a value to `options.defaultTracking`. The value must either be a boolean, to enable and disable all default events, or an object, for advanced configuration. For example:\n\namplitude.init(<YOUR_API_KEY>, {\n  defaultTracking: true,\n});\n\nVisit https://www.docs.developers.amplitude.com/data/sdks/browser-2/#tracking-default-events for more details."),Tt=!0)}(this.config),v=this.config.defaultTracking,Ce(v,"fileDownloads")?(this.config.loggerProvider.debug("Adding file download tracking plugin"),[4,this.add(It()).promise]):[3,14];case 13:p.sent(),p.label=14;case 14:return function(e){return Ce(e,"formInteractions")}(this.config.defaultTracking)?(this.config.loggerProvider.debug("Adding form interaction plugin"),[4,this.add(St()).promise]):[3,16];case 15:p.sent(),p.label=16;case 16:return Re(this.config.defaultTracking)?(this.config.loggerProvider.debug("Adding page view tracking plugin"),[4,this.add(mt(je(this.config))).promise]):[3,18];case 17:p.sent(),p.label=18;case 18:return qe(this.config.autocapture)?(this.config.loggerProvider.debug("Adding user interactions plugin (autocapture plugin)"),[4,this.add(yi(Ue(this.config))).promise]):[3,20];case 19:p.sent(),p.label=20;case 20:return this.initializing=!1,[4,this.runQueuedFunctions("dispatchQ")];case 21:return p.sent(),f.eventBridge.setEventReceiver((function(e){h.track(e.eventType,e.eventProperties)})),[2]}var v,g}))}))},r.prototype.getUserId=function(){var e;return null===(e=this.config)||void 0===e?void 0:e.userId},r.prototype.setUserId=function(e){this.config?(this.config.loggerProvider.debug("function setUserId: ",e),e===this.config.userId&&void 0!==e||(this.config.userId=e,function(e,t){Pe(t).identityStore.editIdentity().setUserId(e).commit()}(e,this.config.instanceName))):this.q.push(this.setUserId.bind(this,e))},r.prototype.getDeviceId=function(){var e;return null===(e=this.config)||void 0===e?void 0:e.deviceId},r.prototype.setDeviceId=function(e){this.config?(this.config.loggerProvider.debug("function setDeviceId: ",e),this.config.deviceId=e,function(e,t){Pe(t).identityStore.editIdentity().setDeviceId(e).commit()}(e,this.config.instanceName)):this.q.push(this.setDeviceId.bind(this,e))},r.prototype.reset=function(){this.setDeviceId(U()),this.setUserId(void 0)},r.prototype.getSessionId=function(){var e;return null===(e=this.config)||void 0===e?void 0:e.sessionId},r.prototype.setSessionId=function(e){var t,n=[];if(!this.config)return this.q.push(this.setSessionId.bind(this,e)),W(Promise.resolve());if(e===this.config.sessionId)return W(Promise.resolve());this.config.loggerProvider.debug("function setSessionId: ",e);var r=this.getSessionId(),i=this.config.lastEventTime,o=null!==(t=this.config.lastEventId)&&void 0!==t?t:-1;this.config.sessionId=e,this.config.lastEventTime=void 0,this.config.pageCounter=0,Le(this.config.defaultTracking)&&(r&&i&&n.push(this.track(nt,void 0,{device_id:this.previousSessionDeviceId,event_id:++o,session_id:r,time:i+1,user_id:this.previousSessionUserId}).promise),this.config.lastEventTime=this.config.sessionId);var s=this.trackCampaignEventIfNeeded(++o,n);return Le(this.config.defaultTracking)&&n.push(this.track(tt,void 0,{event_id:s?++o:o,session_id:this.config.sessionId,time:this.config.lastEventTime}).promise),this.previousSessionDeviceId=this.config.deviceId,this.previousSessionUserId=this.config.userId,W(Promise.all(n))},r.prototype.extendSession=function(){this.config?this.config.lastEventTime=Date.now():this.q.push(this.extendSession.bind(this))},r.prototype.setTransport=function(e){this.config?this.config.transportProvider=vt(e):this.q.push(this.setTransport.bind(this,e))},r.prototype.identify=function(t,n){if(Me(t)){var r=t._q;t._q=[],t=Fe(new K,r)}return(null==n?void 0:n.user_id)&&this.setUserId(n.user_id),(null==n?void 0:n.device_id)&&this.setDeviceId(n.device_id),e.prototype.identify.call(this,t,n)},r.prototype.groupIdentify=function(t,n,r,i){if(Me(r)){var o=r._q;r._q=[],r=Fe(new K,o)}return e.prototype.groupIdentify.call(this,t,n,r,i)},r.prototype.revenue=function(t,n){if(Me(t)){var r=t._q;t._q=[],t=Fe(new J,r)}return e.prototype.revenue.call(this,t,n)},r.prototype.trackCampaignEventIfNeeded=function(e,t){if(!this.webAttribution||!this.webAttribution.shouldTrackNewCampaign)return!1;var n=this.webAttribution.generateCampaignEvent(e);return t?t.push(this.track(n).promise):this.track(n),this.config.loggerProvider.log("Tracking attribution."),!0},r.prototype.process=function(t){return i(this,void 0,void 0,(function(){var n,r,i;return o(this,(function(o){return n=Date.now(),r=pe(this.config.sessionTimeout,this.config.lastEventTime),i=this.webAttribution&&this.webAttribution.shouldSetSessionIdOnNewCampaign(),t.event_type===tt||t.event_type===nt||t.session_id&&t.session_id!==this.getSessionId()||(r||i?(this.setSessionId(n),i&&this.config.loggerProvider.log("Created a new session for new campaign.")):r||this.trackCampaignEventIfNeeded()),[2,e.prototype.process.call(this,t)]}))}))},r.prototype.logBrowserOptions=function(e){try{var t=n(n({},e),{apiKey:e.apiKey.substring(0,10)+"********"});this.config.loggerProvider.debug("Initialized Amplitude with BrowserConfig:",JSON.stringify(t))}catch(e){this.config.loggerProvider.error("Error logging browser config",e)}},r}($),bi=function(){var e=new mi;return{init:ue(e.init.bind(e),"init",ie(e),se(e,["config"])),add:ue(e.add.bind(e),"add",ie(e),se(e,["config.apiKey","timeline.plugins"])),remove:ue(e.remove.bind(e),"remove",ie(e),se(e,["config.apiKey","timeline.plugins"])),track:ue(e.track.bind(e),"track",ie(e),se(e,["config.apiKey","timeline.queue.length"])),logEvent:ue(e.logEvent.bind(e),"logEvent",ie(e),se(e,["config.apiKey","timeline.queue.length"])),identify:ue(e.identify.bind(e),"identify",ie(e),se(e,["config.apiKey","timeline.queue.length"])),groupIdentify:ue(e.groupIdentify.bind(e),"groupIdentify",ie(e),se(e,["config.apiKey","timeline.queue.length"])),setGroup:ue(e.setGroup.bind(e),"setGroup",ie(e),se(e,["config.apiKey","timeline.queue.length"])),revenue:ue(e.revenue.bind(e),"revenue",ie(e),se(e,["config.apiKey","timeline.queue.length"])),flush:ue(e.flush.bind(e),"flush",ie(e),se(e,["config.apiKey","timeline.queue.length"])),getUserId:ue(e.getUserId.bind(e),"getUserId",ie(e),se(e,["config","config.userId"])),setUserId:ue(e.setUserId.bind(e),"setUserId",ie(e),se(e,["config","config.userId"])),getDeviceId:ue(e.getDeviceId.bind(e),"getDeviceId",ie(e),se(e,["config","config.deviceId"])),setDeviceId:ue(e.setDeviceId.bind(e),"setDeviceId",ie(e),se(e,["config","config.deviceId"])),reset:ue(e.reset.bind(e),"reset",ie(e),se(e,["config","config.userId","config.deviceId"])),getSessionId:ue(e.getSessionId.bind(e),"getSessionId",ie(e),se(e,["config"])),setSessionId:ue(e.setSessionId.bind(e),"setSessionId",ie(e),se(e,["config"])),extendSession:ue(e.extendSession.bind(e),"extendSession",ie(e),se(e,["config"])),setOptOut:ue(e.setOptOut.bind(e),"setOptOut",ie(e),se(e,["config"])),setTransport:ue(e.setTransport.bind(e),"setTransport",ie(e),se(e,["config"]))}},wi=bi(),_i=wi.add,Si=wi.extendSession,Ei=wi.flush,Ii=wi.getDeviceId,Ti=wi.getSessionId,ki=wi.getUserId,Oi=wi.groupIdentify,Pi=wi.identify,xi=wi.init,Ai=wi.logEvent,Ci=wi.remove,Ni=wi.reset,Ri=wi.revenue,Li=wi.setDeviceId,qi=wi.setGroup,Ui=wi.setOptOut,ji=wi.setSessionId,Di=wi.setTransport,Fi=wi.setUserId,Mi=wi.track,Vi=Object.freeze({__proto__:null,add:_i,extendSession:Si,flush:Ei,getDeviceId:Ii,getSessionId:Ti,getUserId:ki,groupIdentify:Oi,identify:Pi,init:xi,logEvent:Ai,remove:Ci,reset:Ni,revenue:Ri,setDeviceId:Li,setGroup:qi,setOptOut:Ui,setSessionId:ji,setTransport:Di,setUserId:Fi,track:Mi,Types:L,createInstance:bi,runQueuedFunctions:De,Revenue:J,Identify:K});!function(){var e=m();if(e){var t=function(e){var t=bi(),n=m();return n&&n.amplitude&&n.amplitude._iq&&e&&(n.amplitude._iq[e]=t),t};if(e.amplitude=Object.assign(e.amplitude||{},Vi,{createInstance:t}),e.amplitude.invoked){var n=e.amplitude._q;e.amplitude._q=[],De(Vi,n);for(var r=Object.keys(e.amplitude._iq)||[],i=0;i<r.length;i++){var o=r[i],s=Object.assign(e.amplitude._iq[o],t(o)),u=s._q;s._q=[],De(s,u)}}}else console.error("[Amplitude] Error: GlobalScope is not defined")}()}();
//# sourceMappingURL=amplitude-min.js.map
