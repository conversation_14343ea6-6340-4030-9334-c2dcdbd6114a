// Amplitude Analytics Configuration
// This file initializes Amplitude with session replay and autocapture

// Wait for Amplitude scripts to load
function waitForAmplitude() {
  return new Promise((resolve, reject) => {
    let attempts = 0;
    const maxAttempts = 50; // 5 seconds max wait

    const checkAmplitude = () => {
      attempts++;

      if (window.amplitude && window.sessionReplay) {
        resolve();
      } else if (attempts >= maxAttempts) {
        reject(new Error('Amplitude scripts failed to load'));
      } else {
        setTimeout(checkAmplitude, 100);
      }
    };

    checkAmplitude();
  });
}

// Initialize Amplitude with session replay plugin
async function initializeAmplitude() {
  try {
    // Wait for scripts to load
    await waitForAmplitude();

    // Add session replay plugin
    window.amplitude.add(window.sessionReplay.plugin({
      sampleRate: 1 // 100% sampling rate - adjust as needed for production
    }));

    // Initialize Amplitude with your API key
    window.amplitude.init('e536f074ed1081e2d622a70005e3d9ad', {
      autocapture: {
        elementInteractions: true // Automatically capture clicks, form submissions, etc.
      }
    });

    // Initialize tracking
    window.amplitude.track('amplitude_initialized', {
      timestamp: new Date().toISOString(),
      page: window.location.pathname
    });

    return true;
  } catch (error) {
    // Fallback: create dummy functions to prevent errors
    createAmplitudeFallback();
    return false;
  }
}

// Create fallback functions if Amplitude fails to load
function createAmplitudeFallback() {
  window.amplitudeUtils = {
    track: function(eventName, properties = {}) {
      // Silent fallback
    },
    setUserProperties: function(properties) {
      // Silent fallback
    },
    setUserId: function(userId) {
      // Silent fallback
    }
  };
}

// Initialize when DOM is ready
async function startAmplitude() {
  const success = await initializeAmplitude();
  createAmplitudeUtils();

  // Track page view after initialization
  if (success) {
    setTimeout(() => {
      window.amplitudeUtils.track('page_viewed', {
        page_path: window.location.pathname,
        page_title: document.title,
        referrer: document.referrer,
        timestamp: new Date().toISOString()
      });
    }, 1000); // Wait 1 second to ensure everything is ready
  }
}

if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', startAmplitude);
} else {
  startAmplitude();
}

// Create utility functions after initialization
function createAmplitudeUtils() {
  window.amplitudeUtils = {
    // Track custom events
    track: function(eventName, properties = {}) {
      try {
        if (window.amplitude && window.amplitude.track) {
          window.amplitude.track(eventName, properties);
        }
      } catch (error) {
        // Silent error handling
      }
    },

    // Set user properties
    setUserProperties: function(properties) {
      try {
        if (window.amplitude && window.amplitude.setUserProperties) {
          window.amplitude.setUserProperties(properties);
        }
      } catch (error) {
        // Silent error handling
      }
    },

    // Identify user
    setUserId: function(userId) {
      try {
        if (window.amplitude && window.amplitude.setUserId) {
          window.amplitude.setUserId(userId);
        }
      } catch (error) {
        // Silent error handling
      }
    }
  };
}
