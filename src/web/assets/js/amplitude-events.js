// Amplitude Custom Events for Rent Report Application
// This file contains predefined event tracking functions for common user actions

// Authentication Events
window.AuthEvents = {
  userSignUp: (method = 'email') => {
    if (window.amplitudeUtils && window.amplitudeUtils.track) {
      window.amplitudeUtils.track('User Sign Up', {
        method: method,
        timestamp: new Date().toISOString()
      });
    }
  },

  userLogin: (method = 'email') => {
    if (window.amplitudeUtils && window.amplitudeUtils.track) {
      window.amplitudeUtils.track('User Login', {
        method: method,
        timestamp: new Date().toISOString()
      });
    }
  },

  userLogout: () => {
    if (window.amplitudeUtils && window.amplitudeUtils.track) {
      window.amplitudeUtils.track('User Logout', {
        timestamp: new Date().toISOString()
      });
    }
  }
};

// Property Management Events
window.PropertyEvents = {
  propertyCreated: (propertyType) => {
    if (window.amplitudeUtils && window.amplitudeUtils.track) {
      window.amplitudeUtils.track('Property Created', {
        property_type: propertyType,
        timestamp: new Date().toISOString()
      });
    }
  },

  propertyViewed: (propertyId) => {
    if (window.amplitudeUtils && window.amplitudeUtils.track) {
      window.amplitudeUtils.track('Property Viewed', {
        property_id: propertyId,
        timestamp: new Date().toISOString()
      });
    }
  },

  propertyArchived: (propertyId) => {
    if (window.amplitudeUtils && window.amplitudeUtils.track) {
      window.amplitudeUtils.track('Property Archived', {
        property_id: propertyId,
        timestamp: new Date().toISOString()
      });
    }
  }
};

// Lease Management Events
window.LeaseEvents = {
  leaseCreated: (leaseData) => {
    if (window.amplitudeUtils && window.amplitudeUtils.track) {
      window.amplitudeUtils.track('Lease Created', {
        lease_id: leaseData.id,
        property_id: leaseData.propertyId,
        tenant_count: leaseData.tenantCount || 1,
        lease_duration: leaseData.duration,
        timestamp: new Date().toISOString()
      });
    }
  },

  leaseViewed: (leaseId) => {
    if (window.amplitudeUtils && window.amplitudeUtils.track) {
      window.amplitudeUtils.track('Lease Viewed', {
        lease_id: leaseId,
        timestamp: new Date().toISOString()
      });
    }
  },

  leaseEnded: (leaseId) => {
    if (window.amplitudeUtils && window.amplitudeUtils.track) {
      window.amplitudeUtils.track('Lease Ended', {
        lease_id: leaseId,
        timestamp: new Date().toISOString()
      });
    }
  },

  tenantAdded: (leaseId, tenantEmail) => {
    if (window.amplitudeUtils && window.amplitudeUtils.track) {
      window.amplitudeUtils.track('Tenant Added', {
        lease_id: leaseId,
        tenant_email: tenantEmail,
        timestamp: new Date().toISOString()
      });
    }
  }
};

// Payment Events
window.PaymentEvents = {
  paymentAdded: (paymentData) => {
    window.amplitudeUtils?.track('Payment Added', {
      lease_id: paymentData.leaseId,
      amount: paymentData.amount,
      payment_type: paymentData.type,
      timestamp: new Date().toISOString()
    });
  },

  paymentViewed: (paymentId) => {
    window.amplitudeUtils?.track('Payment Viewed', {
      payment_id: paymentId,
      timestamp: new Date().toISOString()
    });
  }
};

// Document Events
window.DocumentEvents = {
  documentUploaded: (documentType, leaseId) => {
    window.amplitudeUtils?.track('Document Uploaded', {
      document_type: documentType,
      lease_id: leaseId,
      timestamp: new Date().toISOString()
    });
  },

  documentDownloaded: (documentType, leaseId) => {
    window.amplitudeUtils?.track('Document Downloaded', {
      document_type: documentType,
      lease_id: leaseId,
      timestamp: new Date().toISOString()
    });
  },

  documentDeleted: (documentType, leaseId) => {
    window.amplitudeUtils?.track('Document Deleted', {
      document_type: documentType,
      lease_id: leaseId,
      timestamp: new Date().toISOString()
    });
  }
};

// Report Events
window.ReportEvents = {
  rentReportGenerated: (reportType, leaseId) => {
    window.amplitudeUtils?.track('Rent Report Generated', {
      report_type: reportType,
      lease_id: leaseId,
      timestamp: new Date().toISOString()
    });
  },

  reportDownloaded: (reportType, format) => {
    window.amplitudeUtils?.track('Report Downloaded', {
      report_type: reportType,
      format: format,
      timestamp: new Date().toISOString()
    });
  }
};

// Navigation Events
window.NavigationEvents = {
  pageViewed: (pageName, userRole) => {
    if (window.amplitudeUtils && window.amplitudeUtils.track) {
      window.amplitudeUtils.track('Page Viewed', {
        page_name: pageName,
        user_role: userRole,
        timestamp: new Date().toISOString()
      });
    }
  },

  modalOpened: (modalName) => {
    if (window.amplitudeUtils && window.amplitudeUtils.track) {
      window.amplitudeUtils.track('Modal Opened', {
        modal_name: modalName,
        timestamp: new Date().toISOString()
      });
    }
  }
};

// User Identification Helper
window.identifyUser = (userId, userProperties = {}) => {
  if (window.amplitudeUtils) {
    if (window.amplitudeUtils.setUserId) {
      window.amplitudeUtils.setUserId(userId);
    }
    if (window.amplitudeUtils.setUserProperties) {
      window.amplitudeUtils.setUserProperties({
        ...userProperties,
        last_seen: new Date().toISOString()
      });
    }
  }
};
