var sessionReplay=function(e){"use strict";var t=function(e,n){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])},t(e,n)};function n(e,n){if("function"!=typeof n&&null!==n)throw new TypeError("Class extends value "+String(n)+" is not a constructor or null");function r(){this.constructor=e}t(e,n),e.prototype=null===n?Object.create(n):(r.prototype=n.prototype,new r)}var r,o,s,i=function(){return i=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e},i.apply(this,arguments)};function a(e,t,n,r){return new(n||(n=Promise))((function(o,s){function i(e){try{c(r.next(e))}catch(e){s(e)}}function a(e){try{c(r.throw(e))}catch(e){s(e)}}function c(e){var t;e.done?o(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(i,a)}c((r=r.apply(e,t||[])).next())}))}function c(e,t){var n,r,o,s,i={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return s={next:a(0),throw:a(1),return:a(2)},"function"==typeof Symbol&&(s[Symbol.iterator]=function(){return this}),s;function a(a){return function(c){return function(a){if(n)throw new TypeError("Generator is already executing.");for(;s&&(s=0,a[0]&&(i=0)),i;)try{if(n=1,r&&(o=2&a[0]?r.return:a[0]?r.throw||((o=r.return)&&o.call(r),0):r.next)&&!(o=o.call(r,a[1])).done)return o;switch(r=0,o&&(a=[2&a[0],o.value]),a[0]){case 0:case 1:o=a;break;case 4:return i.label++,{value:a[1],done:!1};case 5:i.label++,r=a[1],a=[0];continue;case 7:a=i.ops.pop(),i.trys.pop();continue;default:if(!(o=i.trys,(o=o.length>0&&o[o.length-1])||6!==a[0]&&2!==a[0])){i=0;continue}if(3===a[0]&&(!o||a[1]>o[0]&&a[1]<o[3])){i.label=a[1];break}if(6===a[0]&&i.label<o[1]){i.label=o[1],o=a;break}if(o&&i.label<o[2]){i.label=o[2],i.ops.push(a);break}o[2]&&i.ops.pop(),i.trys.pop();continue}a=t.call(e,i)}catch(e){a=[6,e],r=0}finally{n=o=0}if(5&a[0])throw a[1];return{value:a[0]?a[1]:void 0,done:!0}}([a,c])}}}function l(e){var t="function"==typeof Symbol&&Symbol.iterator,n=t&&e[t],r=0;if(n)return n.call(e);if(e&&"number"==typeof e.length)return{next:function(){return e&&r>=e.length&&(e=void 0),{value:e&&e[r++],done:!e}}};throw new TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")}function u(e,t){var n="function"==typeof Symbol&&e[Symbol.iterator];if(!n)return e;var r,o,s=n.call(e),i=[];try{for(;(void 0===t||t-- >0)&&!(r=s.next()).done;)i.push(r.value)}catch(e){o={error:e}}finally{try{r&&!r.done&&(n=s.return)&&n.call(s)}finally{if(o)throw o.error}}return i}function d(e,t,n){if(n||2===arguments.length)for(var r,o=0,s=t.length;o<s;o++)!r&&o in t||(r||(r=Array.prototype.slice.call(t,0,o)),r[o]=t[o]);return e.concat(r||Array.prototype.slice.call(t))}!function(e){e[e.None=0]="None",e[e.Error=1]="Error",e[e.Warn=2]="Warn",e[e.Verbose=3]="Verbose",e[e.Debug=4]="Debug"}(r||(r={})),function(e){e.US="US",e.EU="EU",e.STAGING="STAGING"}(o||(o={})),function(e){e.Unknown="unknown",e.Skipped="skipped",e.Success="success",e.RateLimit="rate_limit",e.PayloadTooLarge="payload_too_large",e.Invalid="invalid",e.Failed="failed",e.Timeout="Timeout",e.SystemError="SystemError"}(s||(s={}));var h="".concat("AMP","_unsent"),p="https://api2.amplitude.com/2/httpapi",f=function(e){return{promise:e||Promise.resolve()}},v="Amplitude Logger ",m=function(){function e(){this.logLevel=r.None}return e.prototype.disable=function(){this.logLevel=r.None},e.prototype.enable=function(e){void 0===e&&(e=r.Warn),this.logLevel=e},e.prototype.log=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];this.logLevel<r.Verbose||console.log("".concat(v,"[Log]: ").concat(e.join(" ")))},e.prototype.warn=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];this.logLevel<r.Warn||console.warn("".concat(v,"[Warn]: ").concat(e.join(" ")))},e.prototype.error=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];this.logLevel<r.Error||console.error("".concat(v,"[Error]: ").concat(e.join(" ")))},e.prototype.debug=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];this.logLevel<r.Debug||console.log("".concat(v,"[Debug]: ").concat(e.join(" ")))},e}(),g=function(){return{flushMaxRetries:12,flushQueueSize:200,flushIntervalMillis:1e4,instanceName:"$default_instance",logLevel:r.Warn,loggerProvider:new m,optOut:!1,serverUrl:p,serverZone:o.US,useBatch:!1}},y=function(){function e(e){var t,n,r,o;this._optOut=!1;var s=g();this.apiKey=e.apiKey,this.flushIntervalMillis=null!==(t=e.flushIntervalMillis)&&void 0!==t?t:s.flushIntervalMillis,this.flushMaxRetries=e.flushMaxRetries||s.flushMaxRetries,this.flushQueueSize=e.flushQueueSize||s.flushQueueSize,this.instanceName=e.instanceName||s.instanceName,this.loggerProvider=e.loggerProvider||s.loggerProvider,this.logLevel=null!==(n=e.logLevel)&&void 0!==n?n:s.logLevel,this.minIdLength=e.minIdLength,this.plan=e.plan,this.ingestionMetadata=e.ingestionMetadata,this.optOut=null!==(r=e.optOut)&&void 0!==r?r:s.optOut,this.serverUrl=e.serverUrl,this.serverZone=e.serverZone||s.serverZone,this.storageProvider=e.storageProvider,this.transportProvider=e.transportProvider,this.useBatch=null!==(o=e.useBatch)&&void 0!==o?o:s.useBatch,this.loggerProvider.enable(this.logLevel);var i=b(e.serverUrl,e.serverZone,e.useBatch);this.serverZone=i.serverZone,this.serverUrl=i.serverUrl}return Object.defineProperty(e.prototype,"optOut",{get:function(){return this._optOut},set:function(e){this._optOut=e},enumerable:!1,configurable:!0}),e}(),S=function(e,t){return e===o.EU?t?"https://api.eu.amplitude.com/batch":"https://api.eu.amplitude.com/2/httpapi":t?"https://api2.amplitude.com/batch":p},b=function(e,t,n){if(void 0===e&&(e=""),void 0===t&&(t=g().serverZone),void 0===n&&(n=g().useBatch),e)return{serverUrl:e,serverZone:void 0};var r=["US","EU"].includes(t)?t:g().serverZone;return{serverZone:r,serverUrl:S(r,n)}},I=function(e){return void 0===e&&(e=0),((new Error).stack||"").split("\n").slice(2+e).map((function(e){return e.trim()}))},w=function(e,t,n,o,s){return void 0===s&&(s=null),function(){for(var i=[],a=0;a<arguments.length;a++)i[a]=arguments[a];var c=n(),l=c.logger,u=c.logLevel;if(u&&u<r.Debug||!u||!l)return e.apply(s,i);var d={type:"invoke public method",name:t,args:i,stacktrace:I(1),time:{start:(new Date).toISOString()},states:{}};o&&d.states&&(d.states.before=o());var h=e.apply(s,i);return h&&h.promise?h.promise.then((function(){o&&d.states&&(d.states.after=o()),d.time&&(d.time.end=(new Date).toISOString()),l.debug(JSON.stringify(d,null,2))})):(o&&d.states&&(d.states.after=o()),d.time&&(d.time.end=(new Date).toISOString()),l.debug(JSON.stringify(d,null,2))),h}},C=function(){function e(){}return e.prototype.send=function(e,t){return Promise.resolve(null)},e.prototype.buildResponse=function(e){var t,n,r,o,i,a,c,l,u,d,h,p,f,v,m,g,y,S,b,I,w,C;if("object"!=typeof e)return null;var k=e.code||0,M=this.buildStatus(k);switch(M){case s.Success:return{status:M,statusCode:k,body:{eventsIngested:null!==(t=e.events_ingested)&&void 0!==t?t:0,payloadSizeBytes:null!==(n=e.payload_size_bytes)&&void 0!==n?n:0,serverUploadTime:null!==(r=e.server_upload_time)&&void 0!==r?r:0}};case s.Invalid:return{status:M,statusCode:k,body:{error:null!==(o=e.error)&&void 0!==o?o:"",missingField:null!==(i=e.missing_field)&&void 0!==i?i:"",eventsWithInvalidFields:null!==(a=e.events_with_invalid_fields)&&void 0!==a?a:{},eventsWithMissingFields:null!==(c=e.events_with_missing_fields)&&void 0!==c?c:{},eventsWithInvalidIdLengths:null!==(l=e.events_with_invalid_id_lengths)&&void 0!==l?l:{},epsThreshold:null!==(u=e.eps_threshold)&&void 0!==u?u:0,exceededDailyQuotaDevices:null!==(d=e.exceeded_daily_quota_devices)&&void 0!==d?d:{},silencedDevices:null!==(h=e.silenced_devices)&&void 0!==h?h:[],silencedEvents:null!==(p=e.silenced_events)&&void 0!==p?p:[],throttledDevices:null!==(f=e.throttled_devices)&&void 0!==f?f:{},throttledEvents:null!==(v=e.throttled_events)&&void 0!==v?v:[]}};case s.PayloadTooLarge:return{status:M,statusCode:k,body:{error:null!==(m=e.error)&&void 0!==m?m:""}};case s.RateLimit:return{status:M,statusCode:k,body:{error:null!==(g=e.error)&&void 0!==g?g:"",epsThreshold:null!==(y=e.eps_threshold)&&void 0!==y?y:0,throttledDevices:null!==(S=e.throttled_devices)&&void 0!==S?S:{},throttledUsers:null!==(b=e.throttled_users)&&void 0!==b?b:{},exceededDailyQuotaDevices:null!==(I=e.exceeded_daily_quota_devices)&&void 0!==I?I:{},exceededDailyQuotaUsers:null!==(w=e.exceeded_daily_quota_users)&&void 0!==w?w:{},throttledEvents:null!==(C=e.throttled_events)&&void 0!==C?C:[]}};case s.Timeout:default:return{status:M,statusCode:k}}},e.prototype.buildStatus=function(e){return e>=200&&e<300?s.Success:429===e?s.RateLimit:413===e?s.PayloadTooLarge:408===e?s.Timeout:e>=400&&e<500?s.Invalid:e>=500?s.Failed:s.Unknown},e}(),k=function(){function e(){}return e.prototype.getApplicationContext=function(){return{versionName:this.versionName,language:M(),platform:"Web",os:void 0,deviceModel:void 0}},e}(),M=function(){return"undefined"!=typeof navigator&&(navigator.languages&&navigator.languages[0]||navigator.language)||""},E=function(){function e(){this.queue=[]}return e.prototype.logEvent=function(e){this.receiver?this.receiver(e):this.queue.length<512&&this.queue.push(e)},e.prototype.setEventReceiver=function(e){this.receiver=e,this.queue.length>0&&(this.queue.forEach((function(t){e(t)})),this.queue=[])},e}(),T=function(){return T=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e},T.apply(this,arguments)},O=function(e,t){var n=typeof e;if(n!==typeof t)return!1;for(var r=0,o=["string","number","boolean","undefined"];r<o.length;r++){if(o[r]===n)return e===t}if(null==e&&null==t)return!0;if(null==e||null==t)return!1;if(e.length!==t.length)return!1;var s=Array.isArray(e),i=Array.isArray(t);if(s!==i)return!1;if(!s||!i){var a=Object.keys(e).sort(),c=Object.keys(t).sort();if(!O(a,c))return!1;var l=!0;return Object.keys(e).forEach((function(n){O(e[n],t[n])||(l=!1)})),l}for(var u=0;u<e.length;u++)if(!O(e[u],t[u]))return!1;return!0};Object.entries||(Object.entries=function(e){for(var t=Object.keys(e),n=t.length,r=new Array(n);n--;)r[n]=[t[n],e[t[n]]];return r});var x,R=function(){function e(){this.identity={userProperties:{}},this.listeners=new Set}return e.prototype.editIdentity=function(){var e=this,t=T({},this.identity.userProperties),n=T(T({},this.identity),{userProperties:t});return{setUserId:function(e){return n.userId=e,this},setDeviceId:function(e){return n.deviceId=e,this},setUserProperties:function(e){return n.userProperties=e,this},setOptOut:function(e){return n.optOut=e,this},updateUserProperties:function(e){for(var t=n.userProperties||{},r=0,o=Object.entries(e);r<o.length;r++){var s=o[r],i=s[0],a=s[1];switch(i){case"$set":for(var c=0,l=Object.entries(a);c<l.length;c++){var u=l[c],d=u[0],h=u[1];t[d]=h}break;case"$unset":for(var p=0,f=Object.keys(a);p<f.length;p++){delete t[d=f[p]]}break;case"$clearAll":t={}}}return n.userProperties=t,this},commit:function(){return e.setIdentity(n),this}}},e.prototype.getIdentity=function(){return T({},this.identity)},e.prototype.setIdentity=function(e){var t=T({},this.identity);this.identity=T({},e),O(t,this.identity)||this.listeners.forEach((function(t){t(e)}))},e.prototype.addIdentityListener=function(e){this.listeners.add(e)},e.prototype.removeIdentityListener=function(e){this.listeners.delete(e)},e}(),N="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof global?global:self,D=function(){function e(){this.identityStore=new R,this.eventBridge=new E,this.applicationContextProvider=new k}return e.getInstance=function(t){return N.analyticsConnectorInstances||(N.analyticsConnectorInstances={}),N.analyticsConnectorInstances[t]||(N.analyticsConnectorInstances[t]=new e),N.analyticsConnectorInstances[t]},e}(),L=function(){return"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof self?self:"undefined"!=typeof global?global:void 0},_=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return n(t,e),t.prototype.send=function(e,t){return a(this,void 0,void 0,(function(){var n,r,o;return c(this,(function(s){switch(s.label){case 0:if("undefined"==typeof fetch)throw new Error("FetchTransport is not supported");return n={headers:{"Content-Type":"application/json",Accept:"*/*"},body:JSON.stringify(t),method:"POST"},[4,fetch(e,n)];case 1:return[4,(r=s.sent()).text()];case 2:o=s.sent();try{return[2,this.buildResponse(JSON.parse(o))]}catch(e){return[2,this.buildResponse({code:r.status})]}return[2]}}))}))},t}(C),P="[Amplitude]",A="".concat(P," Session Replay ID"),F=o.US,q="".concat(P," Session Replay Debug"),U="amp-mask";!function(e){e.GET_SR_PROPS="get-sr-props",e.DEBUG_INFO="debug-info"}(x||(x={}));var j,W=function(){return{flushMaxRetries:2,logLevel:r.Warn,loggerProvider:new m,transportProvider:new _}},B=function(e){function t(t,n){var r,o=this,s=W();return(o=e.call(this,i(i({transportProvider:s.transportProvider},n),{apiKey:t}))||this).flushMaxRetries=void 0!==n.flushMaxRetries&&n.flushMaxRetries<=s.flushMaxRetries?n.flushMaxRetries:s.flushMaxRetries,o.apiKey=t,o.sampleRate=n.sampleRate||0,o.serverZone=n.serverZone||F,o.configEndpointUrl=n.configEndpointUrl,o.shouldInlineStylesheet=n.shouldInlineStylesheet,o.version=n.version,o.performanceConfig=n.performanceConfig,o.storeType=null!==(r=n.storeType)&&void 0!==r?r:"idb",n.privacyConfig&&(o.privacyConfig=n.privacyConfig),n.debugMode&&(o.debugMode=n.debugMode),o}return n(t,e),t}(y);function z(e){const t=null==e?void 0:e.host;return Boolean((null==t?void 0:t.shadowRoot)===e)}function G(e){return"[object ShadowRoot]"===Object.prototype.toString.call(e)}function H(e){try{const n=e.rules||e.cssRules;return n?((t=Array.from(n,V).join("")).includes(" background-clip: text;")&&!t.includes(" -webkit-background-clip: text;")&&(t=t.replace(" background-clip: text;"," -webkit-background-clip: text; background-clip: text;")),t):null}catch(e){return null}var t}function V(e){let t;if(function(e){return"styleSheet"in e}(e))try{t=H(e.styleSheet)||function(e){const{cssText:t}=e;if(t.split('"').length<3)return t;const n=["@import",`url(${JSON.stringify(e.href)})`];return""===e.layerName?n.push("layer"):e.layerName&&n.push(`layer(${e.layerName})`),e.supportsText&&n.push(`supports(${e.supportsText})`),e.media.length&&n.push(e.media.mediaText),n.join(" ")+";"}(e)}catch(e){}else if(function(e){return"selectorText"in e}(e)&&e.selectorText.includes(":"))return function(e){const t=/(\[(?:[\w-]+)[^\\])(:(?:[\w-]+)\])/gm;return e.replace(t,"$1\\$2")}(e.cssText);return t||e.cssText}!function(e){e[e.Document=0]="Document",e[e.DocumentType=1]="DocumentType",e[e.Element=2]="Element",e[e.Text=3]="Text",e[e.CDATA=4]="CDATA",e[e.Comment=5]="Comment"}(j||(j={}));class K{constructor(){this.idNodeMap=new Map,this.nodeMetaMap=new WeakMap}getId(e){var t;if(!e)return-1;const n=null===(t=this.getMeta(e))||void 0===t?void 0:t.id;return null!=n?n:-1}getNode(e){return this.idNodeMap.get(e)||null}getIds(){return Array.from(this.idNodeMap.keys())}getMeta(e){return this.nodeMetaMap.get(e)||null}removeNodeFromMap(e){const t=this.getId(e);this.idNodeMap.delete(t),e.childNodes&&e.childNodes.forEach((e=>this.removeNodeFromMap(e)))}has(e){return this.idNodeMap.has(e)}hasNode(e){return this.nodeMetaMap.has(e)}add(e,t){const n=t.id;this.idNodeMap.set(n,e),this.nodeMetaMap.set(e,t)}replace(e,t){const n=this.getNode(e);if(n){const e=this.nodeMetaMap.get(n);e&&this.nodeMetaMap.set(t,e)}this.idNodeMap.set(e,t)}reset(){this.idNodeMap=new Map,this.nodeMetaMap=new WeakMap}}function $({element:e,maskInputOptions:t,tagName:n,type:r,value:o,maskInputFn:s}){let i=o||"";const a=r&&X(r);return(t[n.toLowerCase()]||a&&t[a])&&(i=s?s(i,e):"*".repeat(i.length)),i}function X(e){return e.toLowerCase()}const J="__rrweb_original__";function Z(e){const t=e.type;return e.hasAttribute("data-rr-is-password")?"password":t?X(t):null}let Q=1;const Y=new RegExp("[^a-z0-9-_:]");function ee(){return Q++}let te,ne;const re=/url\((?:(')([^']*)'|(")(.*?)"|([^)]*))\)/gm,oe=/^(?:[a-z+]+:)?\/\//i,se=/^www\..*/i,ie=/^(data:)([^,]*),(.*)/i;function ae(e,t){return(e||"").replace(re,((e,n,r,o,s,i)=>{const a=r||s||i,c=n||o||"";if(!a)return e;if(oe.test(a)||se.test(a))return`url(${c}${a}${c})`;if(ie.test(a))return`url(${c}${a}${c})`;if("/"===a[0])return`url(${c}${function(e){let t="";return t=e.indexOf("//")>-1?e.split("/").slice(0,3).join("/"):e.split("/")[0],t=t.split("?")[0],t}(t)+a}${c})`;const l=t.split("/"),u=a.split("/");l.pop();for(const e of u)"."!==e&&(".."===e?l.pop():l.push(e));return`url(${c}${l.join("/")}${c})`}))}const ce=/^[^ \t\n\r\u000c]+/,le=/^[, \t\n\r\u000c]+/;function ue(e,t){if(!t||""===t.trim())return t;const n=e.createElement("a");return n.href=t,n.href}function de(e){return Boolean("svg"===e.tagName||e.ownerSVGElement)}function he(){const e=document.createElement("a");return e.href="",e.href}function pe(e,t,n,r){return r?"src"===n||"href"===n&&("use"!==t||"#"!==r[0])||"xlink:href"===n&&"#"!==r[0]?ue(e,r):"background"!==n||"table"!==t&&"td"!==t&&"th"!==t?"srcset"===n?function(e,t){if(""===t.trim())return t;let n=0;function r(e){let r;const o=e.exec(t.substring(n));return o?(r=o[0],n+=r.length,r):""}const o=[];for(;r(le),!(n>=t.length);){let s=r(ce);if(","===s.slice(-1))s=ue(e,s.substring(0,s.length-1)),o.push(s);else{let r="";s=ue(e,s);let i=!1;for(;;){const e=t.charAt(n);if(""===e){o.push((s+r).trim());break}if(i)")"===e&&(i=!1);else{if(","===e){n+=1,o.push((s+r).trim());break}"("===e&&(i=!0)}r+=e,n+=1}}}return o.join(", ")}(e,r):"style"===n?ae(r,he()):"object"===t&&"data"===n?ue(e,r):r:ue(e,r):r}function fe(e,t,n){return("video"===e||"audio"===e)&&"autoplay"===t}function ve(e,t,n){if(!e)return!1;if(e.nodeType!==e.ELEMENT_NODE)return!!n&&ve(e.parentNode,t,n);for(let n=e.classList.length;n--;){const r=e.classList[n];if(t.test(r))return!0}return!!n&&ve(e.parentNode,t,n)}function me(e,t,n){try{const r=e.nodeType===e.ELEMENT_NODE?e:e.parentElement;if(null===r)return!1;if("string"==typeof t){if(r.classList.contains(t))return!0;if(r.closest(`.${t}`))return!0}else if(ve(r,t,!0))return!0;if(n){if(r.matches(n))return!0;if(r.closest(n))return!0}}catch(e){}return!1}function ge(e,t){const{doc:n,mirror:r,blockClass:o,blockSelector:s,maskTextClass:i,maskTextSelector:a,inlineStylesheet:c,maskInputOptions:l={},maskTextFn:u,maskInputFn:d,dataURLOptions:h={},inlineImages:p,recordCanvas:f,keepIframeSrcFn:v,newlyAddedElement:m=!1}=t,g=function(e,t){if(!t.hasNode(e))return;const n=t.getId(e);return 1===n?void 0:n}(n,r);switch(e.nodeType){case e.DOCUMENT_NODE:return"CSS1Compat"!==e.compatMode?{type:j.Document,childNodes:[],compatMode:e.compatMode}:{type:j.Document,childNodes:[]};case e.DOCUMENT_TYPE_NODE:return{type:j.DocumentType,name:e.name,publicId:e.publicId,systemId:e.systemId,rootId:g};case e.ELEMENT_NODE:return function(e,t){const{doc:n,blockClass:r,blockSelector:o,inlineStylesheet:s,maskInputOptions:i={},maskInputFn:a,dataURLOptions:c={},inlineImages:l,recordCanvas:u,keepIframeSrcFn:d,newlyAddedElement:h=!1,rootId:p}=t,f=function(e,t,n){try{if("string"==typeof t){if(e.classList.contains(t))return!0}else for(let n=e.classList.length;n--;){const r=e.classList[n];if(t.test(r))return!0}if(n)return e.matches(n)}catch(e){}return!1}(e,r,o),v=function(e){if(e instanceof HTMLFormElement)return"form";const t=X(e.tagName);return Y.test(t)?"div":t}(e);let m={};const g=e.attributes.length;for(let t=0;t<g;t++){const r=e.attributes[t];fe(v,r.name,r.value)||(m[r.name]=pe(n,v,X(r.name),r.value))}if("link"===v&&s){const t=Array.from(n.styleSheets).find((t=>t.href===e.href));let r=null;t&&(r=H(t)),r&&(delete m.rel,delete m.href,m._cssText=ae(r,t.href))}if("style"===v&&e.sheet&&!(e.innerText||e.textContent||"").trim().length){const t=H(e.sheet);t&&(m._cssText=ae(t,he()))}if("input"===v||"textarea"===v||"select"===v){const t=e.value,n=e.checked;"radio"!==m.type&&"checkbox"!==m.type&&"submit"!==m.type&&"button"!==m.type&&t?m.value=$({element:e,type:Z(e),tagName:v,value:t,maskInputOptions:i,maskInputFn:a}):n&&(m.checked=n)}"option"===v&&(e.selected&&!i.select?m.selected=!0:delete m.selected);if("canvas"===v&&u)if("2d"===e.__context)(function(e){const t=e.getContext("2d");if(!t)return!0;for(let n=0;n<e.width;n+=50)for(let r=0;r<e.height;r+=50){const o=t.getImageData,s=J in o?o[J]:o;if(new Uint32Array(s.call(t,n,r,Math.min(50,e.width-n),Math.min(50,e.height-r)).data.buffer).some((e=>0!==e)))return!1}return!0})(e)||(m.rr_dataURL=e.toDataURL(c.type,c.quality));else if(!("__context"in e)){const t=e.toDataURL(c.type,c.quality),n=document.createElement("canvas");n.width=e.width,n.height=e.height;t!==n.toDataURL(c.type,c.quality)&&(m.rr_dataURL=t)}if("img"===v&&l){te||(te=n.createElement("canvas"),ne=te.getContext("2d"));const t=e,r=t.crossOrigin;t.crossOrigin="anonymous";const o=()=>{t.removeEventListener("load",o);try{te.width=t.naturalWidth,te.height=t.naturalHeight,ne.drawImage(t,0,0),m.rr_dataURL=te.toDataURL(c.type,c.quality)}catch(e){console.warn(`Cannot inline img src=${t.currentSrc}! Error: ${e}`)}r?m.crossOrigin=r:t.removeAttribute("crossorigin")};t.complete&&0!==t.naturalWidth?o():t.addEventListener("load",o)}"audio"!==v&&"video"!==v||(m.rr_mediaState=e.paused?"paused":"played",m.rr_mediaCurrentTime=e.currentTime);h||(e.scrollLeft&&(m.rr_scrollLeft=e.scrollLeft),e.scrollTop&&(m.rr_scrollTop=e.scrollTop));if(f){const{width:t,height:n}=e.getBoundingClientRect();m={class:m.class,rr_width:`${t}px`,rr_height:`${n}px`}}"iframe"!==v||d(m.src)||(e.contentDocument||(m.rr_src=m.src),delete m.src);return{type:j.Element,tagName:v,attributes:m,childNodes:[],isSVG:de(e)||void 0,needBlock:f,rootId:p}}(e,{doc:n,blockClass:o,blockSelector:s,inlineStylesheet:c,maskInputOptions:l,maskInputFn:d,dataURLOptions:h,inlineImages:p,recordCanvas:f,keepIframeSrcFn:v,newlyAddedElement:m,rootId:g});case e.TEXT_NODE:return function(e,t){var n;const{maskTextClass:r,maskTextSelector:o,maskTextFn:s,rootId:i}=t,a=e.parentNode&&e.parentNode.tagName;let c=e.textContent;const l="STYLE"===a||void 0,u="SCRIPT"===a||void 0;if(l&&c){try{e.nextSibling||e.previousSibling||(null===(n=e.parentNode.sheet)||void 0===n?void 0:n.cssRules)&&(c=H(e.parentNode.sheet))}catch(t){console.warn(`Cannot get CSS styles from text's parentNode. Error: ${t}`,e)}c=ae(c,he())}u&&(c="SCRIPT_PLACEHOLDER");!l&&!u&&c&&me(e,r,o)&&(c=s?s(c,e.parentElement):c.replace(/[\S]/g,"*"));return{type:j.Text,textContent:c||"",isStyle:l,rootId:i}}(e,{maskTextClass:i,maskTextSelector:a,maskTextFn:u,rootId:g});case e.CDATA_SECTION_NODE:return{type:j.CDATA,textContent:"",rootId:g};case e.COMMENT_NODE:return{type:j.Comment,textContent:e.textContent||"",rootId:g};default:return!1}}function ye(e){return null==e?"":e.toLowerCase()}function Se(e,t){const{doc:n,mirror:r,blockClass:o,blockSelector:s,maskTextClass:i,maskTextSelector:a,skipChild:c=!1,inlineStylesheet:l=!0,maskInputOptions:u={},maskTextFn:d,maskInputFn:h,slimDOMOptions:p,dataURLOptions:f={},inlineImages:v=!1,recordCanvas:m=!1,onSerialize:g,onIframeLoad:y,iframeLoadTimeout:S=5e3,onStylesheetLoad:b,stylesheetLoadTimeout:I=5e3,keepIframeSrcFn:w=(()=>!1),newlyAddedElement:C=!1}=t;let{preserveWhiteSpace:k=!0}=t;const M=ge(e,{doc:n,mirror:r,blockClass:o,blockSelector:s,maskTextClass:i,maskTextSelector:a,inlineStylesheet:l,maskInputOptions:u,maskTextFn:d,maskInputFn:h,dataURLOptions:f,inlineImages:v,recordCanvas:m,keepIframeSrcFn:w,newlyAddedElement:C});if(!M)return console.warn(e,"not serialized"),null;let E;E=r.hasNode(e)?r.getId(e):!function(e,t){if(t.comment&&e.type===j.Comment)return!0;if(e.type===j.Element){if(t.script&&("script"===e.tagName||"link"===e.tagName&&("preload"===e.attributes.rel||"modulepreload"===e.attributes.rel)&&"script"===e.attributes.as||"link"===e.tagName&&"prefetch"===e.attributes.rel&&"string"==typeof e.attributes.href&&e.attributes.href.endsWith(".js")))return!0;if(t.headFavicon&&("link"===e.tagName&&"shortcut icon"===e.attributes.rel||"meta"===e.tagName&&(ye(e.attributes.name).match(/^msapplication-tile(image|color)$/)||"application-name"===ye(e.attributes.name)||"icon"===ye(e.attributes.rel)||"apple-touch-icon"===ye(e.attributes.rel)||"shortcut icon"===ye(e.attributes.rel))))return!0;if("meta"===e.tagName){if(t.headMetaDescKeywords&&ye(e.attributes.name).match(/^description|keywords$/))return!0;if(t.headMetaSocial&&(ye(e.attributes.property).match(/^(og|twitter|fb):/)||ye(e.attributes.name).match(/^(og|twitter):/)||"pinterest"===ye(e.attributes.name)))return!0;if(t.headMetaRobots&&("robots"===ye(e.attributes.name)||"googlebot"===ye(e.attributes.name)||"bingbot"===ye(e.attributes.name)))return!0;if(t.headMetaHttpEquiv&&void 0!==e.attributes["http-equiv"])return!0;if(t.headMetaAuthorship&&("author"===ye(e.attributes.name)||"generator"===ye(e.attributes.name)||"framework"===ye(e.attributes.name)||"publisher"===ye(e.attributes.name)||"progid"===ye(e.attributes.name)||ye(e.attributes.property).match(/^article:/)||ye(e.attributes.property).match(/^product:/)))return!0;if(t.headMetaVerification&&("google-site-verification"===ye(e.attributes.name)||"yandex-verification"===ye(e.attributes.name)||"csrf-token"===ye(e.attributes.name)||"p:domain_verify"===ye(e.attributes.name)||"verify-v1"===ye(e.attributes.name)||"verification"===ye(e.attributes.name)||"shopify-checkout-api-token"===ye(e.attributes.name)))return!0}}return!1}(M,p)&&(k||M.type!==j.Text||M.isStyle||M.textContent.replace(/^\s+|\s+$/gm,"").length)?ee():-2;const T=Object.assign(M,{id:E});if(r.add(e,T),-2===E)return null;g&&g(e);let O=!c;if(T.type===j.Element){O=O&&!T.needBlock,delete T.needBlock;const t=e.shadowRoot;t&&G(t)&&(T.isShadowHost=!0)}if((T.type===j.Document||T.type===j.Element)&&O){p.headWhitespace&&T.type===j.Element&&"head"===T.tagName&&(k=!1);const t={doc:n,mirror:r,blockClass:o,blockSelector:s,maskTextClass:i,maskTextSelector:a,skipChild:c,inlineStylesheet:l,maskInputOptions:u,maskTextFn:d,maskInputFn:h,slimDOMOptions:p,dataURLOptions:f,inlineImages:v,recordCanvas:m,preserveWhiteSpace:k,onSerialize:g,onIframeLoad:y,iframeLoadTimeout:S,onStylesheetLoad:b,stylesheetLoadTimeout:I,keepIframeSrcFn:w};if(T.type===j.Element&&"textarea"===T.tagName&&void 0!==T.attributes.value);else for(const n of Array.from(e.childNodes)){const e=Se(n,t);e&&T.childNodes.push(e)}if(function(e){return e.nodeType===e.ELEMENT_NODE}(e)&&e.shadowRoot)for(const n of Array.from(e.shadowRoot.childNodes)){const r=Se(n,t);r&&(G(e.shadowRoot)&&(r.isShadow=!0),T.childNodes.push(r))}}return e.parentNode&&z(e.parentNode)&&G(e.parentNode)&&(T.isShadow=!0),T.type===j.Element&&"iframe"===T.tagName&&function(e,t,n){const r=e.contentWindow;if(!r)return;let o,s=!1;try{o=r.document.readyState}catch(e){return}if("complete"!==o){const r=setTimeout((()=>{s||(t(),s=!0)}),n);return void e.addEventListener("load",(()=>{clearTimeout(r),s=!0,t()}))}const i="about:blank";if(r.location.href!==i||e.src===i||""===e.src)return setTimeout(t,0),e.addEventListener("load",t);e.addEventListener("load",t)}(e,(()=>{const t=e.contentDocument;if(t&&y){const n=Se(t,{doc:t,mirror:r,blockClass:o,blockSelector:s,maskTextClass:i,maskTextSelector:a,skipChild:!1,inlineStylesheet:l,maskInputOptions:u,maskTextFn:d,maskInputFn:h,slimDOMOptions:p,dataURLOptions:f,inlineImages:v,recordCanvas:m,preserveWhiteSpace:k,onSerialize:g,onIframeLoad:y,iframeLoadTimeout:S,onStylesheetLoad:b,stylesheetLoadTimeout:I,keepIframeSrcFn:w});n&&y(e,n)}}),S),T.type===j.Element&&"link"===T.tagName&&"stylesheet"===T.attributes.rel&&function(e,t,n){let r,o=!1;try{r=e.sheet}catch(e){return}if(r)return;const s=setTimeout((()=>{o||(t(),o=!0)}),n);e.addEventListener("load",(()=>{clearTimeout(s),o=!0,t()}))}(e,(()=>{if(b){const t=Se(e,{doc:n,mirror:r,blockClass:o,blockSelector:s,maskTextClass:i,maskTextSelector:a,skipChild:!1,inlineStylesheet:l,maskInputOptions:u,maskTextFn:d,maskInputFn:h,slimDOMOptions:p,dataURLOptions:f,inlineImages:v,recordCanvas:m,preserveWhiteSpace:k,onSerialize:g,onIframeLoad:y,iframeLoadTimeout:S,onStylesheetLoad:b,stylesheetLoadTimeout:I,keepIframeSrcFn:w});t&&b(e,t)}}),I),T}var be=(e=>(e[e.DomContentLoaded=0]="DomContentLoaded",e[e.Load=1]="Load",e[e.FullSnapshot=2]="FullSnapshot",e[e.IncrementalSnapshot=3]="IncrementalSnapshot",e[e.Meta=4]="Meta",e[e.Custom=5]="Custom",e[e.Plugin=6]="Plugin",e))(be||{}),Ie=(e=>(e[e.Mutation=0]="Mutation",e[e.MouseMove=1]="MouseMove",e[e.MouseInteraction=2]="MouseInteraction",e[e.Scroll=3]="Scroll",e[e.ViewportResize=4]="ViewportResize",e[e.Input=5]="Input",e[e.TouchMove=6]="TouchMove",e[e.MediaInteraction=7]="MediaInteraction",e[e.StyleSheetRule=8]="StyleSheetRule",e[e.CanvasMutation=9]="CanvasMutation",e[e.Font=10]="Font",e[e.Log=11]="Log",e[e.Drag=12]="Drag",e[e.StyleDeclaration=13]="StyleDeclaration",e[e.Selection=14]="Selection",e[e.AdoptedStyleSheet=15]="AdoptedStyleSheet",e))(Ie||{}),we=(e=>(e[e.MouseUp=0]="MouseUp",e[e.MouseDown=1]="MouseDown",e[e.Click=2]="Click",e[e.ContextMenu=3]="ContextMenu",e[e.DblClick=4]="DblClick",e[e.Focus=5]="Focus",e[e.Blur=6]="Blur",e[e.TouchStart=7]="TouchStart",e[e.TouchMove_Departed=8]="TouchMove_Departed",e[e.TouchEnd=9]="TouchEnd",e[e.TouchCancel=10]="TouchCancel",e))(we||{}),Ce=(e=>(e[e.Mouse=0]="Mouse",e[e.Pen=1]="Pen",e[e.Touch=2]="Touch",e))(Ce||{}),ke=(e=>(e[e["2D"]=0]="2D",e[e.WebGL=1]="WebGL",e[e.WebGL2=2]="WebGL2",e))(ke||{});function Me(e,t,n=document){const r={capture:!0,passive:!0};return n.addEventListener(e,t,r),()=>n.removeEventListener(e,t,r)}const Ee="Please stop import mirror directly. Instead of that,\r\nnow you can use replayer.getMirror() to access the mirror instance of a replayer,\r\nor you can use record.mirror to access the mirror instance during recording.";let Te={map:{},getId:()=>(console.error(Ee),-1),getNode:()=>(console.error(Ee),null),removeNodeFromMap(){console.error(Ee)},has:()=>(console.error(Ee),!1),reset(){console.error(Ee)}};function Oe(e,t,n={}){let r=null,o=0;return function(...s){const i=Date.now();o||!1!==n.leading||(o=i);const a=t-(i-o),c=this;a<=0||a>t?(r&&(clearTimeout(r),r=null),o=i,e.apply(c,s)):r||!1===n.trailing||(r=setTimeout((()=>{o=!1===n.leading?0:Date.now(),r=null,e.apply(c,s)}),a))}}function xe(e,t,n,r,o=window){const s=o.Object.getOwnPropertyDescriptor(e,t);return o.Object.defineProperty(e,t,r?n:{set(e){setTimeout((()=>{n.set.call(this,e)}),0),s&&s.set&&s.set.call(this,e)}}),()=>xe(e,t,s||{},!0)}function Re(e,t,n){try{if(!(t in e))return()=>{};const r=e[t],o=n(r);return"function"==typeof o&&(o.prototype=o.prototype||{},Object.defineProperties(o,{__rrweb_original__:{enumerable:!1,value:r}})),e[t]=o,()=>{e[t]=r}}catch(e){return()=>{}}}"undefined"!=typeof window&&window.Proxy&&window.Reflect&&(Te=new Proxy(Te,{get:(e,t,n)=>("map"===t&&console.error(Ee),Reflect.get(e,t,n))}));let Ne,De=Date.now;function Le(e){var t,n,r,o,s,i;const a=e.document;return{left:a.scrollingElement?a.scrollingElement.scrollLeft:void 0!==e.pageXOffset?e.pageXOffset:(null==a?void 0:a.documentElement.scrollLeft)||(null===(n=null===(t=null==a?void 0:a.body)||void 0===t?void 0:t.parentElement)||void 0===n?void 0:n.scrollLeft)||(null===(r=null==a?void 0:a.body)||void 0===r?void 0:r.scrollLeft)||0,top:a.scrollingElement?a.scrollingElement.scrollTop:void 0!==e.pageYOffset?e.pageYOffset:(null==a?void 0:a.documentElement.scrollTop)||(null===(s=null===(o=null==a?void 0:a.body)||void 0===o?void 0:o.parentElement)||void 0===s?void 0:s.scrollTop)||(null===(i=null==a?void 0:a.body)||void 0===i?void 0:i.scrollTop)||0}}function _e(){return window.innerHeight||document.documentElement&&document.documentElement.clientHeight||document.body&&document.body.clientHeight}function Pe(){return window.innerWidth||document.documentElement&&document.documentElement.clientWidth||document.body&&document.body.clientWidth}function Ae(e){if(!e)return null;return e.nodeType===e.ELEMENT_NODE?e:e.parentElement}function Fe(e,t,n,r){if(!e)return!1;const o=Ae(e);if(!o)return!1;try{if("string"==typeof t){if(o.classList.contains(t))return!0;if(r&&null!==o.closest("."+t))return!0}else if(ve(o,t,r))return!0}catch(e){}if(n){if(o.matches(n))return!0;if(r&&null!==o.closest(n))return!0}return!1}function qe(e,t){return-2===t.getId(e)}function Ue(e,t){if(z(e))return!1;const n=t.getId(e);return!t.has(n)||(!e.parentNode||e.parentNode.nodeType!==e.DOCUMENT_NODE)&&(!e.parentNode||Ue(e.parentNode,t))}function je(e){return Boolean(e.changedTouches)}function We(e,t){return Boolean("IFRAME"===e.nodeName&&t.getMeta(e))}function Be(e,t){return Boolean("LINK"===e.nodeName&&e.nodeType===e.ELEMENT_NODE&&e.getAttribute&&"stylesheet"===e.getAttribute("rel")&&t.getMeta(e))}function ze(e){return Boolean(null==e?void 0:e.shadowRoot)}/[1-9][0-9]{12}/.test(Date.now().toString())||(De=()=>(new Date).getTime());class Ge{constructor(){this.id=1,this.styleIDMap=new WeakMap,this.idStyleMap=new Map}getId(e){var t;return null!==(t=this.styleIDMap.get(e))&&void 0!==t?t:-1}has(e){return this.styleIDMap.has(e)}add(e,t){if(this.has(e))return this.getId(e);let n;return n=void 0===t?this.id++:t,this.styleIDMap.set(e,n),this.idStyleMap.set(n,e),n}getStyle(e){return this.idStyleMap.get(e)||null}reset(){this.styleIDMap=new WeakMap,this.idStyleMap=new Map,this.id=1}generateId(){return this.id++}}function He(e){var t,n;let r=null;return(null===(n=null===(t=e.getRootNode)||void 0===t?void 0:t.call(e))||void 0===n?void 0:n.nodeType)===Node.DOCUMENT_FRAGMENT_NODE&&e.getRootNode().host&&(r=e.getRootNode().host),r}function Ve(e){const t=e.ownerDocument;if(!t)return!1;const n=function(e){let t,n=e;for(;t=He(n);)n=t;return n}(e);return t.contains(n)}function Ke(e){const t=e.ownerDocument;return!!t&&(t.contains(e)||Ve(e))}function $e(e){Ne=e}function Xe(){Ne=void 0}const Je=e=>{if(!Ne)return e;return(...t)=>{try{return e(...t)}catch(e){if(Ne&&!0===Ne(e))return;throw e}}};function Ze(e){return(...t)=>{try{return e(...t)}catch(e){try{e._external_=!0}catch(e){}throw e}}}class Qe{constructor(e){this.generateIdFn=e,this.iframeIdToRemoteIdMap=new WeakMap,this.iframeRemoteIdToIdMap=new WeakMap}getId(e,t,n,r){const o=n||this.getIdToRemoteIdMap(e),s=r||this.getRemoteIdToIdMap(e);let i=o.get(t);return i||(i=this.generateIdFn(),o.set(t,i),s.set(i,t)),i}getIds(e,t){const n=this.getIdToRemoteIdMap(e),r=this.getRemoteIdToIdMap(e);return t.map((t=>this.getId(e,t,n,r)))}getRemoteId(e,t,n){const r=n||this.getRemoteIdToIdMap(e);if("number"!=typeof t)return t;const o=r.get(t);return o||-1}getRemoteIds(e,t){const n=this.getRemoteIdToIdMap(e);return t.map((t=>this.getRemoteId(e,t,n)))}reset(e){if(!e)return this.iframeIdToRemoteIdMap=new WeakMap,void(this.iframeRemoteIdToIdMap=new WeakMap);this.iframeIdToRemoteIdMap.delete(e),this.iframeRemoteIdToIdMap.delete(e)}getIdToRemoteIdMap(e){let t=this.iframeIdToRemoteIdMap.get(e);return t||(t=new Map,this.iframeIdToRemoteIdMap.set(e,t)),t}getRemoteIdToIdMap(e){let t=this.iframeRemoteIdToIdMap.get(e);return t||(t=new Map,this.iframeRemoteIdToIdMap.set(e,t)),t}}class Ye{constructor(e){this.iframes=new WeakMap,this.crossOriginIframeMap=new WeakMap,this.crossOriginIframeMirror=new Qe(ee),this.crossOriginIframeRootIdMap=new WeakMap,this.mutationCb=e.mutationCb,this.wrappedEmit=e.wrappedEmit,this.stylesheetManager=e.stylesheetManager,this.recordCrossOriginIframes=e.recordCrossOriginIframes,this.crossOriginIframeStyleMirror=new Qe(this.stylesheetManager.styleMirror.generateId.bind(this.stylesheetManager.styleMirror)),this.mirror=e.mirror,this.recordCrossOriginIframes&&window.addEventListener("message",this.handleMessage.bind(this))}addIframe(e){this.iframes.set(e,!0),e.contentWindow&&this.crossOriginIframeMap.set(e.contentWindow,e)}addLoadListener(e){this.loadListener=e}attachIframe(e,t){var n;this.mutationCb({adds:[{parentId:this.mirror.getId(e),nextId:null,node:t}],removes:[],texts:[],attributes:[],isAttachIframe:!0}),null===(n=this.loadListener)||void 0===n||n.call(this,e),e.contentDocument&&e.contentDocument.adoptedStyleSheets&&e.contentDocument.adoptedStyleSheets.length>0&&this.stylesheetManager.adoptStyleSheets(e.contentDocument.adoptedStyleSheets,this.mirror.getId(e.contentDocument))}handleMessage(e){const t=e;if("rrweb"!==t.data.type||t.origin!==t.data.origin)return;if(!e.source)return;const n=this.crossOriginIframeMap.get(e.source);if(!n)return;const r=this.transformCrossOriginEvent(n,t.data.event);r&&this.wrappedEmit(r,t.data.isCheckout)}transformCrossOriginEvent(e,t){var n;switch(t.type){case be.FullSnapshot:{this.crossOriginIframeMirror.reset(e),this.crossOriginIframeStyleMirror.reset(e),this.replaceIdOnNode(t.data.node,e);const n=t.data.node.id;return this.crossOriginIframeRootIdMap.set(e,n),this.patchRootIdOnNode(t.data.node,n),{timestamp:t.timestamp,type:be.IncrementalSnapshot,data:{source:Ie.Mutation,adds:[{parentId:this.mirror.getId(e),nextId:null,node:t.data.node}],removes:[],texts:[],attributes:[],isAttachIframe:!0}}}case be.Meta:case be.Load:case be.DomContentLoaded:return!1;case be.Plugin:return t;case be.Custom:return this.replaceIds(t.data.payload,e,["id","parentId","previousId","nextId"]),t;case be.IncrementalSnapshot:switch(t.data.source){case Ie.Mutation:return t.data.adds.forEach((t=>{this.replaceIds(t,e,["parentId","nextId","previousId"]),this.replaceIdOnNode(t.node,e);const n=this.crossOriginIframeRootIdMap.get(e);n&&this.patchRootIdOnNode(t.node,n)})),t.data.removes.forEach((t=>{this.replaceIds(t,e,["parentId","id"])})),t.data.attributes.forEach((t=>{this.replaceIds(t,e,["id"])})),t.data.texts.forEach((t=>{this.replaceIds(t,e,["id"])})),t;case Ie.Drag:case Ie.TouchMove:case Ie.MouseMove:return t.data.positions.forEach((t=>{this.replaceIds(t,e,["id"])})),t;case Ie.ViewportResize:return!1;case Ie.MediaInteraction:case Ie.MouseInteraction:case Ie.Scroll:case Ie.CanvasMutation:case Ie.Input:return this.replaceIds(t.data,e,["id"]),t;case Ie.StyleSheetRule:case Ie.StyleDeclaration:return this.replaceIds(t.data,e,["id"]),this.replaceStyleIds(t.data,e,["styleId"]),t;case Ie.Font:return t;case Ie.Selection:return t.data.ranges.forEach((t=>{this.replaceIds(t,e,["start","end"])})),t;case Ie.AdoptedStyleSheet:return this.replaceIds(t.data,e,["id"]),this.replaceStyleIds(t.data,e,["styleIds"]),null===(n=t.data.styles)||void 0===n||n.forEach((t=>{this.replaceStyleIds(t,e,["styleId"])})),t}}}replace(e,t,n,r){for(const o of r)(Array.isArray(t[o])||"number"==typeof t[o])&&(Array.isArray(t[o])?t[o]=e.getIds(n,t[o]):t[o]=e.getId(n,t[o]));return t}replaceIds(e,t,n){return this.replace(this.crossOriginIframeMirror,e,t,n)}replaceStyleIds(e,t,n){return this.replace(this.crossOriginIframeStyleMirror,e,t,n)}replaceIdOnNode(e,t){this.replaceIds(e,t,["id","rootId"]),"childNodes"in e&&e.childNodes.forEach((e=>{this.replaceIdOnNode(e,t)}))}patchRootIdOnNode(e,t){e.type===j.Document||e.rootId||(e.rootId=t),"childNodes"in e&&e.childNodes.forEach((e=>{this.patchRootIdOnNode(e,t)}))}}function et(e){return"__ln"in e}class tt{constructor(){this.length=0,this.head=null,this.tail=null}get(e){if(e>=this.length)throw new Error("Position outside of list range");let t=this.head;for(let n=0;n<e;n++)t=(null==t?void 0:t.next)||null;return t}addNode(e){const t={value:e,previous:null,next:null};if(e.__ln=t,e.previousSibling&&et(e.previousSibling)){const n=e.previousSibling.__ln.next;t.next=n,t.previous=e.previousSibling.__ln,e.previousSibling.__ln.next=t,n&&(n.previous=t)}else if(e.nextSibling&&et(e.nextSibling)&&e.nextSibling.__ln.previous){const n=e.nextSibling.__ln.previous;t.previous=n,t.next=e.nextSibling.__ln,e.nextSibling.__ln.previous=t,n&&(n.next=t)}else this.head&&(this.head.previous=t),t.next=this.head,this.head=t;null===t.next&&(this.tail=t),this.length++}removeNode(e){const t=e.__ln;this.head&&(t.previous?(t.previous.next=t.next,t.next?t.next.previous=t.previous:this.tail=t.previous):(this.head=t.next,this.head?this.head.previous=null:this.tail=null),e.__ln&&delete e.__ln,this.length--)}}const nt=(e,t)=>`${e}@${t}`;class rt{constructor(){this.frozen=!1,this.locked=!1,this.texts=[],this.attributes=[],this.attributeMap=new WeakMap,this.removes=[],this.mapRemoves=[],this.movedMap={},this.addedSet=new Set,this.movedSet=new Set,this.droppedSet=new Set,this.processMutations=e=>{e.forEach(this.processMutation),this.emit()},this.emit=()=>{if(this.frozen||this.locked)return;const e=[],t=new Set,n=new tt,r=e=>{let t=e,n=-2;for(;-2===n;)t=t&&t.nextSibling,n=t&&this.mirror.getId(t);return n},o=o=>{if(!o.parentNode||!Ke(o)||"TEXTAREA"===o.parentNode.tagName)return;const s=z(o.parentNode)?this.mirror.getId(He(o)):this.mirror.getId(o.parentNode),i=r(o);if(-1===s||-1===i)return n.addNode(o);const a=Se(o,{doc:this.doc,mirror:this.mirror,blockClass:this.blockClass,blockSelector:this.blockSelector,maskTextClass:this.maskTextClass,maskTextSelector:this.maskTextSelector,skipChild:!0,newlyAddedElement:!0,inlineStylesheet:this.inlineStylesheet,maskInputOptions:this.maskInputOptions,maskTextFn:this.maskTextFn,maskInputFn:this.maskInputFn,slimDOMOptions:this.slimDOMOptions,dataURLOptions:this.dataURLOptions,recordCanvas:this.recordCanvas,inlineImages:this.inlineImages,onSerialize:e=>{We(e,this.mirror)&&this.iframeManager.addIframe(e),Be(e,this.mirror)&&this.stylesheetManager.trackLinkElement(e),ze(o)&&this.shadowDomManager.addShadowRoot(o.shadowRoot,this.doc)},onIframeLoad:(e,t)=>{this.iframeManager.attachIframe(e,t),this.shadowDomManager.observeAttachShadow(e)},onStylesheetLoad:(e,t)=>{this.stylesheetManager.attachLinkElement(e,t)}});a&&(e.push({parentId:s,nextId:i,node:a}),t.add(a.id))};for(;this.mapRemoves.length;)this.mirror.removeNodeFromMap(this.mapRemoves.shift());for(const e of this.movedSet)st(this.removes,e,this.mirror)&&!this.movedSet.has(e.parentNode)||o(e);for(const e of this.addedSet)at(this.droppedSet,e)||st(this.removes,e,this.mirror)?at(this.movedSet,e)?o(e):this.droppedSet.add(e):o(e);let s=null;for(;n.length;){let e=null;if(s){const t=this.mirror.getId(s.value.parentNode),n=r(s.value);-1!==t&&-1!==n&&(e=s)}if(!e){let t=n.tail;for(;t;){const n=t;if(t=t.previous,n){const t=this.mirror.getId(n.value.parentNode);if(-1===r(n.value))continue;if(-1!==t){e=n;break}{const t=n.value;if(t.parentNode&&t.parentNode.nodeType===Node.DOCUMENT_FRAGMENT_NODE){const r=t.parentNode.host;if(-1!==this.mirror.getId(r)){e=n;break}}}}}}if(!e){for(;n.head;)n.removeNode(n.head.value);break}s=e.previous,n.removeNode(e.value),o(e.value)}const i={texts:this.texts.map((e=>{const t=e.node;return t.parentNode&&"TEXTAREA"===t.parentNode.tagName&&this.genTextAreaValueMutation(t.parentNode),{id:this.mirror.getId(t),value:e.value}})).filter((e=>!t.has(e.id))).filter((e=>this.mirror.has(e.id))),attributes:this.attributes.map((e=>{const{attributes:t}=e;if("string"==typeof t.style){const n=JSON.stringify(e.styleDiff),r=JSON.stringify(e._unchangedStyles);n.length<t.style.length&&(n+r).split("var(").length===t.style.split("var(").length&&(t.style=e.styleDiff)}return{id:this.mirror.getId(e.node),attributes:t}})).filter((e=>!t.has(e.id))).filter((e=>this.mirror.has(e.id))),removes:this.removes,adds:e};(i.texts.length||i.attributes.length||i.removes.length||i.adds.length)&&(this.texts=[],this.attributes=[],this.attributeMap=new WeakMap,this.removes=[],this.addedSet=new Set,this.movedSet=new Set,this.droppedSet=new Set,this.movedMap={},this.mutationCb(i))},this.genTextAreaValueMutation=e=>{let t=this.attributeMap.get(e);t||(t={node:e,attributes:{},styleDiff:{},_unchangedStyles:{}},this.attributes.push(t),this.attributeMap.set(e,t)),t.attributes.value=Array.from(e.childNodes,(e=>e.textContent||"")).join("")},this.processMutation=e=>{if(!qe(e.target,this.mirror))switch(e.type){case"characterData":{const t=e.target.textContent;Fe(e.target,this.blockClass,this.blockSelector,!1)||t===e.oldValue||this.texts.push({value:me(e.target,this.maskTextClass,this.maskTextSelector)&&t?this.maskTextFn?this.maskTextFn(t,Ae(e.target)):t.replace(/[\S]/g,"*"):t,node:e.target});break}case"attributes":{const t=e.target;let n=e.attributeName,r=e.target.getAttribute(n);if("value"===n){const e=Z(t);r=$({element:t,maskInputOptions:this.maskInputOptions,tagName:t.tagName,type:e,value:r,maskInputFn:this.maskInputFn})}if(Fe(e.target,this.blockClass,this.blockSelector,!1)||r===e.oldValue)return;let o=this.attributeMap.get(e.target);if("IFRAME"===t.tagName&&"src"===n&&!this.keepIframeSrcFn(r)){if(t.contentDocument)return;n="rr_src"}if(o||(o={node:e.target,attributes:{},styleDiff:{},_unchangedStyles:{}},this.attributes.push(o),this.attributeMap.set(e.target,o)),"type"===n&&"INPUT"===t.tagName&&"password"===(e.oldValue||"").toLowerCase()&&t.setAttribute("data-rr-is-password","true"),!fe(t.tagName,n)&&(o.attributes[n]=pe(this.doc,X(t.tagName),X(n),r),"style"===n)){let n;try{n=document.implementation.createHTMLDocument()}catch(e){n=this.doc}const r=n.createElement("span");e.oldValue&&r.setAttribute("style",e.oldValue);for(const e of Array.from(t.style)){const n=t.style.getPropertyValue(e),s=t.style.getPropertyPriority(e);n!==r.style.getPropertyValue(e)||s!==r.style.getPropertyPriority(e)?o.styleDiff[e]=""===s?n:[n,s]:o._unchangedStyles[e]=[n,s]}for(const e of Array.from(r.style))""===t.style.getPropertyValue(e)&&(o.styleDiff[e]=!1)}break}case"childList":if(Fe(e.target,this.blockClass,this.blockSelector,!0))return;if("TEXTAREA"===e.target.tagName)return void this.genTextAreaValueMutation(e.target);e.addedNodes.forEach((t=>this.genAdds(t,e.target))),e.removedNodes.forEach((t=>{const n=this.mirror.getId(t),r=z(e.target)?this.mirror.getId(e.target.host):this.mirror.getId(e.target);Fe(e.target,this.blockClass,this.blockSelector,!1)||qe(t,this.mirror)||!function(e,t){return-1!==t.getId(e)}(t,this.mirror)||(this.addedSet.has(t)?(ot(this.addedSet,t),this.droppedSet.add(t)):this.addedSet.has(e.target)&&-1===n||Ue(e.target,this.mirror)||(this.movedSet.has(t)&&this.movedMap[nt(n,r)]?ot(this.movedSet,t):this.removes.push({parentId:r,id:n,isShadow:!(!z(e.target)||!G(e.target))||void 0})),this.mapRemoves.push(t))}))}},this.genAdds=(e,t)=>{if(!this.processedNodeManager.inOtherBuffer(e,this)&&!this.addedSet.has(e)&&!this.movedSet.has(e)){if(this.mirror.hasNode(e)){if(qe(e,this.mirror))return;this.movedSet.add(e);let n=null;t&&this.mirror.hasNode(t)&&(n=this.mirror.getId(t)),n&&-1!==n&&(this.movedMap[nt(this.mirror.getId(e),n)]=!0)}else this.addedSet.add(e),this.droppedSet.delete(e);Fe(e,this.blockClass,this.blockSelector,!1)||(e.childNodes.forEach((e=>this.genAdds(e))),ze(e)&&e.shadowRoot.childNodes.forEach((t=>{this.processedNodeManager.add(t,this),this.genAdds(t,e)})))}}}init(e){["mutationCb","blockClass","blockSelector","maskTextClass","maskTextSelector","inlineStylesheet","maskInputOptions","maskTextFn","maskInputFn","keepIframeSrcFn","recordCanvas","inlineImages","slimDOMOptions","dataURLOptions","doc","mirror","iframeManager","stylesheetManager","shadowDomManager","canvasManager","processedNodeManager"].forEach((t=>{this[t]=e[t]}))}freeze(){this.frozen=!0,this.canvasManager.freeze()}unfreeze(){this.frozen=!1,this.canvasManager.unfreeze(),this.emit()}isFrozen(){return this.frozen}lock(){this.locked=!0,this.canvasManager.lock()}unlock(){this.locked=!1,this.canvasManager.unlock(),this.emit()}reset(){this.shadowDomManager.reset(),this.canvasManager.reset()}}function ot(e,t){e.delete(t),t.childNodes.forEach((t=>ot(e,t)))}function st(e,t,n){return 0!==e.length&&it(e,t,n)}function it(e,t,n){const{parentNode:r}=t;if(!r)return!1;const o=n.getId(r);return!!e.some((e=>e.id===o))||it(e,r,n)}function at(e,t){return 0!==e.size&&ct(e,t)}function ct(e,t){const{parentNode:n}=t;return!!n&&(!!e.has(n)||ct(e,n))}const lt=[];function ut(e){try{if("composedPath"in e){const t=e.composedPath();if(t.length)return t[0]}else if("path"in e&&e.path.length)return e.path[0]}catch(e){}return e&&e.target}function dt(e,t){var n,r;const o=new rt;lt.push(o),o.init(e);let s=window.MutationObserver||window.__rrMutationObserver;const i=null===(r=null===(n=null===window||void 0===window?void 0:window.Zone)||void 0===n?void 0:n.__symbol__)||void 0===r?void 0:r.call(n,"MutationObserver");i&&window[i]&&(s=window[i]);const a=new s(Je(o.processMutations.bind(o)));return a.observe(t,{attributes:!0,attributeOldValue:!0,characterData:!0,characterDataOldValue:!0,childList:!0,subtree:!0}),a}function ht({mouseInteractionCb:e,doc:t,mirror:n,blockClass:r,blockSelector:o,sampling:s}){if(!1===s.mouseInteraction)return()=>{};const i=!0===s.mouseInteraction||void 0===s.mouseInteraction?{}:s.mouseInteraction,a=[];let c=null;return Object.keys(we).filter((e=>Number.isNaN(Number(e))&&!e.endsWith("_Departed")&&!1!==i[e])).forEach((s=>{let i=X(s);const l=(t=>s=>{const i=ut(s);if(Fe(i,r,o,!0))return;let a=null,l=t;if("pointerType"in s){switch(s.pointerType){case"mouse":a=Ce.Mouse;break;case"touch":a=Ce.Touch;break;case"pen":a=Ce.Pen}a===Ce.Touch?we[t]===we.MouseDown?l="TouchStart":we[t]===we.MouseUp&&(l="TouchEnd"):Ce.Pen}else je(s)&&(a=Ce.Touch);null!==a?(c=a,(l.startsWith("Touch")&&a===Ce.Touch||l.startsWith("Mouse")&&a===Ce.Mouse)&&(a=null)):we[t]===we.Click&&(a=c,c=null);const u=je(s)?s.changedTouches[0]:s;if(!u)return;const d=n.getId(i),{clientX:h,clientY:p}=u;Je(e)(Object.assign({type:we[l],id:d,x:h,y:p},null!==a&&{pointerType:a}))})(s);if(window.PointerEvent)switch(we[s]){case we.MouseDown:case we.MouseUp:i=i.replace("mouse","pointer");break;case we.TouchStart:case we.TouchEnd:return}a.push(Me(i,l,t))})),Je((()=>{a.forEach((e=>e()))}))}function pt({scrollCb:e,doc:t,mirror:n,blockClass:r,blockSelector:o,sampling:s}){const i=Je(Oe(Je((s=>{const i=ut(s);if(!i||Fe(i,r,o,!0))return;const a=n.getId(i);if(i===t&&t.defaultView){const n=Le(t.defaultView);e({id:a,x:n.left,y:n.top})}else e({id:a,x:i.scrollLeft,y:i.scrollTop})})),s.scroll||100));return Me("scroll",i,t)}function ft(e,t){const n=Object.assign({},e);return t||delete n.userTriggered,n}const vt=["INPUT","TEXTAREA","SELECT"],mt=new WeakMap;function gt(e){return function(e,t){if(It("CSSGroupingRule")&&e.parentRule instanceof CSSGroupingRule||It("CSSMediaRule")&&e.parentRule instanceof CSSMediaRule||It("CSSSupportsRule")&&e.parentRule instanceof CSSSupportsRule||It("CSSConditionRule")&&e.parentRule instanceof CSSConditionRule){const n=Array.from(e.parentRule.cssRules).indexOf(e);t.unshift(n)}else if(e.parentStyleSheet){const n=Array.from(e.parentStyleSheet.cssRules).indexOf(e);t.unshift(n)}return t}(e,[])}function yt(e,t,n){let r,o;return e?(e.ownerNode?r=t.getId(e.ownerNode):o=n.getId(e),{styleId:o,id:r}):{}}function St({mirror:e,stylesheetManager:t},n){var r,o,s;let i=null;i="#document"===n.nodeName?e.getId(n):e.getId(n.host);const a="#document"===n.nodeName?null===(r=n.defaultView)||void 0===r?void 0:r.Document:null===(s=null===(o=n.ownerDocument)||void 0===o?void 0:o.defaultView)||void 0===s?void 0:s.ShadowRoot,c=(null==a?void 0:a.prototype)?Object.getOwnPropertyDescriptor(null==a?void 0:a.prototype,"adoptedStyleSheets"):void 0;return null!==i&&-1!==i&&a&&c?(Object.defineProperty(n,"adoptedStyleSheets",{configurable:c.configurable,enumerable:c.enumerable,get(){var e;return null===(e=c.get)||void 0===e?void 0:e.call(this)},set(e){var n;const r=null===(n=c.set)||void 0===n?void 0:n.call(this,e);if(null!==i&&-1!==i)try{t.adoptStyleSheets(e,i)}catch(e){}return r}}),Je((()=>{Object.defineProperty(n,"adoptedStyleSheets",{configurable:c.configurable,enumerable:c.enumerable,get:c.get,set:c.set})}))):()=>{}}function bt(e,t={}){const n=e.doc.defaultView;if(!n)return()=>{};let r;!function(e,t){const{mutationCb:n,mousemoveCb:r,mouseInteractionCb:o,scrollCb:s,viewportResizeCb:i,inputCb:a,mediaInteractionCb:c,styleSheetRuleCb:l,styleDeclarationCb:u,canvasMutationCb:d,fontCb:h,selectionCb:p}=e;e.mutationCb=(...e)=>{t.mutation&&t.mutation(...e),n(...e)},e.mousemoveCb=(...e)=>{t.mousemove&&t.mousemove(...e),r(...e)},e.mouseInteractionCb=(...e)=>{t.mouseInteraction&&t.mouseInteraction(...e),o(...e)},e.scrollCb=(...e)=>{t.scroll&&t.scroll(...e),s(...e)},e.viewportResizeCb=(...e)=>{t.viewportResize&&t.viewportResize(...e),i(...e)},e.inputCb=(...e)=>{t.input&&t.input(...e),a(...e)},e.mediaInteractionCb=(...e)=>{t.mediaInteaction&&t.mediaInteaction(...e),c(...e)},e.styleSheetRuleCb=(...e)=>{t.styleSheetRule&&t.styleSheetRule(...e),l(...e)},e.styleDeclarationCb=(...e)=>{t.styleDeclaration&&t.styleDeclaration(...e),u(...e)},e.canvasMutationCb=(...e)=>{t.canvasMutation&&t.canvasMutation(...e),d(...e)},e.fontCb=(...e)=>{t.font&&t.font(...e),h(...e)},e.selectionCb=(...e)=>{t.selection&&t.selection(...e),p(...e)}}(e,t),e.recordDOM&&(r=dt(e,e.doc));const o=function({mousemoveCb:e,sampling:t,doc:n,mirror:r}){if(!1===t.mousemove)return()=>{};const o="number"==typeof t.mousemove?t.mousemove:50,s="number"==typeof t.mousemoveCallback?t.mousemoveCallback:500;let i,a=[];const c=Oe(Je((t=>{const n=Date.now()-i;e(a.map((e=>(e.timeOffset-=n,e))),t),a=[],i=null})),s),l=Je(Oe(Je((e=>{const t=ut(e),{clientX:n,clientY:o}=je(e)?e.changedTouches[0]:e;i||(i=De()),a.push({x:n,y:o,id:r.getId(t),timeOffset:De()-i}),c("undefined"!=typeof DragEvent&&e instanceof DragEvent?Ie.Drag:e instanceof MouseEvent?Ie.MouseMove:Ie.TouchMove)})),o,{trailing:!1})),u=[Me("mousemove",l,n),Me("touchmove",l,n),Me("drag",l,n)];return Je((()=>{u.forEach((e=>e()))}))}(e),s=ht(e),i=pt(e),a=function({viewportResizeCb:e},{win:t}){let n=-1,r=-1;return Me("resize",Je(Oe(Je((()=>{const t=_e(),o=Pe();n===t&&r===o||(e({width:Number(o),height:Number(t)}),n=t,r=o)})),200)),t)}(e,{win:n}),c=function({inputCb:e,doc:t,mirror:n,blockClass:r,blockSelector:o,ignoreClass:s,ignoreSelector:i,maskInputOptions:a,maskInputFn:c,sampling:l,userTriggeredOnInput:u}){function d(e){let n=ut(e);const l=e.isTrusted,d=n&&n.tagName;if(n&&"OPTION"===d&&(n=n.parentElement),!n||!d||vt.indexOf(d)<0||Fe(n,r,o,!0))return;if(n.classList.contains(s)||i&&n.matches(i))return;let p=n.value,f=!1;const v=Z(n)||"";"radio"===v||"checkbox"===v?f=n.checked:(a[d.toLowerCase()]||a[v])&&(p=$({element:n,maskInputOptions:a,tagName:d,type:v,value:p,maskInputFn:c})),h(n,Je(ft)({text:p,isChecked:f,userTriggered:l},u));const m=n.name;"radio"===v&&m&&f&&t.querySelectorAll(`input[type="radio"][name="${m}"]`).forEach((e=>{e!==n&&h(e,Je(ft)({text:e.value,isChecked:!f,userTriggered:!1},u))}))}function h(t,r){const o=mt.get(t);if(!o||o.text!==r.text||o.isChecked!==r.isChecked){mt.set(t,r);const o=n.getId(t);Je(e)(Object.assign(Object.assign({},r),{id:o}))}}const p=("last"===l.input?["change"]:["input","change"]).map((e=>Me(e,Je(d),t))),f=t.defaultView;if(!f)return()=>{p.forEach((e=>e()))};const v=f.Object.getOwnPropertyDescriptor(f.HTMLInputElement.prototype,"value"),m=[[f.HTMLInputElement.prototype,"value"],[f.HTMLInputElement.prototype,"checked"],[f.HTMLSelectElement.prototype,"value"],[f.HTMLTextAreaElement.prototype,"value"],[f.HTMLSelectElement.prototype,"selectedIndex"],[f.HTMLOptionElement.prototype,"selected"]];return v&&v.set&&p.push(...m.map((e=>xe(e[0],e[1],{set(){Je(d)({target:this,isTrusted:!1})}},!1,f)))),Je((()=>{p.forEach((e=>e()))}))}(e),l=function({mediaInteractionCb:e,blockClass:t,blockSelector:n,mirror:r,sampling:o,doc:s}){const i=Je((s=>Oe(Je((o=>{const i=ut(o);if(!i||Fe(i,t,n,!0))return;const{currentTime:a,volume:c,muted:l,playbackRate:u}=i;e({type:s,id:r.getId(i),currentTime:a,volume:c,muted:l,playbackRate:u})})),o.media||500))),a=[Me("play",i(0),s),Me("pause",i(1),s),Me("seeked",i(2),s),Me("volumechange",i(3),s),Me("ratechange",i(4),s)];return Je((()=>{a.forEach((e=>e()))}))}(e);let u=()=>{},d=()=>{},h=()=>{},p=()=>{};e.recordDOM&&(u=function({styleSheetRuleCb:e,mirror:t,stylesheetManager:n},{win:r}){if(!r.CSSStyleSheet||!r.CSSStyleSheet.prototype)return()=>{};const o=r.CSSStyleSheet.prototype.insertRule;r.CSSStyleSheet.prototype.insertRule=new Proxy(o,{apply:Je(((r,o,s)=>{const[i,a]=s,{id:c,styleId:l}=yt(o,t,n.styleMirror);return(c&&-1!==c||l&&-1!==l)&&e({id:c,styleId:l,adds:[{rule:i,index:a}]}),Ze((()=>r.apply(o,s)))()}))});const s=r.CSSStyleSheet.prototype.deleteRule;let i,a;r.CSSStyleSheet.prototype.deleteRule=new Proxy(s,{apply:Je(((r,o,s)=>{const[i]=s,{id:a,styleId:c}=yt(o,t,n.styleMirror);return(a&&-1!==a||c&&-1!==c)&&e({id:a,styleId:c,removes:[{index:i}]}),Ze((()=>r.apply(o,s)))()}))}),r.CSSStyleSheet.prototype.replace&&(i=r.CSSStyleSheet.prototype.replace,r.CSSStyleSheet.prototype.replace=new Proxy(i,{apply:Je(((r,o,s)=>{const[i]=s,{id:a,styleId:c}=yt(o,t,n.styleMirror);return(a&&-1!==a||c&&-1!==c)&&e({id:a,styleId:c,replace:i}),r.apply(o,s)}))})),r.CSSStyleSheet.prototype.replaceSync&&(a=r.CSSStyleSheet.prototype.replaceSync,r.CSSStyleSheet.prototype.replaceSync=new Proxy(a,{apply:Je(((r,o,s)=>{const[i]=s,{id:a,styleId:c}=yt(o,t,n.styleMirror);return(a&&-1!==a||c&&-1!==c)&&e({id:a,styleId:c,replaceSync:i}),r.apply(o,s)}))}));const c={};wt("CSSGroupingRule")?c.CSSGroupingRule=r.CSSGroupingRule:(wt("CSSMediaRule")&&(c.CSSMediaRule=r.CSSMediaRule),wt("CSSConditionRule")&&(c.CSSConditionRule=r.CSSConditionRule),wt("CSSSupportsRule")&&(c.CSSSupportsRule=r.CSSSupportsRule));const l={};return Object.entries(c).forEach((([r,o])=>{l[r]={insertRule:o.prototype.insertRule,deleteRule:o.prototype.deleteRule},o.prototype.insertRule=new Proxy(l[r].insertRule,{apply:Je(((r,o,s)=>{const[i,a]=s,{id:c,styleId:l}=yt(o.parentStyleSheet,t,n.styleMirror);return(c&&-1!==c||l&&-1!==l)&&e({id:c,styleId:l,adds:[{rule:i,index:[...gt(o),a||0]}]}),r.apply(o,s)}))}),o.prototype.deleteRule=new Proxy(l[r].deleteRule,{apply:Je(((r,o,s)=>{const[i]=s,{id:a,styleId:c}=yt(o.parentStyleSheet,t,n.styleMirror);return(a&&-1!==a||c&&-1!==c)&&e({id:a,styleId:c,removes:[{index:[...gt(o),i]}]}),r.apply(o,s)}))})})),Je((()=>{r.CSSStyleSheet.prototype.insertRule=o,r.CSSStyleSheet.prototype.deleteRule=s,i&&(r.CSSStyleSheet.prototype.replace=i),a&&(r.CSSStyleSheet.prototype.replaceSync=a),Object.entries(c).forEach((([e,t])=>{t.prototype.insertRule=l[e].insertRule,t.prototype.deleteRule=l[e].deleteRule}))}))}(e,{win:n}),d=St(e,e.doc),h=function({styleDeclarationCb:e,mirror:t,ignoreCSSAttributes:n,stylesheetManager:r},{win:o}){const s=o.CSSStyleDeclaration.prototype.setProperty;o.CSSStyleDeclaration.prototype.setProperty=new Proxy(s,{apply:Je(((o,i,a)=>{var c;const[l,u,d]=a;if(n.has(l))return s.apply(i,[l,u,d]);const{id:h,styleId:p}=yt(null===(c=i.parentRule)||void 0===c?void 0:c.parentStyleSheet,t,r.styleMirror);return(h&&-1!==h||p&&-1!==p)&&e({id:h,styleId:p,set:{property:l,value:u,priority:d},index:gt(i.parentRule)}),o.apply(i,a)}))});const i=o.CSSStyleDeclaration.prototype.removeProperty;return o.CSSStyleDeclaration.prototype.removeProperty=new Proxy(i,{apply:Je(((o,s,a)=>{var c;const[l]=a;if(n.has(l))return i.apply(s,[l]);const{id:u,styleId:d}=yt(null===(c=s.parentRule)||void 0===c?void 0:c.parentStyleSheet,t,r.styleMirror);return(u&&-1!==u||d&&-1!==d)&&e({id:u,styleId:d,remove:{property:l},index:gt(s.parentRule)}),o.apply(s,a)}))}),Je((()=>{o.CSSStyleDeclaration.prototype.setProperty=s,o.CSSStyleDeclaration.prototype.removeProperty=i}))}(e,{win:n}),e.collectFonts&&(p=function({fontCb:e,doc:t}){const n=t.defaultView;if(!n)return()=>{};const r=[],o=new WeakMap,s=n.FontFace;n.FontFace=function(e,t,n){const r=new s(e,t,n);return o.set(r,{family:e,buffer:"string"!=typeof t,descriptors:n,fontSource:"string"==typeof t?t:JSON.stringify(Array.from(new Uint8Array(t)))}),r};const i=Re(t.fonts,"add",(function(t){return function(n){return setTimeout(Je((()=>{const t=o.get(n);t&&(e(t),o.delete(n))})),0),t.apply(this,[n])}}));return r.push((()=>{n.FontFace=s})),r.push(i),Je((()=>{r.forEach((e=>e()))}))}(e)));const f=function(e){const{doc:t,mirror:n,blockClass:r,blockSelector:o,selectionCb:s}=e;let i=!0;const a=Je((()=>{const e=t.getSelection();if(!e||i&&(null==e?void 0:e.isCollapsed))return;i=e.isCollapsed||!1;const a=[],c=e.rangeCount||0;for(let t=0;t<c;t++){const s=e.getRangeAt(t),{startContainer:i,startOffset:c,endContainer:l,endOffset:u}=s;Fe(i,r,o,!0)||Fe(l,r,o,!0)||a.push({start:n.getId(i),startOffset:c,end:n.getId(l),endOffset:u})}s({ranges:a})}));return a(),Me("selectionchange",a)}(e),v=[];for(const t of e.plugins)v.push(t.observer(t.callback,n,t.options));return Je((()=>{lt.forEach((e=>e.reset())),null==r||r.disconnect(),o(),s(),i(),a(),c(),l(),u(),d(),h(),p(),f(),v.forEach((e=>e()))}))}function It(e){return void 0!==window[e]}function wt(e){return Boolean(void 0!==window[e]&&window[e].prototype&&"insertRule"in window[e].prototype&&"deleteRule"in window[e].prototype)}function Ct(e,t,n,r){return new(n||(n=Promise))((function(o,s){function i(e){try{c(r.next(e))}catch(e){s(e)}}function a(e){try{c(r.throw(e))}catch(e){s(e)}}function c(e){var t;e.done?o(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(i,a)}c((r=r.apply(e,t||[])).next())}))}for(var kt,Mt,Et,Tt=(kt=function(){!function(){function e(e,t,n,r){return new(n||(n=Promise))((function(o,s){function i(e){try{c(r.next(e))}catch(e){s(e)}}function a(e){try{c(r.throw(e))}catch(e){s(e)}}function c(e){var t;e.done?o(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(i,a)}c((r=r.apply(e,t||[])).next())}))}for(var t="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",n="undefined"==typeof Uint8Array?[]:new Uint8Array(256),r=0;r<t.length;r++)n[t.charCodeAt(r)]=r;var o=function(e){var n,r=new Uint8Array(e),o=r.length,s="";for(n=0;n<o;n+=3)s+=t[r[n]>>2],s+=t[(3&r[n])<<4|r[n+1]>>4],s+=t[(15&r[n+1])<<2|r[n+2]>>6],s+=t[63&r[n+2]];return o%3==2?s=s.substring(0,s.length-1)+"=":o%3==1&&(s=s.substring(0,s.length-2)+"=="),s};const s=new Map,i=new Map,a=self;a.onmessage=function(t){return e(this,void 0,void 0,(function*(){if(!("OffscreenCanvas"in globalThis))return a.postMessage({id:t.data.id});{const{id:n,bitmap:r,width:c,height:l,dataURLOptions:u}=t.data,d=function(t,n,r){return e(this,void 0,void 0,(function*(){const e=`${t}-${n}`;if("OffscreenCanvas"in globalThis){if(i.has(e))return i.get(e);const s=new OffscreenCanvas(t,n);s.getContext("2d");const a=yield s.convertToBlob(r),c=yield a.arrayBuffer(),l=o(c);return i.set(e,l),l}return""}))}(c,l,u),h=new OffscreenCanvas(c,l);h.getContext("2d").drawImage(r,0,0),r.close();const p=yield h.convertToBlob(u),f=p.type,v=yield p.arrayBuffer(),m=o(v);if(!s.has(n)&&(yield d)===m)return s.set(n,m),a.postMessage({id:n});if(s.get(n)===m)return a.postMessage({id:n});a.postMessage({id:n,type:f,base64:m,width:c,height:l}),s.set(n,m)}}))}}()},Mt=null,function(e){return Et=Et||function(e,t){var n=function(e,t){var n=void 0===t?null:t,r=e.toString().split("\n");r.pop(),r.shift();for(var o=r[0].search(/\S/),s=/(['"])__worker_loader_strict__(['"])/g,i=0,a=r.length;i<a;++i)r[i]=r[i].substring(o).replace(s,"$1use strict$2")+"\n";return n&&r.push("//# sourceMappingURL="+n+"\n"),r}(e,t),r=new Blob(n,{type:"application/javascript"});return URL.createObjectURL(r)}(kt,Mt),new Worker(Et,e)}),Ot="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",xt="undefined"==typeof Uint8Array?[]:new Uint8Array(256),Rt=0;Rt<Ot.length;Rt++)xt[Ot.charCodeAt(Rt)]=Rt;const Nt=new Map;const Dt=(e,t,n)=>{if(!e||!Pt(e,t)&&"object"!=typeof e)return;const r=function(e,t){let n=Nt.get(e);return n||(n=new Map,Nt.set(e,n)),n.has(t)||n.set(t,[]),n.get(t)}(n,e.constructor.name);let o=r.indexOf(e);return-1===o&&(o=r.length,r.push(e)),o};function Lt(e,t,n){if(e instanceof Array)return e.map((e=>Lt(e,t,n)));if(null===e)return e;if(e instanceof Float32Array||e instanceof Float64Array||e instanceof Int32Array||e instanceof Uint32Array||e instanceof Uint8Array||e instanceof Uint16Array||e instanceof Int16Array||e instanceof Int8Array||e instanceof Uint8ClampedArray){return{rr_type:e.constructor.name,args:[Object.values(e)]}}if(e instanceof ArrayBuffer){const t=e.constructor.name,n=function(e){var t,n=new Uint8Array(e),r=n.length,o="";for(t=0;t<r;t+=3)o+=Ot[n[t]>>2],o+=Ot[(3&n[t])<<4|n[t+1]>>4],o+=Ot[(15&n[t+1])<<2|n[t+2]>>6],o+=Ot[63&n[t+2]];return r%3==2?o=o.substring(0,o.length-1)+"=":r%3==1&&(o=o.substring(0,o.length-2)+"=="),o}(e);return{rr_type:t,base64:n}}if(e instanceof DataView){return{rr_type:e.constructor.name,args:[Lt(e.buffer,t,n),e.byteOffset,e.byteLength]}}if(e instanceof HTMLImageElement){const t=e.constructor.name,{src:n}=e;return{rr_type:t,src:n}}if(e instanceof HTMLCanvasElement){return{rr_type:"HTMLImageElement",src:e.toDataURL()}}if(e instanceof ImageData){return{rr_type:e.constructor.name,args:[Lt(e.data,t,n),e.width,e.height]}}if(Pt(e,t)||"object"==typeof e){return{rr_type:e.constructor.name,index:Dt(e,t,n)}}return e}const _t=(e,t,n)=>[...e].map((e=>Lt(e,t,n))),Pt=(e,t)=>{const n=["WebGLActiveInfo","WebGLBuffer","WebGLFramebuffer","WebGLProgram","WebGLRenderbuffer","WebGLShader","WebGLShaderPrecisionFormat","WebGLTexture","WebGLUniformLocation","WebGLVertexArrayObject","WebGLVertexArrayObjectOES"].filter((e=>"function"==typeof t[e]));return Boolean(n.find((n=>e instanceof t[n])))};function At(e,t,n,r){const o=[];try{const s=Re(e.HTMLCanvasElement.prototype,"getContext",(function(e){return function(o,...s){if(!Fe(this,t,n,!0)){const e=function(e){return"experimental-webgl"===e?"webgl":e}(o);if("__context"in this||(this.__context=e),r&&["webgl","webgl2"].includes(e))if(s[0]&&"object"==typeof s[0]){const e=s[0];e.preserveDrawingBuffer||(e.preserveDrawingBuffer=!0)}else s.splice(0,1,{preserveDrawingBuffer:!0})}return e.apply(this,[o,...s])}}));o.push(s)}catch(e){console.error("failed to patch HTMLCanvasElement.prototype.getContext")}return()=>{o.forEach((e=>e()))}}function Ft(e,t,n,r,o,s,i){const a=[],c=Object.getOwnPropertyNames(e);for(const s of c)if(!["isContextLost","canvas","drawingBufferWidth","drawingBufferHeight"].includes(s))try{if("function"!=typeof e[s])continue;const c=Re(e,s,(function(e){return function(...a){const c=e.apply(this,a);if(Dt(c,i,this),"tagName"in this.canvas&&!Fe(this.canvas,r,o,!0)){const e=_t([...a],i,this),r={type:t,property:s,args:e};n(this.canvas,r)}return c}}));a.push(c)}catch(r){const o=xe(e,s,{set(e){n(this.canvas,{type:t,property:s,args:[e],setter:!0})}});a.push(o)}return a}class qt{reset(){this.pendingCanvasMutations.clear(),this.resetObservers&&this.resetObservers()}freeze(){this.frozen=!0}unfreeze(){this.frozen=!1}lock(){this.locked=!0}unlock(){this.locked=!1}constructor(e){this.pendingCanvasMutations=new Map,this.rafStamps={latestId:0,invokeId:null},this.frozen=!1,this.locked=!1,this.processMutation=(e,t)=>{!(this.rafStamps.invokeId&&this.rafStamps.latestId!==this.rafStamps.invokeId)&&this.rafStamps.invokeId||(this.rafStamps.invokeId=this.rafStamps.latestId),this.pendingCanvasMutations.has(e)||this.pendingCanvasMutations.set(e,[]),this.pendingCanvasMutations.get(e).push(t)};const{sampling:t="all",win:n,blockClass:r,blockSelector:o,recordCanvas:s,dataURLOptions:i}=e;this.mutationCb=e.mutationCb,this.mirror=e.mirror,s&&"all"===t&&this.initCanvasMutationObserver(n,r,o),s&&"number"==typeof t&&this.initCanvasFPSObserver(t,n,r,o,{dataURLOptions:i})}initCanvasFPSObserver(e,t,n,r,o){const s=At(t,n,r,!0),i=new Map,a=new Tt;a.onmessage=e=>{const{id:t}=e.data;if(i.set(t,!1),!("base64"in e.data))return;const{base64:n,type:r,width:o,height:s}=e.data;this.mutationCb({id:t,type:ke["2D"],commands:[{property:"clearRect",args:[0,0,o,s]},{property:"drawImage",args:[{rr_type:"ImageBitmap",args:[{rr_type:"Blob",data:[{rr_type:"ArrayBuffer",base64:n}],type:r}]},0,0]}]})};const c=1e3/e;let l,u=0;const d=e=>{u&&e-u<c||(u=e,(()=>{const e=[];return t.document.querySelectorAll("canvas").forEach((t=>{Fe(t,n,r,!0)||e.push(t)})),e})().forEach((e=>Ct(this,void 0,void 0,(function*(){var t;const n=this.mirror.getId(e);if(i.get(n))return;if(i.set(n,!0),["webgl","webgl2"].includes(e.__context)){const n=e.getContext(e.__context);!1===(null===(t=null==n?void 0:n.getContextAttributes())||void 0===t?void 0:t.preserveDrawingBuffer)&&n.clear(n.COLOR_BUFFER_BIT)}const r=yield createImageBitmap(e);a.postMessage({id:n,bitmap:r,width:e.width,height:e.height,dataURLOptions:o.dataURLOptions},[r])}))))),l=requestAnimationFrame(d)};l=requestAnimationFrame(d),this.resetObservers=()=>{s(),cancelAnimationFrame(l)}}initCanvasMutationObserver(e,t,n){this.startRAFTimestamping(),this.startPendingCanvasMutationFlusher();const r=At(e,t,n,!1),o=function(e,t,n,r){const o=[],s=Object.getOwnPropertyNames(t.CanvasRenderingContext2D.prototype);for(const i of s)try{if("function"!=typeof t.CanvasRenderingContext2D.prototype[i])continue;const s=Re(t.CanvasRenderingContext2D.prototype,i,(function(o){return function(...s){return Fe(this.canvas,n,r,!0)||setTimeout((()=>{const n=_t([...s],t,this);e(this.canvas,{type:ke["2D"],property:i,args:n})}),0),o.apply(this,s)}}));o.push(s)}catch(n){const r=xe(t.CanvasRenderingContext2D.prototype,i,{set(t){e(this.canvas,{type:ke["2D"],property:i,args:[t],setter:!0})}});o.push(r)}return()=>{o.forEach((e=>e()))}}(this.processMutation.bind(this),e,t,n),s=function(e,t,n,r,o){const s=[];return s.push(...Ft(t.WebGLRenderingContext.prototype,ke.WebGL,e,n,r,0,t)),void 0!==t.WebGL2RenderingContext&&s.push(...Ft(t.WebGL2RenderingContext.prototype,ke.WebGL2,e,n,r,0,t)),()=>{s.forEach((e=>e()))}}(this.processMutation.bind(this),e,t,n,this.mirror);this.resetObservers=()=>{r(),o(),s()}}startPendingCanvasMutationFlusher(){requestAnimationFrame((()=>this.flushPendingCanvasMutations()))}startRAFTimestamping(){const e=t=>{this.rafStamps.latestId=t,requestAnimationFrame(e)};requestAnimationFrame(e)}flushPendingCanvasMutations(){this.pendingCanvasMutations.forEach(((e,t)=>{const n=this.mirror.getId(t);this.flushPendingCanvasMutationFor(t,n)})),requestAnimationFrame((()=>this.flushPendingCanvasMutations()))}flushPendingCanvasMutationFor(e,t){if(this.frozen||this.locked)return;const n=this.pendingCanvasMutations.get(e);if(!n||-1===t)return;const r=n.map((e=>{const t=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var o=0;for(r=Object.getOwnPropertySymbols(e);o<r.length;o++)t.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]])}return n}(e,["type"]);return t})),{type:o}=n[0];this.mutationCb({id:t,type:o,commands:r}),this.pendingCanvasMutations.delete(e)}}class Ut{constructor(){this.nodeMap=new WeakMap,this.loop=!0,this.periodicallyClear()}periodicallyClear(){requestAnimationFrame((()=>{this.clear(),this.loop&&this.periodicallyClear()}))}inOtherBuffer(e,t){const n=this.nodeMap.get(e);return n&&Array.from(n).some((e=>e!==t))}add(e,t){this.nodeMap.set(e,(this.nodeMap.get(e)||new Set).add(t))}clear(){this.nodeMap=new WeakMap}destroy(){this.loop=!1}}class jt{constructor(e){this.shadowDoms=new WeakSet,this.restoreHandlers=[],this.mutationCb=e.mutationCb,this.scrollCb=e.scrollCb,this.bypassOptions=e.bypassOptions,this.mirror=e.mirror,this.init()}init(){this.reset(),this.patchAttachShadow(Element,document)}addShadowRoot(e,t){if(!G(e))return;if(this.shadowDoms.has(e))return;this.shadowDoms.add(e);const n=dt(Object.assign(Object.assign({},this.bypassOptions),{doc:t,mutationCb:this.mutationCb,mirror:this.mirror,shadowDomManager:this}),e);this.restoreHandlers.push((()=>n.disconnect())),this.restoreHandlers.push(pt(Object.assign(Object.assign({},this.bypassOptions),{scrollCb:this.scrollCb,doc:e,mirror:this.mirror}))),setTimeout((()=>{e.adoptedStyleSheets&&e.adoptedStyleSheets.length>0&&this.bypassOptions.stylesheetManager.adoptStyleSheets(e.adoptedStyleSheets,this.mirror.getId(e.host)),this.restoreHandlers.push(St({mirror:this.mirror,stylesheetManager:this.bypassOptions.stylesheetManager},e))}),0)}observeAttachShadow(e){e.contentWindow&&e.contentDocument&&this.patchAttachShadow(e.contentWindow.Element,e.contentDocument)}patchAttachShadow(e,t){const n=this;this.restoreHandlers.push(Re(e.prototype,"attachShadow",(function(e){return function(r){const o=e.call(this,r);return this.shadowRoot&&Ke(this)&&n.addShadowRoot(this.shadowRoot,t),o}})))}reset(){this.restoreHandlers.forEach((e=>{try{e()}catch(e){}})),this.restoreHandlers=[],this.shadowDoms=new WeakSet}}class Wt{constructor(e){this.trackedLinkElements=new WeakSet,this.styleMirror=new Ge,this.mutationCb=e.mutationCb,this.adoptedStyleSheetCb=e.adoptedStyleSheetCb}attachLinkElement(e,t){"_cssText"in t.attributes&&this.mutationCb({adds:[],removes:[],texts:[],attributes:[{id:t.id,attributes:t.attributes}]}),this.trackLinkElement(e)}trackLinkElement(e){this.trackedLinkElements.has(e)||(this.trackedLinkElements.add(e),this.trackStylesheetInLinkElement(e))}adoptStyleSheets(e,t){if(0===e.length)return;const n={id:t,styleIds:[]},r=[];for(const t of e){let e;this.styleMirror.has(t)?e=this.styleMirror.getId(t):(e=this.styleMirror.add(t),r.push({styleId:e,rules:Array.from(t.rules||CSSRule,((e,t)=>({rule:V(e),index:t})))})),n.styleIds.push(e)}r.length>0&&(n.styles=r),this.adoptedStyleSheetCb(n)}reset(){this.styleMirror.reset(),this.trackedLinkElements=new WeakSet}trackStylesheetInLinkElement(e){}}function Bt(e){return Object.assign(Object.assign({},e),{timestamp:De()})}let zt,Gt,Ht,Vt=!1;const Kt=new K;function $t(e={}){const{emit:t,checkoutEveryNms:n,checkoutEveryNth:r,blockClass:o="rr-block",blockSelector:s=null,ignoreClass:i="rr-ignore",ignoreSelector:a=null,maskTextClass:c="rr-mask",maskTextSelector:l=null,inlineStylesheet:u=!0,maskAllInputs:d,maskInputOptions:h,slimDOMOptions:p,maskInputFn:f,maskTextFn:v,hooks:m,packFn:g,sampling:y={},dataURLOptions:S={},mousemoveWait:b,recordDOM:I=!0,recordCanvas:w=!1,recordCrossOriginIframes:C=!1,recordAfter:k=("DOMContentLoaded"===e.recordAfter?e.recordAfter:"load"),userTriggeredOnInput:M=!1,collectFonts:E=!1,inlineImages:T=!1,plugins:O,keepIframeSrcFn:x=(()=>!1),ignoreCSSAttributes:R=new Set([]),errorHandler:N}=e;$e(N);const D=!C||window.parent===window;let L=!1;if(!D)try{window.parent.document&&(L=!1)}catch(e){L=!0}if(D&&!t)throw new Error("emit function is required");void 0!==b&&void 0===y.mousemove&&(y.mousemove=b),Kt.reset();const _=!0===d?{color:!0,date:!0,"datetime-local":!0,email:!0,month:!0,number:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0,textarea:!0,select:!0,password:!0}:void 0!==h?h:{password:!0},P=!0===p||"all"===p?{script:!0,comment:!0,headFavicon:!0,headWhitespace:!0,headMetaSocial:!0,headMetaRobots:!0,headMetaHttpEquiv:!0,headMetaVerification:!0,headMetaAuthorship:"all"===p,headMetaDescKeywords:"all"===p}:p||{};let A;!function(e=window){"NodeList"in e&&!e.NodeList.prototype.forEach&&(e.NodeList.prototype.forEach=Array.prototype.forEach),"DOMTokenList"in e&&!e.DOMTokenList.prototype.forEach&&(e.DOMTokenList.prototype.forEach=Array.prototype.forEach),Node.prototype.contains||(Node.prototype.contains=(...e)=>{let t=e[0];if(!(0 in e))throw new TypeError("1 argument is required");do{if(this===t)return!0}while(t=t&&t.parentNode);return!1})}();let F=0;const q=e=>{for(const t of O||[])t.eventProcessor&&(e=t.eventProcessor(e));return g&&!L&&(e=g(e)),e};zt=(e,o)=>{var s;if(!(null===(s=lt[0])||void 0===s?void 0:s.isFrozen())||e.type===be.FullSnapshot||e.type===be.IncrementalSnapshot&&e.data.source===Ie.Mutation||lt.forEach((e=>e.unfreeze())),D)null==t||t(q(e),o);else if(L){const t={type:"rrweb",event:q(e),origin:window.location.origin,isCheckout:o};window.parent.postMessage(t,"*")}if(e.type===be.FullSnapshot)A=e,F=0;else if(e.type===be.IncrementalSnapshot){if(e.data.source===Ie.Mutation&&e.data.isAttachIframe)return;F++;const t=r&&F>=r,o=n&&e.timestamp-A.timestamp>n;(t||o)&&Gt(!0)}};const U=e=>{zt(Bt({type:be.IncrementalSnapshot,data:Object.assign({source:Ie.Mutation},e)}))},j=e=>zt(Bt({type:be.IncrementalSnapshot,data:Object.assign({source:Ie.Scroll},e)})),W=e=>zt(Bt({type:be.IncrementalSnapshot,data:Object.assign({source:Ie.CanvasMutation},e)})),B=new Wt({mutationCb:U,adoptedStyleSheetCb:e=>zt(Bt({type:be.IncrementalSnapshot,data:Object.assign({source:Ie.AdoptedStyleSheet},e)}))}),z=new Ye({mirror:Kt,mutationCb:U,stylesheetManager:B,recordCrossOriginIframes:C,wrappedEmit:zt});for(const e of O||[])e.getMirror&&e.getMirror({nodeMirror:Kt,crossOriginIframeMirror:z.crossOriginIframeMirror,crossOriginIframeStyleMirror:z.crossOriginIframeStyleMirror});const G=new Ut;Ht=new qt({recordCanvas:w,mutationCb:W,win:window,blockClass:o,blockSelector:s,mirror:Kt,sampling:y.canvas,dataURLOptions:S});const H=new jt({mutationCb:U,scrollCb:j,bypassOptions:{blockClass:o,blockSelector:s,maskTextClass:c,maskTextSelector:l,inlineStylesheet:u,maskInputOptions:_,dataURLOptions:S,maskTextFn:v,maskInputFn:f,recordCanvas:w,inlineImages:T,sampling:y,slimDOMOptions:P,iframeManager:z,stylesheetManager:B,canvasManager:Ht,keepIframeSrcFn:x,processedNodeManager:G},mirror:Kt});Gt=(e=!1)=>{if(!I)return;zt(Bt({type:be.Meta,data:{href:window.location.href,width:Pe(),height:_e()}}),e),B.reset(),H.init(),lt.forEach((e=>e.lock()));const t=function(e,t){const{mirror:n=new K,blockClass:r="rr-block",blockSelector:o=null,maskTextClass:s="rr-mask",maskTextSelector:i=null,inlineStylesheet:a=!0,inlineImages:c=!1,recordCanvas:l=!1,maskAllInputs:u=!1,maskTextFn:d,maskInputFn:h,slimDOM:p=!1,dataURLOptions:f,preserveWhiteSpace:v,onSerialize:m,onIframeLoad:g,iframeLoadTimeout:y,onStylesheetLoad:S,stylesheetLoadTimeout:b,keepIframeSrcFn:I=(()=>!1)}=t||{};return Se(e,{doc:e,mirror:n,blockClass:r,blockSelector:o,maskTextClass:s,maskTextSelector:i,skipChild:!1,inlineStylesheet:a,maskInputOptions:!0===u?{color:!0,date:!0,"datetime-local":!0,email:!0,month:!0,number:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0,textarea:!0,select:!0,password:!0}:!1===u?{password:!0}:u,maskTextFn:d,maskInputFn:h,slimDOMOptions:!0===p||"all"===p?{script:!0,comment:!0,headFavicon:!0,headWhitespace:!0,headMetaDescKeywords:"all"===p,headMetaSocial:!0,headMetaRobots:!0,headMetaHttpEquiv:!0,headMetaAuthorship:!0,headMetaVerification:!0}:!1===p?{}:p,dataURLOptions:f,inlineImages:c,recordCanvas:l,preserveWhiteSpace:v,onSerialize:m,onIframeLoad:g,iframeLoadTimeout:y,onStylesheetLoad:S,stylesheetLoadTimeout:b,keepIframeSrcFn:I,newlyAddedElement:!1})}(document,{mirror:Kt,blockClass:o,blockSelector:s,maskTextClass:c,maskTextSelector:l,inlineStylesheet:u,maskAllInputs:_,maskTextFn:v,maskInputFn:f,slimDOM:P,dataURLOptions:S,recordCanvas:w,inlineImages:T,onSerialize:e=>{We(e,Kt)&&z.addIframe(e),Be(e,Kt)&&B.trackLinkElement(e),ze(e)&&H.addShadowRoot(e.shadowRoot,document)},onIframeLoad:(e,t)=>{z.attachIframe(e,t),H.observeAttachShadow(e)},onStylesheetLoad:(e,t)=>{B.attachLinkElement(e,t)},keepIframeSrcFn:x});if(!t)return console.warn("Failed to snapshot the document");zt(Bt({type:be.FullSnapshot,data:{node:t,initialOffset:Le(window)}}),e),lt.forEach((e=>e.unlock())),document.adoptedStyleSheets&&document.adoptedStyleSheets.length>0&&B.adoptStyleSheets(document.adoptedStyleSheets,Kt.getId(document))};try{const e=[],t=e=>{var t;return Je(bt)({mutationCb:U,mousemoveCb:(e,t)=>zt(Bt({type:be.IncrementalSnapshot,data:{source:t,positions:e}})),mouseInteractionCb:e=>zt(Bt({type:be.IncrementalSnapshot,data:Object.assign({source:Ie.MouseInteraction},e)})),scrollCb:j,viewportResizeCb:e=>zt(Bt({type:be.IncrementalSnapshot,data:Object.assign({source:Ie.ViewportResize},e)})),inputCb:e=>zt(Bt({type:be.IncrementalSnapshot,data:Object.assign({source:Ie.Input},e)})),mediaInteractionCb:e=>zt(Bt({type:be.IncrementalSnapshot,data:Object.assign({source:Ie.MediaInteraction},e)})),styleSheetRuleCb:e=>zt(Bt({type:be.IncrementalSnapshot,data:Object.assign({source:Ie.StyleSheetRule},e)})),styleDeclarationCb:e=>zt(Bt({type:be.IncrementalSnapshot,data:Object.assign({source:Ie.StyleDeclaration},e)})),canvasMutationCb:W,fontCb:e=>zt(Bt({type:be.IncrementalSnapshot,data:Object.assign({source:Ie.Font},e)})),selectionCb:e=>{zt(Bt({type:be.IncrementalSnapshot,data:Object.assign({source:Ie.Selection},e)}))},blockClass:o,ignoreClass:i,ignoreSelector:a,maskTextClass:c,maskTextSelector:l,maskInputOptions:_,inlineStylesheet:u,sampling:y,recordDOM:I,recordCanvas:w,inlineImages:T,userTriggeredOnInput:M,collectFonts:E,doc:e,maskInputFn:f,maskTextFn:v,keepIframeSrcFn:x,blockSelector:s,slimDOMOptions:P,dataURLOptions:S,mirror:Kt,iframeManager:z,stylesheetManager:B,shadowDomManager:H,processedNodeManager:G,canvasManager:Ht,ignoreCSSAttributes:R,plugins:(null===(t=null==O?void 0:O.filter((e=>e.observer)))||void 0===t?void 0:t.map((e=>({observer:e.observer,options:e.options,callback:t=>zt(Bt({type:be.Plugin,data:{plugin:e.name,payload:t}}))}))))||[]},m)};z.addLoadListener((n=>{try{e.push(t(n.contentDocument))}catch(e){console.warn(e)}}));const n=()=>{Gt(),e.push(t(document)),Vt=!0};return"interactive"===document.readyState||"complete"===document.readyState?n():(e.push(Me("DOMContentLoaded",(()=>{zt(Bt({type:be.DomContentLoaded,data:{}})),"DOMContentLoaded"===k&&n()}))),e.push(Me("load",(()=>{zt(Bt({type:be.Load,data:{}})),"load"===k&&n()}),window))),()=>{e.forEach((e=>e())),G.destroy(),Vt=!1,Xe()}}catch(e){console.warn(e)}}var Xt,Jt;$t.addCustomEvent=(e,t)=>{if(!Vt)throw new Error("please add custom event after start recording");zt(Bt({type:be.Custom,data:{tag:e,payload:t}}))},$t.freezePage=()=>{lt.forEach((e=>e.freeze()))},$t.takeFullSnapshot=e=>{if(!Vt)throw new Error("please take full snapshot after start recording");Gt(e)},$t.mirror=Kt,function(e){e[e.Document=0]="Document",e[e.DocumentType=1]="DocumentType",e[e.Element=2]="Element",e[e.Text=3]="Text",e[e.CDATA=4]="CDATA",e[e.Comment=5]="Comment"}(Xt||(Xt={})),function(e){e[e.PLACEHOLDER=0]="PLACEHOLDER",e[e.ELEMENT_NODE=1]="ELEMENT_NODE",e[e.ATTRIBUTE_NODE=2]="ATTRIBUTE_NODE",e[e.TEXT_NODE=3]="TEXT_NODE",e[e.CDATA_SECTION_NODE=4]="CDATA_SECTION_NODE",e[e.ENTITY_REFERENCE_NODE=5]="ENTITY_REFERENCE_NODE",e[e.ENTITY_NODE=6]="ENTITY_NODE",e[e.PROCESSING_INSTRUCTION_NODE=7]="PROCESSING_INSTRUCTION_NODE",e[e.COMMENT_NODE=8]="COMMENT_NODE",e[e.DOCUMENT_NODE=9]="DOCUMENT_NODE",e[e.DOCUMENT_TYPE_NODE=10]="DOCUMENT_TYPE_NODE",e[e.DOCUMENT_FRAGMENT_NODE=11]="DOCUMENT_FRAGMENT_NODE"}(Jt||(Jt={}));var Zt=Uint8Array,Qt=Uint16Array,Yt=Uint32Array,en=new Zt([0,0,0,0,0,0,0,0,1,1,1,1,2,2,2,2,3,3,3,3,4,4,4,4,5,5,5,5,0,0,0,0]),tn=new Zt([0,0,0,0,1,1,2,2,3,3,4,4,5,5,6,6,7,7,8,8,9,9,10,10,11,11,12,12,13,13,0,0]),nn=new Zt([16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15]),rn=function(e,t){for(var n=new Qt(31),r=0;r<31;++r)n[r]=t+=1<<e[r-1];var o=new Yt(n[30]);for(r=1;r<30;++r)for(var s=n[r];s<n[r+1];++s)o[s]=s-n[r]<<5|r;return[n,o]},on=rn(en,2),sn=on[0],an=on[1];sn[28]=258,an[258]=28;for(var cn=rn(tn,0)[1],ln=new Qt(32768),un=0;un<32768;++un){var dn=(43690&un)>>>1|(21845&un)<<1;dn=(61680&(dn=(52428&dn)>>>2|(13107&dn)<<2))>>>4|(3855&dn)<<4,ln[un]=((65280&dn)>>>8|(255&dn)<<8)>>>1}var hn=function(e,t,n){for(var r=e.length,o=0,s=new Qt(t);o<r;++o)++s[e[o]-1];var i,a=new Qt(t);for(o=0;o<t;++o)a[o]=a[o-1]+s[o-1]<<1;if(n){i=new Qt(1<<t);var c=15-t;for(o=0;o<r;++o)if(e[o])for(var l=o<<4|e[o],u=t-e[o],d=a[e[o]-1]++<<u,h=d|(1<<u)-1;d<=h;++d)i[ln[d]>>>c]=l}else for(i=new Qt(r),o=0;o<r;++o)i[o]=ln[a[e[o]-1]++]>>>15-e[o];return i},pn=new Zt(288);for(un=0;un<144;++un)pn[un]=8;for(un=144;un<256;++un)pn[un]=9;for(un=256;un<280;++un)pn[un]=7;for(un=280;un<288;++un)pn[un]=8;var fn=new Zt(32);for(un=0;un<32;++un)fn[un]=5;var vn=hn(pn,9,0),mn=hn(fn,5,0),gn=function(e){return(e/8>>0)+(7&e&&1)},yn=function(e,t,n){(null==t||t<0)&&(t=0),(null==n||n>e.length)&&(n=e.length);var r=new(e instanceof Qt?Qt:e instanceof Yt?Yt:Zt)(n-t);return r.set(e.subarray(t,n)),r},Sn=function(e,t,n){n<<=7&t;var r=t/8>>0;e[r]|=n,e[r+1]|=n>>>8},bn=function(e,t,n){n<<=7&t;var r=t/8>>0;e[r]|=n,e[r+1]|=n>>>8,e[r+2]|=n>>>16},In=function(e,t){for(var n=[],r=0;r<e.length;++r)e[r]&&n.push({s:r,f:e[r]});var o=n.length,s=n.slice();if(!o)return[new Zt(0),0];if(1==o){var i=new Zt(n[0].s+1);return i[n[0].s]=1,[i,1]}n.sort((function(e,t){return e.f-t.f})),n.push({s:-1,f:25001});var a=n[0],c=n[1],l=0,u=1,d=2;for(n[0]={s:-1,f:a.f+c.f,l:a,r:c};u!=o-1;)a=n[n[l].f<n[d].f?l++:d++],c=n[l!=u&&n[l].f<n[d].f?l++:d++],n[u++]={s:-1,f:a.f+c.f,l:a,r:c};var h=s[0].s;for(r=1;r<o;++r)s[r].s>h&&(h=s[r].s);var p=new Qt(h+1),f=wn(n[u-1],p,0);if(f>t){r=0;var v=0,m=f-t,g=1<<m;for(s.sort((function(e,t){return p[t.s]-p[e.s]||e.f-t.f}));r<o;++r){var y=s[r].s;if(!(p[y]>t))break;v+=g-(1<<f-p[y]),p[y]=t}for(v>>>=m;v>0;){var S=s[r].s;p[S]<t?v-=1<<t-p[S]++-1:++r}for(;r>=0&&v;--r){var b=s[r].s;p[b]==t&&(--p[b],++v)}f=t}return[new Zt(p),f]},wn=function(e,t,n){return-1==e.s?Math.max(wn(e.l,t,n+1),wn(e.r,t,n+1)):t[e.s]=n},Cn=function(e){for(var t=e.length;t&&!e[--t];);for(var n=new Qt(++t),r=0,o=e[0],s=1,i=function(e){n[r++]=e},a=1;a<=t;++a)if(e[a]==o&&a!=t)++s;else{if(!o&&s>2){for(;s>138;s-=138)i(32754);s>2&&(i(s>10?s-11<<5|28690:s-3<<5|12305),s=0)}else if(s>3){for(i(o),--s;s>6;s-=6)i(8304);s>2&&(i(s-3<<5|8208),s=0)}for(;s--;)i(o);s=1,o=e[a]}return[n.subarray(0,r),t]},kn=function(e,t){for(var n=0,r=0;r<t.length;++r)n+=e[r]*t[r];return n},Mn=function(e,t,n){var r=n.length,o=gn(t+2);e[o]=255&r,e[o+1]=r>>>8,e[o+2]=255^e[o],e[o+3]=255^e[o+1];for(var s=0;s<r;++s)e[o+s+4]=n[s];return 8*(o+4+r)},En=function(e,t,n,r,o,s,i,a,c,l,u){Sn(t,u++,n),++o[256];for(var d=In(o,15),h=d[0],p=d[1],f=In(s,15),v=f[0],m=f[1],g=Cn(h),y=g[0],S=g[1],b=Cn(v),I=b[0],w=b[1],C=new Qt(19),k=0;k<y.length;++k)C[31&y[k]]++;for(k=0;k<I.length;++k)C[31&I[k]]++;for(var M=In(C,7),E=M[0],T=M[1],O=19;O>4&&!E[nn[O-1]];--O);var x,R,N,D,L=l+5<<3,_=kn(o,pn)+kn(s,fn)+i,P=kn(o,h)+kn(s,v)+i+14+3*O+kn(C,E)+(2*C[16]+3*C[17]+7*C[18]);if(L<=_&&L<=P)return Mn(t,u,e.subarray(c,c+l));if(Sn(t,u,1+(P<_)),u+=2,P<_){x=hn(h,p,0),R=h,N=hn(v,m,0),D=v;var A=hn(E,T,0);Sn(t,u,S-257),Sn(t,u+5,w-1),Sn(t,u+10,O-4),u+=14;for(k=0;k<O;++k)Sn(t,u+3*k,E[nn[k]]);u+=3*O;for(var F=[y,I],q=0;q<2;++q){var U=F[q];for(k=0;k<U.length;++k){var j=31&U[k];Sn(t,u,A[j]),u+=E[j],j>15&&(Sn(t,u,U[k]>>>5&127),u+=U[k]>>>12)}}}else x=vn,R=pn,N=mn,D=fn;for(k=0;k<a;++k)if(r[k]>255){j=r[k]>>>18&31;bn(t,u,x[j+257]),u+=R[j+257],j>7&&(Sn(t,u,r[k]>>>23&31),u+=en[j]);var W=31&r[k];bn(t,u,N[W]),u+=D[W],W>3&&(bn(t,u,r[k]>>>5&8191),u+=tn[W])}else bn(t,u,x[r[k]]),u+=R[r[k]];return bn(t,u,x[256]),u+R[256]},Tn=new Yt([65540,131080,131088,131104,262176,1048704,1048832,2114560,2117632]),On=new Zt(0),xn=function(e,t,n,r,o){return function(e,t,n,r,o,s){var i=e.length,a=new Zt(r+i+5*(1+Math.floor(i/7e3))+o),c=a.subarray(r,a.length-o),l=0;if(!t||i<8)for(var u=0;u<=i;u+=65535){var d=u+65535;d<i?l=Mn(c,l,e.subarray(u,d)):(c[u]=s,l=Mn(c,l,e.subarray(u,i)))}else{for(var h=Tn[t-1],p=h>>>13,f=8191&h,v=(1<<n)-1,m=new Qt(32768),g=new Qt(v+1),y=Math.ceil(n/3),S=2*y,b=function(t){return(e[t]^e[t+1]<<y^e[t+2]<<S)&v},I=new Yt(25e3),w=new Qt(288),C=new Qt(32),k=0,M=0,E=(u=0,0),T=0,O=0;u<i;++u){var x=b(u),R=32767&u,N=g[x];if(m[R]=N,g[x]=R,T<=u){var D=i-u;if((k>7e3||E>24576)&&D>423){l=En(e,c,0,I,w,C,M,E,O,u-O,l),E=k=M=0,O=u;for(var L=0;L<286;++L)w[L]=0;for(L=0;L<30;++L)C[L]=0}var _=2,P=0,A=f,F=R-N&32767;if(D>2&&x==b(u-F))for(var q=Math.min(p,D)-1,U=Math.min(32767,u),j=Math.min(258,D);F<=U&&--A&&R!=N;){if(e[u+_]==e[u+_-F]){for(var W=0;W<j&&e[u+W]==e[u+W-F];++W);if(W>_){if(_=W,P=F,W>q)break;var B=Math.min(F,W-2),z=0;for(L=0;L<B;++L){var G=u-F+L+32768&32767,H=G-m[G]+32768&32767;H>z&&(z=H,N=G)}}}F+=(R=N)-(N=m[R])+32768&32767}if(P){I[E++]=268435456|an[_]<<18|cn[P];var V=31&an[_],K=31&cn[P];M+=en[V]+tn[K],++w[257+V],++C[K],T=u+_,++k}else I[E++]=e[u],++w[e[u]]}}l=En(e,c,s,I,w,C,M,E,O,u-O,l),s||(l=Mn(c,l,On))}return yn(a,0,r+gn(l)+o)}(e,null==t.level?6:t.level,null==t.mem?Math.ceil(1.5*Math.max(8,Math.min(13,Math.log(e.length)))):12+t.mem,n,r,!o)};function Rn(e,t){void 0===t&&(t={});var n=function(){var e=1,t=0;return{p:function(n){for(var r=e,o=t,s=n.length,i=0;i!=s;){for(var a=Math.min(i+5552,s);i<a;++i)o+=r+=n[i];r%=65521,o%=65521}e=r,t=o},d:function(){return(e>>>8<<16|(255&t)<<8|t>>>8)+2*((255&e)<<23)}}}();n.p(e);var r=xn(e,t,2,4);return function(e,t){var n=t.level,r=0==n?0:n<6?1:9==n?3:2;e[0]=120,e[1]=r<<6|(r?32-2*r:1)}(r,t),function(e,t,n){for(;n;++t)e[t]=n,n>>>=8}(r,r.length-4,n.d()),r}const Nn=e=>{const t=Object.assign(Object.assign({},e),{v:"v1"});return function(e,t){var n="";if(!t&&"undefined"!=typeof TextDecoder)return(new TextDecoder).decode(e);for(var r=0;r<e.length;){var o=e[r++];o<128||t?n+=String.fromCharCode(o):o<224?n+=String.fromCharCode((31&o)<<6|63&e[r++]):o<240?n+=String.fromCharCode((15&o)<<12|(63&e[r++])<<6|63&e[r++]):(o=((15&o)<<18|(63&e[r++])<<12|(63&e[r++])<<6|63&e[r++])-65536,n+=String.fromCharCode(55296|o>>10,56320|1023&o))}return n}(Rn(function(e,t){var n=e.length;if(!t&&"undefined"!=typeof TextEncoder)return(new TextEncoder).encode(e);for(var r=new Zt(e.length+(e.length>>>1)),o=0,s=function(e){r[o++]=e},i=0;i<n;++i){if(o+5>r.length){var a=new Zt(o+8+(n-i<<1));a.set(r),r=a}var c=e.charCodeAt(i);c<128||t?s(c):c<2048?(s(192|c>>>6),s(128|63&c)):c>55295&&c<57344?(s(240|(c=65536+(1047552&c)|1023&e.charCodeAt(++i))>>>18),s(128|c>>>12&63),s(128|c>>>6&63),s(128|63&c)):(s(224|c>>>12),s(128|c>>>6&63),s(128|63&c))}return yn(r,0,o)}(JSON.stringify(t))),!0)};var Dn,Ln;(Ln=Dn||(Dn={}))[Ln.NotStarted=0]="NotStarted",Ln[Ln.Running=1]="Running",Ln[Ln.Stopped=2]="Stopped";var _n,Pn="Remote config fetch rejected due to timeout after 5 seconds",An="Unexpected error occurred",Fn=function(){function e(e){var t=e.localConfig,n=e.configKeys,r=this;this.retryTimeout=1e3,this.attempts=0,this.sessionTargetingMatch=!1,this.metrics={},this.getRemoteConfig=function(e,t,n){return a(r,void 0,void 0,(function(){var r,o,s;return c(this,(function(i){switch(i.label){case 0:return r=Date.now(),[4,this.fetchWithTimeout(n)];case 1:return(o=i.sent())&&(s=o.configs&&o.configs[e])?(this.metrics.fetchTimeAPISuccess=Date.now()-r,[2,s[t]]):(this.metrics.fetchTimeAPIFail=Date.now()-r,[2,void 0])}}))}))},this.fetchWithTimeout=function(e){return a(r,void 0,void 0,(function(){var t,n,r;return c(this,(function(o){switch(o.label){case 0:return t=new AbortController,n=setTimeout((function(){return t.abort()}),5e3),[4,this.fetchRemoteConfig(t.signal,e)];case 1:return r=o.sent(),clearTimeout(n),[2,r]}}))}))},this.fetchRemoteConfig=function(e,t){return a(r,void 0,void 0,(function(){var n,r,o,a,u,d,h,p,f,v,m,g;return c(this,(function(c){switch(c.label){case 0:if(t===this.lastFetchedSessionId&&this.attempts>=this.localConfig.flushMaxRetries)return[2,this.completeRequest({err:"Remote config fetch rejected due to exceeded retry count"})];if(e.aborted)return[2,this.completeRequest({err:Pn})];t!==this.lastFetchedSessionId&&(this.lastFetchedSessionId=t,this.attempts=0),c.label=1;case 1:c.trys.push([1,3,,4]),n=new URLSearchParams({api_key:this.localConfig.apiKey});try{for(r=l(this.configKeys),o=r.next();!o.done;o=r.next())a=o.value,n.append("config_keys",a)}catch(e){v={error:e}}finally{try{o&&!o.done&&(m=r.return)&&m.call(r)}finally{if(v)throw v.error}}return t&&n.set("session_id",String(t)),u={headers:{Accept:"*/*"},method:"GET"},d="".concat(this.getServerUrl(),"?").concat(n.toString()),this.attempts+=1,[4,fetch(d,i(i({},u),{signal:e}))];case 2:if(null===(h=c.sent()))return[2,this.completeRequest({err:An})];switch((new C).buildStatus(h.status)){case s.Success:return this.attempts=0,[2,this.parseAndStoreConfig(h)];case s.Failed:return[2,this.retryFetch(e,t)];default:return[2,this.completeRequest({err:"Network error occurred, remote config fetch failed"})]}case 3:return p=c.sent(),f=p,e.aborted?[2,this.completeRequest({err:Pn})]:[2,this.completeRequest({err:null!==(g=f.message)&&void 0!==g?g:An})];case 4:return[2]}}))}))},this.retryFetch=function(e,t){return a(r,void 0,void 0,(function(){var n=this;return c(this,(function(r){switch(r.label){case 0:return[4,new Promise((function(e){return setTimeout(e,n.attempts*n.retryTimeout)}))];case 1:return r.sent(),[2,this.fetchRemoteConfig(e,t)]}}))}))},this.parseAndStoreConfig=function(e){return a(r,void 0,void 0,(function(){var t;return c(this,(function(n){switch(n.label){case 0:return[4,e.json()];case 1:return t=n.sent(),this.completeRequest({success:"Remote config successfully fetched"}),[2,t]}}))}))},this.localConfig=t,this.configKeys=n}return e.prototype.getServerUrl=function(){return this.localConfig.serverZone===o.STAGING?"https://sr-client-cfg.stag2.amplitude.com/config":this.localConfig.serverZone===o.EU?"https://sr-client-cfg.eu.amplitude.com/config":"https://sr-client-cfg.amplitude.com/config"},e.prototype.completeRequest=function(e){var t=e.err,n=e.success;if(t)throw new Error(t);n&&this.localConfig.loggerProvider.log(n)},e}(),qn=function(e){var t=e.localConfig,n=e.configKeys;return a(void 0,void 0,void 0,(function(){return c(this,(function(e){return[2,new Fn({localConfig:t,configKeys:n})]}))}))},Un="medium";!function(e){e[e.Document=0]="Document",e[e.DocumentType=1]="DocumentType",e[e.Element=2]="Element",e[e.Text=3]="Text",e[e.CDATA=4]="CDATA",e[e.Comment=5]="Comment"}(_n||(_n={}));var jn=function(e,t,n){switch(t){case"light":if("input"!==e)return!0;var r=n?function(e){const t=e.type;return e.hasAttribute("data-rr-is-password")?"password":t?t.toLowerCase():null}(n):"";return!!r&&(!!["password","hidden","email","tel"].includes(r)||!!n.autocomplete.startsWith("cc-"));case"medium":case"conservative":return!0;default:return jn(e,Un,n)}},Wn=function(e,t){return function(n,r){return function(e,t,n){var r,o,s;if(void 0===t&&(t={defaultMaskLevel:Un}),n){if(n.closest("."+U))return!0;var i=(null!==(r=t.maskSelector)&&void 0!==r?r:[]).some((function(e){return n.closest(e)}));if(i)return!0;if(n.closest(".amp-unmask"))return!1;var a=(null!==(o=t.unmaskSelector)&&void 0!==o?o:[]).some((function(e){return n.closest(e)}));if(a)return!1}return jn(e,null!==(s=t.defaultMaskLevel)&&void 0!==s?s:Un,n)}(e,t,r)?n.replace(/[^\s]/g,"*"):n}},Bn=function(e){var t=0;if(0===e.length)return t;for(var n=0;n<e.length;n++){t=(t<<5)-t+e.charCodeAt(n),t|=0}return t},zn=function(e){return e===o.STAGING?"https://api-sr.stag2.amplitude.com/sessions/v2/track":e===o.EU?"https://api-sr.eu.amplitude.com/sessions/v2/track":"https://api-sr.amplitude.com/sessions/v2/track"},Gn=function(){return a(void 0,void 0,void 0,(function(){var e,t,n,r,o,s,i;return c(this,(function(a){switch(a.label){case 0:return a.trys.push([0,3,,4]),(e=L())?[4,e.navigator.storage.estimate()]:[3,2];case 1:return t=a.sent(),n=t.usage,r=t.quota,o=t.usageDetails,s=n?Math.round(n/1024):0,i=n&&r?Math.round(1e3*(n/r+Number.EPSILON))/1e3:0,[2,{totalStorageSize:s,percentOfQuota:i,usageDetails:JSON.stringify(o)}];case 2:return[3,4];case 3:return a.sent(),[3,4];case 4:return[2,{totalStorageSize:0,percentOfQuota:0,usageDetails:""}]}}))}))},Hn=function(e){var t=i({},e),n=t.apiKey;return t.apiKey="****".concat(n.substring(n.length-4)),t},Vn=function(){function e(e,t){this.localConfig=t,this.remoteConfigFetch=e}return e.prototype.generateJoinedConfig=function(e){var t,n,r;return a(this,void 0,void 0,(function(){var o,s,a,d,h,p,f,v,m,g,y,S,b,I,w,C,k,M,E,T;return c(this,(function(c){switch(c.label){case 0:(o=i({},this.localConfig)).optOut=this.localConfig.optOut,o.captureEnabled=!0,c.label=1;case 1:return c.trys.push([1,5,,6]),[4,this.remoteConfigFetch.getRemoteConfig("sessionReplay","sr_sampling_config",e)];case 2:return a=c.sent(),[4,this.remoteConfigFetch.getRemoteConfig("sessionReplay","sr_privacy_config",e)];case 3:return d=c.sent(),h=o,[4,this.remoteConfigFetch.getRemoteConfig("sessionReplay","sr_interaction_config",e)];case 4:return h.interactionConfig=c.sent(),(a||d)&&(s={},a&&(s.sr_sampling_config=a),d&&(s.sr_privacy_config=d)),[3,6];case 5:return p=c.sent(),f=p,this.localConfig.loggerProvider.warn(f.message),o.captureEnabled=!1,[3,6];case 6:if(!s)return[2,o];if(v=s.sr_sampling_config,m=s.sr_privacy_config,v&&Object.keys(v).length>0?(Object.prototype.hasOwnProperty.call(v,"capture_enabled")?o.captureEnabled=v.capture_enabled:o.captureEnabled=!1,Object.prototype.hasOwnProperty.call(v,"sample_rate")&&(o.sampleRate=v.sample_rate)):(o.captureEnabled=!0,this.localConfig.loggerProvider.debug("Remote config successfully fetched, but no values set for project, Session Replay capture enabled.")),m){g=null!==(t=o.privacyConfig)&&void 0!==t?t:{},y={defaultMaskLevel:null!==(r=null!==(n=m.defaultMaskLevel)&&void 0!==n?n:g.defaultMaskLevel)&&void 0!==r?r:"medium",blockSelector:[],maskSelector:[],unmaskSelector:[]},S=function(e){var t,n,r,o,s,i,a,c,u,d={};"string"==typeof e.blockSelector&&(e.blockSelector=[e.blockSelector]);try{for(var h=l(null!==(a=e.blockSelector)&&void 0!==a?a:[]),p=h.next();!p.done;p=h.next()){d[p.value]="block"}}catch(e){t={error:e}}finally{try{p&&!p.done&&(n=h.return)&&n.call(h)}finally{if(t)throw t.error}}try{for(var f=l(null!==(c=e.maskSelector)&&void 0!==c?c:[]),v=f.next();!v.done;v=f.next()){d[v.value]="mask"}}catch(e){r={error:e}}finally{try{v&&!v.done&&(o=f.return)&&o.call(f)}finally{if(r)throw r.error}}try{for(var m=l(null!==(u=e.unmaskSelector)&&void 0!==u?u:[]),g=m.next();!g.done;g=m.next()){d[g.value]="unmask"}}catch(e){s={error:e}}finally{try{g&&!g.done&&(i=m.return)&&i.call(m)}finally{if(s)throw s.error}}return d},b=i(i({},S(g)),S(m));try{for(I=l(Object.entries(b)),w=I.next();!w.done;w=I.next())C=u(w.value,2),k=C[0],"mask"===(M=C[1])?y.maskSelector.push(k):"block"===M?y.blockSelector.push(k):"unmask"===M&&y.unmaskSelector.push(k)}catch(e){E={error:e}}finally{try{w&&!w.done&&(T=I.return)&&T.call(I)}finally{if(E)throw E.error}}o.privacyConfig=function(e,t){var n=document.createDocumentFragment(),r=function(e){if(void 0===e&&(e=[]),"string"==typeof e&&(e=[e]),e=e.filter((function(e){try{n.querySelector(e)}catch(n){return t.warn('[session-replay-browser] omitting selector "'.concat(e,'" because it is invalid')),!1}return!0})),0!==e.length)return e};return e.blockSelector=r(e.blockSelector),e.maskSelector=r(e.maskSelector),e.unmaskSelector=r(e.unmaskSelector),e}(y,this.localConfig.loggerProvider)}return this.localConfig.loggerProvider.debug(JSON.stringify({name:"session replay joined config",config:Hn(o)},null,2)),[2,o]}}))}))},e}(),Kn=function(e,t){return a(void 0,void 0,void 0,(function(){var n,r;return c(this,(function(o){switch(o.label){case 0:return n=new B(e,t),[4,qn({localConfig:n,configKeys:["sessionReplay"]})];case 1:return r=o.sent(),[2,new Vn(r,n)]}}))}))},$n="Session replay event batch rejected due to exceeded retry count",Xn="Failed to store session replay events in IndexedDB",Jn="1.15.0",Zn=function(){function e(e){var t=e.loggerProvider,n=e.payloadBatcher;this.storageKey="",this.retryTimeout=1e3,this.scheduled=null,this.queue=[],this.loggerProvider=t,this.payloadBatcher=n||function(e){return e}}return e.prototype.sendEventsList=function(e){this.addToQueue(i(i({},e),{attempts:0,timeout:0}))},e.prototype.addToQueue=function(){for(var e=this,t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];var r=t.filter((function(t){return t.attempts<(t.flushMaxRetries||0)?(t.attempts+=1,!0):(e.completeRequest({context:t,err:$n}),!1)}));r.forEach((function(t){e.queue=e.queue.concat(t),0!==t.timeout?setTimeout((function(){t.timeout=0,e.schedule(0)}),t.timeout):e.schedule(0)}))},e.prototype.schedule=function(e){var t=this;this.scheduled||(this.scheduled=setTimeout((function(){t.flush(!0).then((function(){t.queue.length>0&&t.schedule(e)}))}),e))},e.prototype.flush=function(e){return void 0===e&&(e=!1),a(this,void 0,void 0,(function(){var t,n,r=this;return c(this,(function(o){switch(o.label){case 0:return t=[],n=[],this.queue.forEach((function(e){return 0===e.timeout?t.push(e):n.push(e)})),this.queue=n,this.scheduled&&(clearTimeout(this.scheduled),this.scheduled=null),[4,Promise.all(t.map((function(t){return r.send(t,e)})))];case 1:return o.sent(),[2]}}))}))},e.prototype.send=function(e,t){var n,r;return void 0===t&&(t=!0),a(this,void 0,void 0,(function(){var o,s,i,a,l,u,d,h,p,f,v,m,g;return c(this,(function(c){switch(c.label){case 0:if(!(o=e.apiKey))return[2,this.completeRequest({context:e,err:"Session replay event batch not sent due to missing api key"})];if(!(s=e.deviceId))return[2,this.completeRequest({context:e,err:"Session replay event batch not sent due to missing device ID"})];if(y=L(),i=(null==y?void 0:y.location)?y.location.href:"",a=Jn,l=e.sampleRate,u=new URLSearchParams({device_id:s,session_id:"".concat(e.sessionId),type:"".concat(e.type)}),d="".concat((null===(n=e.version)||void 0===n?void 0:n.type)||"standalone","/").concat((null===(r=e.version)||void 0===r?void 0:r.version)||a),0===(h=this.payloadBatcher({version:1,events:e.events})).events.length)return this.completeRequest({context:e}),[2];c.label=1;case 1:return c.trys.push([1,3,,4]),p={headers:{"Content-Type":"application/json",Accept:"*/*",Authorization:"Bearer ".concat(o),"X-Client-Version":a,"X-Client-Library":d,"X-Client-Url":i,"X-Client-Sample-Rate":"".concat(l)},body:JSON.stringify(h),method:"POST"},f="".concat(zn(e.serverZone),"?").concat(u.toString()),[4,fetch(f,p)];case 2:if(null===(v=c.sent()))return this.completeRequest({context:e,err:"Unexpected error occurred"}),[2];if(t)this.handleReponse(v.status,e);else{m="";try{m=JSON.stringify(v.body,null,2)}catch(e){}this.completeRequest({context:e,success:"".concat(v.status,": ").concat(m)})}return[3,4];case 3:return g=c.sent(),this.completeRequest({context:e,err:g}),[3,4];case 4:return[2]}var y}))}))},e.prototype.handleReponse=function(e,t){switch((new C).buildStatus(e)){case s.Success:this.handleSuccessResponse(t);break;case s.Failed:this.handleOtherResponse(t);break;default:this.completeRequest({context:t,err:"Network error occurred, event batch rejected"})}},e.prototype.handleSuccessResponse=function(e){var t=Math.round(new Blob(e.events).size/1024);this.completeRequest({context:e,success:"Session replay event batch tracked successfully for session id ".concat(e.sessionId,", size of events: ").concat(t," KB")})},e.prototype.handleOtherResponse=function(e){this.addToQueue(i(i({},e),{timeout:e.attempts*this.retryTimeout}))},e.prototype.completeRequest=function(e){var t=e.context,n=e.err,r=e.success;t.onComplete(),n?this.loggerProvider.warn(n):r&&this.loggerProvider.log(r)},e}();const Qn=(e,t)=>t.some((t=>e instanceof t));let Yn,er;const tr=new WeakMap,nr=new WeakMap,rr=new WeakMap;let or={get(e,t,n){if(e instanceof IDBTransaction){if("done"===t)return tr.get(e);if("store"===t)return n.objectStoreNames[1]?void 0:n.objectStore(n.objectStoreNames[0])}return cr(e[t])},set:(e,t,n)=>(e[t]=n,!0),has:(e,t)=>e instanceof IDBTransaction&&("done"===t||"store"===t)||t in e};function sr(e){or=e(or)}function ir(e){return(er||(er=[IDBCursor.prototype.advance,IDBCursor.prototype.continue,IDBCursor.prototype.continuePrimaryKey])).includes(e)?function(...t){return e.apply(lr(this),t),cr(this.request)}:function(...t){return cr(e.apply(lr(this),t))}}function ar(e){return"function"==typeof e?ir(e):(e instanceof IDBTransaction&&function(e){if(tr.has(e))return;const t=new Promise(((t,n)=>{const r=()=>{e.removeEventListener("complete",o),e.removeEventListener("error",s),e.removeEventListener("abort",s)},o=()=>{t(),r()},s=()=>{n(e.error||new DOMException("AbortError","AbortError")),r()};e.addEventListener("complete",o),e.addEventListener("error",s),e.addEventListener("abort",s)}));tr.set(e,t)}(e),Qn(e,Yn||(Yn=[IDBDatabase,IDBObjectStore,IDBIndex,IDBCursor,IDBTransaction]))?new Proxy(e,or):e)}function cr(e){if(e instanceof IDBRequest)return function(e){const t=new Promise(((t,n)=>{const r=()=>{e.removeEventListener("success",o),e.removeEventListener("error",s)},o=()=>{t(cr(e.result)),r()},s=()=>{n(e.error),r()};e.addEventListener("success",o),e.addEventListener("error",s)}));return rr.set(t,e),t}(e);if(nr.has(e))return nr.get(e);const t=ar(e);return t!==e&&(nr.set(e,t),rr.set(t,e)),t}const lr=e=>rr.get(e);function ur(e,t,{blocked:n,upgrade:r,blocking:o,terminated:s}={}){const i=indexedDB.open(e,t),a=cr(i);return r&&i.addEventListener("upgradeneeded",(e=>{r(cr(i.result),e.oldVersion,e.newVersion,cr(i.transaction),e)})),n&&i.addEventListener("blocked",(e=>n(e.oldVersion,e.newVersion,e))),a.then((e=>{s&&e.addEventListener("close",(()=>s())),o&&e.addEventListener("versionchange",(e=>o(e.oldVersion,e.newVersion,e)))})).catch((()=>{})),a}const dr=["get","getKey","getAll","getAllKeys","count"],hr=["put","add","delete","clear"],pr=new Map;function fr(e,t){if(!(e instanceof IDBDatabase)||t in e||"string"!=typeof t)return;if(pr.get(t))return pr.get(t);const n=t.replace(/FromIndex$/,""),r=t!==n,o=hr.includes(n);if(!(n in(r?IDBIndex:IDBObjectStore).prototype)||!o&&!dr.includes(n))return;const s=async function(e,...t){const s=this.transaction(e,o?"readwrite":"readonly");let i=s.store;return r&&(i=i.index(t.shift())),(await Promise.all([i[n](...t),o&&s.done]))[0]};return pr.set(t,s),s}sr((e=>({...e,get:(t,n,r)=>fr(t,n)||e.get(t,n,r),has:(t,n)=>!!fr(t,n)||e.has(t,n)})));const vr=["continue","continuePrimaryKey","advance"],mr={},gr=new WeakMap,yr=new WeakMap,Sr={get(e,t){if(!vr.includes(t))return e[t];let n=mr[t];return n||(n=mr[t]=function(...e){gr.set(this,yr.get(this)[t](...e))}),n}};async function*br(...e){let t=this;if(t instanceof IDBCursor||(t=await t.openCursor(...e)),!t)return;const n=new Proxy(t,Sr);for(yr.set(n,t),rr.set(n,lr(t));t;)yield n,t=await(gr.get(n)||t.continue()),gr.delete(n)}function Ir(e,t){return t===Symbol.asyncIterator&&Qn(e,[IDBIndex,IDBObjectStore,IDBCursor])||"iterate"===t&&Qn(e,[IDBIndex,IDBObjectStore])}sr((e=>({...e,get:(t,n,r)=>Ir(t,n)?br:e.get(t,n,r),has:(t,n)=>Ir(t,n)||e.has(t,n)})));var wr,Cr=function(){function e(e){var t,n,r,o=this;this.minInterval=500,this.maxInterval=1e4,this.maxPersistedEventsSize=1e6,this.interval=this.minInterval,this._timeAtLastSplit=Date.now(),this.shouldSplitEventsList=function(e,t){var n=new Blob([t]).size;return new Blob(e).size+n>=o.maxPersistedEventsSize||!!(Date.now()-o.timeAtLastSplit>o.interval&&e.length)&&(o.interval=Math.min(o.maxInterval,o.interval+o.minInterval),o._timeAtLastSplit=Date.now(),!0)},this.loggerProvider=e.loggerProvider,this.minInterval=null!==(t=e.minInterval)&&void 0!==t?t:this.minInterval,this.maxInterval=null!==(n=e.maxInterval)&&void 0!==n?n:this.maxInterval,this.maxPersistedEventsSize=null!==(r=e.maxPersistedEventsSize)&&void 0!==r?r:this.maxPersistedEventsSize}return Object.defineProperty(e.prototype,"timeAtLastSplit",{get:function(){return this._timeAtLastSplit},enumerable:!1,configurable:!0}),e}();!function(e){e.RECORDING="recording",e.SENT="sent"}(wr||(wr={}));var kr,Mr,Er,Tr="sessionCurrentSequence",Or="sequencesToSend",xr=function(){var e=L();return new Promise((function(t,n){if(!e)return n(new Error("Global scope not found"));if(!e.indexedDB)return n(new Error("Session Replay: cannot find indexedDB"));try{var r=e.indexedDB.open("keyval-store");r.onupgradeneeded=function(){1===r.result.version&&(r.result.close(),r.transaction&&r.transaction.abort(),e.indexedDB.deleteDatabase("keyval-store"),t())},r.onsuccess=function(){t(r.result)}}catch(e){n(e)}}))},Rr=function(e){return a(void 0,void 0,void 0,(function(){var t;return c(this,(function(n){switch(n.label){case 0:return e.length>0?(10,t=e.splice(0,10),[4,Promise.all(t)]):[3,2];case 1:return n.sent(),[3,0];case 2:return[2]}}))}))},Nr=function(e){var t,n;return e.objectStoreNames.contains(Tr)||(n=e.createObjectStore(Tr,{keyPath:"sessionId"})),e.objectStoreNames.contains(Or)||(t=e.createObjectStore(Or,{keyPath:"sequenceId",autoIncrement:!0})).createIndex("sessionId","sessionId"),{sequencesStore:t,currentSequenceStore:n}},Dr=function(e){return a(void 0,void 0,void 0,(function(){return c(this,(function(t){switch(t.label){case 0:return[4,ur(e,1,{upgrade:Nr})];case 1:return[2,t.sent()]}}))}))},Lr=function(e){function t(t){var n=e.call(this,t)||this;return n.getSequencesToSend=function(){return a(n,void 0,void 0,(function(){var e,t,n,r,o,s;return c(this,(function(i){switch(i.label){case 0:return i.trys.push([0,5,,6]),e=[],[4,this.db.transaction("sequencesToSend").store.openCursor()];case 1:t=i.sent(),i.label=2;case 2:return t?(n=t.value,r=n.sessionId,o=n.events,e.push({events:o,sequenceId:t.key,sessionId:r}),[4,t.continue()]):[3,4];case 3:return t=i.sent(),[3,2];case 4:return[2,e];case 5:return s=i.sent(),this.loggerProvider.warn("".concat(Xn,": ").concat(s)),[3,6];case 6:return[2,void 0]}}))}))},n.storeCurrentSequence=function(e){return a(n,void 0,void 0,(function(){var t,n,r;return c(this,(function(o){switch(o.label){case 0:return o.trys.push([0,4,,5]),[4,this.db.get(Tr,e)];case 1:return(t=o.sent())?[4,this.db.put(Or,{sessionId:e,events:t.events})]:[2,void 0];case 2:return n=o.sent(),[4,this.db.put(Tr,{sessionId:e,events:[]})];case 3:return o.sent(),[2,i(i({},t),{sessionId:e,sequenceId:n})];case 4:return r=o.sent(),this.loggerProvider.warn("".concat(Xn,": ").concat(r)),[3,5];case 5:return[2,void 0]}}))}))},n.addEventToCurrentSequence=function(e,t){return a(n,void 0,void 0,(function(){var n,r,o,s,i,a;return c(this,(function(c){switch(c.label){case 0:return c.trys.push([0,10,,11]),[4,(n=this.db.transaction(Tr,"readwrite")).store.get(e)];case 1:return(r=c.sent())?[3,3]:[4,n.store.put({sessionId:e,events:[t]})];case 2:return c.sent(),[2];case 3:return o=void 0,this.shouldSplitEventsList(r.events,t)?(o=r.events,[4,n.store.put({sessionId:e,events:[t]})]):[3,5];case 4:return c.sent(),[3,7];case 5:return s=r.events.concat(t),[4,n.store.put({sessionId:e,events:s})];case 6:c.sent(),c.label=7;case 7:return[4,n.done];case 8:return c.sent(),o?[4,this.storeSendingEvents(e,o)]:[2,void 0];case 9:return(i=c.sent())?[2,{events:o,sessionId:e,sequenceId:i}]:[2,void 0];case 10:return a=c.sent(),this.loggerProvider.warn("".concat(Xn,": ").concat(a)),[3,11];case 11:return[2,void 0]}}))}))},n.storeSendingEvents=function(e,t){return a(n,void 0,void 0,(function(){var n;return c(this,(function(r){switch(r.label){case 0:return r.trys.push([0,2,,3]),[4,this.db.put(Or,{sessionId:e,events:t})];case 1:return[2,r.sent()];case 2:return n=r.sent(),this.loggerProvider.warn("".concat(Xn,": ").concat(n)),[3,3];case 3:return[2,void 0]}}))}))},n.cleanUpSessionEventsStore=function(e,t){return a(n,void 0,void 0,(function(){var e;return c(this,(function(n){switch(n.label){case 0:if(!t)return[2];n.label=1;case 1:return n.trys.push([1,3,,4]),[4,this.db.delete(Or,t)];case 2:return n.sent(),[3,4];case 3:return e=n.sent(),this.loggerProvider.warn("".concat(Xn,": ").concat(e)),[3,4];case 4:return[2]}}))}))},n.transitionFromKeyValStore=function(e){return a(n,void 0,void 0,(function(){var t,n,r,o,s,i,l,u,d=this;return c(this,(function(p){switch(p.label){case 0:return p.trys.push([0,6,,7]),[4,xr()];case 1:if(!(t=p.sent()))return[2];n=function(e,t){return a(d,void 0,void 0,(function(){var n,r,o=this;return c(this,(function(s){switch(s.label){case 0:return n=t.sessionSequences,r=[],Object.keys(n).forEach((function(s){var i=parseInt(s,10),l=n[i];if(i===t.currentSequenceId){var u=l.events.map((function(t){return a(o,void 0,void 0,(function(){return c(this,(function(n){return[2,this.addEventToCurrentSequence(e,t)]}))}))}));r.concat(u)}else l.status!==wr.SENT&&r.push(o.storeSendingEvents(e,l.events))})),[4,Rr(r)];case 1:return s.sent(),[2]}}))}))},r="".concat(h,"_").concat(this.apiKey.substring(0,10)),p.label=2;case 2:return p.trys.push([2,4,,5]),o=t.transaction("keyval").objectStore("keyval").getAll(r),s=new Promise((function(t){o.onsuccess=function(r){return a(d,void 0,void 0,(function(){var o,s,i,a=this;return c(this,(function(c){switch(c.label){case 0:return o=r&&r.target.result,(s=o&&o[0])?(i=[],Object.keys(s).forEach((function(t){var r=parseInt(t,10),o=s[r];if(e===r)i.push(n(r,o));else{var c=o.sessionSequences;Object.keys(c).forEach((function(e){var t=parseInt(e,10);c[t].status!==wr.SENT&&i.push(a.storeSendingEvents(r,c[t].events))}))}})),[4,Rr(i)]):[3,2];case 1:c.sent(),c.label=2;case 2:return t(),[2]}}))}))}})),[4,s];case 3:return p.sent(),(i=L())&&i.indexedDB.deleteDatabase("keyval-store"),[3,5];case 4:return l=p.sent(),this.loggerProvider.warn("Failed to transition session replay events from keyval to new store: ".concat(l)),[3,5];case 5:return[3,7];case 6:return u=p.sent(),this.loggerProvider.warn("Failed to access keyval store: ".concat(u,". For more information, visit: https://www.docs.developers.amplitude.com/session-replay/sdks/standalone/#indexeddb-best-practices")),[3,7];case 7:return[2]}}))}))},n.apiKey=t.apiKey,n.db=t.db,n}return n(t,e),t.new=function(e,n,r){return a(this,void 0,void 0,(function(){var o,s,a,l,u;return c(this,(function(c){switch(c.label){case 0:return c.trys.push([0,3,,4]),o="replay"===e?"":"_".concat(e),s="".concat(n.apiKey.substring(0,10),"_amp_session_replay_events").concat(o),[4,Dr(s)];case 1:return a=c.sent(),[4,(l=new t(i(i({},n),{db:a}))).transitionFromKeyValStore(r)];case 2:return c.sent(),[2,l];case 3:return u=c.sent(),n.loggerProvider.warn("".concat(Xn,": ").concat(u)),[3,4];case 4:return[2]}}))}))},t.prototype.getCurrentSequenceEvents=function(e){return a(this,void 0,void 0,(function(){var t,n,r,o,s,i,a;return c(this,(function(c){switch(c.label){case 0:return e?[4,this.db.get("sessionCurrentSequence",e)]:[3,2];case 1:return(o=c.sent())?[2,[o]]:[2,void 0];case 2:t=[],c.label=3;case 3:return c.trys.push([3,8,9,10]),[4,this.db.getAll("sessionCurrentSequence")];case 4:n=l.apply(void 0,[c.sent()]),r=n.next(),c.label=5;case 5:if(r.done)return[3,7];o=r.value,t.push(o),c.label=6;case 6:return r=n.next(),[3,5];case 7:return[3,10];case 8:return s=c.sent(),i={error:s},[3,10];case 9:try{r&&!r.done&&(a=n.return)&&a.call(n)}finally{if(i)throw i.error}return[7];case 10:return[2,t]}}))}))},t}(Cr),_r=function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t.finalizedSequences={},t.sequences={},t.sequenceId=0,t}return n(t,e),t.prototype.resetCurrentSequence=function(e){this.sequences[e]=[]},t.prototype.addSequence=function(e){var t=this.sequenceId++,n=d([],u(this.sequences[e]),!1);return this.finalizedSequences[t]={sessionId:e,events:n},this.resetCurrentSequence(e),{sequenceId:t,events:n,sessionId:e}},t.prototype.getSequencesToSend=function(){return a(this,void 0,void 0,(function(){return c(this,(function(e){return[2,Object.entries(this.finalizedSequences).map((function(e){var t=u(e,2),n=t[0],r=t[1],o=r.sessionId,s=r.events;return{sequenceId:Number(n),sessionId:o,events:s}}))]}))}))},t.prototype.storeCurrentSequence=function(e){return a(this,void 0,void 0,(function(){return c(this,(function(t){return this.sequences[e]?[2,this.addSequence(e)]:[2,void 0]}))}))},t.prototype.addEventToCurrentSequence=function(e,t){return a(this,void 0,void 0,(function(){var n;return c(this,(function(r){return this.sequences[e]||this.resetCurrentSequence(e),this.shouldSplitEventsList(this.sequences[e],t)&&(n=this.addSequence(e)),this.sequences[e].push(t),[2,n]}))}))},t.prototype.storeSendingEvents=function(e,t){return a(this,void 0,void 0,(function(){return c(this,(function(n){return this.finalizedSequences[this.sequenceId]={sessionId:e,events:t},[2,this.sequenceId++]}))}))},t.prototype.cleanUpSessionEventsStore=function(e,t){return a(this,void 0,void 0,(function(){return c(this,(function(e){return void 0!==t&&delete this.finalizedSequences[t],[2]}))}))},t}(Cr),Pr=function(e){var t=e.config,n=e.sessionId,r=e.minInterval,o=e.maxInterval,s=e.type,i=e.payloadBatcher,l=e.storeType;return a(void 0,void 0,void 0,(function(){function e(e){return void 0===e&&(e=!1),a(this,void 0,void 0,(function(){return c(this,(function(t){return[2,u.flush(e)]}))}))}var u,d,h,p,f,v,m,g,y;return c(this,(function(S){switch(S.label){case 0:return u=new Zn({loggerProvider:t.loggerProvider,payloadBatcher:i}),d=function(){return new _r({loggerProvider:t.loggerProvider,maxInterval:o,minInterval:r})},h=function(){return a(void 0,void 0,void 0,(function(){var e;return c(this,(function(i){switch(i.label){case 0:return[4,Lr.new(s,{loggerProvider:t.loggerProvider,minInterval:r,maxInterval:o,apiKey:t.apiKey},n)];case 1:return e=i.sent(),t.loggerProvider.log("Failed to initialize idb store, falling back to memory store."),[2,null!=e?e:d()]}}))}))},"idb"!==l?[3,2]:[4,h()];case 1:return f=S.sent(),[3,3];case 2:f=d(),S.label=3;case 3:return p=f,v=function(e){var n=e.events,r=e.sessionId,o=e.deviceId,i=e.sequenceId;t.debugMode&&Gn().then((function(e){var n=e.totalStorageSize,r=e.percentOfQuota,o=e.usageDetails;t.loggerProvider.debug("Total storage size: ".concat(n," KB, percentage of quota: ").concat(r,"%, usage details: ").concat(o))})).catch((function(){})),u.sendEventsList({events:n,sessionId:r,flushMaxRetries:t.flushMaxRetries,apiKey:t.apiKey,deviceId:o,sampleRate:t.sampleRate,serverZone:t.serverZone,version:t.version,type:s,onComplete:function(){return a(void 0,void 0,void 0,(function(){return c(this,(function(e){switch(e.label){case 0:return[4,p.cleanUpSessionEventsStore(r,i)];case 1:return e.sent(),[2]}}))}))}})},m=function(e){var n=e.sessionId,r=e.deviceId;p.storeCurrentSequence(n).then((function(e){e&&v({sequenceId:e.sequenceId,events:e.events,sessionId:e.sessionId,deviceId:r})})).catch((function(e){t.loggerProvider.warn("Failed to get current sequence of session replay events for session:",e)}))},g=function(e){var t=e.deviceId;return a(void 0,void 0,void 0,(function(){var e;return c(this,(function(n){switch(n.label){case 0:return[4,p.getSequencesToSend()];case 1:return(e=n.sent())&&e.forEach((function(e){v({sequenceId:e.sequenceId,events:e.events,sessionId:e.sessionId,deviceId:t})})),[2]}}))}))},y=function(e){var n=e.event,r=e.sessionId,o=e.deviceId;p.addEventToCurrentSequence(r,n.data).then((function(e){return e&&v({sequenceId:e.sequenceId,events:e.events,sessionId:e.sessionId,deviceId:o})})).catch((function(e){t.loggerProvider.warn("Failed to add event to session replay capture:",e)}))},[2,{sendCurrentSequenceEvents:m,addEvent:y,sendStoredEvents:g,flush:e}]}}))}))},Ar=function(){function e(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];var n=new Map;e.forEach((function(e){n.set(e.name,e.manager)})),this.managers=n}return e.prototype.sendStoredEvents=function(e){return a(this,void 0,void 0,(function(){var t;return c(this,(function(n){switch(n.label){case 0:return t=[],this.managers.forEach((function(n){t.push(n.sendStoredEvents(e))})),[4,Promise.all(t)];case 1:return n.sent(),[2]}}))}))},e.prototype.addEvent=function(e){var t,n=e.sessionId,r=e.event,o=e.deviceId;null===(t=this.managers.get(r.type))||void 0===t||t.addEvent({sessionId:n,event:r,deviceId:o})},e.prototype.sendCurrentSequenceEvents=function(e){var t=e.sessionId,n=e.deviceId;this.managers.forEach((function(e){e.sendCurrentSequenceEvents({sessionId:t,deviceId:n})}))},e.prototype.flush=function(e){return a(this,void 0,void 0,(function(){var t;return c(this,(function(n){switch(n.label){case 0:return t=[],this.managers.forEach((function(n){t.push(n.flush(e))})),[4,Promise.all(t)];case 1:return n.sent(),[2]}}))}))},e}(),Fr=(e=>(e[e.MouseUp=0]="MouseUp",e[e.MouseDown=1]="MouseDown",e[e.Click=2]="Click",e[e.ContextMenu=3]="ContextMenu",e[e.DblClick=4]="DblClick",e[e.Focus=5]="Focus",e[e.Blur=6]="Blur",e[e.TouchStart=7]="TouchStart",e[e.TouchMove_Departed=8]="TouchMove_Departed",e[e.TouchEnd=9]="TouchEnd",e[e.TouchCancel=10]="TouchCancel",e))(Fr||{});function qr(e,t){if(Er=new Date,e.nodeType!==Node.ELEMENT_NODE)throw new Error("Can't generate CSS selector for non-element node type.");if("html"===e.tagName.toLowerCase())return"html";var n={root:document.body,idName:function(e){return!0},className:function(e){return!0},tagName:function(e){return!0},attr:function(e,t){return!1},seedMinLength:1,optimizedMinLength:2,threshold:1e3,maxNumberOfTries:1e4,timeoutMs:void 0};kr=i(i({},n),t),Mr=function(e,t){if(e.nodeType===Node.DOCUMENT_NODE)return e;if(e===t.root)return e.ownerDocument;return e}(kr.root,n);var r=Ur(e,"all",(function(){return Ur(e,"two",(function(){return Ur(e,"one",(function(){return Ur(e,"none")}))}))}));if(r){var o=Xr(Jr(r,e));return o.length>0&&(r=o[0]),Wr(r)}throw new Error("Selector was not found.")}function Ur(e,t,n){for(var r=null,o=[],s=e,i=0,a=function(){var e,a,c=(new Date).getTime()-Er.getTime();if(void 0!==kr.timeoutMs&&c>kr.timeoutMs)throw new Error("Timeout: Can't find a unique selector after ".concat(c,"ms"));var h=Vr(function(e){var t=e.getAttribute("id");if(t&&kr.idName(t))return{name:"#"+CSS.escape(t),penalty:0};return null}(s))||Vr.apply(void 0,d([],u(function(e){var t=Array.from(e.attributes).filter((function(e){return kr.attr(e.name,e.value)}));return t.map((function(e){return{name:"[".concat(CSS.escape(e.name),'="').concat(CSS.escape(e.value),'"]'),penalty:.5}}))}(s)),!1))||Vr.apply(void 0,d([],u(function(e){var t=Array.from(e.classList).filter(kr.className);return t.map((function(e){return{name:"."+CSS.escape(e),penalty:1}}))}(s)),!1))||Vr(function(e){var t=e.tagName.toLowerCase();if(kr.tagName(t))return{name:t,penalty:2};return null}(s))||[{name:"*",penalty:3}],p=function(e){var t=e.parentNode;if(!t)return null;var n=t.firstChild;if(!n)return null;var r=0;for(;n&&(n.nodeType===Node.ELEMENT_NODE&&r++,n!==e);)n=n.nextSibling;return r}(s);if("all"==t)p&&(h=h.concat(h.filter(Hr).map((function(e){return Gr(e,p)}))));else if("two"==t)h=h.slice(0,1),p&&(h=h.concat(h.filter(Hr).map((function(e){return Gr(e,p)}))));else if("one"==t){var f=u(h=h.slice(0,1),1)[0];p&&Hr(f)&&(h=[Gr(f,p)])}else"none"==t&&(h=[{name:"*",penalty:3}],p&&(h=[Gr(h[0],p)]));try{for(var v=(e=void 0,l(h)),m=v.next();!m.done;m=v.next()){(f=m.value).level=i}}catch(t){e={error:t}}finally{try{m&&!m.done&&(a=v.return)&&a.call(v)}finally{if(e)throw e.error}}if(o.push(h),o.length>=kr.seedMinLength&&(r=jr(o,n)))return"break";s=s.parentElement,i++};s;){if("break"===a())break}return r||(r=jr(o,n)),!r&&n?n():r}function jr(e,t){var n,r,o=Xr($r(e));if(o.length>kr.threshold)return t?t():null;try{for(var s=l(o),i=s.next();!i.done;i=s.next()){var a=i.value;if(zr(a))return a}}catch(e){n={error:e}}finally{try{i&&!i.done&&(r=s.return)&&r.call(s)}finally{if(n)throw n.error}}return null}function Wr(e){for(var t=e[0],n=t.name,r=1;r<e.length;r++){var o=e[r].level||0;n=t.level===o-1?"".concat(e[r].name," > ").concat(n):"".concat(e[r].name," ").concat(n),t=e[r]}return n}function Br(e){return e.map((function(e){return e.penalty})).reduce((function(e,t){return e+t}),0)}function zr(e){var t=Wr(e);switch(Mr.querySelectorAll(t).length){case 0:throw new Error("Can't select any node with this selector: ".concat(t));case 1:return!0;default:return!1}}function Gr(e,t){return{name:e.name+":nth-child(".concat(t,")"),penalty:e.penalty+1}}function Hr(e){return"html"!==e.name&&!e.name.startsWith("#")}function Vr(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];var n=e.filter(Kr);return n.length>0?n:null}function Kr(e){return null!=e}function $r(e,t){var n,r,o,s,i,a;return void 0===t&&(t=[]),c(this,(function(c){switch(c.label){case 0:if(!(e.length>0))return[3,9];c.label=1;case 1:c.trys.push([1,6,7,8]),n=l(e[0]),r=n.next(),c.label=2;case 2:return r.done?[3,5]:(o=r.value,[5,l($r(e.slice(1,e.length),t.concat(o)))]);case 3:c.sent(),c.label=4;case 4:return r=n.next(),[3,2];case 5:return[3,8];case 6:return s=c.sent(),i={error:s},[3,8];case 7:try{r&&!r.done&&(a=n.return)&&a.call(n)}finally{if(i)throw i.error}return[7];case 8:return[3,11];case 9:return[4,t];case 10:c.sent(),c.label=11;case 11:return[2]}}))}function Xr(e){return d([],u(e),!1).sort((function(e,t){return Br(e)-Br(t)}))}function Jr(e,t,n){var r,o,s;return void 0===n&&(n={counter:0,visited:new Map}),c(this,(function(i){switch(i.label){case 0:if(!(e.length>2&&e.length>kr.optimizedMinLength))return[3,5];r=1,i.label=1;case 1:return r<e.length-1?n.counter>kr.maxNumberOfTries?[2]:(n.counter+=1,(o=d([],u(e),!1)).splice(r,1),s=Wr(o),n.visited.has(s)?[2]:zr(o)&&function(e,t){return Mr.querySelector(Wr(e))===t}(o,t)?[4,o]:[3,4]):[3,5];case 2:return i.sent(),n.visited.set(s,!0),[5,l(Jr(o,t,n))];case 3:i.sent(),i.label=4;case 4:return r++,[3,1];case 5:return[2]}}))}var Zr=function(e){var t=e.version,n=e.events,r=[];return n.forEach((function(e){var t=JSON.parse(e);t.count=1,"click"===t.type&&r.push(t)})),{version:t,events:r}},Qr=function(e){var t=e.version,n=e.events,r=[];n.forEach((function(e){var t=JSON.parse(e);"click"===t.type&&r.push(t)}));var o=r.reduce((function(e,t){var n=t.x,r=t.y,o=t.selector,s=t.timestamp,a=s-s%36e5,c="".concat(n,":").concat(r,":").concat(null!=o?o:"",":").concat(a);return e[c]?e[c].count+=1:e[c]=i(i({},t),{timestamp:a,count:1}),e}),{});return{version:t,events:Object.values(o)}},Yr=function(e){var t=e.eventsManager,n=e.sessionId,r=e.deviceIdFn;return function(e){if(e.type===Fr.Click){var o=L();if(o){var s=o.location,i=o.innerHeight,a=o.innerWidth;if(s){var c,l=e.x,u=e.y,d=$t.mirror.getNode(e.id);d&&(c=qr(d));var h=Le(o),p={x:l+h.left,y:u+h.top,selector:c,viewportHeight:i,viewportWidth:a,pageUrl:s.href,timestamp:Date.now(),type:"click"},f=r();f&&t.addEvent({sessionId:n,event:{type:"interaction",data:JSON.stringify(p)},deviceId:f})}}}}},eo=function(){function e(e,t){var n=L();n&&n.navigator&&"function"==typeof n.navigator.sendBeacon?this.sendBeacon=function(e,t){try{if(n.navigator.sendBeacon(e,JSON.stringify(t)))return!0}catch(e){}return!1}:this.sendBeacon=function(){return!1},this.sendXhr=function(e,t){var n=new XMLHttpRequest;return n.open("POST",e,!0),n.setRequestHeader("Accept","*/*"),n.send(JSON.stringify(t)),!0},this.basePageUrl=zn(t.serverZone),this.apiKey=t.apiKey,this.context=e}return e.prototype.send=function(e,t){var n=this.context,r=n.sessionId,o=n.type,s=new URLSearchParams({device_id:e,session_id:String(r),type:String(o),api_key:this.apiKey}),i="".concat(this.basePageUrl,"?").concat(s.toString());this.sendBeacon(i,t)||this.sendXhr(i,t)},e}(),to=_e,no=Pe,ro=function(){function e(e){var t=this;this.timestamp=Date.now(),this.hook=function(e){t.update(e)},this.send=function(e){return function(n){var r=e(),o=L();o&&r&&t.transport.send(r,{version:1,events:[{maxScrollX:t._maxScrollX,maxScrollY:t._maxScrollY,maxScrollWidth:t._maxScrollWidth,maxScrollHeight:t._maxScrollHeight,viewportHeight:to(),viewportWidth:no(),pageUrl:o.location.href,timestamp:t.timestamp,type:"scroll"}]})}},this._maxScrollX=0,this._maxScrollY=0,this._maxScrollWidth=no(),this._maxScrollHeight=to(),this.transport=e}return e.default=function(t,n){return new e(new eo(t,n))},Object.defineProperty(e.prototype,"maxScrollX",{get:function(){return this._maxScrollX},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"maxScrollY",{get:function(){return this._maxScrollY},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"maxScrollWidth",{get:function(){return this._maxScrollWidth},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"maxScrollHeight",{get:function(){return this._maxScrollHeight},enumerable:!1,configurable:!0}),e.prototype.update=function(e){var t=Date.now();if(e.x>this._maxScrollX){var n=no();this._maxScrollX=e.x;var r=e.x+n;r>this._maxScrollWidth&&(this._maxScrollWidth=r),this.timestamp=t}if(e.y>this._maxScrollY){var o=to();this._maxScrollY=e.y;var s=e.y+o;s>this._maxScrollHeight&&(this._maxScrollHeight=s),this.timestamp=t}},e}(),oo=function(e){var t=e.sessionId,n=e.deviceId;this.deviceId=n,this.sessionId=t,t&&n&&(this.sessionReplayId=function(e,t){return"".concat(t,"/").concat(e)}(t,n))},so=function(){function e(e,t,n){var r,o=this;this.taskQueue=[],this.isProcessing=!1,this.compressEvent=function(e){var t=Nn(e);return JSON.stringify(t)},this.addCompressedEvent=function(e,t){var n=o.compressEvent(e);o.eventsManager&&o.deviceId&&o.eventsManager.addEvent({event:{type:"replay",data:n},sessionId:t,deviceId:o.deviceId})};var s=L();this.canUseIdleCallback=s&&"requestIdleCallback"in s,this.eventsManager=e,this.config=t,this.deviceId=n,this.timeout=(null===(r=t.performanceConfig)||void 0===r?void 0:r.timeout)||2e3}return e.prototype.scheduleIdleProcessing=function(){var e=this;this.isProcessing||(this.isProcessing=!0,requestIdleCallback((function(t){e.processQueue(t)}),{timeout:this.timeout}))},e.prototype.enqueueEvent=function(e,t){var n;this.canUseIdleCallback&&(null===(n=this.config.performanceConfig)||void 0===n?void 0:n.enabled)?(this.config.loggerProvider.debug("Enqueuing event for processing during idle time."),this.taskQueue.push({event:e,sessionId:t}),this.scheduleIdleProcessing()):(this.config.loggerProvider.debug("Processing event without idle callback."),this.addCompressedEvent(e,t))},e.prototype.processQueue=function(e){for(var t=this;this.taskQueue.length>0&&(e.timeRemaining()>0||e.didTimeout);){var n=this.taskQueue.shift();if(n){var r=n.event,o=n.sessionId;this.addCompressedEvent(r,o)}}this.taskQueue.length>0?requestIdleCallback((function(e){t.processQueue(e)}),{timeout:this.timeout}):this.isProcessing=!1},e}(),io=function(){function e(){var e=this;this.name="@amplitude/session-replay-browser",this.recordCancelCallback=null,this.eventCount=0,this.pageLeaveFns=[],this.teardownEventListeners=function(t){var n=L();n&&(n.removeEventListener("blur",e.blurListener),n.removeEventListener("focus",e.focusListener),!t&&n.addEventListener("blur",e.blurListener),!t&&n.addEventListener("focus",e.focusListener),n.self&&"onpagehide"in n.self?(n.removeEventListener("pagehide",e.pageLeaveListener),!t&&n.addEventListener("pagehide",e.pageLeaveListener)):(n.removeEventListener("beforeunload",e.pageLeaveListener),!t&&n.addEventListener("beforeunload",e.pageLeaveListener)))},this.blurListener=function(){e.sendEvents()},this.focusListener=function(){e.recordEvents()},this.pageLeaveListener=function(t){e.pageLeaveFns.forEach((function(e){e(t)}))},this.addCustomRRWebEvent=function(t,n,r){return void 0===n&&(n={}),void 0===r&&(r=!0),a(e,void 0,void 0,(function(){var e,o,s,a;return c(this,(function(c){switch(c.label){case 0:return c.trys.push([0,3,,4]),e=void 0,(o=this.config)?(e={config:Hn(o),version:Jn},r?[4,Gn()]:[3,2]):[3,2];case 1:s=c.sent(),e=i(i({},s),e),c.label=2;case 2:return this.recordCancelCallback?$t.addCustomEvent(t,i(i({},n),e)):this.loggerProvider.debug("Not able to add custom replay capture event ".concat(t," due to no ongoing recording.")),[3,4];case 3:return a=c.sent(),this.loggerProvider.debug("Error while adding custom replay capture event: ",a),[3,4];case 4:return[2]}}))}))},this.stopRecordingEvents=function(){try{e.loggerProvider.log("Session Replay capture stopping."),e.recordCancelCallback&&e.recordCancelCallback(),e.recordCancelCallback=null}catch(n){var t=n;e.loggerProvider.warn("Error occurred while stopping replay capture: ".concat(t.toString()))}},this.loggerProvider=new m}return e.prototype.init=function(e,t){return f(this._init(e,t))},e.prototype._init=function(e,t){var n,r,o,s;return a(this,void 0,void 0,(function(){var i,a,l,h,p,f,v,g,y,S,b;return c(this,(function(c){switch(c.label){case 0:return this.loggerProvider=t.loggerProvider||new m,Object.prototype.hasOwnProperty.call(t,"logLevel")&&this.loggerProvider.enable(t.logLevel),this.identifiers=new oo({sessionId:t.sessionId,deviceId:t.deviceId}),i=this,[4,Kn(e,t)];case 1:return i.joinedConfigGenerator=c.sent(),a=this,[4,this.joinedConfigGenerator.generateJoinedConfig(this.identifiers.sessionId)];case 2:a.config=c.sent(),t.sessionId&&(null===(n=this.config.interactionConfig)||void 0===n?void 0:n.enabled)&&(l=ro.default({sessionId:t.sessionId,type:"interaction"},this.config),this.pageLeaveFns=[l.send(this.getDeviceId.bind(this)).bind(l)],this.scrollHook=l.hook.bind(l)),h=[],"idb"!==(p=this.config.storeType)||(null===(r=L())||void 0===r?void 0:r.indexedDB)||(p="memory",this.loggerProvider.warn("Could not use preferred indexedDB storage, reverting to in memory option.")),this.loggerProvider.log("Using ".concat(p," for event storage.")),c.label=3;case 3:return c.trys.push([3,5,,6]),[4,Pr({config:this.config,sessionId:this.identifiers.sessionId,type:"replay",storeType:p})];case 4:return f=c.sent(),h.push({name:"replay",manager:f}),[3,6];case 5:return v=c.sent(),b=v,this.loggerProvider.warn("Error occurred while creating replay events manager: ".concat(b.toString())),[3,6];case 6:if(!(null===(o=this.config.interactionConfig)||void 0===o?void 0:o.enabled))return[3,10];g=this.config.interactionConfig.batch?Qr:Zr,c.label=7;case 7:return c.trys.push([7,9,,10]),[4,Pr({config:this.config,sessionId:this.identifiers.sessionId,type:"interaction",minInterval:null!==(s=this.config.interactionConfig.trackEveryNms)&&void 0!==s?s:3e4,maxInterval:6e4,payloadBatcher:g,storeType:p})];case 8:return y=c.sent(),h.push({name:"interaction",manager:y}),[3,10];case 9:return S=c.sent(),b=S,this.loggerProvider.warn("Error occurred while creating interaction events manager: ".concat(b.toString())),[3,10];case 10:return this.eventsManager=new(Ar.bind.apply(Ar,d([void 0],u(h),!1))),this.eventCompressor=new so(this.eventsManager,this.config,this.getDeviceId()),this.loggerProvider.log("Installing @amplitude/session-replay-browser."),this.teardownEventListeners(!1),this.initialize(!0),[2]}}))}))},e.prototype.setSessionId=function(e,t){return f(this.asyncSetSessionId(e,t))},e.prototype.asyncSetSessionId=function(e,t){return a(this,void 0,void 0,(function(){var n,r,o;return c(this,(function(s){switch(s.label){case 0:return(n=this.identifiers&&this.identifiers.sessionId)&&this.sendEvents(n),r=t||this.getDeviceId(),this.identifiers=new oo({sessionId:e,deviceId:r}),this.joinedConfigGenerator&&n?(o=this,[4,this.joinedConfigGenerator.generateJoinedConfig(this.identifiers.sessionId)]):[3,2];case 1:o.config=s.sent(),s.label=2;case 2:return this.recordEvents(),[2]}}))}))},e.prototype.getSessionReplayProperties=function(){var e,t=this.config,n=this.identifiers;if(!t||!n)return this.loggerProvider.warn("Session replay init has not been called, cannot get session replay properties."),{};var r=this.getShouldRecord(),o={};return r&&((e={})[A]=n.sessionReplayId?n.sessionReplayId:null,o=e,t.debugMode&&(o[q]=JSON.stringify({appHash:Bn(t.apiKey).toString()}))),this.addCustomRRWebEvent(x.GET_SR_PROPS,{shouldRecord:r,eventProperties:o},10===this.eventCount),10===this.eventCount&&(this.eventCount=0),this.eventCount++,o},e.prototype.sendEvents=function(e){var t,n=e||(null===(t=this.identifiers)||void 0===t?void 0:t.sessionId),r=this.getDeviceId();this.eventsManager&&n&&r&&this.eventsManager.sendCurrentSequenceEvents({sessionId:n,deviceId:r})},e.prototype.initialize=function(e){var t;if(void 0===e&&(e=!1),null===(t=this.identifiers)||void 0===t?void 0:t.sessionId){var n=this.getDeviceId();n?(this.eventsManager&&e&&this.eventsManager.sendStoredEvents({deviceId:n}),this.recordEvents()):this.loggerProvider.log("Session is not being recorded due to lack of device id.")}else this.loggerProvider.log("Session is not being recorded due to lack of session id.")},e.prototype.shouldOptOut=function(){var e,t,n,r;(null===(e=this.config)||void 0===e?void 0:e.instanceName)&&(n=(r=this.config.instanceName,void 0===r&&(r="$default_instance"),D.getInstance(r)).identityStore.getIdentity().optOut);return void 0!==n?n:null===(t=this.config)||void 0===t?void 0:t.optOut},e.prototype.getShouldRecord=function(){if(!this.identifiers||!this.config||!this.identifiers.sessionId)return this.loggerProvider.warn("Session is not being recorded due to lack of config, please call sessionReplay.init."),!1;if(!this.config.captureEnabled)return this.loggerProvider.log("Session ".concat(this.identifiers.sessionId," not being captured due to capture being disabled for project or because the remote config could not be fetched.")),!1;if(this.shouldOptOut())return this.loggerProvider.log("Opting session ".concat(this.identifiers.sessionId," out of recording due to optOut config.")),!1;var e,t,n,r=(e=this.identifiers.sessionId,t=this.config.sampleRate,n=Bn(e.toString()),31*Math.abs(n)%1e6/1e6<t);return r||this.loggerProvider.log("Opting session ".concat(this.identifiers.sessionId," out of recording due to sample rate.")),r},e.prototype.getBlockSelectors=function(){var e,t,n,r=null!==(n=null===(t=null===(e=this.config)||void 0===e?void 0:e.privacyConfig)||void 0===t?void 0:t.blockSelector)&&void 0!==n?n:[];if(0!==r.length)return r},e.prototype.getMaskTextSelectors=function(){var e,t,n,r;if("conservative"===(null===(t=null===(e=this.config)||void 0===e?void 0:e.privacyConfig)||void 0===t?void 0:t.defaultMaskLevel))return"*";var o=null===(r=null===(n=this.config)||void 0===n?void 0:n.privacyConfig)||void 0===r?void 0:r.maskSelector;return o||void 0},e.prototype.recordEvents=function(){var e,t=this,n=this.config,r=this.getShouldRecord(),o=null===(e=this.identifiers)||void 0===e?void 0:e.sessionId;if(r&&o&&n){this.stopRecordingEvents();var s=n.privacyConfig;this.loggerProvider.log("Session Replay capture beginning for ".concat(o,".")),this.recordCancelCallback=$t({emit:function(e){if(t.shouldOptOut())return t.loggerProvider.log("Opting session ".concat(o," out of recording due to optOut config.")),t.stopRecordingEvents(),void t.sendEvents();t.eventCompressor&&t.eventCompressor.enqueueEvent(e,o)},inlineStylesheet:n.shouldInlineStylesheet,hooks:{mouseInteraction:this.eventsManager&&Yr({eventsManager:this.eventsManager,sessionId:o,deviceIdFn:this.getDeviceId.bind(this)}),scroll:this.scrollHook},maskAllInputs:!0,maskTextClass:U,blockClass:"amp-block",blockSelector:this.getBlockSelectors(),maskInputFn:Wn("input",s),maskTextFn:Wn("text",s),maskTextSelector:this.getMaskTextSelectors(),recordCanvas:!1,errorHandler:function(e){var n=e;if(n.message.includes("insertRule")&&n.message.includes("CSSStyleSheet"))throw n;if(n._external_)throw n;return t.loggerProvider.warn("Error while capturing replay: ",n.toString()),!0}}),this.addCustomRRWebEvent(x.DEBUG_INFO)}},e.prototype.getDeviceId=function(){var e;return null===(e=this.identifiers)||void 0===e?void 0:e.deviceId},e.prototype.getSessionId=function(){var e;return null===(e=this.identifiers)||void 0===e?void 0:e.sessionId},e.prototype.flush=function(e){var t;return void 0===e&&(e=!1),a(this,void 0,void 0,(function(){return c(this,(function(n){return[2,null===(t=this.eventsManager)||void 0===t?void 0:t.flush(e)]}))}))},e.prototype.shutdown=function(){this.teardownEventListeners(!0),this.stopRecordingEvents(),this.sendEvents()},e}(),ao=function(e){return function(){var t=e.config||W();return{logger:t.loggerProvider,logLevel:t.logLevel}}},co=function(){var e=new io;return{init:w(e.init.bind(e),"init",ao(e)),setSessionId:w(e.setSessionId.bind(e),"setSessionId",ao(e)),getSessionId:w(e.getSessionId.bind(e),"getSessionId",ao(e)),getSessionReplayProperties:w(e.getSessionReplayProperties.bind(e),"getSessionReplayProperties",ao(e)),flush:w(e.flush.bind(e),"flush",ao(e)),shutdown:w(e.shutdown.bind(e),"shutdown",ao(e))}}(),lo=co.init,uo=co.setSessionId,ho=co.getSessionId,po=co.getSessionReplayProperties;co.flush;var fo=co.shutdown,vo="1.8.0",mo=function(){function e(e){this.name="@amplitude/plugin-session-replay-browser",this.type="enrichment",this.options=i({},e),!1!==this.options.forceSessionTracking&&(this.options.forceSessionTracking=!0)}return e.prototype.setup=function(e){var t,n,r,o;return a(this,void 0,void 0,(function(){return c(this,(function(s){switch(s.label){case 0:return e.loggerProvider.log("Installing @amplitude/plugin-session-replay, version ".concat(vo,".")),this.config=e,this.options.forceSessionTracking&&("boolean"==typeof e.defaultTracking?!1===e.defaultTracking&&(e.defaultTracking={pageViews:!1,formInteractions:!1,fileDownloads:!1,sessions:!0}):e.defaultTracking=i(i({},e.defaultTracking),{sessions:!0})),[4,lo(e.apiKey,{instanceName:this.config.instanceName,deviceId:this.config.deviceId,optOut:this.config.optOut,sessionId:this.config.sessionId,loggerProvider:this.config.loggerProvider,logLevel:this.config.logLevel,flushMaxRetries:this.config.flushMaxRetries,serverZone:this.config.serverZone,sampleRate:this.options.sampleRate,privacyConfig:{blockSelector:null===(t=this.options.privacyConfig)||void 0===t?void 0:t.blockSelector,maskSelector:null===(n=this.options.privacyConfig)||void 0===n?void 0:n.maskSelector,unmaskSelector:null===(r=this.options.privacyConfig)||void 0===r?void 0:r.unmaskSelector,defaultMaskLevel:null===(o=this.options.privacyConfig)||void 0===o?void 0:o.defaultMaskLevel},debugMode:this.options.debugMode,configEndpointUrl:this.options.configEndpointUrl,shouldInlineStylesheet:this.options.shouldInlineStylesheet,version:{type:"plugin",version:vo},performanceConfig:this.options.performanceConfig,storeType:this.options.storeType}).promise];case 1:return s.sent(),[2]}}))}))},e.prototype.execute=function(e){return a(this,void 0,void 0,(function(){var t;return c(this,(function(n){switch(n.label){case 0:return this.config.sessionId&&this.config.sessionId!==ho()?[4,uo(this.config.sessionId).promise]:[3,2];case 1:n.sent(),n.label=2;case 2:return this.config.sessionId&&this.config.sessionId===e.session_id&&(t=po(),e.event_properties=i(i({},e.event_properties),t)),[2,Promise.resolve(e)]}}))}))},e.prototype.teardown=function(){return a(this,void 0,void 0,(function(){return c(this,(function(e){return fo(),this.config=null,[2]}))}))},e.prototype.getSessionReplayProperties=function(){return po()},e}(),go=function(e){return new mo(e)};return e.plugin=go,e.sessionReplayPlugin=go,Object.defineProperty(e,"__esModule",{value:!0}),e}({});
