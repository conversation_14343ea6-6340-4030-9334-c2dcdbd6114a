/* Segoe UI font faces */
@font-face {
  font-family: "Segoe UI";
  font-style: normal;
  font-weight: normal;
  src: url("/assets/fonts/Segoe%20UI.ttf") format("truetype");
  font-display: swap;
}

@font-face {
  font-family: "Segoe UI";
  font-style: normal;
  font-weight: bold;
  src: url("/assets/fonts/Segoe%20UI%20Bold.ttf") format("truetype");
  font-display: swap;
}

@font-face {
  font-family: "Segoe UI";
  font-style: italic;
  font-weight: bold;
  src: url("/assets/fonts/Segoe%20UI%20Bold%20Italic.ttf") format("truetype");
  font-display: swap;
}

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --rmred: #ea1a1a;
    --rmred-light: #fca5a5;
    --rmred-dark: #b91c1c;

    --rmgray: #323336;
    --rmgray-light: #525359;
  }

  * {
    font-family: "Segoe UI", -apple-system, BlinkMacSystemFont, system-ui, sans-serif;
  }

  /* Base classes */
  .text-rmred {
    color: var(--rmred);
  }
  .text-rmred-light {
    color: var(--rmred-light);
  }
  .text-rmred-dark {
    color: var(--rmred-dark);
  }

  .bg-rmred {
    background-color: var(--rmred);
  }
  .bg-rmred-light {
    background-color: var(--rmred-light);
  }
  .bg-rmred-dark {
    background-color: var(--rmred-dark);
  }
  .bg-rmgray {
    background-color: var(--rmgray);
  }
  .bg-rmgray-light {
    background-color: var(--rmgray-light);
  }

  /* Hover variants */
  .hover\:bg-rmred:hover {
    background-color: var(--rmred);
  }
  .hover\:bg-rmred-light:hover {
    background-color: var(--rmred-light);
  }
  .hover\:bg-rmred-dark:hover {
    background-color: var(--rmred-dark);
  }
  .hover\:bg-rmgray-light:hover {
    background-color: var(--rmgray-light);
  }

  /* Border classes */
  .border-rmred {
    border-color: var(--rmred);
  }
  .border-rmred-light {
    border-color: var(--rmred-light);
  }
  .border-rmred-dark {
    border-color: var(--rmred-dark);
  }

  /* Custom spacing and typography adjustments for pixel-perfect matching */
  .leading-snug {
    line-height: 1.375;
  }

  /* Ensure consistent letter spacing */
  h1, h2, h3, h4, h5, h6 {
    letter-spacing: -0.025em;
  }

  /* Consistent image border radius - Force include rounded-4xl */
  .rounded-4xl {
    border-radius: 2rem !important;
  }

  /* Ensure proper font weights are applied */
  .font-medium {
    font-weight: 500;
  }

  .font-bold {
    font-weight: 700;
  }

  /* Additional spacing adjustments for perfect matching */
  .w-20 {
    width: 5rem;
  }

  .sm\:w-28 {
    width: 7rem;
  }

  /* Ensure consistent max-width classes */
  .lg\:max-w-5\/12 {
    max-width: 41.666667%;
  }
}