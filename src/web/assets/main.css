@tailwind base;
@tailwind components;
@tailwind utilities;

/* Segoe UI font faces */
@font-face {
  font-family: "Segoe UI";
  font-style: normal;
  font-weight: normal;
  src: url("/assets/fonts/Segoe%20UI.ttf") format("truetype");
  font-display: swap;
}
@font-face {
  font-family: "Segoe UI";
  font-style: normal;
  font-weight: bold;
  src: url("/assets/fonts/Segoe%20UI%20Bold.ttf") format("truetype");
  font-display: swap;
}
@font-face {
  font-family: "Segoe UI";
  font-style: italic;
  font-weight: bold;
  src: url("/assets/fonts/Segoe%20UI%20Bold%20Italic.ttf") format("truetype");
  font-display: swap;
}

/* Set default font to Segoe UI */
@layer base {
  * {
    font-family: "Segoe UI", -apple-system, BlinkMacSystemFont, system-ui, sans-serif !important;
  }
}

/* Disable dark mode - force light theme always */
@media (prefers-color-scheme: dark) {
  :root {
    color-scheme: light !important;
  }
}

/* Tab Styles */
.tab-button {
  @apply w-1/4 px-4 py-4 hover:text-gray-700 hover:bg-gray-50;
}

.tab-button-default {
  @apply text-gray-700 border-b-2 border-transparent hover:border-slate-400;
}

.tab-button-active {
  @apply text-black border-b-2 border-black bg-gray-50;
}

.tab-content {
  @apply flex bg-white px-8 gap-16 pb-16 pt-10 rounded-md;
}

.tab-content-hidden {
  @apply hidden;
}
