/*! tailwindcss v4.0.12 | MIT License | https://tailwindcss.com */
@font-face {
  font-family: "Segoe UI";
  font-style: normal;
  font-weight: normal;
  src: url("./src/assets/fonts/Segoe\ UI.ttf") format("truetype");
  font-display: swap;
}
@font-face {
  font-family: "Segoe UI";
  font-style: normal;
  font-weight: bold;
  src: url("./src/assets/fonts/Segoe\ UI\ Bold.ttf") format("truetype");
  font-display: swap;
}
@font-face {
  font-family: "Segoe UI";
  font-style: italic;
  font-weight: bold;
  src: url("./src/assets/fonts/Segoe\ UI\ Bold.ttf") format("truetype");
  font-display: swap;
}
@layer theme, base, components, utilities;
@layer theme {
  :root, :host {
    --font-sans: ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji",
      "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
    --font-mono: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono",
      "Courier New", monospace;
    --color-red-500: oklch(0.637 0.237 25.331);
    --color-slate-800: oklch(0.279 0.041 260.031);
    --color-gray-50: oklch(0.985 0.002 247.839);
    --color-gray-100: oklch(0.967 0.003 264.542);
    --color-gray-200: oklch(0.928 0.006 264.531);
    --color-gray-300: oklch(0.872 0.01 258.338);
    --color-gray-500: oklch(0.551 0.027 264.364);
    --color-gray-600: oklch(0.446 0.03 256.802);
    --color-gray-700: oklch(0.373 0.034 259.733);
    --color-gray-900: oklch(0.21 0.034 264.665);
    --color-black: #000;
    --color-white: #fff;
    --spacing: 0.25rem;
    --text-xs: 0.75rem;
    --text-xs--line-height: calc(1 / 0.75);
    --text-sm: 0.875rem;
    --text-sm--line-height: calc(1.25 / 0.875);
    --text-base: 1rem;
    --text-base--line-height: calc(1.5 / 1);
    --text-lg: 1.125rem;
    --text-lg--line-height: calc(1.75 / 1.125);
    --text-xl: 1.25rem;
    --text-xl--line-height: calc(1.75 / 1.25);
    --text-2xl: 1.5rem;
    --text-2xl--line-height: calc(2 / 1.5);
    --text-3xl: 1.875rem;
    --text-3xl--line-height: calc(2.25 / 1.875);
    --text-4xl: 2.25rem;
    --text-4xl--line-height: calc(2.5 / 2.25);
    --text-5xl: 3rem;
    --text-5xl--line-height: 1;
    --text-7xl: 4.5rem;
    --text-7xl--line-height: 1;
    --font-weight-medium: 500;
    --font-weight-bold: 700;
    --tracking-wide: 0.025em;
    --leading-snug: 1.375;
    --leading-relaxed: 1.625;
    --radius-md: 0.375rem;
    --radius-lg: 0.5rem;
    --radius-xl: 0.75rem;
    --radius-2xl: 1rem;
    --radius-3xl: 1.5rem;
    --radius-4xl: 2rem;
    --ease-in: cubic-bezier(0.4, 0, 1, 1);
    --ease-out: cubic-bezier(0, 0, 0.2, 1);
    --ease-in-out: cubic-bezier(0.4, 0, 0.2, 1);
    --default-transition-duration: 150ms;
    --default-transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    --default-font-family: var(--font-sans);
    --default-font-feature-settings: var(--font-sans--font-feature-settings);
    --default-font-variation-settings: var(
      --font-sans--font-variation-settings
    );
    --default-mono-font-family: var(--font-mono);
    --default-mono-font-feature-settings: var(
      --font-mono--font-feature-settings
    );
    --default-mono-font-variation-settings: var(
      --font-mono--font-variation-settings
    );
  }
}
@layer base {
  *, ::after, ::before, ::backdrop, ::file-selector-button {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
    border: 0 solid;
  }
  html, :host {
    line-height: 1.5;
    -webkit-text-size-adjust: 100%;
    tab-size: 4;
    font-family: var( --default-font-family, ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji" );
    font-feature-settings: var(--default-font-feature-settings, normal);
    font-variation-settings: var( --default-font-variation-settings, normal );
    -webkit-tap-highlight-color: transparent;
  }
  body {
    line-height: inherit;
  }
  hr {
    height: 0;
    color: inherit;
    border-top-width: 1px;
  }
  abbr:where([title]) {
    -webkit-text-decoration: underline dotted;
    text-decoration: underline dotted;
  }
  h1, h2, h3, h4, h5, h6 {
    font-size: inherit;
    font-weight: inherit;
  }
  a {
    color: inherit;
    -webkit-text-decoration: inherit;
    text-decoration: inherit;
  }
  b, strong {
    font-weight: bolder;
  }
  code, kbd, samp, pre {
    font-family: var( --default-mono-font-family, ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace );
    font-feature-settings: var( --default-mono-font-feature-settings, normal );
    font-variation-settings: var( --default-mono-font-variation-settings, normal );
    font-size: 1em;
  }
  small {
    font-size: 80%;
  }
  sub, sup {
    font-size: 75%;
    line-height: 0;
    position: relative;
    vertical-align: baseline;
  }
  sub {
    bottom: -0.25em;
  }
  sup {
    top: -0.5em;
  }
  table {
    text-indent: 0;
    border-color: inherit;
    border-collapse: collapse;
  }
  :-moz-focusring {
    outline: auto;
  }
  progress {
    vertical-align: baseline;
  }
  summary {
    display: list-item;
  }
  ol, ul, menu {
    list-style: none;
  }
  img, svg, video, canvas, audio, iframe, embed, object {
    display: block;
    vertical-align: middle;
  }
  img, video {
    max-width: 100%;
    height: auto;
  }
  button, input, select, optgroup, textarea, ::file-selector-button {
    font: inherit;
    font-feature-settings: inherit;
    font-variation-settings: inherit;
    letter-spacing: inherit;
    color: inherit;
    border-radius: 0;
    background-color: transparent;
    opacity: 1;
  }
  :where(select:is([multiple], [size])) optgroup {
    font-weight: bolder;
  }
  :where(select:is([multiple], [size])) optgroup option {
    padding-inline-start: 20px;
  }
  ::file-selector-button {
    margin-inline-end: 4px;
  }
  ::placeholder {
    opacity: 1;
    color: color-mix(in oklab, currentColor 50%, transparent);
  }
  textarea {
    resize: vertical;
  }
  ::-webkit-search-decoration {
    -webkit-appearance: none;
  }
  ::-webkit-date-and-time-value {
    min-height: 1lh;
    text-align: inherit;
  }
  ::-webkit-datetime-edit {
    display: inline-flex;
  }
  ::-webkit-datetime-edit-fields-wrapper {
    padding: 0;
  }
  ::-webkit-datetime-edit, ::-webkit-datetime-edit-year-field, ::-webkit-datetime-edit-month-field, ::-webkit-datetime-edit-day-field, ::-webkit-datetime-edit-hour-field, ::-webkit-datetime-edit-minute-field, ::-webkit-datetime-edit-second-field, ::-webkit-datetime-edit-millisecond-field, ::-webkit-datetime-edit-meridiem-field {
    padding-block: 0;
  }
  :-moz-ui-invalid {
    box-shadow: none;
  }
  button, input:where([type="button"], [type="reset"], [type="submit"]), ::file-selector-button {
    appearance: button;
  }
  ::-webkit-inner-spin-button, ::-webkit-outer-spin-button {
    height: auto;
  }
  [hidden]:where(:not([hidden="until-found"])) {
    display: none !important;
  }
}
@layer utilities {
  .diff {
    position: relative;
    display: grid;
    width: 100%;
    overflow: hidden;
    webkit-user-select: none;
    user-select: none;
    direction: ltr;
    container-type: inline-size;
    grid-template-columns: auto 1fr;
    &:focus-visible, &:has(.diff-item-1:focus-visible) {
      outline-style: var(--tw-outline-style);
      outline-width: 2px;
      outline-offset: 1px;
      outline-color: var(--color-base-content);
    }
    &:focus-visible {
      outline-style: var(--tw-outline-style);
      outline-width: 2px;
      outline-offset: 1px;
      outline-color: var(--color-base-content);
      .diff-resizer {
        min-width: 90cqi;
        max-width: 90cqi;
      }
    }
    &:has(.diff-item-2:focus-visible) {
      outline-style: var(--tw-outline-style);
      outline-width: 2px;
      outline-offset: 1px;
      .diff-resizer {
        min-width: 10cqi;
        max-width: 10cqi;
      }
    }
    @supports (-webkit-overflow-scrolling: touch) and (overflow: -webkit-paged-x) {
      &:focus {
        .diff-resizer {
          min-width: 10cqi;
          max-width: 10cqi;
        }
      }
      &:has(.diff-item-1:focus) {
        .diff-resizer {
          min-width: 90cqi;
          max-width: 90cqi;
        }
      }
    }
  }
  .\@container {
    container-type: inline-size;
  }
  .\@container-\[inline-size\] {
    container-type: inline-size;
  }
  .modal {
    pointer-events: none;
    visibility: hidden;
    position: fixed;
    inset: calc(0.25rem * 0);
    margin: calc(0.25rem * 0);
    display: grid;
    height: 100%;
    max-height: none;
    width: 100%;
    max-width: none;
    align-items: center;
    justify-items: center;
    background-color: transparent;
    padding: calc(0.25rem * 0);
    color: inherit;
    overflow-x: hidden;
    transition: translate 0.3s ease-out, visibility 0.3s allow-discrete, background-color 0.3s ease-out, opacity 0.1s ease-out;
    overflow-y: hidden;
    overscroll-behavior: contain;
    z-index: 999;
    &::backdrop {
      display: none;
    }
    &.modal-open, &[open], &:target {
      pointer-events: auto;
      visibility: visible;
      opacity: 100%;
      background-color: oklch(0% 0 0/ 0.4);
      .modal-box {
        translate: 0 0;
        scale: 1;
        opacity: 1;
      }
    }
    @starting-style {
      &.modal-open, &[open], &:target {
        visibility: hidden;
        opacity: 0%;
      }
    }
  }
  .drawer-side {
    pointer-events: none;
    visibility: hidden;
    position: fixed;
    inset-inline-start: calc(0.25rem * 0);
    top: calc(0.25rem * 0);
    z-index: 1;
    grid-column-start: 1;
    grid-row-start: 1;
    display: grid;
    width: 100%;
    grid-template-columns: repeat(1, minmax(0, 1fr));
    grid-template-rows: repeat(1, minmax(0, 1fr));
    align-items: flex-start;
    justify-items: start;
    overflow-x: hidden;
    overflow-y: hidden;
    overscroll-behavior: contain;
    opacity: 0%;
    transition: opacity 0.2s ease-out 0.1s allow-discrete, visibility 0.3s ease-out 0.1s allow-discrete;
    height: 100vh;
    height: 100dvh;
    > .drawer-overlay {
      position: sticky;
      top: calc(0.25rem * 0);
      cursor: pointer;
      place-self: stretch;
      background-color: oklch(0% 0 0 / 40%);
    }
    > * {
      grid-column-start: 1;
      grid-row-start: 1;
    }
    > *:not(.drawer-overlay) {
      will-change: transform;
      transition: translate 0.3s ease-out;
      translate: -100%;
      [dir="rtl"] & {
        translate: 100%;
      }
    }
  }
  .drawer-open {
    > .drawer-side {
      overflow-y: auto;
    }
    > .drawer-toggle {
      display: none;
      & ~ .drawer-side {
        pointer-events: auto;
        visibility: visible;
        position: sticky;
        display: block;
        width: auto;
        overscroll-behavior: auto;
        opacity: 100%;
        & > .drawer-overlay {
          cursor: default;
          background-color: transparent;
        }
        & > *:not(.drawer-overlay) {
          translate: 0%;
          [dir="rtl"] & {
            translate: 0%;
          }
        }
      }
      &:checked ~ .drawer-side {
        pointer-events: auto;
        visibility: visible;
      }
    }
  }
  .modal-toggle {
    position: fixed;
    height: calc(0.25rem * 0);
    width: calc(0.25rem * 0);
    appearance: none;
    opacity: 0%;
    &:checked + .modal {
      pointer-events: auto;
      visibility: visible;
      opacity: 100%;
      background-color: oklch(0% 0 0/ 0.4);
      .modal-box {
        translate: 0 0;
        scale: 1;
        opacity: 1;
      }
    }
    @starting-style {
      &:checked + .modal {
        visibility: hidden;
        opacity: 0%;
      }
    }
  }
  .drawer-toggle {
    position: fixed;
    height: calc(0.25rem * 0);
    width: calc(0.25rem * 0);
    appearance: none;
    opacity: 0%;
    &:checked {
      & ~ .drawer-side {
        pointer-events: auto;
        visibility: visible;
        overflow-y: auto;
        opacity: 100%;
        & > *:not(.drawer-overlay) {
          translate: 0%;
        }
      }
    }
    &:focus-visible ~ .drawer-content label.drawer-button {
      outline: 2px solid;
      outline-offset: 2px;
    }
  }
  .tooltip {
    position: relative;
    display: inline-block;
    --tt-bg: var(--color-neutral);
    --tt-off: calc(100% + 0.5rem);
    --tt-tail: calc(100% + 1px + 0.25rem);
    > :where(.tooltip-content), &:where([data-tip]):before {
      position: absolute;
      max-width: 20rem;
      border-radius: var(--radius-field);
      padding-inline: calc(0.25rem * 2);
      padding-block: calc(0.25rem * 1);
      text-align: center;
      white-space: normal;
      color: var(--color-neutral-content);
      opacity: 0%;
      font-size: 0.875rem;
      line-height: 1.25;
      transition: opacity 0.2s cubic-bezier(0.4, 0, 0.2, 1) 75ms, transform 0.2s cubic-bezier(0.4, 0, 0.2, 1) 75ms;
      background-color: var(--tt-bg);
      width: max-content;
      pointer-events: none;
      z-index: 2;
      --tw-content: attr(data-tip);
      content: var(--tw-content);
    }
    &:after {
      position: absolute;
      position: absolute;
      opacity: 0%;
      background-color: var(--tt-bg);
      transition: opacity 0.2s cubic-bezier(0.4, 0, 0.2, 1) 75ms, transform 0.2s cubic-bezier(0.4, 0, 0.2, 1) 75ms;
      content: "";
      pointer-events: none;
      width: 0.625rem;
      height: 0.25rem;
      display: block;
      mask-repeat: no-repeat;
      mask-position: -1px 0;
      --mask-tooltip: url("data:image/svg+xml,%3Csvg width='10' height='4' viewBox='0 0 8 4' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M0.500009 1C3.5 1 3.00001 4 5.00001 4C7 4 6.5 1 9.5 1C10 1 10 0.499897 10 0H0C-1.99338e-08 0.5 0 1 0.500009 1Z' fill='black'/%3E%3C/svg%3E%0A");
      mask-image: var(--mask-tooltip);
    }
    &.tooltip-open, &[data-tip]:not([data-tip=""]):hover, &:not(:has(.tooltip-content:empty)):has(.tooltip-content):hover, &:has(:focus-visible) {
      > .tooltip-content, &[data-tip]:before, &:after {
        opacity: 100%;
        --tt-pos: 0rem;
        transition: opacity 0.2s cubic-bezier(0.4, 0, 0.2, 1) 0s, transform 0.2s cubic-bezier(0.4, 0, 0.2, 1) 0ms;
      }
    }
    > .tooltip-content, &[data-tip]:before {
      transform: translateX(-50%) translateY(var(--tt-pos, 0.25rem));
      inset: auto auto var(--tt-off) 50%;
    }
    &:after {
      transform: translateX(-50%) translateY(var(--tt-pos, 0.25rem));
      inset: auto auto var(--tt-tail) 50%;
    }
  }
  .tab {
    position: relative;
    display: inline-flex;
    cursor: pointer;
    appearance: none;
    flex-wrap: wrap;
    align-items: center;
    justify-content: center;
    text-align: center;
    webkit-user-select: none;
    user-select: none;
    &:hover {
      @media (hover: hover) {
        color: var(--color-base-content);
      }
    }
    --tab-p: 1rem;
    --tab-bg: var(--color-base-100);
    --tab-border-color: var(--color-base-300);
    --tab-radius-ss: 0;
    --tab-radius-se: 0;
    --tab-radius-es: 0;
    --tab-radius-ee: 0;
    --tab-order: 0;
    --tab-radius-min: calc(0.75rem - var(--border));
    border-color: #0000;
    order: var(--tab-order);
    height: var(--tab-height);
    font-size: 0.875rem;
    padding-inline-start: var(--tab-p);
    padding-inline-end: var(--tab-p);
    &:is(input[type="radio"]) {
      min-width: fit-content;
      &:after {
        content: attr(aria-label);
      }
    }
    &:is(label) {
      position: relative;
      input {
        position: absolute;
        inset: calc(0.25rem * 0);
        cursor: pointer;
        appearance: none;
        opacity: 0%;
      }
    }
    &:checked, &:is(label:has(:checked)), &:is(.tab-active, [aria-selected="true"]) {
      & + .tab-content {
        display: block;
        height: calc(100% - var(--tab-height) + var(--border));
      }
    }
    &:not(:checked, label:has(:checked), :hover, .tab-active, [aria-selected="true"]) {
      color: color-mix(in oklab, var(--color-base-content) 50%, transparent);
    }
    &:not(input):empty {
      flex-grow: 1;
      cursor: default;
    }
    &:focus {
      --tw-outline-style: none;
      outline-style: none;
      @media (forced-colors: active) {
        outline: 2px solid transparent;
        outline-offset: 2px;
      }
    }
    &:focus-visible, &:is(label:has(:checked:focus-visible)) {
      outline: 2px solid currentColor;
      outline-offset: -5px;
    }
    &[disabled] {
      pointer-events: none;
      opacity: 40%;
    }
  }
  .menu {
    display: flex;
    width: fit-content;
    flex-direction: column;
    flex-wrap: wrap;
    padding: calc(0.25rem * 2);
    --menu-active-fg: var(--color-neutral-content);
    --menu-active-bg: var(--color-neutral);
    font-size: 0.875rem;
    :where(li ul) {
      position: relative;
      margin-inline-start: calc(0.25rem * 4);
      padding-inline-start: calc(0.25rem * 2);
      white-space: nowrap;
      &:before {
        position: absolute;
        inset-inline-start: calc(0.25rem * 0);
        top: calc(0.25rem * 3);
        bottom: calc(0.25rem * 3);
        background-color: var(--color-base-content);
        opacity: 10%;
        width: var(--border);
        content: "";
      }
    }
    :where(li > .menu-dropdown:not(.menu-dropdown-show)) {
      display: none;
    }
    :where(li:not(.menu-title) > *:not(ul, details, .menu-title, .btn)), :where(li:not(.menu-title) > details > summary:not(.menu-title)) {
      display: grid;
      grid-auto-flow: column;
      align-content: flex-start;
      align-items: center;
      gap: calc(0.25rem * 2);
      border-radius: var(--radius-field);
      padding-inline: calc(0.25rem * 3);
      padding-block: calc(0.25rem * 1.5);
      text-align: start;
      transition-property: color, background-color, box-shadow;
      transition-duration: 0.2s;
      transition-timing-function: cubic-bezier(0, 0, 0.2, 1);
      grid-auto-columns: minmax(auto, max-content) auto max-content;
      text-wrap: balance;
      user-select: none;
    }
    :where(li > details > summary) {
      --tw-outline-style: none;
      outline-style: none;
      @media (forced-colors: active) {
        outline: 2px solid transparent;
        outline-offset: 2px;
      }
      &::-webkit-details-marker {
        display: none;
      }
    }
    :where(li > details > summary), :where(li > .menu-dropdown-toggle) {
      &:after {
        justify-self: flex-end;
        display: block;
        height: 0.375rem;
        width: 0.375rem;
        rotate: -135deg;
        translate: 0 -1px;
        transition-property: rotate, translate;
        transition-duration: 0.2s;
        content: "";
        transform-origin: 50% 50%;
        box-shadow: 2px 2px inset;
        pointer-events: none;
      }
    }
    :where(li > details[open] > summary):after, :where(li > .menu-dropdown-toggle.menu-dropdown-show):after {
      rotate: 45deg;
      translate: 0 1px;
    }
    :where( li:not(.menu-title, .disabled) > *:not(ul, details, .menu-title), li:not(.menu-title, .disabled) > details > summary:not(.menu-title) ):not(.menu-active, :active, .btn) {
      &.menu-focus, &:focus-visible {
        cursor: pointer;
        background-color: color-mix(in oklab, var(--color-base-content) 10%, transparent);
        color: var(--color-base-content);
        --tw-outline-style: none;
        outline-style: none;
        @media (forced-colors: active) {
          outline: 2px solid transparent;
          outline-offset: 2px;
        }
      }
    }
    :where( li:not(.menu-title, .disabled) > *:not(ul, details, .menu-title):not(.menu-active, :active, .btn):hover, li:not(.menu-title, .disabled) > details > summary:not(.menu-title):not(.menu-active, :active, .btn):hover ) {
      cursor: pointer;
      background-color: color-mix(in oklab, var(--color-base-content) 10%, transparent);
      --tw-outline-style: none;
      outline-style: none;
      @media (forced-colors: active) {
        outline: 2px solid transparent;
        outline-offset: 2px;
      }
      box-shadow: 0 1px oklch(0% 0 0 / 0.01) inset, 0 -1px oklch(100% 0 0 / 0.01) inset;
    }
    :where(li:empty) {
      background-color: var(--color-base-content);
      opacity: 10%;
      margin: 0.5rem 1rem;
      height: 1px;
    }
    :where(li) {
      position: relative;
      display: flex;
      flex-shrink: 0;
      flex-direction: column;
      flex-wrap: wrap;
      align-items: stretch;
      .badge {
        justify-self: flex-end;
      }
      & > *:not(ul, .menu-title, details, .btn):active, & > *:not(ul, .menu-title, details, .btn).menu-active, & > details > summary:active {
        --tw-outline-style: none;
        outline-style: none;
        @media (forced-colors: active) {
          outline: 2px solid transparent;
          outline-offset: 2px;
        }
        color: var(--menu-active-fg);
        background-color: var(--menu-active-bg);
        background-size: auto, calc(var(--noise) * 100%);
        background-image: none, var(--fx-noise);
        &:not(&:active) {
          box-shadow: 0 2px calc(var(--depth) * 3px) -2px var(--menu-active-bg);
        }
      }
      &.menu-disabled {
        pointer-events: none;
        color: color-mix(in oklab, var(--color-base-content) 20%, transparent);
      }
    }
    .dropdown:focus-within {
      .menu-dropdown-toggle:after {
        rotate: 45deg;
        translate: 0 1px;
      }
    }
    .dropdown-content {
      margin-top: calc(0.25rem * 2);
      padding: calc(0.25rem * 2);
      &:before {
        display: none;
      }
    }
  }
  .floating-label {
    position: relative;
    display: block;
    input {
      display: block;
      &::placeholder {
        transition: top 0.1s ease-out, translate 0.1s ease-out, scale 0.1s ease-out, opacity 0.1s ease-out;
      }
    }
    textarea {
      &::placeholder {
        transition: top 0.1s ease-out, translate 0.1s ease-out, scale 0.1s ease-out, opacity 0.1s ease-out;
      }
    }
    > span {
      position: absolute;
      inset-inline-start: calc(0.25rem * 3);
      z-index: 1;
      background-color: var(--color-base-100);
      padding-inline: calc(0.25rem * 1);
      opacity: 0%;
      font-size: 0.875rem;
      top: calc(var(--size-field, 0.25rem) * 10 / 2);
      line-height: 1;
      border-radius: 2px;
      pointer-events: none;
      translate: 0 -50%;
      transition: top 0.1s ease-out, translate 0.1s ease-out, scale 0.1s ease-out, opacity 0.1s ease-out;
    }
    &:focus-within, &:not(:has(input:placeholder-shown, textarea:placeholder-shown)) {
      ::placeholder {
        opacity: 0%;
        top: 0;
        translate: -12.5% calc(-50% - 0.125em);
        scale: 0.75;
        pointer-events: auto;
      }
      > span {
        opacity: 100%;
        top: 0;
        translate: -12.5% calc(-50% - 0.125em);
        scale: 0.75;
        pointer-events: auto;
        z-index: 2;
      }
    }
    &:has(:disabled, [disabled]) {
      > span {
        opacity: 0%;
      }
    }
    &:has(.input-xs, .select-xs, .textarea-xs) span {
      font-size: 0.6875rem;
      top: calc(var(--size-field, 0.25rem) * 6 / 2);
    }
    &:has(.input-sm, .select-sm, .textarea-sm) span {
      font-size: 0.75rem;
      top: calc(var(--size-field, 0.25rem) * 8 / 2);
    }
    &:has(.input-md, .select-md, .textarea-md) span {
      font-size: 0.875rem;
      top: calc(var(--size-field, 0.25rem) * 10 / 2);
    }
    &:has(.input-lg, .select-lg, .textarea-lg) span {
      font-size: 1.125rem;
      top: calc(var(--size-field, 0.25rem) * 12 / 2);
    }
    &:has(.input-xl, .select-xl, .textarea-xl) span {
      font-size: 1.375rem;
      top: calc(var(--size-field, 0.25rem) * 14 / 2);
    }
  }
  .collapse-arrow {
    > .collapse-title:after {
      position: absolute;
      display: block;
      height: 0.5rem;
      width: 0.5rem;
      transform: translateY(-100%) rotate(45deg);
      transition-property: all;
      transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
      transition-duration: 0.2s;
      top: 1.9rem;
      inset-inline-end: 1.4rem;
      content: "";
      transform-origin: 75% 75%;
      box-shadow: 2px 2px;
      pointer-events: none;
    }
  }
  .collapse-plus {
    > .collapse-title:after {
      position: absolute;
      display: block;
      height: 0.5rem;
      width: 0.5rem;
      transition-property: all;
      transition-duration: 300ms;
      transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
      top: 0.9rem;
      inset-inline-end: 1.4rem;
      content: "+";
      pointer-events: none;
    }
  }
  .diff-item-2 {
    position: relative;
    grid-column-start: 1;
    grid-row-start: 1;
    &:after {
      pointer-events: none;
      position: absolute;
      top: calc(1/2 * 100%);
      right: 1px;
      bottom: calc(0.25rem * 0);
      z-index: 2;
      border-radius: calc(infinity * 1px);
      background-color: color-mix(in oklab, var(--color-base-100) 50%, transparent);
      width: 1.2rem;
      height: 1.8rem;
      border: 2px solid var(--color-base-100);
      content: "";
      outline: 1px solid color-mix(in oklab, var(--color-base-content) 5%, #0000);
      outline-offset: -3px;
      backdrop-filter: blur(8px);
      box-shadow: 0 1px 2px 0 oklch(0% 0 0 / 0.1);
      translate: 50% -50%;
    }
    > * {
      pointer-events: none;
      position: absolute;
      top: calc(0.25rem * 0);
      bottom: calc(0.25rem * 0);
      left: calc(0.25rem * 0);
      height: 100%;
      width: 100cqi;
      max-width: none;
      object-fit: cover;
      object-position: center;
    }
    @supports (-webkit-overflow-scrolling: touch) and (overflow: -webkit-paged-x) {
      &:after {
        content: none;
      }
    }
  }
  .pika-single {
    &:is(div) {
      user-select: none;
      font-size: 0.75rem;
      z-index: 999;
      display: inline-block;
      position: relative;
      color: var(--color-base-content);
      background-color: var(--color-base-100);
      border-radius: var(--radius-box);
      border: var(--border) solid var(--color-base-200);
      padding: 0.5rem;
      &:before, &:after {
        content: "";
        display: table;
      }
      &:after {
        clear: both;
      }
      &.is-hidden {
        display: none;
      }
      &.is-bound {
        position: absolute;
      }
      .pika-lendar {
        css-float: left;
      }
      .pika-title {
        position: relative;
        text-align: center;
        select {
          cursor: pointer;
          position: absolute;
          z-index: 999;
          margin: 0;
          left: 0;
          top: 5px;
          opacity: 0;
        }
      }
      .pika-label {
        display: inline-block;
        position: relative;
        z-index: 999;
        overflow: hidden;
        margin: 0;
        padding: 5px 3px;
        background-color: var(--color-base-100);
      }
      .pika-prev, .pika-next {
        display: block;
        cursor: pointer;
        position: absolute;
        top: 0;
        outline: none;
        border: 0;
        width: 2.25rem;
        height: 2.25rem;
        color: #0000;
        font-size: 1.2em;
        border-radius: var(--radius-field);
        &:hover {
          background-color: var(--color-base-200);
        }
        &.is-disabled {
          cursor: default;
          opacity: 0.2;
        }
        &:before {
          display: inline-block;
          width: 2.25rem;
          height: 2.25rem;
          line-height: 2.25;
          color: var(--color-base-content);
        }
      }
      .pika-prev {
        left: 0;
        &:before {
          content: "‹";
        }
      }
      .pika-next {
        right: 0;
        &:before {
          content: "›";
        }
      }
      .pika-select {
        display: inline-block;
      }
      .pika-table {
        width: 100%;
        border-collapse: collapse;
        border-spacing: 0;
        border: 0;
        th, td {
          padding: 0;
        }
        th {
          opacity: 0.6;
          text-align: center;
          width: 2.25rem;
          height: 2.25rem;
        }
      }
      .pika-button {
        cursor: pointer;
        display: block;
        outline: none;
        border: 0;
        margin: 0;
        width: 2.25rem;
        height: 2.25rem;
        padding: 5px;
        text-align: right;
        text-align: center;
      }
      .pika-week {
        color: var(--color-base-content);
      }
      .is-today {
        .pika-button {
          background: var(--color-primary);
          color: var(--color-primary-content);
        }
      }
      .is-selected, .has-event {
        .pika-button {
          &, &:hover {
            color: var(--color-base-100);
            background-color: var(--color-base-content);
            border-radius: var(--radius-field);
          }
        }
      }
      .has-event {
        .pika-button {
          background: var(--color-base-primary);
        }
      }
      .is-disabled, .is-inrange {
        .pika-button {
          background: var(--color-base-primary);
        }
      }
      .is-startrange {
        .pika-button {
          color: var(--color-base-100);
          background: var(--color-base-content);
          border-radius: var(--radius-field);
        }
      }
      .is-endrange {
        .pika-button {
          color: var(--color-base-100);
          background: var(--color-base-content);
          border-radius: var(--radius-field);
        }
      }
      .is-disabled {
        .pika-button {
          pointer-events: none;
          cursor: default;
          color: var(--color-base-content);
          opacity: 0.3;
        }
      }
      .is-outside-current-month {
        .pika-button {
          color: var(--color-base-content);
          opacity: 0.3;
        }
      }
      .is-selection-disabled {
        pointer-events: none;
        cursor: default;
      }
      .pika-button:hover, .pika-row.pick-whole-week:hover .pika-button {
        color: var(--color-base-content);
        background-color: var(--color-base-200);
        border-radius: var(--radius-field);
      }
      .pika-table abbr {
        text-decoration: none;
        font-weight: normal;
      }
    }
  }
  .diff-item-1 {
    position: relative;
    z-index: 1;
    grid-column-start: 1;
    grid-row-start: 1;
    overflow: hidden;
    border-right: 2px solid var(--color-base-100);
    > * {
      pointer-events: none;
      position: absolute;
      top: calc(0.25rem * 0);
      bottom: calc(0.25rem * 0);
      left: calc(0.25rem * 0);
      height: 100%;
      width: 100cqi;
      max-width: none;
      object-fit: cover;
      object-position: center;
    }
  }
  .dock {
    position: fixed;
    right: calc(0.25rem * 0);
    bottom: calc(0.25rem * 0);
    left: calc(0.25rem * 0);
    z-index: 1;
    display: flex;
    width: 100%;
    flex-direction: row;
    align-items: center;
    justify-content: space-around;
    background-color: var(--color-base-100);
    padding: calc(0.25rem * 2);
    color: currentColor;
    border-top: 0.5px solid color-mix(in oklab, var(--color-base-content) 5%, #0000);
    height: 4rem;
    height: calc(4rem + env(safe-area-inset-bottom));
    padding-bottom: env(safe-area-inset-bottom);
    > * {
      position: relative;
      margin-bottom: calc(0.25rem * 2);
      display: flex;
      height: 100%;
      max-width: calc(0.25rem * 32);
      flex-shrink: 1;
      flex-basis: 100%;
      cursor: pointer;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      gap: 1px;
      border-radius: var(--radius-box);
      background-color: transparent;
      transition: opacity 0.2s ease-out;
      @media (hover: hover) {
        &:hover {
          opacity: 80%;
        }
      }
      &[aria-disabled="true"], &[disabled] {
        &, &:hover {
          pointer-events: none;
          color: color-mix(in oklab, var(--color-base-content) 10%, transparent);
          opacity: 100%;
        }
      }
      .dock-label {
        font-size: 0.6875rem;
      }
      &:after {
        content: "";
        position: absolute;
        height: calc(0.25rem * 1);
        width: calc(0.25rem * 6);
        border-radius: calc(infinity * 1px);
        background-color: transparent;
        bottom: 0.2rem;
        border-top: 3px solid transparent;
        transition: background-color 0.1s ease-out, text-color 0.1s ease-out, width 0.1s ease-out;
      }
    }
  }
  .dropdown {
    position: relative;
    display: inline-block;
    position-area: var(--anchor-v, bottom) var(--anchor-h, span-right);
    & > *:not(summary):focus {
      --tw-outline-style: none;
      outline-style: none;
      @media (forced-colors: active) {
        outline: 2px solid transparent;
        outline-offset: 2px;
      }
    }
    .dropdown-content {
      position: absolute;
    }
    &:not(details, .dropdown-open, .dropdown-hover:hover, :focus-within) {
      .dropdown-content {
        display: none;
        transform-origin: top;
        opacity: 0%;
        scale: 95%;
      }
    }
    &[popover], .dropdown-content {
      z-index: 999;
      animation: dropdown 0.2s;
      transition-property: opacity, scale, display;
      transition-behavior: allow-discrete;
      transition-duration: 0.2s;
      transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    }
    @starting-style {
      &[popover], .dropdown-content {
        scale: 95%;
        opacity: 0;
      }
    }
    &.dropdown-open, &:not(.dropdown-hover):focus, &:focus-within {
      > [tabindex]:first-child {
        pointer-events: none;
      }
      .dropdown-content {
        opacity: 100%;
      }
    }
    &.dropdown-hover:hover {
      .dropdown-content {
        opacity: 100%;
        scale: 100%;
      }
    }
    &:is(details) {
      summary {
        &::-webkit-details-marker {
          display: none;
        }
      }
    }
    &.dropdown-open, &:focus, &:focus-within {
      .dropdown-content {
        scale: 100%;
      }
    }
    &:where([popover]) {
      background: #0000;
    }
    &[popover] {
      position: fixed;
      color: inherit;
      @supports not (position-area: bottom) {
        margin: auto;
        &.dropdown-open:not(:popover-open) {
          display: none;
          transform-origin: top;
          opacity: 0%;
          scale: 95%;
        }
        &::backdrop {
          background-color: color-mix(in oklab, #000 30%, #0000);
        }
      }
      &:not(.dropdown-open, :popover-open) {
        display: none;
        transform-origin: top;
        opacity: 0%;
        scale: 95%;
      }
    }
  }
  .btn {
    :where(&) {
      width: unset;
    }
    display: inline-flex;
    flex-shrink: 0;
    cursor: pointer;
    flex-wrap: nowrap;
    align-items: center;
    justify-content: center;
    gap: calc(0.25rem * 1.5);
    text-align: center;
    vertical-align: middle;
    outline-offset: 2px;
    webkit-user-select: none;
    user-select: none;
    padding-inline: var(--btn-p);
    color: var(--btn-fg);
    --tw-prose-links: var(--btn-fg);
    height: var(--size);
    font-size: var(--fontsize, 0.875rem);
    font-weight: 600;
    outline-color: var(--btn-color, var(--color-base-content));
    transition-property: color, background-color, border-color, box-shadow;
    transition-timing-function: cubic-bezier(0, 0, 0.2, 1);
    transition-duration: 0.2s;
    border-start-start-radius: var(--join-ss, var(--radius-field));
    border-start-end-radius: var(--join-se, var(--radius-field));
    border-end-start-radius: var(--join-es, var(--radius-field));
    border-end-end-radius: var(--join-ee, var(--radius-field));
    background-color: var(--btn-bg);
    background-size: auto, calc(var(--noise) * 100%);
    background-image: none, var(--btn-noise);
    border-width: var(--border);
    border-style: solid;
    border-color: var(--btn-border);
    text-shadow: 0 0.5px oklch(100% 0 0 / calc(var(--depth) * 0.15));
    touch-action: manipulation;
    box-shadow: 0 0.5px 0 0.5px oklch(100% 0 0 / calc(var(--depth) * 6%)) inset, var(--btn-shadow);
    --size: calc(var(--size-field, 0.25rem) * 10);
    --btn-bg: var(--btn-color, var(--color-base-200));
    --btn-fg: var(--color-base-content);
    --btn-p: 1rem;
    --btn-border: color-mix(in oklab, var(--btn-bg), #000 calc(var(--depth) * 5%));
    --btn-shadow: 0 3px 2px -2px color-mix(in oklab, var(--btn-bg) calc(var(--depth) * 30%), #0000),
    0 4px 3px -2px color-mix(in oklab, var(--btn-bg) calc(var(--depth) * 30%), #0000);
    --btn-noise: var(--fx-noise);
    .prose & {
      text-decoration-line: none;
    }
    @media (hover: hover) {
      &:hover {
        --btn-bg: color-mix(in oklab, var(--btn-color, var(--color-base-200)), #000 7%);
      }
    }
    &:focus-visible {
      outline-width: 2px;
      outline-style: solid;
      isolation: isolate;
    }
    &:active:not(.btn-active) {
      translate: 0 0.5px;
      --btn-bg: color-mix(in oklab, var(--btn-color, var(--color-base-200)), #000 5%);
      --btn-border: color-mix(in oklab, var(--btn-color, var(--color-base-200)), #000 7%);
      --btn-shadow: 0 0 0 0 oklch(0% 0 0/0), 0 0 0 0 oklch(0% 0 0/0);
    }
    &:is(:disabled, [disabled], .btn-disabled) {
      &:not(.btn-link, .btn-ghost) {
        background-color: color-mix(in oklab, var(--color-base-content) 10%, transparent);
        box-shadow: none;
      }
      pointer-events: none;
      --btn-border: #0000;
      --btn-noise: none;
      --btn-fg: color-mix(in oklch, var(--color-base-content) 20%, #0000);
      @media (hover: hover) {
        &:hover {
          pointer-events: none;
          background-color: color-mix(in oklab, var(--color-neutral) 20%, transparent);
          --btn-border: #0000;
          --btn-fg: color-mix(in oklch, var(--color-base-content) 20%, #0000);
        }
      }
    }
    &:is(input[type="checkbox"], input[type="radio"]) {
      appearance: none;
      &::after {
        content: attr(aria-label);
      }
    }
    &:where(input:checked:not(.filter .btn)) {
      --btn-color: var(--color-primary);
      --btn-fg: var(--color-primary-content);
      isolation: isolate;
    }
  }
  .loading {
    pointer-events: none;
    display: inline-block;
    aspect-ratio: 1 / 1;
    background-color: currentColor;
    vertical-align: middle;
    width: calc(var(--size-selector, 0.25rem) * 6);
    mask-size: 100%;
    mask-repeat: no-repeat;
    mask-position: center;
    mask-image: url("data:image/svg+xml,%3Csvg width='24' height='24' stroke='black' viewBox='0 0 24 24' xmlns='http://www.w3.org/2000/svg'%3E%3Cg transform-origin='center'%3E%3Ccircle cx='12' cy='12' r='9.5' fill='none' stroke-width='3' stroke-linecap='round'%3E%3CanimateTransform attributeName='transform' type='rotate' from='0 12 12' to='360 12 12' dur='2s' repeatCount='indefinite'/%3E%3Canimate attributeName='stroke-dasharray' values='0,150;42,150;42,150' keyTimes='0;0.475;1' dur='1.5s' repeatCount='indefinite'/%3E%3Canimate attributeName='stroke-dashoffset' values='0;-16;-59' keyTimes='0;0.475;1' dur='1.5s' repeatCount='indefinite'/%3E%3C/circle%3E%3C/g%3E%3C/svg%3E");
  }
  .tab-disabled {
    pointer-events: none;
    opacity: 40%;
  }
  .pointer-events-auto {
    pointer-events: auto;
  }
  .pointer-events-none {
    pointer-events: none;
  }
  .react-day-picker {
    user-select: none;
    background-color: var(--color-base-100);
    border-radius: var(--radius-box);
    border: var(--border) solid var(--color-base-200);
    font-size: 0.75rem;
    display: inline-block;
    position: relative;
    overflow: clip;
    &[dir="rtl"] {
      .rdp-nav {
        .rdp-chevron {
          transform-origin: 50%;
          transform: rotate(180deg);
        }
      }
    }
    * {
      box-sizing: border-box;
    }
    .rdp-day {
      width: 2.25rem;
      height: 2.25rem;
      text-align: center;
    }
    .rdp-day_button {
      cursor: pointer;
      font: inherit;
      color: inherit;
      width: 2.25rem;
      height: 2.25rem;
      border: 2px solid #0000;
      border-radius: var(--radius-field);
      background: 0 0;
      justify-content: center;
      align-items: center;
      margin: 0;
      padding: 0;
      display: flex;
      &:disabled {
        cursor: revert;
      }
      &:hover {
        background-color: var(--color-base-200);
      }
    }
    .rdp-caption_label {
      z-index: 1;
      white-space: nowrap;
      border: 0;
      align-items: center;
      display: inline-flex;
      position: relative;
    }
    .rdp-button_next {
      border-radius: var(--radius-field);
      &:hover {
        background-color: var(--color-base-200);
      }
    }
    .rdp-button_previous {
      border-radius: var(--radius-field);
      &:hover {
        background-color: var(--color-base-200);
      }
    }
    .rdp-button_next, .rdp-button_previous {
      cursor: pointer;
      font: inherit;
      color: inherit;
      appearance: none;
      width: 2.25rem;
      height: 2.25rem;
      background: 0 0;
      border: none;
      justify-content: center;
      align-items: center;
      margin: 0;
      padding: 0;
      display: inline-flex;
      position: relative;
      &:disabled {
        cursor: revert;
        opacity: 0.5;
      }
    }
    .rdp-chevron {
      fill: var(--color-base-content);
      width: 1rem;
      height: 1rem;
      display: inline-block;
    }
    .rdp-dropdowns {
      align-items: center;
      gap: 0.5rem;
      display: inline-flex;
      position: relative;
    }
    .rdp-dropdown {
      z-index: 2;
      opacity: 0;
      appearance: none;
      cursor: inherit;
      line-height: inherit;
      border: none;
      width: 100%;
      margin: 0;
      padding: 0;
      position: absolute;
      inset-block: 0;
      inset-inline-start: 0;
      &:focus-visible {
        ~ .rdp-caption_label {
          outline: 5px auto highlight;
          outline: 5px auto -webkit-focus-ring-color;
        }
      }
    }
    .rdp-dropdown_root {
      align-items: center;
      display: inline-flex;
      position: relative;
      &[data-disabled="true"] {
        .rdp-chevron {
          opacity: 0.5;
        }
      }
    }
    .rdp-month_caption {
      height: 2.75rem;
      font-size: 0.75rem;
      font-weight: inherit;
      place-content: center;
      display: flex;
    }
    .rdp-months {
      gap: 2rem;
      flex-wrap: wrap;
      max-width: fit-content;
      padding: 0.5rem;
      display: flex;
      position: relative;
    }
    .rdp-month_grid {
      border-collapse: collapse;
    }
    .rdp-nav {
      height: 2.75rem;
      inset-block-start: 0;
      inset-inline-end: 0;
      justify-content: space-between;
      align-items: center;
      width: 100%;
      padding-inline: 0.5rem;
      display: flex;
      position: absolute;
      top: 0.25rem;
    }
    .rdp-weekday {
      opacity: 0.6;
      padding: 0.5rem 0rem;
      text-align: center;
      font-size: smaller;
      font-weight: 500;
    }
    .rdp-week_number {
      opacity: 0.6;
      height: 2.25rem;
      width: 2.25rem;
      border: none;
      border-radius: 100%;
      text-align: center;
      font-size: small;
      font-weight: 400;
    }
    .rdp-today:not(.rdp-outside) {
      .rdp-day_button {
        background: var(--color-primary);
        color: var(--color-primary-content);
      }
    }
    .rdp-selected {
      font-weight: inherit;
      font-size: 0.75rem;
      .rdp-day_button {
        color: var(--color-base-100);
        background-color: var(--color-base-content);
        border-radius: var(--radius-field);
        border: none;
        &:hover {
          background-color: var(--color-base-content);
        }
      }
    }
    .rdp-outside {
      opacity: 0.75;
    }
    .rdp-disabled {
      opacity: 0.5;
    }
    .rdp-hidden {
      visibility: hidden;
      color: var(--color-base-content);
    }
    .rdp-range_start {
      .rdp-day_button {
        border-radius: var(--radius-field) 0 0 var(--radius-field);
      }
    }
    .rdp-range_start .rdp-day_button {
      background-color: var(--color-base-content);
      color: var(--color-base-100);
    }
    .rdp-range_middle {
      background-color: var(--color-base-200);
    }
    .rdp-range_middle .rdp-day_button {
      border: unset;
      border-radius: unset;
      color: inherit;
    }
    .rdp-range_end {
      color: var(--color-base-content);
      .rdp-day_button {
        border-radius: 0 var(--radius-field) var(--radius-field) 0;
      }
    }
    .rdp-range_end .rdp-day_button {
      background-color: var(--color-base-content);
      color: var(--color-base-100);
    }
    .rdp-range_start.rdp-range_end {
      background: revert;
    }
    .rdp-focusable {
      cursor: pointer;
    }
    .rdp-footer {
      border-top: var(--border) solid var(--color-base-200);
      padding: 0.5rem;
    }
  }
  .collapse {
    &:not(td, tr, colgroup) {
      visibility: visible;
    }
    position: relative;
    display: grid;
    overflow: hidden;
    border-radius: var(--radius-box, 1rem);
    width: 100%;
    grid-template-rows: max-content 0fr;
    transition: grid-template-rows 0.2s;
    isolation: isolate;
    > input:is([type="checkbox"], [type="radio"]) {
      grid-column-start: 1;
      grid-row-start: 1;
      appearance: none;
      opacity: 0;
      z-index: 1;
      width: 100%;
      padding: 1rem;
      padding-inline-end: 3rem;
      min-height: 1lh;
      transition: background-color 0.2s ease-out;
    }
    &:is([open], :focus:not(.collapse-close)), &:not(.collapse-close):has(> input:is([type="checkbox"], [type="radio"]):checked) {
      grid-template-rows: max-content 1fr;
    }
    &:is([open], :focus:not(.collapse-close)) > .collapse-content, &:not(.collapse-close) > :where(input:is([type="checkbox"], [type="radio"]):checked ~ .collapse-content) {
      visibility: visible;
      min-height: fit-content;
    }
    &:focus-visible, &:has(> input:is([type="checkbox"], [type="radio"]):focus-visible) {
      outline-color: var(--color-base-content);
      outline-style: solid;
      outline-width: 2px;
      outline-offset: 2px;
    }
    &:not(.collapse-close) {
      > input[type="checkbox"], > input[type="radio"]:not(:checked), > .collapse-title {
        cursor: pointer;
      }
    }
    &:focus:not(.collapse-close, .collapse[open]) > .collapse-title {
      cursor: unset;
    }
    &:is([open], :focus:not(.collapse-close)) > :where(.collapse-content), &:not(.collapse-close) > :where(input:is([type="checkbox"], [type="radio"]):checked ~ .collapse-content) {
      padding-bottom: 1rem;
      transition: padding 0.2s ease-out, background-color 0.2s ease-out;
    }
    &:is([open]) {
      &.collapse-arrow {
        > .collapse-title:after {
          transform: translateY(-50%) rotate(225deg);
        }
      }
    }
    &.collapse-open {
      &.collapse-arrow {
        > .collapse-title:after {
          transform: translateY(-50%) rotate(225deg);
        }
      }
      &.collapse-plus {
        > .collapse-title:after {
          content: "−";
        }
      }
    }
    &.collapse-arrow:focus:not(.collapse-close) {
      > .collapse-title:after {
        transform: translateY(-50%) rotate(225deg);
      }
    }
    &.collapse-arrow:not(.collapse-close) {
      > input:is([type="checkbox"], [type="radio"]):checked ~ .collapse-title:after {
        transform: translateY(-50%) rotate(225deg);
      }
    }
    &[open] {
      &.collapse-plus {
        > .collapse-title:after {
          content: "−";
        }
      }
    }
    &.collapse-plus:focus:not(.collapse-close) {
      > .collapse-title:after {
        content: "−";
      }
    }
    &.collapse-plus:not(.collapse-close) {
      > input:is([type="checkbox"], [type="radio"]):checked ~ .collapse-title:after {
        content: "−";
      }
    }
    &:is(details) {
      width: 100%;
      & summary {
        position: relative;
        display: block;
        &::-webkit-details-marker {
          display: none;
        }
      }
    }
    &:is(details) summary {
      outline: none;
    }
  }
  .collapse-content {
    grid-column-start: 1;
    grid-row-start: 1;
    visibility: hidden;
    grid-column-start: 1;
    grid-row-start: 2;
    min-height: 0;
    padding-left: 1rem;
    padding-right: 1rem;
    cursor: unset;
    transition: visibility 0.2s, padding 0.2s ease-out, background-color 0.2s ease-out;
  }
  .validator-hint {
    visibility: hidden;
    margin-top: calc(0.25rem * 2);
    font-size: 0.75rem;
  }
  .validator {
    &:user-valid, &:has(:user-valid) {
      &, &:focus, &:checked, &[aria-checked="true"], &:focus-within {
        --input-color: var(--color-success);
      }
    }
    &:user-invalid, &:has(:user-invalid), &[aria-invalid]:not([aria-invalid="false"]) {
      &, &:focus, &:checked, &[aria-checked="true"], &:focus-within {
        --input-color: var(--color-error);
      }
      & ~ .validator-hint {
        visibility: visible;
        display: block;
        color: var(--color-error);
      }
    }
  }
  .collapse-open {
    grid-template-rows: max-content 1fr;
    > .collapse-content {
      visibility: visible;
      min-height: fit-content;
      padding-bottom: 1rem;
      transition: padding 0.2s ease-out, background-color 0.2s ease-out;
    }
  }
  .collapse {
    visibility: collapse;
  }
  .invisible {
    visibility: hidden;
  }
  .visible {
    visibility: visible;
  }
  .tabs-lift {
    --tabs-height: auto;
    --tabs-direction: row;
    > .tab {
      --tab-border: 0 0 var(--border) 0;
      --tab-radius-ss: min(var(--radius-field), var(--tab-radius-min));
      --tab-radius-se: min(var(--radius-field), var(--tab-radius-min));
      --tab-radius-es: 0;
      --tab-radius-ee: 0;
      --tab-paddings: var(--border) var(--tab-p) 0 var(--tab-p);
      --tab-border-colors: #0000 #0000 var(--tab-border-color) #0000;
      --tab-corner-width: calc(100% + min(var(--radius-field), var(--tab-radius-min)) * 2);
      --tab-corner-height: min(var(--radius-field), var(--tab-radius-min));
      --tab-corner-position: top left, top right;
      border-width: var(--tab-border);
      border-start-start-radius: var(--tab-radius-ss);
      border-start-end-radius: var(--tab-radius-se);
      border-end-start-radius: var(--tab-radius-es);
      border-end-end-radius: var(--tab-radius-ee);
      padding: var(--tab-paddings);
      border-color: var(--tab-border-colors);
      &:is(.tab-active, [aria-selected="true"]):not(.tab-disabled, [disabled]), &:is(input:checked, label:has(:checked)) {
        --tab-border: var(--border) var(--border) 0 var(--border);
        --tab-border-colors: var(--tab-border-color) var(--tab-border-color) #0000
        var(--tab-border-color);
        --tab-paddings: 0 calc(var(--tab-p) - var(--border)) var(--border)
        calc(var(--tab-p) - var(--border));
        --tab-inset: auto auto 0 auto;
        --tab-grad: calc(69% - var(--border));
        --radius-start: radial-gradient(
        circle at top left,
        #0000 var(--tab-grad),
        var(--tab-border-color) calc(var(--tab-grad) + 0.25px),
        var(--tab-border-color) calc(var(--tab-grad) + var(--border)),
        var(--tab-bg) calc(var(--tab-grad) + var(--border) + 0.25px)
      );
        --radius-end: radial-gradient(
        circle at top right,
        #0000 var(--tab-grad),
        var(--tab-border-color) calc(var(--tab-grad) + 0.25px),
        var(--tab-border-color) calc(var(--tab-grad) + var(--border)),
        var(--tab-bg) calc(var(--tab-grad) + var(--border) + 0.25px)
      );
        background-color: var(--tab-bg);
        &:before {
          z-index: 1;
          content: "";
          display: block;
          position: absolute;
          width: var(--tab-corner-width);
          height: var(--tab-corner-height);
          background-position: var(--tab-corner-position);
          background-image: var(--radius-start), var(--radius-end);
          background-size: min(var(--radius-field), var(--tab-radius-min)) min(var(--radius-field), var(--tab-radius-min));
          background-repeat: no-repeat;
          inset: var(--tab-inset);
        }
        &:first-child:before {
          --radius-start: none;
        }
        [dir="rtl"] &:first-child:before {
          transform: rotateY(180deg);
        }
        &:last-child:before {
          --radius-end: none;
        }
        [dir="rtl"] &:last-child:before {
          transform: rotateY(180deg);
        }
      }
    }
    &:has(.tab-content) {
      > .tab:first-child {
        &:not(.tab-active, [aria-selected="true"]) {
          --tab-border-colors: var(--tab-border-color) var(--tab-border-color) #0000
          var(--tab-border-color);
        }
      }
    }
    .tab-content {
      --tabcontent-margin: calc(-1 * var(--border)) 0 0 0;
      --tabcontent-radius-ss: 0;
      --tabcontent-radius-se: var(--radius-box);
      --tabcontent-radius-es: var(--radius-box);
      --tabcontent-radius-ee: var(--radius-box);
    }
    :checked, label:has(:checked), :is(.tab-active, [aria-selected="true"]) {
      & + .tab-content {
        &:nth-child(1), &:nth-child(n + 3) {
          --tabcontent-radius-ss: var(--radius-box);
        }
      }
    }
  }
  .radial-progress {
    position: relative;
    display: inline-grid;
    height: var(--size);
    width: var(--size);
    place-content: center;
    border-radius: calc(infinity * 1px);
    background-color: transparent;
    vertical-align: middle;
    box-sizing: content-box;
    --value: 0;
    --size: 5rem;
    --thickness: calc(var(--size) / 10);
    --radialprogress: calc(var(--value) * 1%);
    transition: --radialprogress 0.3s linear;
    &:before {
      position: absolute;
      inset: calc(0.25rem * 0);
      border-radius: calc(infinity * 1px);
      content: "";
      background: radial-gradient(farthest-side, currentColor 98%, #0000) top/var(--thickness) var(--thickness) no-repeat, conic-gradient(currentColor var(--radialprogress), #0000 0);
      webkit-mask: radial-gradient( farthest-side, #0000 calc(100% - var(--thickness)), #000 calc(100% + 0.5px - var(--thickness)) );
      mask: radial-gradient( farthest-side, #0000 calc(100% - var(--thickness)), #000 calc(100% + 0.5px - var(--thickness)) );
    }
    &:after {
      position: absolute;
      border-radius: calc(infinity * 1px);
      background-color: currentColor;
      transition: transform 0.3s linear;
      content: "";
      inset: calc(50% - var(--thickness) / 2);
      transform: rotate(calc(var(--value) * 3.6deg - 90deg)) translate(calc(var(--size) / 2 - 50%));
    }
  }
  .list {
    display: flex;
    flex-direction: column;
    font-size: 0.875rem;
    :where(.list-row) {
      --list-grid-cols: minmax(0, auto) 1fr;
      position: relative;
      display: grid;
      grid-auto-flow: column;
      gap: calc(0.25rem * 4);
      border-radius: var(--radius-box);
      padding: calc(0.25rem * 4);
      word-break: break-word;
      grid-template-columns: var(--list-grid-cols);
      &:has(.list-col-grow:nth-child(1)) {
        --list-grid-cols: 1fr;
      }
      &:has(.list-col-grow:nth-child(2)) {
        --list-grid-cols: minmax(0, auto) 1fr;
      }
      &:has(.list-col-grow:nth-child(3)) {
        --list-grid-cols: minmax(0, auto) minmax(0, auto) 1fr;
      }
      &:has(.list-col-grow:nth-child(4)) {
        --list-grid-cols: minmax(0, auto) minmax(0, auto) minmax(0, auto) 1fr;
      }
      &:has(.list-col-grow:nth-child(5)) {
        --list-grid-cols: minmax(0, auto) minmax(0, auto) minmax(0, auto) minmax(0, auto) 1fr;
      }
      &:has(.list-col-grow:nth-child(6)) {
        --list-grid-cols: minmax(0, auto) minmax(0, auto) minmax(0, auto) minmax(0, auto)
        minmax(0, auto) 1fr;
      }
      :not(.list-col-wrap) {
        grid-row-start: 1;
      }
    }
    & > :not(:last-child) {
      &.list-row, .list-row {
        &:after {
          content: "";
          border-bottom: var(--border) solid;
          inset-inline: var(--radius-box);
          position: absolute;
          bottom: calc(0.25rem * 0);
          border-color: color-mix(in oklab, var(--color-base-content) 5%, transparent);
        }
      }
    }
  }
  .toast {
    position: fixed;
    inset-inline-start: auto;
    inset-inline-end: calc(0.25rem * 4);
    top: auto;
    bottom: calc(0.25rem * 4);
    display: flex;
    flex-direction: column;
    gap: calc(0.25rem * 2);
    background-color: transparent;
    translate: var(--toast-x, 0) var(--toast-y, 0);
    width: max-content;
    max-width: calc(100vw - 2rem);
    & > * {
      animation: toast 0.25s ease-out;
    }
    &:where(.toast-start) {
      inset-inline-start: calc(0.25rem * 4);
      inset-inline-end: auto;
      --toast-x: 0;
    }
    &:where(.toast-center) {
      inset-inline-start: calc(1/2 * 100%);
      inset-inline-end: calc(1/2 * 100%);
      --toast-x: -50%;
    }
    &:where(.toast-end) {
      inset-inline-start: auto;
      inset-inline-end: calc(0.25rem * 4);
      --toast-x: 0;
    }
    &:where(.toast-bottom) {
      top: auto;
      bottom: calc(0.25rem * 4);
      --toast-y: 0;
    }
    &:where(.toast-middle) {
      top: calc(1/2 * 100%);
      bottom: auto;
      --toast-y: -50%;
    }
    &:where(.toast-top) {
      top: calc(0.25rem * 4);
      bottom: auto;
      --toast-y: 0;
    }
  }
  .toggle {
    border: var(--border) solid currentColor;
    color: var(--input-color);
    position: relative;
    display: inline-grid;
    flex-shrink: 0;
    cursor: pointer;
    appearance: none;
    place-content: center;
    vertical-align: middle;
    webkit-user-select: none;
    user-select: none;
    grid-template-columns: 0fr 1fr 1fr;
    --radius-selector-max: calc(
    var(--radius-selector) + var(--radius-selector) + var(--radius-selector)
  );
    border-radius: calc( var(--radius-selector) + min(var(--toggle-p), var(--radius-selector-max)) + min(var(--border), var(--radius-selector-max)) );
    padding: var(--toggle-p);
    box-shadow: 0 1px color-mix(in oklab, currentColor calc(var(--depth) * 10%), #0000) inset;
    transition: color 0.3s, grid-template-columns 0.2s;
    --input-color: color-mix(in oklab, var(--color-base-content) 50%, #0000);
    --toggle-p: calc(var(--size) * 0.125);
    --size: calc(var(--size-selector, 0.25rem) * 6);
    width: calc((var(--size) * 2) - (var(--border) + var(--toggle-p)) * 2);
    height: var(--size);
    > * {
      z-index: 1;
      grid-column: span 1 / span 1;
      grid-column-start: 2;
      grid-row-start: 1;
      height: 100%;
      cursor: pointer;
      appearance: none;
      background-color: transparent;
      padding: calc(0.25rem * 0.5);
      transition: opacity 0.2s, rotate 0.4s;
      border: none;
      &:focus {
        --tw-outline-style: none;
        outline-style: none;
        @media (forced-colors: active) {
          outline: 2px solid transparent;
          outline-offset: 2px;
        }
      }
      &:nth-child(2) {
        color: var(--color-base-100);
        rotate: 0deg;
      }
      &:nth-child(3) {
        color: var(--color-base-100);
        opacity: 0%;
        rotate: -15deg;
      }
    }
    &:has(:checked) {
      > :nth-child(2) {
        opacity: 0%;
        rotate: 15deg;
      }
      > :nth-child(3) {
        opacity: 100%;
        rotate: 0deg;
      }
    }
    &:before {
      position: relative;
      inset-inline-start: calc(0.25rem * 0);
      grid-column-start: 2;
      grid-row-start: 1;
      aspect-ratio: 1 / 1;
      height: 100%;
      border-radius: var(--radius-selector);
      background-color: currentColor;
      translate: 0;
      --tw-content: "";
      content: var(--tw-content);
      transition: background-color 0.1s, translate 0.2s, inset-inline-start 0.2s;
      box-shadow: 0 -1px oklch(0% 0 0 / calc(var(--depth) * 0.1)) inset, 0 8px 0 -4px oklch(100% 0 0 / calc(var(--depth) * 0.1)) inset, 0 1px color-mix(in oklab, currentColor calc(var(--depth) * 10%), #0000);
      background-size: auto, calc(var(--noise) * 100%);
      background-image: none, var(--fx-noise);
    }
    @media (forced-colors: active) {
      &:before {
        outline-style: var(--tw-outline-style);
        outline-width: 1px;
        outline-offset: calc(1px * -1);
      }
    }
    @media print {
      &:before {
        outline: 0.25rem solid;
        outline-offset: -1rem;
      }
    }
    &:focus-visible, &:has(:focus-visible) {
      outline: 2px solid currentColor;
      outline-offset: 2px;
    }
    &:checked, &[aria-checked="true"], &:has(> input:checked) {
      grid-template-columns: 1fr 1fr 0fr;
      background-color: var(--color-base-100);
      --input-color: var(--color-base-content);
      &:before {
        background-color: currentColor;
      }
      @starting-style {
        &:before {
          opacity: 0;
        }
      }
    }
    &:indeterminate {
      grid-template-columns: 0.5fr 1fr 0.5fr;
    }
    &:disabled {
      cursor: not-allowed;
      opacity: 30%;
      &:before {
        background-color: transparent;
        border: var(--border) solid currentColor;
      }
    }
  }
  .input {
    cursor: text;
    border: var(--border) solid #0000;
    position: relative;
    display: inline-flex;
    flex-shrink: 1;
    appearance: none;
    align-items: center;
    gap: calc(0.25rem * 2);
    background-color: var(--color-base-100);
    padding-inline: calc(0.25rem * 3);
    vertical-align: middle;
    white-space: nowrap;
    width: clamp(3rem, 20rem, 100%);
    height: var(--size);
    font-size: 0.875rem;
    touch-action: manipulation;
    border-start-start-radius: var(--join-ss, var(--radius-field));
    border-start-end-radius: var(--join-se, var(--radius-field));
    border-end-start-radius: var(--join-es, var(--radius-field));
    border-end-end-radius: var(--join-ee, var(--radius-field));
    border-color: var(--input-color);
    box-shadow: 0 1px color-mix(in oklab, var(--input-color) calc(var(--depth) * 10%), #0000) inset, 0 -1px oklch(100% 0 0 / calc(var(--depth) * 0.1)) inset;
    --size: calc(var(--size-field, 0.25rem) * 10);
    --input-color: color-mix(in oklab, var(--color-base-content) 20%, #0000);
    &:where(input) {
      display: inline-flex;
    }
    :where(input) {
      display: inline-flex;
      height: 100%;
      width: 100%;
      appearance: none;
      background-color: transparent;
      border: none;
      &:focus, &:focus-within {
        --tw-outline-style: none;
        outline-style: none;
        @media (forced-colors: active) {
          outline: 2px solid transparent;
          outline-offset: 2px;
        }
      }
    }
    :where(input[type="url"]), :where(input[type="email"]) {
      direction: ltr;
    }
    :where(input[type="date"]) {
      display: inline-block;
    }
    &:focus, &:focus-within {
      --input-color: var(--color-base-content);
      box-shadow: 0 1px color-mix(in oklab, var(--input-color) calc(var(--depth) * 10%), #0000);
      outline: 2px solid var(--input-color);
      outline-offset: 2px;
      isolation: isolate;
      z-index: 1;
    }
    &:has(> input[disabled]), &:is(:disabled, [disabled]) {
      cursor: not-allowed;
      border-color: var(--color-base-200);
      background-color: var(--color-base-200);
      color: color-mix(in oklab, var(--color-base-content) 40%, transparent);
      &::placeholder {
        color: color-mix(in oklab, var(--color-base-content) 20%, transparent);
      }
      box-shadow: none;
    }
    &:has(> input[disabled]) > input[disabled] {
      cursor: not-allowed;
    }
    &::-webkit-date-and-time-value {
      text-align: inherit;
    }
    &[type="number"] {
      &::-webkit-inner-spin-button {
        margin-block: calc(0.25rem * -3);
        margin-inline-end: calc(0.25rem * -3);
      }
    }
    &::-webkit-calendar-picker-indicator {
      position: absolute;
      inset-inline-end: 0.75em;
    }
  }
  .indicator {
    position: relative;
    display: inline-flex;
    width: max-content;
    :where(.indicator-item) {
      z-index: 1;
      position: absolute;
      white-space: nowrap;
      top: var(--indicator-t, 0);
      bottom: var(--indicator-b, auto);
      left: var(--indicator-s, auto);
      right: var(--indicator-e, 0);
      translate: var(--indicator-x, 50%) var(--indicator-y, -50%);
    }
  }
  .table {
    font-size: 0.875rem;
    position: relative;
    width: 100%;
    border-radius: var(--radius-box);
    text-align: left;
    &:where(:dir(rtl), [dir="rtl"], [dir="rtl"] *) {
      text-align: right;
    }
    tr.row-hover {
      &, &:nth-child(even) {
        &:hover {
          @media (hover: hover) {
            background-color: var(--color-base-200);
          }
        }
      }
    }
    :where(th, td) {
      padding-inline: calc(0.25rem * 4);
      padding-block: calc(0.25rem * 3);
      vertical-align: middle;
    }
    :where(thead, tfoot) {
      white-space: nowrap;
      color: color-mix(in oklab, var(--color-base-content) 60%, transparent);
      font-size: 0.875rem;
      font-weight: 600;
    }
    :where(tfoot) {
      border-top: var(--border) solid color-mix(in oklch, var(--color-base-content) 5%, #0000);
    }
    :where(.table-pin-rows thead tr) {
      position: sticky;
      top: calc(0.25rem * 0);
      z-index: 1;
      background-color: var(--color-base-100);
    }
    :where(.table-pin-rows tfoot tr) {
      position: sticky;
      bottom: calc(0.25rem * 0);
      z-index: 1;
      background-color: var(--color-base-100);
    }
    :where(.table-pin-cols tr th) {
      position: sticky;
      right: calc(0.25rem * 0);
      left: calc(0.25rem * 0);
      background-color: var(--color-base-100);
    }
    :where(thead tr, tbody tr:not(:last-child)) {
      border-bottom: var(--border) solid color-mix(in oklch, var(--color-base-content) 5%, #0000);
    }
  }
  .avatar-offline {
    &:before {
      content: "";
      position: absolute;
      z-index: 1;
      display: block;
      border-radius: calc(infinity * 1px);
      background-color: var(--color-base-300);
      outline: 2px solid var(--color-base-100);
      width: 15%;
      height: 15%;
      top: 7%;
      right: 7%;
    }
  }
  .avatar-online {
    &:before {
      content: "";
      position: absolute;
      z-index: 1;
      display: block;
      border-radius: calc(infinity * 1px);
      background-color: var(--color-success);
      outline: 2px solid var(--color-base-100);
      width: 15%;
      height: 15%;
      top: 7%;
      right: 7%;
    }
  }
  .steps {
    display: inline-grid;
    grid-auto-flow: column;
    overflow: hidden;
    overflow-x: auto;
    counter-reset: step;
    grid-auto-columns: 1fr;
    .step {
      display: grid;
      grid-template-columns: repeat(1, minmax(0, 1fr));
      grid-template-columns: auto;
      grid-template-rows: repeat(2, minmax(0, 1fr));
      grid-template-rows: 40px 1fr;
      place-items: center;
      text-align: center;
      min-width: 4rem;
      --step-bg: var(--color-base-300);
      --step-fg: var(--color-base-content);
      &:before {
        top: calc(0.25rem * 0);
        grid-column-start: 1;
        grid-row-start: 1;
        height: calc(0.25rem * 2);
        width: 100%;
        border: 1px solid;
        color: var(--step-bg);
        background-color: var(--step-bg);
        --tw-content: "";
        content: var(--tw-content);
        margin-inline-start: -100%;
      }
      > .step-icon, &:not(:has(.step-icon)):after {
        content: counter(step);
        counter-increment: step;
        z-index: 1;
        color: var(--step-fg);
        background-color: var(--step-bg);
        border: 1px solid var(--step-bg);
        position: relative;
        grid-column-start: 1;
        grid-row-start: 1;
        display: grid;
        height: calc(0.25rem * 8);
        width: calc(0.25rem * 8);
        place-items: center;
        place-self: center;
        border-radius: calc(infinity * 1px);
      }
      &:first-child:before {
        content: none;
      }
      &[data-content]:after {
        content: attr(data-content);
      }
    }
    .step-neutral {
      + .step-neutral:before, &:after, > .step-icon {
        --step-bg: var(--color-neutral);
        --step-fg: var(--color-neutral-content);
      }
    }
    .step-primary {
      + .step-primary:before, &:after, > .step-icon {
        --step-bg: var(--color-primary);
        --step-fg: var(--color-primary-content);
      }
    }
    .step-secondary {
      + .step-secondary:before, &:after, > .step-icon {
        --step-bg: var(--color-secondary);
        --step-fg: var(--color-secondary-content);
      }
    }
    .step-accent {
      + .step-accent:before, &:after, > .step-icon {
        --step-bg: var(--color-accent);
        --step-fg: var(--color-accent-content);
      }
    }
    .step-info {
      + .step-info:before, &:after, > .step-icon {
        --step-bg: var(--color-info);
        --step-fg: var(--color-info-content);
      }
    }
    .step-success {
      + .step-success:before, &:after, > .step-icon {
        --step-bg: var(--color-success);
        --step-fg: var(--color-success-content);
      }
    }
    .step-warning {
      + .step-warning:before, &:after, > .step-icon {
        --step-bg: var(--color-warning);
        --step-fg: var(--color-warning-content);
      }
    }
    .step-error {
      + .step-error:before, &:after, > .step-icon {
        --step-bg: var(--color-error);
        --step-fg: var(--color-error-content);
      }
    }
  }
  .diff-resizer {
    position: relative;
    top: calc(1/2 * 100%);
    z-index: 1;
    grid-column-start: 1;
    grid-row-start: 1;
    height: calc(0.25rem * 2);
    width: 50cqi;
    max-width: calc(100cqi - 1rem);
    min-width: 1rem;
    resize: horizontal;
    overflow: hidden;
    opacity: 0%;
    transform: scaleY(3) translate(0.35rem, 0.08rem);
    cursor: ew-resize;
    transform-origin: 100% 100%;
    clip-path: inset(calc(100% - 0.75rem) 0 0 calc(100% - 0.75rem));
    transition: min-width 0.3s ease-out, max-width 0.3s ease-out;
  }
  .range {
    appearance: none;
    webkit-appearance: none;
    --range-thumb: var(--color-base-100);
    --range-thumb-size: calc(var(--size-selector, 0.25rem) * 6);
    --range-progress: currentColor;
    --range-fill: 1;
    --range-p: 0.25rem;
    --range-bg: color-mix(in oklab, currentColor 10%, #0000);
    cursor: pointer;
    overflow: hidden;
    background-color: transparent;
    vertical-align: middle;
    width: clamp(3rem, 20rem, 100%);
    --radius-selector-max: calc(
    var(--radius-selector) + var(--radius-selector) + var(--radius-selector)
  );
    border-radius: calc(var(--radius-selector) + min(var(--range-p), var(--radius-selector-max)));
    border: none;
    height: var(--range-thumb-size);
    [dir="rtl"] & {
      --range-dir: -1;
    }
    &:focus {
      outline: none;
    }
    &:focus-visible {
      outline: 2px solid;
      outline-offset: 2px;
    }
    &::-webkit-slider-runnable-track {
      width: 100%;
      background-color: var(--range-bg);
      border-radius: var(--radius-selector);
      height: calc(var(--range-thumb-size) * 0.5);
    }
    @media (forced-colors: active) {
      &::-webkit-slider-runnable-track {
        border: 1px solid;
      }
    }
    @media (forced-colors: active) {
      &::-moz-range-track {
        border: 1px solid;
      }
    }
    &::-webkit-slider-thumb {
      position: relative;
      box-sizing: border-box;
      border-radius: calc(var(--radius-selector) + min(var(--range-p), var(--radius-selector-max)));
      background-color: currentColor;
      height: var(--range-thumb-size);
      width: var(--range-thumb-size);
      border: var(--range-p) solid;
      appearance: none;
      webkit-appearance: none;
      top: 50%;
      color: var(--range-progress);
      transform: translateY(-50%);
      box-shadow: 0 -1px oklch(0% 0 0 / calc(var(--depth) * 0.1)) inset, 0 8px 0 -4px oklch(100% 0 0 / calc(var(--depth) * 0.1)) inset, 0 1px color-mix(in oklab, currentColor calc(var(--depth) * 10%), #0000), 0 0 0 2rem var(--range-thumb) inset, calc((var(--range-dir, 1) * -100rem) - (var(--range-dir, 1) * var(--range-thumb-size) / 2)) 0 0 calc(100rem * var(--range-fill));
    }
    &::-moz-range-track {
      width: 100%;
      background-color: var(--range-bg);
      border-radius: var(--radius-selector);
      height: calc(var(--range-thumb-size) * 0.5);
    }
    &::-moz-range-thumb {
      position: relative;
      box-sizing: border-box;
      border-radius: calc(var(--radius-selector) + min(var(--range-p), var(--radius-selector-max)));
      background-color: currentColor;
      height: var(--range-thumb-size);
      width: var(--range-thumb-size);
      border: var(--range-p) solid;
      top: 50%;
      color: var(--range-progress);
      box-shadow: 0 -1px oklch(0% 0 0 / calc(var(--depth) * 0.1)) inset, 0 8px 0 -4px oklch(100% 0 0 / calc(var(--depth) * 0.1)) inset, 0 1px color-mix(in oklab, currentColor calc(var(--depth) * 10%), #0000), 0 0 0 2rem var(--range-thumb) inset, calc((var(--range-dir, 1) * -100rem) - (var(--range-dir, 1) * var(--range-thumb-size) / 2)) 0 0 calc(100rem * var(--range-fill));
    }
    &:disabled {
      cursor: not-allowed;
      opacity: 30%;
    }
  }
  .countdown {
    display: inline-flex;
    &.countdown {
      line-height: 1em;
    }
    & > * {
      display: inline-block;
      overflow-y: hidden;
      height: 1em;
      &:before {
        position: relative;
        content: "00\A 01\A 02\A 03\A 04\A 05\A 06\A 07\A 08\A 09\A 10\A 11\A 12\A 13\A 14\A 15\A 16\A 17\A 18\A 19\A 20\A 21\A 22\A 23\A 24\A 25\A 26\A 27\A 28\A 29\A 30\A 31\A 32\A 33\A 34\A 35\A 36\A 37\A 38\A 39\A 40\A 41\A 42\A 43\A 44\A 45\A 46\A 47\A 48\A 49\A 50\A 51\A 52\A 53\A 54\A 55\A 56\A 57\A 58\A 59\A 60\A 61\A 62\A 63\A 64\A 65\A 66\A 67\A 68\A 69\A 70\A 71\A 72\A 73\A 74\A 75\A 76\A 77\A 78\A 79\A 80\A 81\A 82\A 83\A 84\A 85\A 86\A 87\A 88\A 89\A 90\A 91\A 92\A 93\A 94\A 95\A 96\A 97\A 98\A 99\A";
        white-space: pre;
        top: calc(var(--value) * -1em);
        text-align: center;
        transition: all 1s cubic-bezier(1, 0, 0, 1);
      }
    }
  }
  .tabs-border {
    .tab {
      --tab-border-color: #0000 #0000 var(--tab-border-color) #0000;
      position: relative;
      border-radius: var(--radius-field);
      &:before {
        --tw-content: "";
        content: var(--tw-content);
        background-color: var(--tab-border-color);
        transition: background-color 0.2s ease;
        width: 80%;
        height: 3px;
        border-radius: var(--radius-field);
        bottom: 0;
        left: 10%;
        position: absolute;
      }
      &:is(.tab-active, [aria-selected="true"]):not(.tab-disabled, [disabled]), &:is(input:checked), &:is(label:has(:checked)) {
        &:before {
          --tab-border-color: currentColor;
          border-top: 3px solid;
        }
      }
    }
  }
  .chat-bubble {
    position: relative;
    display: block;
    width: fit-content;
    border-radius: var(--radius-field);
    background-color: var(--color-base-300);
    padding-inline: calc(0.25rem * 4);
    padding-block: calc(0.25rem * 2);
    color: var(--color-base-content);
    grid-row-end: 3;
    min-height: 2rem;
    min-width: 2.5rem;
    max-width: 90%;
    &:before {
      position: absolute;
      bottom: calc(0.25rem * 0);
      height: calc(0.25rem * 3);
      width: calc(0.25rem * 3);
      background-color: inherit;
      content: "";
      mask-repeat: no-repeat;
      mask-image: var(--mask-chat);
      mask-position: 0px -1px;
      mask-size: 13px;
    }
  }
  .select {
    border: var(--border) solid #0000;
    position: relative;
    display: inline-flex;
    flex-shrink: 1;
    appearance: none;
    align-items: center;
    gap: calc(0.25rem * 1.5);
    background-color: var(--color-base-100);
    padding-inline-start: calc(0.25rem * 4);
    padding-inline-end: calc(0.25rem * 7);
    vertical-align: middle;
    width: clamp(3rem, 20rem, 100%);
    height: var(--size);
    font-size: 0.875rem;
    touch-action: manipulation;
    border-start-start-radius: var(--join-ss, var(--radius-field));
    border-start-end-radius: var(--join-se, var(--radius-field));
    border-end-start-radius: var(--join-es, var(--radius-field));
    border-end-end-radius: var(--join-ee, var(--radius-field));
    background-image: linear-gradient(45deg, #0000 50%, currentColor 50%), linear-gradient(135deg, currentColor 50%, #0000 50%);
    background-position: calc(100% - 20px) calc(1px + 50%), calc(100% - 16.1px) calc(1px + 50%);
    background-size: 4px 4px, 4px 4px;
    background-repeat: no-repeat;
    text-overflow: ellipsis;
    box-shadow: 0 1px color-mix(in oklab, var(--input-color) calc(var(--depth) * 10%), #0000) inset, 0 -1px oklch(100% 0 0 / calc(var(--depth) * 0.1)) inset;
    border-color: var(--input-color);
    --input-color: color-mix(in oklab, var(--color-base-content) 20%, #0000);
    --size: calc(var(--size-field, 0.25rem) * 10);
    [dir="rtl"] & {
      background-position: calc(0% + 12px) calc(1px + 50%), calc(0% + 16px) calc(1px + 50%);
    }
    select {
      margin-inline-start: calc(0.25rem * -4);
      margin-inline-end: calc(0.25rem * -7);
      width: calc(100% + 2.75rem);
      appearance: none;
      padding-inline-start: calc(0.25rem * 4);
      padding-inline-end: calc(0.25rem * 7);
      height: calc(100% - 2px);
      background: inherit;
      border-radius: inherit;
      border-style: none;
      &:focus, &:focus-within {
        --tw-outline-style: none;
        outline-style: none;
        @media (forced-colors: active) {
          outline: 2px solid transparent;
          outline-offset: 2px;
        }
      }
      &:not(:last-child) {
        margin-inline-end: calc(0.25rem * -5.5);
        background-image: none;
      }
    }
    &:focus, &:focus-within {
      --input-color: var(--color-base-content);
      box-shadow: 0 1px color-mix(in oklab, var(--input-color) calc(var(--depth) * 10%), #0000);
      outline: 2px solid var(--input-color);
      outline-offset: 2px;
      isolation: isolate;
      z-index: 1;
    }
    &:has(> select[disabled]), &:is(:disabled, [disabled]) {
      cursor: not-allowed;
      border-color: var(--color-base-200);
      background-color: var(--color-base-200);
      color: color-mix(in oklab, var(--color-base-content) 40%, transparent);
      &::placeholder {
        color: color-mix(in oklab, var(--color-base-content) 20%, transparent);
      }
    }
    &:has(> select[disabled]) > select[disabled] {
      cursor: not-allowed;
    }
  }
  .timeline {
    position: relative;
    display: flex;
    > li {
      position: relative;
      display: grid;
      flex-shrink: 0;
      align-items: center;
      grid-template-rows: var(--timeline-row-start, minmax(0, 1fr)) auto var( --timeline-row-end, minmax(0, 1fr) );
      grid-template-columns: var(--timeline-col-start, minmax(0, 1fr)) auto var( --timeline-col-end, minmax(0, 1fr) );
      > hr {
        border: none;
        width: 100%;
        &:first-child {
          grid-column-start: 1;
          grid-row-start: 2;
        }
        &:last-child {
          grid-column-start: 3;
          grid-column-end: none;
          grid-row-start: 2;
          grid-row-end: auto;
        }
        @media print {
          border: 0.1px solid var(--color-base-300);
        }
      }
    }
    :where(hr) {
      height: calc(0.25rem * 1);
      background-color: var(--color-base-300);
    }
    &:has(.timeline-middle hr) {
      &:first-child {
        border-start-start-radius: 0;
        border-end-start-radius: 0;
        border-start-end-radius: var(--radius-selector);
        border-end-end-radius: var(--radius-selector);
      }
      &:last-child {
        border-start-start-radius: var(--radius-selector);
        border-end-start-radius: var(--radius-selector);
        border-start-end-radius: 0;
        border-end-end-radius: 0;
      }
    }
    &:not(:has(.timeline-middle)) {
      :first-child hr:last-child {
        border-start-start-radius: var(--radius-selector);
        border-end-start-radius: var(--radius-selector);
        border-start-end-radius: 0;
        border-end-end-radius: 0;
      }
      :last-child hr:first-child {
        border-start-start-radius: 0;
        border-end-start-radius: 0;
        border-start-end-radius: var(--radius-selector);
        border-end-end-radius: var(--radius-selector);
      }
    }
  }
  .card {
    position: relative;
    display: flex;
    flex-direction: column;
    border-radius: var(--radius-box);
    outline-width: 2px;
    transition: outline 0.2s ease-in-out;
    outline: 0 solid #0000;
    outline-offset: 2px;
    &:focus {
      --tw-outline-style: none;
      outline-style: none;
      @media (forced-colors: active) {
        outline: 2px solid transparent;
        outline-offset: 2px;
      }
    }
    &:focus-visible {
      outline-color: currentColor;
    }
    :where(figure:first-child) {
      overflow: hidden;
      border-start-start-radius: inherit;
      border-start-end-radius: inherit;
      border-end-start-radius: unset;
      border-end-end-radius: unset;
    }
    :where(figure:last-child) {
      overflow: hidden;
      border-start-start-radius: unset;
      border-start-end-radius: unset;
      border-end-start-radius: inherit;
      border-end-end-radius: inherit;
    }
    &:where(.card-border) {
      border: var(--border) solid var(--color-base-200);
    }
    &:where(.card-dash) {
      border: var(--border) dashed var(--color-base-200);
    }
    &.image-full {
      display: grid;
      > * {
        grid-column-start: 1;
        grid-row-start: 1;
      }
      > .card-body {
        position: relative;
        color: var(--color-neutral-content);
      }
      :where(figure) {
        overflow: hidden;
        border-radius: inherit;
      }
      > figure img {
        height: 100%;
        object-fit: cover;
        filter: brightness(28%);
      }
    }
    figure {
      display: flex;
      align-items: center;
      justify-content: center;
    }
    &:has(> input:is(input[type="checkbox"], input[type="radio"])) {
      cursor: pointer;
      user-select: none;
    }
    &:has(> :checked) {
      outline: 2px solid currentColor;
    }
  }
  .swap {
    position: relative;
    display: inline-grid;
    cursor: pointer;
    place-content: center;
    vertical-align: middle;
    webkit-user-select: none;
    user-select: none;
    input {
      appearance: none;
      border: none;
    }
    > * {
      grid-column-start: 1;
      grid-row-start: 1;
      transition-property: transform, rotate, opacity;
      transition-duration: 0.2s;
      transition-timing-function: cubic-bezier(0, 0, 0.2, 1);
    }
    .swap-on, .swap-indeterminate, input:indeterminate ~ .swap-on {
      opacity: 0%;
    }
    input:is(:checked, :indeterminate) {
      & ~ .swap-off {
        opacity: 0%;
      }
    }
    input:checked ~ .swap-on, input:indeterminate ~ .swap-indeterminate {
      opacity: 100%;
      backface-visibility: visible;
    }
  }
  .collapse-title {
    grid-column-start: 1;
    grid-row-start: 1;
    position: relative;
    width: 100%;
    padding: 1rem;
    padding-inline-end: 3rem;
    min-height: 1lh;
    transition: background-color 0.2s ease-out;
  }
  .sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border-width: 0;
  }
  .not-sr-only {
    position: static;
    width: auto;
    height: auto;
    padding: 0;
    margin: 0;
    overflow: visible;
    clip: auto;
    white-space: normal;
  }
  .mockup-browser {
    position: relative;
    overflow: hidden;
    overflow-x: auto;
    border-radius: var(--radius-box);
    pre[data-prefix] {
      &:before {
        content: attr(data-prefix);
        display: inline-block;
        text-align: right;
      }
    }
    .mockup-browser-toolbar {
      margin-block: calc(0.25rem * 3);
      display: inline-flex;
      width: 100%;
      align-items: center;
      padding-right: 1.4em;
      &:where(:dir(rtl), [dir="rtl"], [dir="rtl"] *) {
        flex-direction: row-reverse;
      }
      &:before {
        content: "";
        margin-right: 4.8rem;
        display: inline-block;
        aspect-ratio: 1 / 1;
        height: calc(0.25rem * 3);
        border-radius: calc(infinity * 1px);
        opacity: 30%;
        box-shadow: 1.4em 0, 2.8em 0, 4.2em 0;
      }
      .input {
        margin-inline: auto;
        display: flex;
        height: 100%;
        align-items: center;
        gap: calc(0.25rem * 2);
        overflow: hidden;
        background-color: var(--color-base-200);
        text-overflow: ellipsis;
        white-space: nowrap;
        font-size: 0.75rem;
        direction: ltr;
        &:before {
          content: "";
          width: calc(0.25rem * 4);
          height: calc(0.25rem * 4);
          opacity: 30%;
          background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='currentColor' class='size-4'%3E%3Cpath fill-rule='evenodd' d='M9.965 11.026a5 5 0 1 1 1.06-1.06l2.755 2.754a.75.75 0 1 1-1.06 1.06l-2.755-2.754ZM10.5 7a3.5 3.5 0 1 1-7 0 3.5 3.5 0 0 1 7 0Z' clip-rule='evenodd' /%3E%3C/svg%3E%0A");
        }
      }
    }
  }
  .menu-horizontal {
    display: inline-flex;
    flex-direction: row;
    & > li:not(.menu-title) > details > ul {
      position: absolute;
      margin-inline-start: calc(0.25rem * 0);
      margin-top: calc(0.25rem * 4);
      padding-block: calc(0.25rem * 2);
      padding-inline-end: calc(0.25rem * 2);
    }
    & > li > details > ul {
      &:before {
        content: none;
      }
    }
    :where(& > li:not(.menu-title) > details > ul) {
      border-radius: var(--radius-box);
      background-color: var(--color-base-100);
      box-shadow: 0 1px 3px 0 oklch(0% 0 0/0.1), 0 1px 2px -1px oklch(0% 0 0/0.1);
    }
  }
  .menu-vertical {
    display: inline-flex;
    flex-direction: column;
    & > li:not(.menu-title) > details > ul {
      position: relative;
      margin-inline-start: calc(0.25rem * 4);
      margin-top: calc(0.25rem * 0);
      padding-block: calc(0.25rem * 0);
      padding-inline-end: calc(0.25rem * 0);
    }
  }
  .mockup-code {
    position: relative;
    overflow: hidden;
    overflow-x: auto;
    border-radius: var(--radius-box);
    background-color: var(--color-neutral);
    padding-block: calc(0.25rem * 5);
    color: var(--color-neutral-content);
    font-size: 0.875rem;
    direction: ltr;
    &:before {
      content: "";
      margin-bottom: calc(0.25rem * 4);
      display: block;
      height: calc(0.25rem * 3);
      width: calc(0.25rem * 3);
      border-radius: calc(infinity * 1px);
      opacity: 30%;
      box-shadow: 1.4em 0, 2.8em 0, 4.2em 0;
    }
    pre {
      padding-right: calc(0.25rem * 5);
      &:before {
        content: "";
        margin-right: 2ch;
      }
      &[data-prefix] {
        &:before {
          content: attr(data-prefix);
          display: inline-block;
          width: calc(0.25rem * 8);
          text-align: right;
          opacity: 50%;
        }
      }
    }
  }
  .mockup-window {
    position: relative;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    overflow-x: auto;
    border-radius: var(--radius-box);
    padding-top: calc(0.25rem * 5);
    &:before {
      content: "";
      margin-bottom: calc(0.25rem * 4);
      display: block;
      aspect-ratio: 1 / 1;
      height: calc(0.25rem * 3);
      flex-shrink: 0;
      align-self: flex-start;
      border-radius: calc(infinity * 1px);
      opacity: 30%;
      box-shadow: 1.4em 0, 2.8em 0, 4.2em 0;
    }
    [dir="rtl"] &:before {
      align-self: flex-end;
    }
    pre[data-prefix] {
      &:before {
        content: attr(data-prefix);
        display: inline-block;
        text-align: right;
      }
    }
  }
  .avatar {
    position: relative;
    display: inline-flex;
    vertical-align: middle;
    & > div {
      display: block;
      aspect-ratio: 1 / 1;
      overflow: hidden;
    }
    img {
      height: 100%;
      width: 100%;
      object-fit: cover;
    }
  }
  .checkbox {
    border: var(--border) solid var(--input-color, color-mix(in oklab, var(--color-base-content) 20%, #0000));
    position: relative;
    display: inline-block;
    flex-shrink: 0;
    cursor: pointer;
    appearance: none;
    border-radius: var(--radius-selector);
    padding: calc(0.25rem * 1);
    vertical-align: middle;
    color: var(--color-base-content);
    box-shadow: 0 1px oklch(0% 0 0 / calc(var(--depth) * 0.1)) inset, 0 0 #0000 inset, 0 0 #0000;
    transition: background-color 0.2s, box-shadow 0.2s;
    --size: calc(var(--size-selector, 0.25rem) * 6);
    width: var(--size);
    height: var(--size);
    background-size: auto, calc(var(--noise) * 100%);
    background-image: none, var(--fx-noise);
    &:before {
      --tw-content: "";
      content: var(--tw-content);
      display: block;
      width: 100%;
      height: 100%;
      rotate: 45deg;
      background-color: currentColor;
      opacity: 0%;
      transition: clip-path 0.3s, opacity 0.1s, rotate 0.3s, translate 0.3s;
      transition-delay: 0.1s;
      clip-path: polygon(20% 100%, 20% 80%, 50% 80%, 50% 80%, 70% 80%, 70% 100%);
      box-shadow: 0px 3px 0 0px oklch(100% 0 0 / calc(var(--depth) * 0.1)) inset;
      font-size: 1rem;
      line-height: 0.75;
    }
    &:focus-visible {
      outline: 2px solid var(--input-color, currentColor);
      outline-offset: 2px;
    }
    &:checked, &[aria-checked="true"] {
      background-color: var(--input-color, #0000);
      box-shadow: 0 0 #0000 inset, 0 8px 0 -4px oklch(100% 0 0 / calc(var(--depth) * 0.1)) inset, 0 1px oklch(0% 0 0 / calc(var(--depth) * 0.1));
      &:before {
        clip-path: polygon(20% 100%, 20% 80%, 50% 80%, 50% 0%, 70% 0%, 70% 100%);
        opacity: 100%;
      }
      @media (forced-colors: active) {
        &:before {
          rotate: 0deg;
          background-color: transparent;
          --tw-content: "✔︎";
          clip-path: none;
        }
      }
      @media print {
        &:before {
          rotate: 0deg;
          background-color: transparent;
          --tw-content: "✔︎";
          clip-path: none;
        }
      }
    }
    &:indeterminate {
      &:before {
        rotate: 0deg;
        opacity: 100%;
        translate: 0 -35%;
        clip-path: polygon(20% 100%, 20% 80%, 50% 80%, 50% 80%, 80% 80%, 80% 100%);
      }
    }
    &:disabled {
      cursor: not-allowed;
      opacity: 20%;
    }
  }
  .radio {
    position: relative;
    display: inline-block;
    flex-shrink: 0;
    cursor: pointer;
    appearance: none;
    border-radius: calc(infinity * 1px);
    padding: calc(0.25rem * 1);
    vertical-align: middle;
    border: var(--border) solid var(--input-color, color-mix(in srgb, currentColor 20%, #0000));
    box-shadow: 0 1px oklch(0% 0 0 / calc(var(--depth) * 0.1)) inset;
    --size: calc(var(--size-selector, 0.25rem) * 6);
    width: var(--size);
    height: var(--size);
    color: var(--input-color, currentColor);
    &:before {
      display: block;
      width: 100%;
      height: 100%;
      border-radius: calc(infinity * 1px);
      --tw-content: "";
      content: var(--tw-content);
      background-size: auto, calc(var(--noise) * 100%);
      background-image: none, var(--fx-noise);
    }
    &:focus-visible {
      outline: 2px solid currentColor;
    }
    &:checked, &[aria-checked="true"] {
      animation: radio 0.2s ease-out;
      border-color: currentColor;
      background-color: var(--color-base-100);
      &:before {
        background-color: currentColor;
        box-shadow: 0 -1px oklch(0% 0 0 / calc(var(--depth) * 0.1)) inset, 0 8px 0 -4px oklch(100% 0 0 / calc(var(--depth) * 0.1)) inset, 0 1px oklch(0% 0 0 / calc(var(--depth) * 0.1));
      }
      @media (forced-colors: active) {
        &:before {
          outline-style: var(--tw-outline-style);
          outline-width: 1px;
          outline-offset: calc(1px * -1);
        }
      }
      @media print {
        &:before {
          outline: 0.25rem solid;
          outline-offset: -1rem;
        }
      }
    }
    &:disabled {
      cursor: not-allowed;
      opacity: 20%;
    }
  }
  .rating {
    position: relative;
    display: inline-flex;
    vertical-align: middle;
    & input {
      border: none;
      appearance: none;
    }
    :where(*) {
      animation: rating 0.25s ease-out;
      height: calc(0.25rem * 6);
      width: calc(0.25rem * 6);
      border-radius: 0;
      background-color: var(--color-base-content);
      opacity: 20%;
      &:is(input) {
        cursor: pointer;
      }
    }
    & .rating-hidden {
      width: calc(0.25rem * 2);
      background-color: transparent;
    }
    input[type="radio"]:checked {
      background-image: none;
    }
    * {
      &:checked, &[aria-checked="true"], &[aria-current="true"], &:has(~ *:checked, ~ *[aria-checked="true"], ~ *[aria-current="true"]) {
        opacity: 100%;
      }
      &:focus-visible {
        transition: scale 0.2s ease-out;
        scale: 1.1;
      }
    }
    & *:active:focus {
      animation: none;
      scale: 1.1;
    }
    &.rating-xs :where(*:not(.rating-hidden)) {
      width: calc(0.25rem * 4);
      height: calc(0.25rem * 4);
    }
    &.rating-sm :where(*:not(.rating-hidden)) {
      width: calc(0.25rem * 5);
      height: calc(0.25rem * 5);
    }
    &.rating-md :where(*:not(.rating-hidden)) {
      width: calc(0.25rem * 6);
      height: calc(0.25rem * 6);
    }
    &.rating-lg :where(*:not(.rating-hidden)) {
      width: calc(0.25rem * 7);
      height: calc(0.25rem * 7);
    }
    &.rating-xl :where(*:not(.rating-hidden)) {
      width: calc(0.25rem * 8);
      height: calc(0.25rem * 8);
    }
  }
  .drawer {
    position: relative;
    display: grid;
    width: 100%;
    grid-auto-columns: max-content auto;
  }
  .stats {
    position: relative;
    display: inline-grid;
    grid-auto-flow: column;
    overflow-x: auto;
    border-radius: var(--radius-box);
  }
  .progress {
    position: relative;
    height: calc(0.25rem * 2);
    width: 100%;
    appearance: none;
    overflow: hidden;
    border-radius: var(--radius-box);
    background-color: color-mix(in oklab, currentColor 20%, transparent);
    color: var(--color-base-content);
    &:indeterminate {
      background-image: repeating-linear-gradient( 90deg, currentColor -1%, currentColor 10%, #0000 10%, #0000 90% );
      background-size: 200%;
      background-position-x: 15%;
      animation: progress 5s ease-in-out infinite;
      @supports (-moz-appearance: none) {
        &::-moz-progress-bar {
          background-color: transparent;
          background-image: repeating-linear-gradient( 90deg, currentColor -1%, currentColor 10%, #0000 10%, #0000 90% );
          background-size: 200%;
          background-position-x: 15%;
          animation: progress 5s ease-in-out infinite;
        }
      }
    }
    @supports (-moz-appearance: none) {
      &::-moz-progress-bar {
        border-radius: var(--radius-box);
        background-color: currentColor;
      }
    }
    @supports (-webkit-appearance: none) {
      &::-webkit-progress-bar {
        border-radius: var(--radius-box);
        background-color: transparent;
      }
      &::-webkit-progress-value {
        border-radius: var(--radius-box);
        background-color: currentColor;
      }
    }
  }
  .absolute {
    position: absolute;
  }
  .fixed {
    position: fixed;
  }
  .relative {
    position: relative;
  }
  .static {
    position: static;
  }
  .sticky {
    position: sticky;
  }
  .tooltip-bottom {
    > .tooltip-content, &[data-tip]:before {
      transform: translateX(-50%) translateY(var(--tt-pos, -0.25rem));
      inset: var(--tt-off) auto auto 50%;
    }
    &:after {
      transform: translateX(-50%) translateY(var(--tt-pos, -0.25rem)) rotate(180deg);
      inset: var(--tt-tail) auto auto 50%;
    }
  }
  .tooltip-left {
    > .tooltip-content, &[data-tip]:before {
      transform: translateX(calc(var(--tt-pos, 0.25rem) - 0.25rem)) translateY(-50%);
      inset: 50% var(--tt-off) auto auto;
    }
    &:after {
      transform: translateX(var(--tt-pos, 0.25rem)) translateY(-50%) rotate(-90deg);
      inset: 50% calc(var(--tt-tail) + 1px) auto auto;
    }
  }
  .tooltip-right {
    > .tooltip-content, &[data-tip]:before {
      transform: translateX(calc(var(--tt-pos, -0.25rem) + 0.25rem)) translateY(-50%);
      inset: 50% auto auto var(--tt-off);
    }
    &:after {
      transform: translateX(var(--tt-pos, -0.25rem)) translateY(-50%) rotate(90deg);
      inset: 50% auto auto calc(var(--tt-tail) + 1px);
    }
  }
  .tooltip-top {
    > .tooltip-content, &[data-tip]:before {
      transform: translateX(-50%) translateY(var(--tt-pos, 0.25rem));
      inset: auto auto var(--tt-off) 50%;
    }
    &:after {
      transform: translateX(-50%) translateY(var(--tt-pos, 0.25rem));
      inset: auto auto var(--tt-tail) 50%;
    }
  }
  .dropdown-right {
    --anchor-h: right;
    --anchor-v: span-bottom;
    .dropdown-content {
      inset-inline-start: 100%;
      top: calc(0.25rem * 0);
      bottom: auto;
      transform-origin: left;
    }
  }
  .chat-end {
    place-items: end;
    grid-template-columns: 1fr auto;
    .chat-header {
      grid-column-start: 1;
    }
    .chat-footer {
      grid-column-start: 1;
    }
    .chat-image {
      grid-column-start: 2;
    }
    .chat-bubble {
      grid-column-start: 1;
      border-end-end-radius: 0;
      &:before {
        transform: rotateY(180deg);
        inset-inline-start: 100%;
      }
      [dir="rtl"] &:before {
        transform: rotateY(0deg);
      }
    }
  }
  .chat-start {
    place-items: start;
    grid-template-columns: auto 1fr;
    .chat-header {
      grid-column-start: 2;
    }
    .chat-footer {
      grid-column-start: 2;
    }
    .chat-image {
      grid-column-start: 1;
    }
    .chat-bubble {
      grid-column-start: 2;
      border-end-start-radius: 0;
      &:before {
        transform: rotateY(0deg);
        inset-inline-start: -0.75rem;
      }
      [dir="rtl"] &:before {
        transform: rotateY(180deg);
      }
    }
  }
  .dropdown-left {
    --anchor-h: left;
    --anchor-v: span-bottom;
    .dropdown-content {
      inset-inline-end: 100%;
      top: calc(0.25rem * 0);
      bottom: auto;
      transform-origin: right;
    }
  }
  .dropdown-center {
    --anchor-h: center;
    :where(.dropdown-content) {
      inset-inline-end: calc(1/2 * 100%);
      translate: 50% 0;
      [dir="rtl"] & {
        translate: -50% 0;
      }
    }
    &.dropdown-left {
      --anchor-h: left;
      --anchor-v: center;
      .dropdown-content {
        top: auto;
        bottom: calc(1/2 * 100%);
        translate: 0 50%;
      }
    }
    &.dropdown-right {
      --anchor-h: right;
      --anchor-v: center;
      .dropdown-content {
        top: auto;
        bottom: calc(1/2 * 100%);
        translate: 0 50%;
      }
    }
  }
  .dropdown-end {
    --anchor-h: span-left;
    :where(.dropdown-content) {
      inset-inline-end: calc(0.25rem * 0);
      translate: 0 0;
    }
    &.dropdown-left {
      --anchor-h: left;
      --anchor-v: span-top;
      .dropdown-content {
        top: auto;
        bottom: calc(0.25rem * 0);
      }
    }
    &.dropdown-right {
      --anchor-h: right;
      --anchor-v: span-top;
      .dropdown-content {
        top: auto;
        bottom: calc(0.25rem * 0);
      }
    }
  }
  .dropdown-start {
    --anchor-h: span-right;
    :where(.dropdown-content) {
      inset-inline-end: auto;
    }
    &.dropdown-left {
      --anchor-h: left;
      --anchor-v: span-bottom;
      .dropdown-content {
        top: calc(0.25rem * 0);
        bottom: auto;
      }
    }
    &.dropdown-right {
      --anchor-h: right;
      --anchor-v: span-bottom;
      .dropdown-content {
        top: calc(0.25rem * 0);
        bottom: auto;
      }
    }
  }
  .dropdown-bottom {
    --anchor-v: bottom;
    .dropdown-content {
      top: 100%;
      bottom: auto;
      transform-origin: top;
    }
  }
  .dropdown-top {
    --anchor-v: top;
    .dropdown-content {
      top: auto;
      bottom: 100%;
      transform-origin: bottom;
    }
  }
  .top-0 {
    top: calc(var(--spacing) * 0);
  }
  .dock-sm {
    height: calc(0.25rem * 14);
    height: 3.5rem;
    height: calc(3.5rem + env(safe-area-inset-bottom));
    .dock-active {
      &:after {
        bottom: -0.1rem;
      }
    }
    .dock-label {
      font-size: 0.625rem;
    }
  }
  .dock-lg {
    height: 4.5rem;
    height: calc(4.5rem + env(safe-area-inset-bottom));
    .dock-active {
      &:after {
        bottom: 0.4rem;
      }
    }
    .dock-label {
      font-size: 0.6875rem;
    }
  }
  .dock-xl {
    height: 5rem;
    height: calc(5rem + env(safe-area-inset-bottom));
    .dock-active {
      &:after {
        bottom: 0.4rem;
      }
    }
    .dock-label {
      font-size: 0.75rem;
    }
  }
  .dock-xs {
    height: 3rem;
    height: calc(3rem + env(safe-area-inset-bottom));
    .dock-active {
      &:after {
        bottom: -0.1rem;
      }
    }
    .dock-label {
      font-size: 0.625rem;
    }
  }
  .file-input {
    cursor: pointer;
    cursor: pointer;
    border: var(--border) solid #0000;
    display: inline-flex;
    appearance: none;
    align-items: center;
    background-color: var(--color-base-100);
    vertical-align: middle;
    webkit-user-select: none;
    user-select: none;
    width: clamp(3rem, 20rem, 100%);
    height: var(--size);
    padding-inline-end: 0.75rem;
    font-size: 0.875rem;
    line-height: 2;
    border-start-start-radius: var(--join-ss, var(--radius-field));
    border-start-end-radius: var(--join-se, var(--radius-field));
    border-end-start-radius: var(--join-es, var(--radius-field));
    border-end-end-radius: var(--join-ee, var(--radius-field));
    border-color: var(--input-color);
    box-shadow: 0 1px color-mix(in oklab, var(--input-color) calc(var(--depth) * 10%), #0000) inset, 0 -1px oklch(100% 0 0 / calc(var(--depth) * 0.1)) inset;
    --size: calc(var(--size-field, 0.25rem) * 10);
    --input-color: color-mix(in oklab, var(--color-base-content) 20%, #0000);
    &::file-selector-button {
      margin-inline-end: calc(0.25rem * 4);
      cursor: pointer;
      padding-inline: calc(0.25rem * 4);
      webkit-user-select: none;
      user-select: none;
      height: calc(100% + var(--border) * 2);
      margin-block: calc(var(--border) * -1);
      margin-inline-start: calc(var(--border) * -1);
      font-size: 0.875rem;
      color: var(--btn-fg);
      border-width: var(--border);
      border-style: solid;
      border-color: var(--btn-border);
      border-start-start-radius: calc(var(--join-ss, var(--radius-field) - var(--border)));
      border-end-start-radius: calc(var(--join-es, var(--radius-field) - var(--border)));
      font-weight: 600;
      background-color: var(--btn-bg);
      background-size: calc(var(--noise) * 100%);
      background-image: var(--btn-noise);
      text-shadow: 0 0.5px oklch(1 0 0 / calc(var(--depth) * 0.15));
      box-shadow: 0 0.5px 0 0.5px color-mix( in oklab, color-mix(in oklab, white 30%, var(--btn-bg)) calc(var(--depth) * 20%), #0000 ) inset, var(--btn-shadow);
      --size: calc(var(--size-field, 0.25rem) * 10);
      --btn-bg: var(--btn-color, var(--color-base-200));
      --btn-fg: var(--color-base-content);
      --btn-border: color-mix(in oklab, var(--btn-bg), #000 5%);
      --btn-shadow: 0 3px 2px -2px color-mix(in oklab, var(--btn-bg) 30%, #0000),
      0 4px 3px -2px color-mix(in oklab, var(--btn-bg) 30%, #0000);
      --btn-noise: var(--fx-noise);
    }
    &:focus {
      --input-color: var(--color-base-content);
      box-shadow: 0 1px color-mix(in oklab, var(--input-color) 10%, #0000);
      outline: 2px solid var(--input-color);
      outline-offset: 2px;
      isolation: isolate;
    }
    &:has(> input[disabled]), &:is(:disabled, [disabled]) {
      cursor: not-allowed;
      border-color: var(--color-base-200);
      background-color: var(--color-base-200);
      &::placeholder {
        color: color-mix(in oklab, var(--color-base-content) 20%, transparent);
      }
      box-shadow: none;
      color: color-mix(in oklch, var(--color-base-content) 20%, #0000);
      &::file-selector-button {
        cursor: not-allowed;
        border-color: var(--color-base-200);
        background-color: var(--color-base-200);
        --btn-border: #0000;
        --btn-noise: none;
        --btn-fg: color-mix(in oklch, var(--color-base-content) 20%, #0000);
      }
    }
  }
  .hero-content {
    isolation: isolate;
    display: flex;
    max-width: 80rem;
    align-items: center;
    justify-content: center;
    gap: calc(0.25rem * 4);
    padding: calc(0.25rem * 4);
  }
  .textarea {
    border: var(--border) solid #0000;
    min-height: calc(0.25rem * 20);
    flex-shrink: 1;
    appearance: none;
    border-radius: var(--radius-field);
    background-color: var(--color-base-100);
    padding-block: calc(0.25rem * 2);
    vertical-align: middle;
    width: clamp(3rem, 20rem, 100%);
    padding-inline-start: 0.75rem;
    padding-inline-end: 0.75rem;
    font-size: 0.875rem;
    touch-action: manipulation;
    border-color: var(--input-color);
    box-shadow: 0 1px color-mix(in oklab, var(--input-color) calc(var(--depth) * 10%), #0000) inset, 0 -1px oklch(100% 0 0 / calc(var(--depth) * 0.1)) inset;
    --input-color: color-mix(in oklab, var(--color-base-content) 20%, #0000);
    textarea {
      appearance: none;
      background-color: transparent;
      border: none;
      &:focus, &:focus-within {
        --tw-outline-style: none;
        outline-style: none;
        @media (forced-colors: active) {
          outline: 2px solid transparent;
          outline-offset: 2px;
        }
      }
    }
    &:focus, &:focus-within {
      --input-color: var(--color-base-content);
      box-shadow: 0 1px color-mix(in oklab, var(--input-color) calc(var(--depth) * 10%), #0000);
      outline: 2px solid var(--input-color);
      outline-offset: 2px;
      isolation: isolate;
    }
    &:has(> textarea[disabled]), &:is(:disabled, [disabled]) {
      cursor: not-allowed;
      border-color: var(--color-base-200);
      background-color: var(--color-base-200);
      color: color-mix(in oklab, var(--color-base-content) 40%, transparent);
      &::placeholder {
        color: color-mix(in oklab, var(--color-base-content) 20%, transparent);
      }
      box-shadow: none;
    }
    &:has(> textarea[disabled]) > textarea[disabled] {
      cursor: not-allowed;
    }
  }
  .btn-active {
    --btn-bg: color-mix(in oklab, var(--btn-color, var(--color-base-200)), #000 7%);
    --btn-shadow: 0 0 0 0 oklch(0% 0 0/0), 0 0 0 0 oklch(0% 0 0/0);
    isolation: isolate;
  }
  .isolate {
    isolation: isolate;
  }
  .isolation-auto {
    isolation: auto;
  }
  .mockup-phone-camera {
    grid-column: 1/1;
    grid-row: 1/1;
    background: #000;
    height: 32px;
    width: 126px;
    border-radius: 17px;
    z-index: 1;
    margin-top: 6px;
  }
  .stack {
    display: inline-grid;
    grid-template-columns: 3px 4px 1fr 4px 3px;
    grid-template-rows: 3px 4px 1fr 4px 3px;
    & > * {
      height: 100%;
      width: 100%;
      &:nth-child(n + 2) {
        width: 100%;
        opacity: 70%;
      }
      &:nth-child(2) {
        z-index: 2;
        opacity: 90%;
      }
      &:nth-child(1) {
        z-index: 3;
        width: 100%;
      }
    }
    &, &.stack-bottom {
      > * {
        grid-column: 3 / 4;
        grid-row: 3 / 6;
        &:nth-child(2) {
          grid-column: 2 / 5;
          grid-row: 2 / 5;
        }
        &:nth-child(1) {
          grid-column: 1 / 6;
          grid-row: 1 / 4;
        }
      }
    }
    &.stack-top {
      > * {
        grid-column: 3 / 4;
        grid-row: 1 / 4;
        &:nth-child(2) {
          grid-column: 2 / 5;
          grid-row: 2 / 5;
        }
        &:nth-child(1) {
          grid-column: 1 / 6;
          grid-row: 3 / 6;
        }
      }
    }
    &.stack-start {
      > * {
        grid-column: 1 / 4;
        grid-row: 3 / 4;
        &:nth-child(2) {
          grid-column: 2 / 5;
          grid-row: 2 / 5;
        }
        &:nth-child(1) {
          grid-column: 3 / 6;
          grid-row: 1 / 6;
        }
      }
    }
    &.stack-end {
      > * {
        grid-column: 3 / 6;
        grid-row: 3 / 4;
        &:nth-child(2) {
          grid-column: 2 / 5;
          grid-row: 2 / 5;
        }
        &:nth-child(1) {
          grid-column: 1 / 4;
          grid-row: 1 / 6;
        }
      }
    }
  }
  .modal-backdrop {
    grid-column-start: 1;
    grid-row-start: 1;
    display: grid;
    align-self: stretch;
    justify-self: stretch;
    color: transparent;
    z-index: -1;
    button {
      cursor: pointer;
    }
  }
  .z-10 {
    z-index: 10;
  }
  .z-auto {
    z-index: auto;
  }
  .tab-content {
    order: var(--tabcontent-order);
    display: none;
    border-color: transparent;
    --tabcontent-radius-ss: 0;
    --tabcontent-radius-se: 0;
    --tabcontent-radius-es: 0;
    --tabcontent-radius-ee: 0;
    --tabcontent-order: 1;
    width: 100%;
    margin: var(--tabcontent-margin);
    border-width: var(--border);
    border-start-start-radius: var(--tabcontent-radius-ss);
    border-start-end-radius: var(--tabcontent-radius-se);
    border-end-start-radius: var(--tabcontent-radius-es);
    border-end-end-radius: var(--tabcontent-radius-ee);
  }
  .order-1 {
    order: 1;
  }
  .order-2 {
    order: 2;
  }
  .order-first {
    order: -9999;
  }
  .order-last {
    order: 9999;
  }
  .order-none {
    order: 0;
  }
  .mockup-phone-display {
    grid-column: 1/1;
    grid-row: 1/1;
    overflow: hidden;
    border-radius: 49px;
    width: 390px;
    height: 845px;
  }
  .col-auto {
    grid-column: auto;
  }
  .col-span-full {
    grid-column: 1 / -1;
  }
  .timeline-end {
    grid-column-start: 1;
    grid-column-end: 4;
    grid-row-start: 3;
    grid-row-end: 4;
    margin: calc(0.25rem * 1);
    align-self: flex-start;
    justify-self: center;
  }
  .timeline-start {
    grid-column-start: 1;
    grid-column-end: 4;
    grid-row-start: 1;
    grid-row-end: 2;
    margin: calc(0.25rem * 1);
    align-self: flex-end;
    justify-self: center;
  }
  .timeline-horizontal {
    flex-direction: row;
    > li {
      align-items: center;
      > hr {
        height: calc(0.25rem * 1);
        width: 100%;
        &:first-child {
          grid-column-start: 1;
          grid-row-start: 2;
        }
        &:last-child {
          grid-column-start: 3;
          grid-column-end: none;
          grid-row-start: 2;
          grid-row-end: auto;
        }
      }
    }
    .timeline-start {
      grid-column-start: 1;
      grid-column-end: 4;
      grid-row-start: 1;
      grid-row-end: 2;
      align-self: flex-end;
      justify-self: center;
    }
    .timeline-end {
      grid-column-start: 1;
      grid-column-end: 4;
      grid-row-start: 3;
      grid-row-end: 4;
      align-self: flex-start;
      justify-self: center;
    }
    &:has(.timeline-middle) {
      > li {
        > hr {
          &:first-child {
            border-start-start-radius: 0;
            border-end-start-radius: 0;
            border-start-end-radius: var(--radius-selector);
            border-end-end-radius: var(--radius-selector);
          }
          &:last-child {
            border-start-start-radius: var(--radius-selector);
            border-end-start-radius: var(--radius-selector);
            border-start-end-radius: 0;
            border-end-end-radius: 0;
          }
        }
      }
    }
    &:not(:has(.timeline-middle)) {
      :first-child {
        > hr:last-child {
          border-start-start-radius: var(--radius-selector);
          border-end-start-radius: var(--radius-selector);
          border-start-end-radius: 0;
          border-end-end-radius: 0;
        }
      }
      :last-child {
        > hr:first-child {
          border-start-start-radius: 0;
          border-end-start-radius: 0;
          border-start-end-radius: var(--radius-selector);
          border-end-end-radius: var(--radius-selector);
        }
      }
    }
  }
  .timeline-vertical {
    flex-direction: column;
    > li {
      justify-items: center;
      --timeline-row-start: minmax(0, 1fr);
      --timeline-row-end: minmax(0, 1fr);
      > hr {
        height: 100%;
        width: calc(0.25rem * 1);
        &:first-child {
          grid-column-start: 2;
          grid-row-start: 1;
        }
        &:last-child {
          grid-column-start: 2;
          grid-column-end: auto;
          grid-row-start: 3;
          grid-row-end: none;
        }
      }
    }
    .timeline-start {
      grid-column-start: 1;
      grid-column-end: 2;
      grid-row-start: 1;
      grid-row-end: 4;
      align-self: center;
      justify-self: flex-end;
    }
    .timeline-end {
      grid-column-start: 3;
      grid-column-end: 4;
      grid-row-start: 1;
      grid-row-end: 4;
      align-self: center;
      justify-self: flex-start;
    }
    &:has(.timeline-middle) {
      > li {
        > hr {
          &:first-child {
            border-top-left-radius: 0;
            border-top-right-radius: 0;
            border-bottom-right-radius: var(--radius-selector);
            border-bottom-left-radius: var(--radius-selector);
          }
          &:last-child {
            border-top-left-radius: var(--radius-selector);
            border-top-right-radius: var(--radius-selector);
            border-bottom-right-radius: 0;
            border-bottom-left-radius: 0;
          }
        }
      }
    }
    &:not(:has(.timeline-middle)) {
      :first-child {
        > hr:last-child {
          border-top-left-radius: var(--radius-selector);
          border-top-right-radius: var(--radius-selector);
          border-bottom-right-radius: 0;
          border-bottom-left-radius: 0;
        }
      }
      :last-child {
        > hr:first-child {
          border-top-left-radius: 0;
          border-top-right-radius: 0;
          border-bottom-right-radius: var(--radius-selector);
          border-bottom-left-radius: var(--radius-selector);
        }
      }
    }
    &.timeline-snap-icon {
      > li {
        --timeline-col-start: minmax(0, 1fr);
        --timeline-row-start: 0.5rem;
      }
    }
  }
  .timeline-compact {
    --timeline-row-start: 0;
    .timeline-start {
      grid-column-start: 1;
      grid-column-end: 4;
      grid-row-start: 3;
      grid-row-end: 4;
      align-self: flex-start;
      justify-self: center;
    }
    li:has(.timeline-start) {
      .timeline-end {
        grid-column-start: none;
        grid-row-start: auto;
      }
    }
    &.timeline-vertical {
      > li {
        --timeline-col-start: 0;
      }
      .timeline-start {
        grid-column-start: 3;
        grid-column-end: 4;
        grid-row-start: 1;
        grid-row-end: 4;
        align-self: center;
        justify-self: flex-start;
      }
      li:has(.timeline-start) {
        .timeline-end {
          grid-column-start: auto;
          grid-row-start: none;
        }
      }
    }
  }
  .stat-figure {
    grid-column-start: 2;
    grid-row: span 3 / span 3;
    grid-row-start: 1;
    place-self: center;
    justify-self: flex-end;
  }
  .hero {
    display: grid;
    width: 100%;
    place-items: center;
    background-size: cover;
    background-position: center;
    & > * {
      grid-column-start: 1;
      grid-row-start: 1;
    }
  }
  .hero-overlay {
    grid-column-start: 1;
    grid-row-start: 1;
    height: 100%;
    width: 100%;
    background-color: color-mix(in oklab, var(--color-neutral) 50%, transparent);
  }
  .modal-box {
    grid-column-start: 1;
    grid-row-start: 1;
    max-height: 100vh;
    width: calc(11/12 * 100%);
    max-width: 32rem;
    background-color: var(--color-base-100);
    padding: calc(0.25rem * 6);
    transition: translate 0.3s ease-out, scale 0.3s ease-out, opacity 0.2s ease-out 0.05s, box-shadow 0.3s ease-out;
    border-top-left-radius: var(--modal-tl, var(--radius-box));
    border-top-right-radius: var(--modal-tr, var(--radius-box));
    border-bottom-left-radius: var(--modal-bl, var(--radius-box));
    border-bottom-right-radius: var(--modal-br, var(--radius-box));
    scale: 95%;
    opacity: 0;
    box-shadow: oklch(0% 0 0/ 0.25) 0px 25px 50px -12px;
    overflow-y: auto;
    overscroll-behavior: contain;
  }
  .drawer-content {
    grid-column-start: 2;
    grid-row-start: 1;
    min-width: calc(0.25rem * 0);
  }
  .timeline-middle {
    grid-column-start: 2;
    grid-row-start: 2;
  }
  .drawer-end {
    grid-auto-columns: auto max-content;
    > .drawer-toggle {
      & ~ .drawer-content {
        grid-column-start: 1;
      }
      & ~ .drawer-side {
        grid-column-start: 2;
        justify-items: end;
      }
      & ~ .drawer-side > *:not(.drawer-overlay) {
        translate: 100%;
        [dir="rtl"] & {
          translate: -100%;
        }
      }
      &:checked ~ .drawer-side > *:not(.drawer-overlay) {
        translate: 0%;
      }
    }
  }
  .stat-value {
    grid-column-start: 1;
    white-space: nowrap;
    font-size: 2rem;
    font-weight: 800;
  }
  .stat-desc {
    grid-column-start: 1;
    white-space: nowrap;
    color: color-mix(in oklab, var(--color-base-content) 60%, transparent);
    font-size: 0.75rem;
  }
  .stat-title {
    grid-column-start: 1;
    white-space: nowrap;
    color: color-mix(in oklab, var(--color-base-content) 60%, transparent);
    font-size: 0.75rem;
  }
  .stat-actions {
    grid-column-start: 1;
    white-space: nowrap;
  }
  .col-start-auto {
    grid-column-start: auto;
  }
  .col-end-auto {
    grid-column-end: auto;
  }
  .chat-image {
    grid-row: span 2 / span 2;
    align-self: flex-end;
  }
  .row-auto {
    grid-row: auto;
  }
  .row-span-full {
    grid-row: 1 / -1;
  }
  .chat-footer {
    grid-row-start: 3;
    display: flex;
    gap: calc(0.25rem * 1);
    font-size: 0.6875rem;
  }
  .chat-header {
    grid-row-start: 1;
    display: flex;
    gap: calc(0.25rem * 1);
    font-size: 0.6875rem;
  }
  .list-col-wrap {
    grid-row-start: 2;
  }
  .row-start-auto {
    grid-row-start: auto;
  }
  .row-end-auto {
    grid-row-end: auto;
  }
  .float-end {
    float: inline-end;
  }
  .float-left {
    float: left;
  }
  .float-none {
    float: none;
  }
  .float-right {
    float: right;
  }
  .float-start {
    float: inline-start;
  }
  .clear-both {
    clear: both;
  }
  .clear-end {
    clear: inline-end;
  }
  .clear-left {
    clear: left;
  }
  .clear-none {
    clear: none;
  }
  .clear-right {
    clear: right;
  }
  .clear-start {
    clear: inline-start;
  }
  .container {
    width: 100%;
    @media (width >= 40rem) {
      max-width: 40rem;
    }
    @media (width >= 48rem) {
      max-width: 48rem;
    }
    @media (width >= 64rem) {
      max-width: 64rem;
    }
    @media (width >= 80rem) {
      max-width: 80rem;
    }
    @media (width >= 96rem) {
      max-width: 96rem;
    }
  }
  .container\! {
    width: 100% !important;
    @media (width >= 40rem) {
      max-width: 40rem !important;
    }
    @media (width >= 48rem) {
      max-width: 48rem !important;
    }
    @media (width >= 64rem) {
      max-width: 64rem !important;
    }
    @media (width >= 80rem) {
      max-width: 80rem !important;
    }
    @media (width >= 96rem) {
      max-width: 96rem !important;
    }
  }
  .divider {
    display: flex;
    height: calc(0.25rem * 4);
    flex-direction: row;
    align-items: center;
    align-self: stretch;
    white-space: nowrap;
    margin: var(--divider-m, 1rem 0);
    --divider-color: color-mix(in oklab, var(--color-base-content) 10%, transparent);
    &:before, &:after {
      content: "";
      height: calc(0.25rem * 0.5);
      width: 100%;
      flex-grow: 1;
      background-color: var(--divider-color);
    }
    @media print {
      &:before, &:after {
        border: 0.5px solid;
      }
    }
    &:not(:empty) {
      gap: calc(0.25rem * 4);
    }
  }
  .filter {
    display: flex;
    flex-wrap: wrap;
    input[type="radio"] {
      width: auto;
    }
    input {
      overflow: hidden;
      opacity: 100%;
      scale: 1;
      transition: margin 0.1s, opacity 0.3s, padding 0.3s, border-width 0.1s;
      &:not(:last-child) {
        margin-inline-end: calc(0.25rem * 1);
      }
      &.filter-reset {
        aspect-ratio: 1 / 1;
        &::after {
          content: "×";
        }
      }
    }
    &:not(:has(input:checked:not(.filter-reset))) {
      .filter-reset, input[type="reset"] {
        scale: 0;
        border-width: 0;
        margin-inline: calc(0.25rem * 0);
        width: calc(0.25rem * 0);
        padding-inline: calc(0.25rem * 0);
        opacity: 0%;
      }
    }
    &:has(input:checked:not(.filter-reset)) {
      input:not(:checked, .filter-reset, input[type="reset"]) {
        scale: 0;
        border-width: 0;
        margin-inline: calc(0.25rem * 0);
        width: calc(0.25rem * 0);
        padding-inline: calc(0.25rem * 0);
        opacity: 0%;
      }
    }
  }
  .file-input-ghost {
    background-color: transparent;
    transition: background-color 0.2s;
    box-shadow: none;
    border-color: #0000;
    &::file-selector-button {
      margin-inline-start: calc(0.25rem * 0);
      margin-inline-end: calc(0.25rem * 4);
      height: 100%;
      cursor: pointer;
      padding-inline: calc(0.25rem * 4);
      webkit-user-select: none;
      user-select: none;
      margin-block: 0;
      border-start-end-radius: calc(var(--join-ss, var(--radius-field) - var(--border)));
      border-end-end-radius: calc(var(--join-es, var(--radius-field) - var(--border)));
    }
    &:focus, &:focus-within {
      background-color: var(--color-base-100);
      color: var(--color-base-content);
      border-color: #0000;
      box-shadow: none;
    }
  }
  .input-lg {
    --size: calc(var(--size-field, 0.25rem) * 12);
    font-size: 1.125rem;
    &[type="number"] {
      &::-webkit-inner-spin-button {
        margin-block: calc(0.25rem * -3);
        margin-inline-end: calc(0.25rem * -3);
      }
    }
  }
  .input-md {
    --size: calc(var(--size-field, 0.25rem) * 10);
    font-size: 0.875rem;
    &[type="number"] {
      &::-webkit-inner-spin-button {
        margin-block: calc(0.25rem * -3);
        margin-inline-end: calc(0.25rem * -3);
      }
    }
  }
  .input-sm {
    --size: calc(var(--size-field, 0.25rem) * 8);
    font-size: 0.75rem;
    &[type="number"] {
      &::-webkit-inner-spin-button {
        margin-block: calc(0.25rem * -2);
        margin-inline-end: calc(0.25rem * -3);
      }
    }
  }
  .input-xl {
    --size: calc(var(--size-field, 0.25rem) * 14);
    font-size: 1.375rem;
    &[type="number"] {
      &::-webkit-inner-spin-button {
        margin-block: calc(0.25rem * -4);
        margin-inline-end: calc(0.25rem * -3);
      }
    }
  }
  .input-xs {
    --size: calc(var(--size-field, 0.25rem) * 6);
    font-size: 0.6875rem;
    &[type="number"] {
      &::-webkit-inner-spin-button {
        margin-block: calc(0.25rem * -1);
        margin-inline-end: calc(0.25rem * -3);
      }
    }
  }
  .my-4 {
    margin-block: calc(var(--spacing) * 4);
  }
  .my-8 {
    margin-block: calc(var(--spacing) * 8);
  }
  .my-16 {
    margin-block: calc(var(--spacing) * 16);
  }
  .my-24 {
    margin-block: calc(var(--spacing) * 24);
  }
  .my-32 {
    margin-block: calc(var(--spacing) * 32);
  }
  .label {
    display: inline-flex;
    align-items: center;
    gap: calc(0.25rem * 1.5);
    white-space: nowrap;
    color: color-mix(in oklab, currentColor 60%, transparent);
    &:has(input) {
      cursor: pointer;
    }
    &:is(.input > *, .select > *) {
      display: flex;
      height: calc(100% - 0.5rem);
      align-items: center;
      padding-inline: calc(0.25rem * 3);
      white-space: nowrap;
      font-size: inherit;
      &:first-child {
        margin-inline-start: calc(0.25rem * -3);
        margin-inline-end: calc(0.25rem * 3);
        border-inline-end: var(--border) solid color-mix(in oklab, currentColor 10%, #0000);
      }
      &:last-child {
        margin-inline-start: calc(0.25rem * 3);
        margin-inline-end: calc(0.25rem * -3);
        border-inline-start: var(--border) solid color-mix(in oklab, currentColor 10%, #0000);
      }
    }
  }
  .label\! {
    display: inline-flex !important;
    align-items: center !important;
    gap: calc(0.25rem * 1.5) !important;
    white-space: nowrap !important;
    color: color-mix(in oklab, currentColor 60%, transparent) !important;
    &:has(input) {
      cursor: pointer !important;
    }
    &:is(.input > *, .select > *) {
      display: flex !important;
      height: calc(100% - 0.5rem) !important;
      align-items: center !important;
      padding-inline: calc(0.25rem * 3) !important;
      white-space: nowrap !important;
      font-size: inherit !important;
      &:first-child {
        margin-inline-start: calc(0.25rem * -3) !important;
        margin-inline-end: calc(0.25rem * 3) !important;
        border-inline-end: var(--border) solid color-mix(in oklab, currentColor 10%, #0000) !important;
      }
      &:last-child {
        margin-inline-start: calc(0.25rem * 3) !important;
        margin-inline-end: calc(0.25rem * -3) !important;
        border-inline-start: var(--border) solid color-mix(in oklab, currentColor 10%, #0000) !important;
      }
    }
  }
  .steps-vertical {
    grid-auto-rows: 1fr;
    grid-auto-flow: row;
    .step {
      display: grid;
      grid-template-columns: repeat(2, minmax(0, 1fr));
      grid-template-columns: 40px 1fr;
      grid-template-rows: repeat(1, minmax(0, 1fr));
      grid-template-rows: auto;
      gap: 0.5rem;
      min-height: 4rem;
      justify-items: start;
      &:before {
        height: 100%;
        width: calc(0.25rem * 2);
        translate: -50% -50%;
        margin-inline-start: 50%;
      }
      [dir="rtl"] &:before {
        translate: 50% -50%;
      }
    }
  }
  .steps-horizontal {
    grid-auto-columns: 1fr;
    display: inline-grid;
    grid-auto-flow: column;
    overflow: hidden;
    overflow-x: auto;
    .step {
      display: grid;
      grid-template-columns: repeat(1, minmax(0, 1fr));
      grid-template-columns: auto;
      grid-template-rows: repeat(2, minmax(0, 1fr));
      grid-template-rows: 40px 1fr;
      place-items: center;
      text-align: center;
      min-width: 4rem;
      &:before {
        height: calc(0.25rem * 2);
        width: 100%;
        translate: 0;
        content: "";
        margin-inline-start: -100%;
      }
      [dir="rtl"] &:before {
        translate: 0;
      }
    }
  }
  .join-horizontal {
    flex-direction: row;
    > .join-item:first-child {
      --join-ss: var(--radius-field);
      --join-se: 0;
      --join-es: var(--radius-field);
      --join-ee: 0;
    }
    :first-child:not(:last-child) {
      .join-item {
        --join-ss: var(--radius-field);
        --join-se: 0;
        --join-es: var(--radius-field);
        --join-ee: 0;
      }
    }
    > .join-item:last-child {
      --join-ss: 0;
      --join-se: var(--radius-field);
      --join-es: 0;
      --join-ee: var(--radius-field);
    }
    :last-child:not(:first-child) {
      .join-item {
        --join-ss: 0;
        --join-se: var(--radius-field);
        --join-es: 0;
        --join-ee: var(--radius-field);
      }
    }
    > .join-item:only-child {
      --join-ss: var(--radius-field);
      --join-se: var(--radius-field);
      --join-es: var(--radius-field);
      --join-ee: var(--radius-field);
    }
    :only-child {
      .join-item {
        --join-ss: var(--radius-field);
        --join-se: var(--radius-field);
        --join-es: var(--radius-field);
        --join-ee: var(--radius-field);
      }
    }
    .join-item {
      &:where(*:not(:first-child)) {
        margin-inline-start: calc(var(--border, 1px) * -1);
        margin-block-start: 0;
      }
    }
  }
  .join-vertical {
    flex-direction: column;
    > .join-item:first-child {
      --join-ss: var(--radius-field);
      --join-se: var(--radius-field);
      --join-es: 0;
      --join-ee: 0;
    }
    :first-child:not(:last-child) {
      .join-item {
        --join-ss: var(--radius-field);
        --join-se: var(--radius-field);
        --join-es: 0;
        --join-ee: 0;
      }
    }
    > .join-item:last-child {
      --join-ss: 0;
      --join-se: 0;
      --join-es: var(--radius-field);
      --join-ee: var(--radius-field);
    }
    :last-child:not(:first-child) {
      .join-item {
        --join-ss: 0;
        --join-se: 0;
        --join-es: var(--radius-field);
        --join-ee: var(--radius-field);
      }
    }
    > .join-item:only-child {
      --join-ss: var(--radius-field);
      --join-se: var(--radius-field);
      --join-es: var(--radius-field);
      --join-ee: var(--radius-field);
    }
    :only-child {
      .join-item {
        --join-ss: var(--radius-field);
        --join-se: var(--radius-field);
        --join-es: var(--radius-field);
        --join-ee: var(--radius-field);
      }
    }
    .join-item {
      &:where(*:not(:first-child)) {
        margin-inline-start: 0;
        margin-block-start: calc(var(--border, 1px) * -1);
      }
    }
  }
  .join-item {
    &:where(*:not(:first-child, :disabled, [disabled], .btn-disabled)) {
      margin-inline-start: calc(var(--border, 1px) * -1);
      margin-block-start: 0;
    }
    &:where(*:is(:disabled, [disabled], .btn-disabled)) {
      border-width: var(--border, 1px) 0 var(--border, 1px) var(--border, 1px);
    }
  }
  .modal-action {
    margin-top: calc(0.25rem * 6);
    display: flex;
    justify-content: flex-end;
    gap: calc(0.25rem * 2);
  }
  .mt-0 {
    margin-top: calc(var(--spacing) * 0);
  }
  .mt-1 {
    margin-top: calc(var(--spacing) * 1);
  }
  .mt-4 {
    margin-top: calc(var(--spacing) * 4);
  }
  .mt-8 {
    margin-top: calc(var(--spacing) * 8);
  }
  .mt-16 {
    margin-top: calc(var(--spacing) * 16);
  }
  .mt-24 {
    margin-top: calc(var(--spacing) * 24);
  }
  .breadcrumbs {
    max-width: 100%;
    overflow-x: auto;
    padding-block: calc(0.25rem * 2);
    > menu, > ul, > ol {
      display: flex;
      min-height: min-content;
      align-items: center;
      white-space: nowrap;
      > li {
        display: flex;
        align-items: center;
        > * {
          display: flex;
          cursor: pointer;
          align-items: center;
          gap: calc(0.25rem * 2);
          &:hover {
            @media (hover: hover) {
              text-decoration-line: underline;
            }
          }
          &:focus {
            --tw-outline-style: none;
            outline-style: none;
            @media (forced-colors: active) {
              outline: 2px solid transparent;
              outline-offset: 2px;
            }
          }
          &:focus-visible {
            outline: 2px solid currentColor;
            outline-offset: 2px;
          }
        }
        & + *:before {
          content: "";
          margin-right: calc(0.25rem * 3);
          margin-left: calc(0.25rem * 2);
          display: block;
          height: calc(0.25rem * 1.5);
          width: calc(0.25rem * 1.5);
          opacity: 40%;
          rotate: 45deg;
          border-top: 1px solid;
          border-right: 1px solid;
          background-color: #0000;
        }
        [dir="rtl"] & + *:before {
          rotate: -135deg;
        }
      }
    }
  }
  .mr-4 {
    margin-right: calc(var(--spacing) * 4);
  }
  .fieldset-legend {
    margin-bottom: calc(0.25rem * -1);
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: calc(0.25rem * 2);
    padding-block: calc(0.25rem * 2);
    color: var(--color-base-content);
    font-weight: 600;
  }
  .footer-title {
    margin-bottom: calc(0.25rem * 2);
    text-transform: uppercase;
    opacity: 60%;
    font-weight: 600;
  }
  .mb-2 {
    margin-bottom: calc(var(--spacing) * 2);
  }
  .mb-4 {
    margin-bottom: calc(var(--spacing) * 4);
  }
  .mb-8 {
    margin-bottom: calc(var(--spacing) * 8);
  }
  .mb-16 {
    margin-bottom: calc(var(--spacing) * 16);
  }
  .mb-24 {
    margin-bottom: calc(var(--spacing) * 24);
  }
  .mb-32 {
    margin-bottom: calc(var(--spacing) * 32);
  }
  .ml-2 {
    margin-left: calc(var(--spacing) * 2);
  }
  .ml-8 {
    margin-left: calc(var(--spacing) * 8);
  }
  .carousel-item {
    box-sizing: content-box;
    display: flex;
    flex: none;
    scroll-snap-align: start;
  }
  .box-border {
    box-sizing: border-box;
  }
  .box-content {
    box-sizing: content-box;
  }
  .status {
    display: inline-block;
    aspect-ratio: 1 / 1;
    width: calc(0.25rem * 2);
    height: calc(0.25rem * 2);
    border-radius: var(--radius-selector);
    background-color: color-mix(in oklab, var(--color-base-content) 20%, transparent);
    background-position: center;
    background-repeat: no-repeat;
    vertical-align: middle;
    color: color-mix(in srgb, #000 30%, transparent);
    @supports (color: color-mix(in lab, red, red)) {
      color: color-mix(in oklab, var(--color-black) 30%, transparent);
    }
    background-image: radial-gradient( circle at 35% 30%, oklch(1 0 0 / calc(var(--depth) * 0.5)), #0000 );
    box-shadow: 0 2px 3px -1px color-mix(in oklab, currentColor calc(var(--depth) * 100%), #0000);
  }
  .status\! {
    display: inline-block !important;
    aspect-ratio: 1 / 1 !important;
    width: calc(0.25rem * 2) !important;
    height: calc(0.25rem * 2) !important;
    border-radius: var(--radius-selector) !important;
    background-color: color-mix(in oklab, var(--color-base-content) 20%, transparent) !important;
    background-position: center !important;
    background-repeat: no-repeat !important;
    vertical-align: middle !important;
    color: color-mix(in srgb, #000 30%, transparent) !important;
    @supports (color: color-mix(in lab, red, red)) {
      color: color-mix(in oklab, var(--color-black) 30%, transparent) !important;
    }
    background-image: radial-gradient( circle at 35% 30%, oklch(1 0 0 / calc(var(--depth) * 0.5)), #0000 ) !important;
    box-shadow: 0 2px 3px -1px color-mix(in oklab, currentColor calc(var(--depth) * 100%), #0000) !important;
  }
  .badge {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: calc(0.25rem * 2);
    border-radius: var(--radius-selector);
    vertical-align: middle;
    color: var(--badge-fg);
    border: var(--border) solid var(--badge-color, var(--color-base-200));
    font-size: 0.875rem;
    width: fit-content;
    padding-inline: calc(0.25rem * 3 - var(--border));
    background-size: auto, calc(var(--noise) * 100%);
    background-image: none, var(--fx-noise);
    background-color: var(--badge-bg);
    --badge-bg: var(--badge-color, var(--color-base-100));
    --badge-fg: var(--color-base-content);
    --size: calc(var(--size-selector, 0.25rem) * 6);
    height: var(--size);
  }
  .kbd {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    border-radius: var(--radius-field);
    background-color: var(--color-base-200);
    vertical-align: middle;
    padding-left: 0.5em;
    padding-right: 0.5em;
    border: var(--border) solid color-mix(in srgb, var(--color-base-content) 20%, #0000);
    border-bottom: calc(var(--border) + 1px) solid color-mix(in srgb, var(--color-base-content) 20%, #0000);
    --size: calc(var(--size-selector, 0.25rem) * 6);
    font-size: 0.875rem;
    height: var(--size);
    min-width: var(--size);
  }
  .tabs {
    display: flex;
    flex-wrap: wrap;
    --tabs-height: auto;
    --tabs-direction: row;
    --tab-height: calc(var(--size-field, 0.25rem) * 10);
    height: var(--tabs-height);
    flex-direction: var(--tabs-direction);
  }
  .navbar {
    display: flex;
    width: 100%;
    align-items: center;
    padding: 0.5rem;
    min-height: 4rem;
  }
  .footer {
    display: grid;
    width: 100%;
    grid-auto-flow: row;
    place-items: start;
    column-gap: calc(0.25rem * 4);
    row-gap: calc(0.25rem * 10);
    font-size: 0.875rem;
    line-height: 1.25rem;
    & > * {
      display: grid;
      place-items: start;
      gap: calc(0.25rem * 2);
    }
    &.footer-center {
      grid-auto-flow: column dense;
      place-items: center;
      text-align: center;
      & > * {
        place-items: center;
      }
    }
  }
  .stat {
    display: inline-grid;
    width: 100%;
    column-gap: calc(0.25rem * 4);
    padding-inline: calc(0.25rem * 6);
    padding-block: calc(0.25rem * 4);
    grid-template-columns: repeat(1, 1fr);
    &:not(:last-child) {
      border-inline-end: var(--border) dashed color-mix(in oklab, currentColor 10%, #0000);
      border-block-end: none;
    }
  }
  .navbar-end {
    display: inline-flex;
    align-items: center;
    width: 50%;
    justify-content: flex-end;
  }
  .navbar-start {
    display: inline-flex;
    align-items: center;
    width: 50%;
    justify-content: flex-start;
  }
  .card-body {
    display: flex;
    flex: auto;
    flex-direction: column;
    gap: calc(0.25rem * 2);
    padding: var(--card-p, 1.5rem);
    font-size: var(--card-fs, 0.875rem);
    :where(p) {
      flex-grow: 1;
    }
  }
  .navbar-center {
    display: inline-flex;
    align-items: center;
    flex-shrink: 0;
  }
  .fieldset-label {
    display: flex;
    align-items: center;
    gap: calc(0.25rem * 1.5);
    color: color-mix(in oklab, var(--color-base-content) 60%, transparent);
    &:has(input) {
      cursor: pointer;
    }
  }
  .carousel {
    display: inline-flex;
    overflow-x: scroll;
    scroll-snap-type: x mandatory;
    scroll-behavior: smooth;
    scrollbar-width: none;
    &::-webkit-scrollbar {
      display: none;
    }
  }
  .alert {
    display: grid;
    align-items: center;
    gap: calc(0.25rem * 4);
    border-radius: var(--radius-box);
    padding-inline: calc(0.25rem * 4);
    padding-block: calc(0.25rem * 3);
    color: var(--color-base-content);
    background-color: var(--alert-color, var(--color-base-200));
    justify-content: start;
    justify-items: start;
    grid-auto-flow: column;
    grid-template-columns: auto;
    text-align: start;
    border: var(--border) solid var(--color-base-200);
    font-size: 0.875rem;
    line-height: 1.25rem;
    background-size: auto, calc(var(--noise) * 100%);
    background-image: none, var(--fx-noise);
    box-shadow: 0 3px 0 -2px oklch(100% 0 0 / calc(var(--depth) * 0.08)) inset, 0 1px color-mix( in oklab, color-mix(in oklab, #000 20%, var(--alert-color, var(--color-base-200))) calc(var(--depth) * 20%), #0000 ), 0 4px 3px -2px oklch(0% 0 0 / calc(var(--depth) * 0.08));
    &:has(:nth-child(2)) {
      grid-template-columns: auto minmax(auto, 1fr);
    }
    &.alert-outline {
      background-color: transparent;
      color: var(--alert-color);
      box-shadow: none;
      background-image: none;
    }
    &.alert-dash {
      background-color: transparent;
      color: var(--alert-color);
      border-style: dashed;
      box-shadow: none;
      background-image: none;
    }
    &.alert-soft {
      color: var(--alert-color, var(--color-base-content));
      background: color-mix( in oklab, var(--alert-color, var(--color-base-content)) 8%, var(--color-base-100) );
      border-color: color-mix( in oklab, var(--alert-color, var(--color-base-content)) 10%, var(--color-base-100) );
      box-shadow: none;
      background-image: none;
    }
  }
  .fieldset {
    display: grid;
    gap: calc(0.25rem * 1.5);
    padding-block: calc(0.25rem * 1);
    font-size: 0.75rem;
    grid-template-columns: 1fr;
    grid-auto-rows: max-content;
  }
  .card-actions {
    display: flex;
    flex-wrap: wrap;
    align-items: flex-start;
    gap: calc(0.25rem * 2);
  }
  .avatar-placeholder {
    & > div {
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }
  .card-title {
    display: flex;
    align-items: center;
    gap: calc(0.25rem * 2);
    font-size: var(--cardtitle-fs, 1.125rem);
    font-weight: 600;
  }
  .join {
    display: inline-flex;
    align-items: stretch;
    --join-ss: 0;
    --join-se: 0;
    --join-es: 0;
    --join-ee: 0;
    :where(.join-item) {
      border-start-start-radius: var(--join-ss, 0);
      border-start-end-radius: var(--join-se, 0);
      border-end-start-radius: var(--join-es, 0);
      border-end-end-radius: var(--join-ee, 0);
      * {
        --join-ss: var(--radius-field);
        --join-se: var(--radius-field);
        --join-es: var(--radius-field);
        --join-ee: var(--radius-field);
      }
    }
    > .join-item:where(:first-child) {
      --join-ss: var(--radius-field);
      --join-se: 0;
      --join-es: var(--radius-field);
      --join-ee: 0;
    }
    :first-child:not(:last-child) {
      :where(.join-item) {
        --join-ss: var(--radius-field);
        --join-se: 0;
        --join-es: var(--radius-field);
        --join-ee: 0;
      }
    }
    > .join-item:where(:last-child) {
      --join-ss: 0;
      --join-se: var(--radius-field);
      --join-es: 0;
      --join-ee: var(--radius-field);
    }
    :last-child:not(:first-child) {
      :where(.join-item) {
        --join-ss: 0;
        --join-se: var(--radius-field);
        --join-es: 0;
        --join-ee: var(--radius-field);
      }
    }
    > .join-item:where(:only-child) {
      --join-ss: var(--radius-field);
      --join-se: var(--radius-field);
      --join-es: var(--radius-field);
      --join-ee: var(--radius-field);
    }
    :only-child {
      :where(.join-item) {
        --join-ss: var(--radius-field);
        --join-se: var(--radius-field);
        --join-es: var(--radius-field);
        --join-ee: var(--radius-field);
      }
    }
  }
  .mockup-phone {
    display: inline-grid;
    justify-items: center;
    border: 6px solid #6b6b6b;
    border-radius: 65px;
    background-color: #000;
    padding: 11px;
    overflow: hidden;
  }
  .chat {
    display: grid;
    column-gap: calc(0.25rem * 3);
    padding-block: calc(0.25rem * 1);
    --mask-chat: url("data:image/svg+xml,%3csvg width='13' height='13' xmlns='http://www.w3.org/2000/svg'%3e%3cpath fill='black' d='M0 11.5004C0 13.0004 2 13.0004 2 13.0004H12H13V0.00036329L12.5 0C12.5 0 11.977 2.09572 11.8581 2.50033C11.6075 3.35237 10.9149 4.22374 9 5.50036C6 7.50036 0 10.0004 0 11.5004Z'/%3e%3c/svg%3e");
  }
  .avatar-group {
    display: flex;
    overflow: hidden;
    :where(.avatar) {
      overflow: hidden;
      border-radius: calc(infinity * 1px);
      border: 4px solid var(--color-base-100);
    }
  }
  .line-clamp-none {
    overflow: visible;
    display: block;
    -webkit-box-orient: horizontal;
    -webkit-line-clamp: unset;
  }
  .prose {
    :root & {
      --tw-prose-body: color-mix(in oklab, var(--color-base-content) 80%, #0000);
      --tw-prose-headings: var(--color-base-content);
      --tw-prose-lead: var(--color-base-content);
      --tw-prose-links: var(--color-base-content);
      --tw-prose-bold: var(--color-base-content);
      --tw-prose-counters: var(--color-base-content);
      --tw-prose-bullets: color-mix(in oklab, var(--color-base-content) 50%, #0000);
      --tw-prose-hr: color-mix(in oklab, var(--color-base-content) 20%, #0000);
      --tw-prose-quotes: var(--color-base-content);
      --tw-prose-quote-borders: color-mix(in oklab, var(--color-base-content) 20%, #0000);
      --tw-prose-captions: color-mix(in oklab, var(--color-base-content) 50%, #0000);
      --tw-prose-code: var(--color-base-content);
      --tw-prose-pre-code: var(--color-neutral-content);
      --tw-prose-pre-bg: var(--color-neutral);
      --tw-prose-th-borders: color-mix(in oklab, var(--color-base-content) 50%, #0000);
      --tw-prose-td-borders: color-mix(in oklab, var(--color-base-content) 20%, #0000);
      --tw-prose-kbd: color-mix(in oklab, var(--color-base-content) 80%, #0000);
      :where(code):not(pre > code) {
        background-color: var(--color-base-200);
        border-radius: var(--radius-selector);
        border: var(--border) solid var(--color-base-300);
        padding-inline: 0.5em;
        font-weight: inherit;
        &:before, &:after {
          display: none;
        }
      }
    }
  }
  .mask {
    display: inline-block;
    vertical-align: middle;
    mask-size: contain;
    mask-repeat: no-repeat;
    mask-position: center;
  }
  .block {
    display: block;
  }
  .contents {
    display: contents;
  }
  .flex {
    display: flex;
  }
  .flow-root {
    display: flow-root;
  }
  .grid {
    display: grid;
  }
  .hidden {
    display: none;
  }
  .inline {
    display: inline;
  }
  .inline-block {
    display: inline-block;
  }
  .inline-flex {
    display: inline-flex;
  }
  .inline-grid {
    display: inline-grid;
  }
  .inline-table {
    display: inline-table;
  }
  .list-item {
    display: list-item;
  }
  .table {
    display: table;
  }
  .table-caption {
    display: table-caption;
  }
  .table-cell {
    display: table-cell;
  }
  .table-column {
    display: table-column;
  }
  .table-column-group {
    display: table-column-group;
  }
  .table-footer-group {
    display: table-footer-group;
  }
  .table-header-group {
    display: table-header-group;
  }
  .table-row {
    display: table-row;
  }
  .table-row-group {
    display: table-row-group;
  }
  .field-sizing-content {
    field-sizing: content;
  }
  .field-sizing-fixed {
    field-sizing: fixed;
  }
  .aspect-auto {
    aspect-ratio: auto;
  }
  .aspect-square {
    aspect-ratio: 1 / 1;
  }
  .modal-bottom {
    place-items: end;
    :where(.modal-box) {
      height: auto;
      width: 100%;
      max-width: none;
      max-height: calc(100vh - 5em);
      translate: 0 100%;
      scale: 1;
      --modal-tl: var(--radius-box);
      --modal-tr: var(--radius-box);
      --modal-bl: 0;
      --modal-br: 0;
    }
  }
  .modal-end {
    place-items: end;
    :where(.modal-box) {
      height: 100vh;
      max-height: none;
      width: auto;
      max-width: none;
      translate: 100% 0;
      scale: 1;
      --modal-tl: var(--radius-box);
      --modal-tr: 0;
      --modal-bl: var(--radius-box);
      --modal-br: 0;
    }
  }
  .modal-middle {
    place-items: center;
    :where(.modal-box) {
      height: auto;
      width: calc(11/12 * 100%);
      max-width: 32rem;
      max-height: calc(100vh - 5em);
      translate: 0 2%;
      scale: 98%;
      --modal-tl: var(--radius-box);
      --modal-tr: var(--radius-box);
      --modal-bl: var(--radius-box);
      --modal-br: var(--radius-box);
    }
  }
  .modal-start {
    place-items: start;
    :where(.modal-box) {
      height: 100vh;
      max-height: none;
      width: auto;
      max-width: none;
      translate: -100% 0;
      scale: 1;
      --modal-tl: 0;
      --modal-tr: var(--radius-box);
      --modal-bl: 0;
      --modal-br: var(--radius-box);
    }
  }
  .modal-top {
    place-items: start;
    :where(.modal-box) {
      height: auto;
      width: 100%;
      max-width: none;
      max-height: calc(100vh - 5em);
      translate: 0 -100%;
      scale: 1;
      --modal-tl: 0;
      --modal-tr: 0;
      --modal-bl: var(--radius-box);
      --modal-br: var(--radius-box);
    }
  }
  .card-side {
    align-items: stretch;
    flex-direction: row;
    :where(figure:first-child) {
      overflow: hidden;
      border-start-start-radius: inherit;
      border-start-end-radius: unset;
      border-end-start-radius: inherit;
      border-end-end-radius: unset;
    }
    :where(figure:last-child) {
      overflow: hidden;
      border-start-start-radius: unset;
      border-start-end-radius: inherit;
      border-end-start-radius: unset;
      border-end-end-radius: inherit;
    }
    figure > * {
      max-width: unset;
    }
    :where(figure > *) {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }
  .divider-horizontal {
    --divider-m: 0 1rem;
    &.divider {
      height: auto;
      width: calc(0.25rem * 4);
      flex-direction: column;
      &:before {
        height: 100%;
        width: calc(0.25rem * 0.5);
      }
      &:after {
        height: 100%;
        width: calc(0.25rem * 0.5);
      }
    }
  }
  .divider-vertical {
    --divider-m: 1rem 0;
    &.divider {
      height: calc(0.25rem * 4);
      width: auto;
      flex-direction: row;
      &:before {
        height: calc(0.25rem * 0.5);
        width: 100%;
      }
      &:after {
        height: calc(0.25rem * 0.5);
        width: 100%;
      }
    }
  }
  .btn-circle {
    border-radius: calc(infinity * 1px);
    padding-inline: calc(0.25rem * 0);
    width: var(--size);
    height: var(--size);
  }
  .btn-square {
    padding-inline: calc(0.25rem * 0);
    width: var(--size);
    height: var(--size);
  }
  .size-4 {
    width: calc(var(--spacing) * 4);
    height: calc(var(--spacing) * 4);
  }
  .size-6 {
    width: calc(var(--spacing) * 6);
    height: calc(var(--spacing) * 6);
  }
  .status-lg {
    width: calc(0.25rem * 3);
    height: calc(0.25rem * 3);
  }
  .status-md {
    width: calc(0.25rem * 2);
    height: calc(0.25rem * 2);
  }
  .status-sm {
    width: calc(0.25rem * 1);
    height: calc(0.25rem * 1);
  }
  .status-xl {
    width: calc(0.25rem * 4);
    height: calc(0.25rem * 4);
  }
  .status-xs {
    width: calc(0.25rem * 0.5);
    height: calc(0.25rem * 0.5);
  }
  .dock-md {
    height: 4rem;
    height: calc(4rem + env(safe-area-inset-bottom));
    .dock-label {
      font-size: 0.6875rem;
    }
  }
  .h-0 {
    height: calc(var(--spacing) * 0);
  }
  .h-4 {
    height: calc(var(--spacing) * 4);
  }
  .h-6 {
    height: calc(var(--spacing) * 6);
  }
  .h-8 {
    height: calc(var(--spacing) * 8);
  }
  .h-11 {
    height: calc(var(--spacing) * 11);
  }
  .h-14 {
    height: calc(var(--spacing) * 14);
  }
  .h-16 {
    height: calc(var(--spacing) * 16);
  }
  .h-72 {
    height: calc(var(--spacing) * 72);
  }
  .h-\[72px\] {
    height: 72px;
  }
  .h-\[400px\] {
    height: 400px;
  }
  .h-\[416px\] {
    height: 416px;
  }
  .h-auto {
    height: auto;
  }
  .h-full {
    height: 100%;
  }
  .h-screen {
    height: 100vh;
  }
  .max-h-0 {
    max-height: calc(var(--spacing) * 0);
  }
  .max-h-8 {
    max-height: calc(var(--spacing) * 8);
  }
  .max-h-none {
    max-height: none;
  }
  .max-h-screen {
    max-height: 100vh;
  }
  .min-h-12 {
    min-height: calc(var(--spacing) * 12);
  }
  .min-h-16 {
    min-height: calc(var(--spacing) * 16);
  }
  .min-h-18 {
    min-height: calc(var(--spacing) * 18);
  }
  .min-h-64 {
    min-height: calc(var(--spacing) * 64);
  }
  .min-h-screen {
    min-height: 100vh;
  }
  .btn-wide {
    width: 100%;
    max-width: calc(0.25rem * 64);
  }
  .cally {
    font-size: 0.7rem;
    &::part(container) {
      padding: 0.5rem 1rem;
      user-select: none;
    }
    ::part(th) {
      font-weight: normal;
      block-size: auto;
    }
    &::part(header) {
      direction: ltr;
    }
    ::part(head) {
      opacity: 0.5;
      font-size: 0.7rem;
    }
    &::part(button) {
      border-radius: var(--radius-field);
      border: none;
      padding: 0.5rem;
      background: #0000;
    }
    &::part(button):hover {
      background: var(--color-base-200);
    }
    ::part(day) {
      border-radius: var(--radius-field);
      font-size: 0.7rem;
    }
    ::part(button day today) {
      background: var(--color-primary);
      color: var(--color-primary-content);
    }
    ::part(selected) {
      color: var(--color-base-100);
      background: var(--color-base-content);
      border-radius: var(--radius-field);
    }
    ::part(range-inner) {
      border-radius: 0;
    }
    ::part(range-start) {
      border-start-end-radius: 0;
      border-end-end-radius: 0;
    }
    ::part(range-end) {
      border-start-start-radius: 0;
      border-end-start-radius: 0;
    }
    ::part(range-start range-end) {
      border-radius: var(--radius-field);
    }
    calendar-month {
      width: 100%;
    }
  }
  .dock-active {
    &:after {
      width: calc(0.25rem * 10);
      background-color: currentColor;
      color: currentColor;
    }
  }
  .rating-half {
    :where(*:not(.rating-hidden)) {
      width: calc(0.25rem * 3);
    }
    &.rating-xs *:not(.rating-hidden) {
      width: calc(0.25rem * 2);
    }
    &.rating-sm *:not(.rating-hidden) {
      width: calc(0.25rem * 2.5);
    }
    &.rating-md *:not(.rating-hidden) {
      width: calc(0.25rem * 3);
    }
    &.rating-lg *:not(.rating-hidden) {
      width: .875rem;
    }
    &.rating-xl *:not(.rating-hidden) {
      width: calc(0.25rem * 4);
    }
  }
  .btn-block {
    width: 100%;
  }
  .loading-lg {
    width: calc(var(--size-selector, 0.25rem) * 7);
  }
  .loading-md {
    width: calc(var(--size-selector, 0.25rem) * 6);
  }
  .loading-sm {
    width: calc(var(--size-selector, 0.25rem) * 5);
  }
  .loading-xl {
    width: calc(var(--size-selector, 0.25rem) * 8);
  }
  .loading-xs {
    width: calc(var(--size-selector, 0.25rem) * 4);
  }
  .w-1\/2 {
    width: calc(1/2 * 100%);
  }
  .w-1\/4 {
    width: calc(1/4 * 100%);
  }
  .w-4 {
    width: calc(var(--spacing) * 4);
  }
  .w-6 {
    width: calc(var(--spacing) * 6);
  }
  .w-11 {
    width: calc(var(--spacing) * 11);
  }
  .w-14 {
    width: calc(var(--spacing) * 14);
  }
  .w-16 {
    width: calc(var(--spacing) * 16);
  }
  .w-20 {
    width: calc(var(--spacing) * 20);
  }
  .w-52 {
    width: calc(var(--spacing) * 52);
  }
  .w-64 {
    width: calc(var(--spacing) * 64);
  }
  .w-\[975px\] {
    width: 975px;
  }
  .w-auto {
    width: auto;
  }
  .w-full {
    width: 100%;
  }
  .w-screen {
    width: 100vw;
  }
  .max-w-\[450px\] {
    max-width: 450px;
  }
  .max-w-\[500px\] {
    max-width: 500px;
  }
  .max-w-\[530px\] {
    max-width: 530px;
  }
  .max-w-\[612px\] {
    max-width: 612px;
  }
  .max-w-\[938px\] {
    max-width: 938px;
  }
  .max-w-\[950px\] {
    max-width: 950px;
  }
  .max-w-\[992px\] {
    max-width: 992px;
  }
  .max-w-\[1024px\] {
    max-width: 1024px;
  }
  .max-w-\[1200px\] {
    max-width: 1200px;
  }
  .max-w-\[1248px\] {
    max-width: 1248px;
  }
  .max-w-\[1288px\] {
    max-width: 1288px;
  }
  .max-w-\[1376px\] {
    max-width: 1376px;
  }
  .max-w-\[1600px\] {
    max-width: 1600px;
  }
  .max-w-none {
    max-width: none;
  }
  .max-w-screen {
    max-width: 100vw;
  }
  .min-w-11 {
    min-width: calc(var(--spacing) * 11);
  }
  .min-w-18 {
    min-width: calc(var(--spacing) * 18);
  }
  .min-w-\[1px\] {
    min-width: 1px;
  }
  .min-w-screen {
    min-width: 100vw;
  }
  .flex-auto {
    flex: auto;
  }
  .flex-initial {
    flex: 0 auto;
  }
  .flex-none {
    flex: none;
  }
  .flex-shrink {
    flex-shrink: 1;
  }
  .shrink {
    flex-shrink: 1;
  }
  .flex-grow {
    flex-grow: 1;
  }
  .grow {
    flex-grow: 1;
  }
  .basis-auto {
    flex-basis: auto;
  }
  .basis-full {
    flex-basis: 100%;
  }
  .table-auto {
    table-layout: auto;
  }
  .table-fixed {
    table-layout: fixed;
  }
  .caption-bottom {
    caption-side: bottom;
  }
  .caption-top {
    caption-side: top;
  }
  .border-collapse {
    border-collapse: collapse;
  }
  .border-separate {
    border-collapse: separate;
  }
  .origin-bottom {
    transform-origin: bottom;
  }
  .origin-bottom-left {
    transform-origin: bottom left;
  }
  .origin-bottom-right {
    transform-origin: bottom right;
  }
  .origin-center {
    transform-origin: center;
  }
  .origin-left {
    transform-origin: left;
  }
  .origin-right {
    transform-origin: right;
  }
  .origin-top {
    transform-origin: top;
  }
  .origin-top-left {
    transform-origin: top left;
  }
  .origin-top-right {
    transform-origin: top right;
  }
  .-translate-full {
    --tw-translate-x: -100%;
    --tw-translate-y: -100%;
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }
  .translate-full {
    --tw-translate-x: 100%;
    --tw-translate-y: 100%;
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }
  .translate-3d {
    translate: var(--tw-translate-x) var(--tw-translate-y) var(--tw-translate-z);
  }
  .translate-none {
    translate: none;
  }
  .scale-3d {
    scale: var(--tw-scale-x) var(--tw-scale-y) var(--tw-scale-z);
  }
  .scale-none {
    scale: none;
  }
  .swap-rotate {
    .swap-on, input:indeterminate ~ .swap-on {
      rotate: 45deg;
    }
    input:is(:checked, :indeterminate) ~ .swap-on, &.swap-active .swap-on {
      rotate: 0deg;
    }
    input:is(:checked, :indeterminate) ~ .swap-off, &.swap-active .swap-off {
      rotate: calc(45deg * -1);
    }
  }
  .rotate-180 {
    rotate: 180deg;
  }
  .rotate-none {
    rotate: none;
  }
  .swap-flip {
    transform-style: preserve-3d;
    perspective: 20rem;
    .swap-on, .swap-indeterminate, input:indeterminate ~ .swap-on {
      transform: rotateY(180deg);
      backface-visibility: hidden;
    }
    input:is(:checked, :indeterminate) ~ .swap-on, &.swap-active .swap-on {
      transform: rotateY(0deg);
    }
    input:is(:checked, :indeterminate) ~ .swap-off, &.swap-active .swap-off {
      transform: rotateY(-180deg);
      backface-visibility: hidden;
      opacity: 100%;
    }
  }
  .transform {
    transform: var(--tw-rotate-x) var(--tw-rotate-y) var(--tw-rotate-z) var(--tw-skew-x) var(--tw-skew-y);
  }
  .transform-cpu {
    transform: var(--tw-rotate-x) var(--tw-rotate-y) var(--tw-rotate-z) var(--tw-skew-x) var(--tw-skew-y);
  }
  .transform-gpu {
    transform: translateZ(0) var(--tw-rotate-x) var(--tw-rotate-y) var(--tw-rotate-z) var(--tw-skew-x) var(--tw-skew-y);
  }
  .transform-none {
    transform: none;
  }
  .skeleton {
    border-radius: var(--radius-box);
    background-color: var(--color-base-300);
    @media (prefers-reduced-motion: reduce) {
      transition-duration: 15s;
    }
    will-change: background-position;
    animation: skeleton 1.8s ease-in-out infinite;
    background-image: linear-gradient( 105deg, #0000 0% 40%, var(--color-base-100) 50%, #0000 60% 100% );
    background-size: 200% auto;
    background-repeat: no-repeat;
    background-position-x: -50%;
  }
  .animate-none {
    animation: none;
  }
  .link {
    cursor: pointer;
    text-decoration-line: underline;
    &:focus {
      --tw-outline-style: none;
      outline-style: none;
      @media (forced-colors: active) {
        outline: 2px solid transparent;
        outline-offset: 2px;
      }
    }
    &:focus-visible {
      outline: 2px solid currentColor;
      outline-offset: 2px;
    }
  }
  .cursor-not-allowed {
    cursor: not-allowed;
  }
  .cursor-pointer {
    cursor: pointer;
  }
  .touch-pinch-zoom {
    --tw-pinch-zoom: pinch-zoom;
    touch-action: var(--tw-pan-x,) var(--tw-pan-y,) var(--tw-pinch-zoom,);
  }
  .resize {
    resize: both;
  }
  .resize-none {
    resize: none;
  }
  .resize-x {
    resize: horizontal;
  }
  .resize-y {
    resize: vertical;
  }
  .carousel-horizontal {
    flex-direction: row;
    overflow-x: scroll;
    scroll-snap-type: x mandatory;
  }
  .carousel-vertical {
    flex-direction: column;
    overflow-y: scroll;
    scroll-snap-type: y mandatory;
  }
  .snap-none {
    scroll-snap-type: none;
  }
  .snap-mandatory {
    --tw-scroll-snap-strictness: mandatory;
  }
  .snap-proximity {
    --tw-scroll-snap-strictness: proximity;
  }
  .carousel-center {
    .carousel-item {
      scroll-snap-align: center;
    }
  }
  .carousel-end {
    .carousel-item {
      scroll-snap-align: end;
    }
  }
  .carousel-start {
    .carousel-item {
      scroll-snap-align: start;
    }
  }
  .snap-align-none {
    scroll-snap-align: none;
  }
  .snap-center {
    scroll-snap-align: center;
  }
  .snap-end {
    scroll-snap-align: end;
  }
  .snap-start {
    scroll-snap-align: start;
  }
  .snap-always {
    scroll-snap-stop: always;
  }
  .snap-normal {
    scroll-snap-stop: normal;
  }
  .list-inside {
    list-style-position: inside;
  }
  .list-outside {
    list-style-position: outside;
  }
  .list-decimal {
    list-style-type: decimal;
  }
  .list-disc {
    list-style-type: disc;
  }
  .list-none {
    list-style-type: none;
  }
  .list-image-none {
    list-style-image: none;
  }
  .appearance-auto {
    appearance: auto;
  }
  .appearance-none {
    appearance: none;
  }
  .columns-auto {
    columns: auto;
  }
  .auto-cols-auto {
    grid-auto-columns: auto;
  }
  .auto-cols-fr {
    grid-auto-columns: minmax(0, 1fr);
  }
  .auto-cols-max {
    grid-auto-columns: max-content;
  }
  .auto-cols-min {
    grid-auto-columns: min-content;
  }
  .alert-horizontal {
    justify-content: start;
    justify-items: start;
    grid-auto-flow: column;
    grid-template-columns: auto;
    text-align: start;
    &:has(:nth-child(2)) {
      grid-template-columns: auto minmax(auto, 1fr);
    }
  }
  .alert-vertical {
    justify-content: center;
    justify-items: center;
    grid-auto-flow: row;
    grid-template-columns: auto;
    text-align: center;
    &:has(:nth-child(2)) {
      grid-template-columns: auto;
    }
  }
  .stats-horizontal {
    grid-auto-flow: column;
    overflow-x: auto;
    .stat:not(:last-child) {
      border-inline-end: var(--border) dashed color-mix(in oklab, currentColor 10%, #0000);
      border-block-end: none;
    }
  }
  .stats-vertical {
    grid-auto-flow: row;
    overflow-y: auto;
    .stat:not(:last-child) {
      border-inline-end: none;
      border-block-end: var(--border) dashed color-mix(in oklab, currentColor 10%, #0000);
    }
  }
  .footer-horizontal {
    grid-auto-flow: column;
    &.footer-center {
      grid-auto-flow: row dense;
    }
  }
  .footer-vertical {
    grid-auto-flow: row;
    &.footer-center {
      grid-auto-flow: column dense;
    }
  }
  .grid-flow-col {
    grid-auto-flow: column;
  }
  .grid-flow-col-dense {
    grid-auto-flow: column dense;
  }
  .grid-flow-dense {
    grid-auto-flow: dense;
  }
  .grid-flow-row {
    grid-auto-flow: row;
  }
  .grid-flow-row-dense {
    grid-auto-flow: row dense;
  }
  .auto-rows-auto {
    grid-auto-rows: auto;
  }
  .auto-rows-fr {
    grid-auto-rows: minmax(0, 1fr);
  }
  .auto-rows-max {
    grid-auto-rows: max-content;
  }
  .auto-rows-min {
    grid-auto-rows: min-content;
  }
  .grid-cols-1 {
    grid-template-columns: repeat(1, minmax(0, 1fr));
  }
  .grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }
  .grid-cols-none {
    grid-template-columns: none;
  }
  .grid-cols-subgrid {
    grid-template-columns: subgrid;
  }
  .grid-rows-none {
    grid-template-rows: none;
  }
  .grid-rows-subgrid {
    grid-template-rows: subgrid;
  }
  .flex-col {
    flex-direction: column;
  }
  .flex-col-reverse {
    flex-direction: column-reverse;
  }
  .flex-row {
    flex-direction: row;
  }
  .flex-row-reverse {
    flex-direction: row-reverse;
  }
  .flex-nowrap {
    flex-wrap: nowrap;
  }
  .flex-wrap {
    flex-wrap: wrap;
  }
  .flex-wrap-reverse {
    flex-wrap: wrap-reverse;
  }
  .place-content-around {
    place-content: space-around;
  }
  .place-content-baseline {
    place-content: baseline;
  }
  .place-content-between {
    place-content: space-between;
  }
  .place-content-center {
    place-content: center;
  }
  .place-content-end {
    place-content: end;
  }
  .place-content-evenly {
    place-content: space-evenly;
  }
  .place-content-start {
    place-content: start;
  }
  .place-content-stretch {
    place-content: stretch;
  }
  .place-items-baseline {
    place-items: baseline;
  }
  .place-items-center {
    place-items: center;
  }
  .place-items-end {
    place-items: end;
  }
  .place-items-start {
    place-items: start;
  }
  .place-items-stretch {
    place-items: stretch;
  }
  .content-around {
    align-content: space-around;
  }
  .content-baseline {
    align-content: baseline;
  }
  .content-between {
    align-content: space-between;
  }
  .content-center {
    align-content: center;
  }
  .content-end {
    align-content: flex-end;
  }
  .content-evenly {
    align-content: space-evenly;
  }
  .content-normal {
    align-content: normal;
  }
  .content-start {
    align-content: flex-start;
  }
  .content-stretch {
    align-content: stretch;
  }
  .items-baseline {
    align-items: baseline;
  }
  .items-center {
    align-items: center;
  }
  .items-end {
    align-items: flex-end;
  }
  .items-start {
    align-items: flex-start;
  }
  .items-stretch {
    align-items: stretch;
  }
  .justify-around {
    justify-content: space-around;
  }
  .justify-baseline {
    justify-content: baseline;
  }
  .justify-between {
    justify-content: space-between;
  }
  .justify-center {
    justify-content: center;
  }
  .justify-end {
    justify-content: flex-end;
  }
  .justify-evenly {
    justify-content: space-evenly;
  }
  .justify-normal {
    justify-content: normal;
  }
  .justify-start {
    justify-content: flex-start;
  }
  .justify-stretch {
    justify-content: stretch;
  }
  .justify-items-center {
    justify-items: center;
  }
  .justify-items-end {
    justify-items: end;
  }
  .justify-items-normal {
    justify-items: normal;
  }
  .justify-items-start {
    justify-items: start;
  }
  .justify-items-stretch {
    justify-items: stretch;
  }
  .gap-2 {
    gap: calc(var(--spacing) * 2);
  }
  .gap-4 {
    gap: calc(var(--spacing) * 4);
  }
  .gap-6 {
    gap: calc(var(--spacing) * 6);
  }
  .gap-8 {
    gap: calc(var(--spacing) * 8);
  }
  .gap-11 {
    gap: calc(var(--spacing) * 11);
  }
  .gap-16 {
    gap: calc(var(--spacing) * 16);
  }
  .gap-24 {
    gap: calc(var(--spacing) * 24);
  }
  .space-y-reverse {
    :where(& > :not(:last-child)) {
      --tw-space-y-reverse: 1;
    }
  }
  .space-x-reverse {
    :where(& > :not(:last-child)) {
      --tw-space-x-reverse: 1;
    }
  }
  .divide-x {
    :where(& > :not(:last-child)) {
      --tw-divide-x-reverse: 0;
      border-inline-style: var(--tw-border-style);
      border-inline-start-width: calc(1px * var(--tw-divide-x-reverse));
      border-inline-end-width: calc(1px * calc(1 - var(--tw-divide-x-reverse)));
    }
  }
  .divide-y {
    :where(& > :not(:last-child)) {
      --tw-divide-y-reverse: 0;
      border-bottom-style: var(--tw-border-style);
      border-top-style: var(--tw-border-style);
      border-top-width: calc(1px * var(--tw-divide-y-reverse));
      border-bottom-width: calc(1px * calc(1 - var(--tw-divide-y-reverse)));
    }
  }
  .divide-y-reverse {
    :where(& > :not(:last-child)) {
      --tw-divide-y-reverse: 1;
    }
  }
  .place-self-auto {
    place-self: auto;
  }
  .place-self-center {
    place-self: center;
  }
  .place-self-end {
    place-self: end;
  }
  .place-self-start {
    place-self: start;
  }
  .place-self-stretch {
    place-self: stretch;
  }
  .self-auto {
    align-self: auto;
  }
  .self-baseline {
    align-self: baseline;
  }
  .self-center {
    align-self: center;
  }
  .self-end {
    align-self: flex-end;
  }
  .self-start {
    align-self: flex-start;
  }
  .self-stretch {
    align-self: stretch;
  }
  .justify-self-auto {
    justify-self: auto;
  }
  .justify-self-center {
    justify-self: center;
  }
  .justify-self-end {
    justify-self: flex-end;
  }
  .justify-self-start {
    justify-self: flex-start;
  }
  .justify-self-stretch {
    justify-self: stretch;
  }
  .truncate {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  .overflow-clip {
    overflow: clip;
  }
  .overflow-hidden {
    overflow: hidden;
  }
  .scroll-auto {
    scroll-behavior: auto;
  }
  .scroll-smooth {
    scroll-behavior: smooth;
  }
  .tabs-box {
    background-color: var(--color-base-200);
    padding: calc(0.25rem * 1);
    --tabs-box-radius: calc(var(--radius-field) + var(--radius-field) + var(--radius-field));
    border-radius: calc(var(--radius-field) + min(0.25rem, var(--tabs-box-radius)));
    box-shadow: 0 -0.5px oklch(100% 0 0 / calc(var(--depth) * 0.1)) inset, 0 0.5px oklch(0% 0 0 / calc(var(--depth) * 0.05)) inset;
    .tab {
      border-radius: var(--radius-field);
      border-style: none;
      &:focus-visible, &:is(label:has(:checked:focus-visible)) {
        outline-offset: 2px;
      }
    }
    > :is(.tab-active, [aria-selected="true"]):not(.tab-disabled, [disabled]), > :is(input:checked), > :is(label:has(:checked)) {
      background-color: var(--tab-bg, var(--color-base-100));
      box-shadow: 0 1px oklch(100% 0 0 / calc(var(--depth) * 0.1)) inset, 0 1px 1px -1px color-mix(in oklab, var(--color-neutral) calc(var(--depth) * 50%), #0000), 0 1px 6px -4px color-mix(in oklab, var(--color-neutral) calc(var(--depth) * 100%), #0000);
      @media (forced-colors: active) {
        border: 1px solid;
      }
    }
  }
  .timeline-box {
    border: var(--border) solid;
    border-radius: var(--radius-box);
    border-color: var(--color-base-300);
    background-color: var(--color-base-100);
    padding-inline: calc(0.25rem * 4);
    padding-block: calc(0.25rem * 2);
    font-size: 0.75rem;
    box-shadow: 0 1px 2px 0 oklch(0% 0 0/0.05);
  }
  .menu-lg {
    :where(li:not(.menu-title) > *:not(ul, details, .menu-title)), :where(li:not(.menu-title) > details > summary:not(.menu-title)) {
      border-radius: var(--radius-field);
      padding-inline: calc(0.25rem * 4);
      padding-block: calc(0.25rem * 1.5);
      font-size: 1.125rem;
    }
    .menu-title {
      padding-inline: calc(0.25rem * 6);
      padding-block: calc(0.25rem * 3);
    }
  }
  .menu-md {
    :where(li:not(.menu-title) > *:not(ul, details, .menu-title)), :where(li:not(.menu-title) > details > summary:not(.menu-title)) {
      border-radius: var(--radius-field);
      padding-inline: calc(0.25rem * 3);
      padding-block: calc(0.25rem * 1.5);
      font-size: 0.875rem;
    }
    .menu-title {
      padding-inline: calc(0.25rem * 3);
      padding-block: calc(0.25rem * 2);
    }
  }
  .menu-sm {
    :where(li:not(.menu-title) > *:not(ul, details, .menu-title)), :where(li:not(.menu-title) > details > summary:not(.menu-title)) {
      border-radius: var(--radius-field);
      padding-inline: calc(0.25rem * 2.5);
      padding-block: calc(0.25rem * 1);
      font-size: 0.75rem;
    }
    .menu-title {
      padding-inline: calc(0.25rem * 3);
      padding-block: calc(0.25rem * 2);
    }
  }
  .menu-xl {
    :where(li:not(.menu-title) > *:not(ul, details, .menu-title)), :where(li:not(.menu-title) > details > summary:not(.menu-title)) {
      border-radius: var(--radius-field);
      padding-inline: calc(0.25rem * 5);
      padding-block: calc(0.25rem * 1.5);
      font-size: 1.375rem;
    }
    .menu-title {
      padding-inline: calc(0.25rem * 6);
      padding-block: calc(0.25rem * 3);
    }
  }
  .menu-xs {
    :where(li:not(.menu-title) > *:not(ul, details, .menu-title)), :where(li:not(.menu-title) > details > summary:not(.menu-title)) {
      border-radius: var(--radius-field);
      padding-inline: calc(0.25rem * 2);
      padding-block: calc(0.25rem * 1);
      font-size: 0.6875rem;
    }
    .menu-title {
      padding-inline: calc(0.25rem * 2);
      padding-block: calc(0.25rem * 1);
    }
  }
  .rounded {
    border-radius: 0.25rem;
  }
  .rounded-2xl {
    border-radius: var(--radius-2xl);
  }
  .rounded-3xl {
    border-radius: var(--radius-3xl);
  }
  .rounded-4xl {
    border-radius: var(--radius-4xl);
  }
  .rounded-box {
    border-radius: var(--radius-box);
  }
  .rounded-box {
    border-radius: var(--radius-box);
  }
  .rounded-field {
    border-radius: var(--radius-field);
  }
  .rounded-field {
    border-radius: var(--radius-field);
  }
  .rounded-full {
    border-radius: calc(infinity * 1px);
  }
  .rounded-lg {
    border-radius: var(--radius-lg);
  }
  .rounded-md {
    border-radius: var(--radius-md);
  }
  .rounded-selector {
    border-radius: var(--radius-selector);
  }
  .rounded-selector {
    border-radius: var(--radius-selector);
  }
  .rounded-xl {
    border-radius: var(--radius-xl);
  }
  .rounded-s {
    border-start-start-radius: 0.25rem;
    border-end-start-radius: 0.25rem;
  }
  .rounded-ss {
    border-start-start-radius: 0.25rem;
  }
  .rounded-e {
    border-start-end-radius: 0.25rem;
    border-end-end-radius: 0.25rem;
  }
  .rounded-se {
    border-start-end-radius: 0.25rem;
  }
  .rounded-ee {
    border-end-end-radius: 0.25rem;
  }
  .rounded-es {
    border-end-start-radius: 0.25rem;
  }
  .rounded-t {
    border-top-left-radius: 0.25rem;
    border-top-right-radius: 0.25rem;
  }
  .rounded-t-box {
    border-top-left-radius: var(--radius-box);
    border-top-right-radius: var(--radius-box);
  }
  .rounded-t-box {
    border-top-left-radius: var(--radius-box);
    border-top-right-radius: var(--radius-box);
  }
  .rounded-t-field {
    border-top-left-radius: var(--radius-field);
    border-top-right-radius: var(--radius-field);
  }
  .rounded-t-field {
    border-top-left-radius: var(--radius-field);
    border-top-right-radius: var(--radius-field);
  }
  .rounded-t-selector {
    border-top-left-radius: var(--radius-selector);
    border-top-right-radius: var(--radius-selector);
  }
  .rounded-t-selector {
    border-top-left-radius: var(--radius-selector);
    border-top-right-radius: var(--radius-selector);
  }
  .rounded-l {
    border-top-left-radius: 0.25rem;
    border-bottom-left-radius: 0.25rem;
  }
  .rounded-l-box {
    border-top-left-radius: var(--radius-box);
    border-bottom-left-radius: var(--radius-box);
  }
  .rounded-l-box {
    border-top-left-radius: var(--radius-box);
    border-bottom-left-radius: var(--radius-box);
  }
  .rounded-l-field {
    border-top-left-radius: var(--radius-field);
    border-bottom-left-radius: var(--radius-field);
  }
  .rounded-l-field {
    border-top-left-radius: var(--radius-field);
    border-bottom-left-radius: var(--radius-field);
  }
  .rounded-l-selector {
    border-top-left-radius: var(--radius-selector);
    border-bottom-left-radius: var(--radius-selector);
  }
  .rounded-l-selector {
    border-top-left-radius: var(--radius-selector);
    border-bottom-left-radius: var(--radius-selector);
  }
  .rounded-tl {
    border-top-left-radius: 0.25rem;
  }
  .rounded-tl-box {
    border-top-left-radius: var(--radius-box);
  }
  .rounded-tl-box {
    border-top-left-radius: var(--radius-box);
  }
  .rounded-tl-field {
    border-top-left-radius: var(--radius-field);
  }
  .rounded-tl-field {
    border-top-left-radius: var(--radius-field);
  }
  .rounded-tl-md {
    border-top-left-radius: var(--radius-md);
  }
  .rounded-tl-selector {
    border-top-left-radius: var(--radius-selector);
  }
  .rounded-tl-selector {
    border-top-left-radius: var(--radius-selector);
  }
  .rounded-r {
    border-top-right-radius: 0.25rem;
    border-bottom-right-radius: 0.25rem;
  }
  .rounded-r-box {
    border-top-right-radius: var(--radius-box);
    border-bottom-right-radius: var(--radius-box);
  }
  .rounded-r-box {
    border-top-right-radius: var(--radius-box);
    border-bottom-right-radius: var(--radius-box);
  }
  .rounded-r-field {
    border-top-right-radius: var(--radius-field);
    border-bottom-right-radius: var(--radius-field);
  }
  .rounded-r-field {
    border-top-right-radius: var(--radius-field);
    border-bottom-right-radius: var(--radius-field);
  }
  .rounded-r-selector {
    border-top-right-radius: var(--radius-selector);
    border-bottom-right-radius: var(--radius-selector);
  }
  .rounded-r-selector {
    border-top-right-radius: var(--radius-selector);
    border-bottom-right-radius: var(--radius-selector);
  }
  .rounded-tr {
    border-top-right-radius: 0.25rem;
  }
  .rounded-tr-box {
    border-top-right-radius: var(--radius-box);
  }
  .rounded-tr-box {
    border-top-right-radius: var(--radius-box);
  }
  .rounded-tr-field {
    border-top-right-radius: var(--radius-field);
  }
  .rounded-tr-field {
    border-top-right-radius: var(--radius-field);
  }
  .rounded-tr-md {
    border-top-right-radius: var(--radius-md);
  }
  .rounded-tr-selector {
    border-top-right-radius: var(--radius-selector);
  }
  .rounded-tr-selector {
    border-top-right-radius: var(--radius-selector);
  }
  .rounded-b {
    border-bottom-right-radius: 0.25rem;
    border-bottom-left-radius: 0.25rem;
  }
  .rounded-b-2xl {
    border-bottom-right-radius: var(--radius-2xl);
    border-bottom-left-radius: var(--radius-2xl);
  }
  .rounded-b-box {
    border-bottom-left-radius: var(--radius-box);
    border-bottom-right-radius: var(--radius-box);
  }
  .rounded-b-box {
    border-bottom-right-radius: var(--radius-box);
    border-bottom-left-radius: var(--radius-box);
  }
  .rounded-b-field {
    border-bottom-left-radius: var(--radius-field);
    border-bottom-right-radius: var(--radius-field);
  }
  .rounded-b-field {
    border-bottom-right-radius: var(--radius-field);
    border-bottom-left-radius: var(--radius-field);
  }
  .rounded-b-selector {
    border-bottom-left-radius: var(--radius-selector);
    border-bottom-right-radius: var(--radius-selector);
  }
  .rounded-b-selector {
    border-bottom-right-radius: var(--radius-selector);
    border-bottom-left-radius: var(--radius-selector);
  }
  .rounded-br {
    border-bottom-right-radius: 0.25rem;
  }
  .rounded-br-box {
    border-bottom-right-radius: var(--radius-box);
  }
  .rounded-br-box {
    border-bottom-right-radius: var(--radius-box);
  }
  .rounded-br-field {
    border-bottom-right-radius: var(--radius-field);
  }
  .rounded-br-field {
    border-bottom-right-radius: var(--radius-field);
  }
  .rounded-br-selector {
    border-bottom-right-radius: var(--radius-selector);
  }
  .rounded-br-selector {
    border-bottom-right-radius: var(--radius-selector);
  }
  .rounded-bl {
    border-bottom-left-radius: 0.25rem;
  }
  .rounded-bl-box {
    border-bottom-left-radius: var(--radius-box);
  }
  .rounded-bl-box {
    border-bottom-left-radius: var(--radius-box);
  }
  .rounded-bl-field {
    border-bottom-left-radius: var(--radius-field);
  }
  .rounded-bl-field {
    border-bottom-left-radius: var(--radius-field);
  }
  .rounded-bl-selector {
    border-bottom-left-radius: var(--radius-selector);
  }
  .rounded-bl-selector {
    border-bottom-left-radius: var(--radius-selector);
  }
  .border {
    border-style: var(--tw-border-style);
    border-width: 1px;
  }
  .border-1 {
    border-style: var(--tw-border-style);
    border-width: 1px;
  }
  .border-x {
    border-inline-style: var(--tw-border-style);
    border-inline-width: 1px;
  }
  .border-y {
    border-block-style: var(--tw-border-style);
    border-block-width: 1px;
  }
  .border-s {
    border-inline-start-style: var(--tw-border-style);
    border-inline-start-width: 1px;
  }
  .border-e {
    border-inline-end-style: var(--tw-border-style);
    border-inline-end-width: 1px;
  }
  .border-t {
    border-top-style: var(--tw-border-style);
    border-top-width: 1px;
  }
  .border-r {
    border-right-style: var(--tw-border-style);
    border-right-width: 1px;
  }
  .border-b {
    border-bottom-style: var(--tw-border-style);
    border-bottom-width: 1px;
  }
  .border-l {
    border-left-style: var(--tw-border-style);
    border-left-width: 1px;
  }
  .badge-dash {
    color: var(--badge-color);
    --badge-bg: #0000;
    background-image: none;
    border-color: currentColor;
    border-style: dashed;
  }
  .btn-dash {
    &:not( .btn-active, :hover, :active:focus, :focus-visible, :disabled, [disabled], .btn-disabled, :checked ) {
      --btn-shadow: "";
      border-style: dashed;
      --btn-bg: #0000;
      --btn-fg: var(--btn-color);
      --btn-border: var(--btn-color);
      --btn-noise: none;
    }
    @media (hover: none) {
      &:hover:not( .btn-active, :active, :focus-visible, :disabled, [disabled], .btn-disabled, :checked ) {
        --btn-shadow: "";
        border-style: dashed;
        --btn-bg: #0000;
        --btn-fg: var(--btn-color);
        --btn-border: var(--btn-color);
        --btn-noise: none;
      }
    }
  }
  .border-dashed {
    --tw-border-style: dashed;
    border-style: dashed;
  }
  .border-dotted {
    --tw-border-style: dotted;
    border-style: dotted;
  }
  .border-double {
    --tw-border-style: double;
    border-style: double;
  }
  .border-hidden {
    --tw-border-style: hidden;
    border-style: hidden;
  }
  .border-none {
    --tw-border-style: none;
    border-style: none;
  }
  .border-solid {
    --tw-border-style: solid;
    border-style: solid;
  }
  .badge-ghost {
    border-color: var(--color-base-200);
    background-color: var(--color-base-200);
    color: var(--color-base-content);
    background-image: none;
  }
  .badge-soft {
    color: var(--badge-color, var(--color-base-content));
    background-color: color-mix( in oklab, var(--badge-color, var(--color-base-content)) 8%, var(--color-base-100) );
    border-color: color-mix( in oklab, var(--badge-color, var(--color-base-content)) 10%, var(--color-base-100) );
    background-image: none;
  }
  .select-ghost {
    background-color: transparent;
    transition: background-color 0.2s;
    box-shadow: none;
    border-color: #0000;
    &:focus, &:focus-within {
      background-color: var(--color-base-100);
      color: var(--color-base-content);
      border-color: #0000;
      box-shadow: none;
    }
  }
  .input-ghost {
    background-color: transparent;
    box-shadow: none;
    border-color: #0000;
    &:focus, &:focus-within {
      background-color: var(--color-base-100);
      color: var(--color-base-content);
      border-color: #0000;
      box-shadow: none;
    }
  }
  .textarea-ghost {
    background-color: transparent;
    box-shadow: none;
    border-color: #0000;
    &:focus, &:focus-within {
      background-color: var(--color-base-100);
      color: var(--color-base-content);
      border-color: #0000;
      box-shadow: none;
    }
  }
  .badge-outline {
    color: var(--badge-color);
    --badge-bg: #0000;
    background-image: none;
    border-color: currentColor;
  }
  .alert-error {
    border-color: var(--color-error);
    color: var(--color-error-content);
    --alert-color: var(--color-error);
  }
  .alert-info {
    border-color: var(--color-info);
    color: var(--color-info-content);
    --alert-color: var(--color-info);
  }
  .alert-success {
    border-color: var(--color-success);
    color: var(--color-success-content);
    --alert-color: var(--color-success);
  }
  .alert-warning {
    border-color: var(--color-warning);
    color: var(--color-warning-content);
    --alert-color: var(--color-warning);
  }
  .border-gray-200 {
    border-color: var(--color-gray-200);
  }
  .border-gray-300 {
    border-color: var(--color-gray-300);
  }
  .glass {
    border: none;
    backdrop-filter: blur(var(--glass-blur, 40px));
    background-color: #0000;
    background-image: linear-gradient( 135deg, oklch(100% 0 0 / var(--glass-opacity, 30%)) 0%, oklch(0% 0 0 / 0%) 100% ), linear-gradient( var(--glass-reflect-degree, 100deg), oklch(100% 0 0 / var(--glass-reflect-opacity, 5%)) 25%, oklch(0% 0 0 / 0%) 25% );
    box-shadow: 0 0 0 1px oklch(100% 0 0 / var(--glass-border-opacity, 20%)) inset, 0 0 0 2px oklch(0% 0 0 / 5%);
    text-shadow: 0 1px oklch(0% 0 0 / var(--glass-text-shadow-opacity, 5%));
  }
  .chat-bubble-accent {
    background-color: var(--color-accent);
    color: var(--color-accent-content);
  }
  .chat-bubble-error {
    background-color: var(--color-error);
    color: var(--color-error-content);
  }
  .chat-bubble-info {
    background-color: var(--color-info);
    color: var(--color-info-content);
  }
  .chat-bubble-neutral {
    background-color: var(--color-neutral);
    color: var(--color-neutral-content);
  }
  .chat-bubble-primary {
    background-color: var(--color-primary);
    color: var(--color-primary-content);
  }
  .chat-bubble-secondary {
    background-color: var(--color-secondary);
    color: var(--color-secondary-content);
  }
  .chat-bubble-success {
    background-color: var(--color-success);
    color: var(--color-success-content);
  }
  .chat-bubble-warning {
    background-color: var(--color-warning);
    color: var(--color-warning-content);
  }
  .status-accent {
    background-color: var(--color-accent);
    color: var(--color-accent);
  }
  .status-error {
    background-color: var(--color-error);
    color: var(--color-error);
  }
  .status-info {
    background-color: var(--color-info);
    color: var(--color-info);
  }
  .status-neutral {
    background-color: var(--color-neutral);
    color: var(--color-neutral);
  }
  .status-primary {
    background-color: var(--color-primary);
    color: var(--color-primary);
  }
  .status-secondary {
    background-color: var(--color-secondary);
    color: var(--color-secondary);
  }
  .status-success {
    background-color: var(--color-success);
    color: var(--color-success);
  }
  .status-warning {
    background-color: var(--color-warning);
    color: var(--color-warning);
  }
  .table-zebra {
    tbody {
      tr {
        &:where(:nth-child(even)) {
          background-color: var(--color-base-200);
          :where(.table-pin-cols tr th) {
            background-color: var(--color-base-200);
          }
        }
        &.row-hover {
          &, &:where(:nth-child(even)) {
            &:hover {
              @media (hover: hover) {
                background-color: var(--color-base-300);
              }
            }
          }
        }
      }
    }
  }
  .bg-\(--my_variable\) {
    background-color: var(--my_variable);
  }
  .bg-\(color\:--my-color\) {
    background-color: var(--my-color);
  }
  .bg-\[\#0088cc\] {
    background-color: #0088cc;
  }
  .bg-\[color\:var\(--my-color\)\] {
    background-color: var(--my-color);
  }
  .bg-\[var\(--my_variable\)\] {
    background-color: var(--my_variable);
  }
  .bg-base-100 {
    background-color: var(--color-base-100);
  }
  .bg-black {
    background-color: var(--color-black);
  }
  .bg-gray-50 {
    background-color: var(--color-gray-50);
  }
  .bg-gray-100 {
    background-color: var(--color-gray-100);
  }
  .bg-gray-200 {
    background-color: var(--color-gray-200);
  }
  .bg-gray-300 {
    background-color: var(--color-gray-300);
  }
  .bg-red-500 {
    background-color: var(--color-red-500);
  }
  .bg-red-500\/50 {
    background-color: color-mix(in oklab, var(--color-red-500) 50%, transparent);
  }
  .bg-red-500\/\[50\%\] {
    background-color: color-mix(in oklab, var(--color-red-500) 50%, transparent);
  }
  .bg-white {
    background-color: var(--color-white);
  }
  .divider-accent {
    &:before, &:after {
      background-color: var(--color-accent);
    }
  }
  .divider-error {
    &:before, &:after {
      background-color: var(--color-error);
    }
  }
  .divider-info {
    &:before, &:after {
      background-color: var(--color-info);
    }
  }
  .divider-neutral {
    &:before, &:after {
      background-color: var(--color-neutral);
    }
  }
  .divider-primary {
    &:before, &:after {
      background-color: var(--color-primary);
    }
  }
  .divider-secondary {
    &:before, &:after {
      background-color: var(--color-secondary);
    }
  }
  .divider-success {
    &:before, &:after {
      background-color: var(--color-success);
    }
  }
  .divider-warning {
    &:before, &:after {
      background-color: var(--color-warning);
    }
  }
  .-bg-conic {
    --tw-gradient-position: in oklab;
    background-image: conic-gradient(var(--tw-gradient-stops));
  }
  .bg-conic {
    --tw-gradient-position: in oklab;
    background-image: conic-gradient(var(--tw-gradient-stops));
  }
  .bg-radial {
    --tw-gradient-position: in oklab;
    background-image: radial-gradient(var(--tw-gradient-stops));
  }
  .bg-none {
    background-image: none;
  }
  .via-none {
    --tw-gradient-via-stops: initial;
  }
  .box-decoration-clone {
    -webkit-box-decoration-break: clone;
    box-decoration-break: clone;
  }
  .box-decoration-slice {
    -webkit-box-decoration-break: slice;
    box-decoration-break: slice;
  }
  .decoration-clone {
    -webkit-box-decoration-break: clone;
    box-decoration-break: clone;
  }
  .decoration-slice {
    -webkit-box-decoration-break: slice;
    box-decoration-break: slice;
  }
  .bg-auto {
    background-size: auto;
  }
  .bg-contain {
    background-size: contain;
  }
  .bg-cover {
    background-size: cover;
  }
  .bg-fixed {
    background-attachment: fixed;
  }
  .bg-local {
    background-attachment: local;
  }
  .bg-scroll {
    background-attachment: scroll;
  }
  .bg-clip-border {
    background-clip: border-box;
  }
  .bg-clip-content {
    background-clip: content-box;
  }
  .bg-clip-padding {
    background-clip: padding-box;
  }
  .bg-clip-text {
    background-clip: text;
  }
  .bg-bottom {
    background-position: bottom;
  }
  .bg-center {
    background-position: center;
  }
  .bg-left {
    background-position: left;
  }
  .bg-left-bottom {
    background-position: left bottom;
  }
  .bg-left-top {
    background-position: left top;
  }
  .bg-right {
    background-position: right;
  }
  .bg-right-bottom {
    background-position: right bottom;
  }
  .bg-right-top {
    background-position: right top;
  }
  .bg-top {
    background-position: top;
  }
  .bg-no-repeat {
    background-repeat: no-repeat;
  }
  .bg-repeat {
    background-repeat: repeat;
  }
  .bg-repeat-round {
    background-repeat: round;
  }
  .bg-repeat-space {
    background-repeat: space;
  }
  .bg-repeat-x {
    background-repeat: repeat-x;
  }
  .bg-repeat-y {
    background-repeat: repeat-y;
  }
  .bg-origin-border {
    background-origin: border-box;
  }
  .bg-origin-content {
    background-origin: content-box;
  }
  .bg-origin-padding {
    background-origin: padding-box;
  }
  .fill-none {
    fill: none;
  }
  .stroke-none {
    stroke: none;
  }
  .object-contain {
    object-fit: contain;
  }
  .object-cover {
    object-fit: cover;
  }
  .object-fill {
    object-fit: fill;
  }
  .object-none {
    object-fit: none;
  }
  .object-scale-down {
    object-fit: scale-down;
  }
  .object-bottom {
    object-position: bottom;
  }
  .object-center {
    object-position: center;
  }
  .object-left {
    object-position: left;
  }
  .object-left-bottom {
    object-position: left bottom;
  }
  .object-left-top {
    object-position: left top;
  }
  .object-right {
    object-position: right;
  }
  .object-right-bottom {
    object-position: right bottom;
  }
  .object-right-top {
    object-position: right top;
  }
  .object-top {
    object-position: top;
  }
  .checkbox-lg {
    padding: 0.3125rem;
    --size: calc(var(--size-selector, 0.25rem) * 7);
  }
  .checkbox-md {
    padding: 0.25rem;
    --size: calc(var(--size-selector, 0.25rem) * 6);
  }
  .checkbox-sm {
    padding: 0.1875rem;
    --size: calc(var(--size-selector, 0.25rem) * 5);
  }
  .checkbox-xl {
    padding: 0.375rem;
    --size: calc(var(--size-selector, 0.25rem) * 8);
  }
  .checkbox-xs {
    padding: 0.125rem;
    --size: calc(var(--size-selector, 0.25rem) * 4);
  }
  .radio-lg {
    padding: 0.3125rem;
    &:is([type="radio"]) {
      --size: calc(var(--size-selector, 0.25rem) * 7);
    }
  }
  .radio-md {
    padding: 0.25rem;
    &:is([type="radio"]) {
      --size: calc(var(--size-selector, 0.25rem) * 6);
    }
  }
  .radio-sm {
    padding: 0.1875rem;
    &:is([type="radio"]) {
      --size: calc(var(--size-selector, 0.25rem) * 5);
    }
  }
  .radio-xl {
    padding: 0.375rem;
    &:is([type="radio"]) {
      --size: calc(var(--size-selector, 0.25rem) * 8);
    }
  }
  .radio-xs {
    padding: 0.125rem;
    &:is([type="radio"]) {
      --size: calc(var(--size-selector, 0.25rem) * 4);
    }
  }
  .p-1 {
    padding: calc(var(--spacing) * 1);
  }
  .p-2 {
    padding: calc(var(--spacing) * 2);
  }
  .p-3 {
    padding: calc(var(--spacing) * 3);
  }
  .p-4 {
    padding: calc(var(--spacing) * 4);
  }
  .p-6 {
    padding: calc(var(--spacing) * 6);
  }
  .p-8 {
    padding: calc(var(--spacing) * 8);
  }
  .menu-title {
    padding-inline: calc(0.25rem * 3);
    padding-block: calc(0.25rem * 2);
    color: color-mix(in oklab, var(--color-base-content) 40%, transparent);
    font-size: 0.875rem;
    font-weight: 600;
  }
  .table-lg {
    :not(thead, tfoot) tr {
      font-size: 1.125rem;
    }
    :where(th, td) {
      padding-inline: calc(0.25rem * 5);
      padding-block: calc(0.25rem * 4);
    }
  }
  .table-md {
    :not(thead, tfoot) tr {
      font-size: 0.875rem;
    }
    :where(th, td) {
      padding-inline: calc(0.25rem * 4);
      padding-block: calc(0.25rem * 3);
    }
  }
  .table-sm {
    :not(thead, tfoot) tr {
      font-size: 0.75rem;
    }
    :where(th, td) {
      padding-inline: calc(0.25rem * 3);
      padding-block: calc(0.25rem * 2);
    }
  }
  .table-xl {
    :not(thead, tfoot) tr {
      font-size: 1.375rem;
    }
    :where(th, td) {
      padding-inline: calc(0.25rem * 6);
      padding-block: calc(0.25rem * 5);
    }
  }
  .table-xs {
    :not(thead, tfoot) tr {
      font-size: 0.6875rem;
    }
    :where(th, td) {
      padding-inline: calc(0.25rem * 2);
      padding-block: calc(0.25rem * 1);
    }
  }
  .badge-lg {
    --size: calc(var(--size-selector, 0.25rem) * 7);
    font-size: 1rem;
    padding-inline: calc(0.25rem * 3.5 - var(--border));
  }
  .badge-md {
    --size: calc(var(--size-selector, 0.25rem) * 6);
    font-size: 0.875rem;
    padding-inline: calc(0.25rem * 3 - var(--border));
  }
  .badge-sm {
    --size: calc(var(--size-selector, 0.25rem) * 5);
    font-size: 0.75rem;
    padding-inline: calc(0.25rem * 2.5 - var(--border));
  }
  .badge-xl {
    --size: calc(var(--size-selector, 0.25rem) * 8);
    font-size: 1.125rem;
    padding-inline: calc(0.25rem * 4 - var(--border));
  }
  .badge-xs {
    --size: calc(var(--size-selector, 0.25rem) * 4);
    font-size: 0.625rem;
    padding-inline: calc(0.25rem * 2 - var(--border));
  }
  .px-0 {
    padding-inline: calc(var(--spacing) * 0);
  }
  .px-4 {
    padding-inline: calc(var(--spacing) * 4);
  }
  .px-6 {
    padding-inline: calc(var(--spacing) * 6);
  }
  .px-8 {
    padding-inline: calc(var(--spacing) * 8);
  }
  .py-2 {
    padding-block: calc(var(--spacing) * 2);
  }
  .py-3 {
    padding-block: calc(var(--spacing) * 3);
  }
  .py-4 {
    padding-block: calc(var(--spacing) * 4);
  }
  .py-6 {
    padding-block: calc(var(--spacing) * 6);
  }
  .py-8 {
    padding-block: calc(var(--spacing) * 8);
  }
  .py-11 {
    padding-block: calc(var(--spacing) * 11);
  }
  .file-input-xl {
    padding-inline-end: calc(0.25rem * 6);
    --size: calc(var(--size-field, 0.25rem) * 14);
    font-size: 1.125rem;
    line-height: 3rem;
    &::file-selector-button {
      font-size: 1.375rem;
    }
  }
  .pt-8 {
    padding-top: calc(var(--spacing) * 8);
  }
  .pt-16 {
    padding-top: calc(var(--spacing) * 16);
  }
  .pb-4 {
    padding-bottom: calc(var(--spacing) * 4);
  }
  .pb-16 {
    padding-bottom: calc(var(--spacing) * 16);
  }
  .text-center {
    text-align: center;
  }
  .text-end {
    text-align: end;
  }
  .text-justify {
    text-align: justify;
  }
  .text-left {
    text-align: left;
  }
  .text-right {
    text-align: right;
  }
  .text-start {
    text-align: start;
  }
  .align-baseline {
    vertical-align: baseline;
  }
  .align-bottom {
    vertical-align: bottom;
  }
  .align-middle {
    vertical-align: middle;
  }
  .align-sub {
    vertical-align: sub;
  }
  .align-super {
    vertical-align: super;
  }
  .align-text-bottom {
    vertical-align: text-bottom;
  }
  .align-text-top {
    vertical-align: text-top;
  }
  .align-top {
    vertical-align: top;
  }
  .file-input-lg {
    --size: calc(var(--size-field, 0.25rem) * 12);
    font-size: 1.125rem;
    line-height: 2.5rem;
    &::file-selector-button {
      font-size: 1.125rem;
    }
  }
  .file-input-md {
    --size: calc(var(--size-field, 0.25rem) * 10);
    font-size: 0.875rem;
    line-height: 2;
    &::file-selector-button {
      font-size: 0.875rem;
    }
  }
  .file-input-sm {
    --size: calc(var(--size-field, 0.25rem) * 8);
    font-size: 0.75rem;
    line-height: 1.5rem;
    &::file-selector-button {
      font-size: 0.75rem;
    }
  }
  .file-input-xs {
    --size: calc(var(--size-field, 0.25rem) * 6);
    font-size: 0.6875rem;
    line-height: 1rem;
    &::file-selector-button {
      font-size: 0.6875rem;
    }
  }
  .text-2xl {
    font-size: var(--text-2xl);
    line-height: var(--tw-leading, var(--text-2xl--line-height));
  }
  .text-3xl {
    font-size: var(--text-3xl);
    line-height: var(--tw-leading, var(--text-3xl--line-height));
  }
  .text-4xl {
    font-size: var(--text-4xl);
    line-height: var(--tw-leading, var(--text-4xl--line-height));
  }
  .text-5xl {
    font-size: var(--text-5xl);
    line-height: var(--tw-leading, var(--text-5xl--line-height));
  }
  .text-7xl {
    font-size: var(--text-7xl);
    line-height: var(--tw-leading, var(--text-7xl--line-height));
  }
  .text-base {
    font-size: var(--text-base);
    line-height: var(--tw-leading, var(--text-base--line-height));
  }
  .text-lg {
    font-size: var(--text-lg);
    line-height: var(--tw-leading, var(--text-lg--line-height));
  }
  .text-sm {
    font-size: var(--text-sm);
    line-height: var(--tw-leading, var(--text-sm--line-height));
  }
  .text-xl {
    font-size: var(--text-xl);
    line-height: var(--tw-leading, var(--text-xl--line-height));
  }
  .text-xs {
    font-size: var(--text-xs);
    line-height: var(--tw-leading, var(--text-xs--line-height));
  }
  .tabs-lg {
    --tab-height: calc(var(--size-field, 0.25rem) * 12);
    :where(.tab) {
      font-size: 1.125rem;
      --tab-p: 1rem;
      --tab-radius-min: calc(1.5rem - var(--border));
    }
  }
  .tabs-md {
    --tab-height: calc(var(--size-field, 0.25rem) * 10);
    :where(.tab) {
      font-size: 0.875rem;
      --tab-p: 0.75rem;
      --tab-radius-min: calc(0.75rem - var(--border));
    }
  }
  .tabs-sm {
    --tab-height: calc(var(--size-field, 0.25rem) * 8);
    :where(.tab) {
      font-size: 0.875rem;
      --tab-p: 0.5rem;
      --tab-radius-min: calc(0.5rem - var(--border));
    }
  }
  .tabs-xl {
    --tab-height: calc(var(--size-field, 0.25rem) * 14);
    :where(.tab) {
      font-size: 1.125rem;
      --tab-p: 1.25rem;
      --tab-radius-min: calc(2rem - var(--border));
    }
  }
  .tabs-xs {
    --tab-height: calc(var(--size-field, 0.25rem) * 6);
    :where(.tab) {
      font-size: 0.75rem;
      --tab-p: 0.375rem;
      --tab-radius-min: calc(0.5rem - var(--border));
    }
  }
  .kbd-lg {
    --size: calc(var(--size-selector, 0.25rem) * 7);
    font-size: 1rem;
  }
  .kbd-md {
    --size: calc(var(--size-selector, 0.25rem) * 6);
    font-size: 0.875rem;
  }
  .kbd-sm {
    --size: calc(var(--size-selector, 0.25rem) * 5);
    font-size: 0.75rem;
  }
  .kbd-xl {
    --size: calc(var(--size-selector, 0.25rem) * 8);
    font-size: 1.125rem;
  }
  .kbd-xs {
    --size: calc(var(--size-selector, 0.25rem) * 4);
    font-size: 0.625rem;
  }
  .select-lg {
    --size: calc(var(--size-field, 0.25rem) * 12);
    font-size: 1.125rem;
  }
  .select-md {
    --size: calc(var(--size-field, 0.25rem) * 10);
    font-size: 0.875rem;
  }
  .select-sm {
    --size: calc(var(--size-field, 0.25rem) * 8);
    font-size: 0.75rem;
  }
  .select-xl {
    --size: calc(var(--size-field, 0.25rem) * 14);
    font-size: 1.375rem;
  }
  .select-xs {
    --size: calc(var(--size-field, 0.25rem) * 6);
    font-size: 0.6875rem;
  }
  .textarea-lg {
    font-size: 1.125rem;
  }
  .textarea-md {
    font-size: 0.875rem;
  }
  .textarea-sm {
    font-size: 0.75rem;
  }
  .textarea-xl {
    font-size: 1.375rem;
  }
  .textarea-xs {
    font-size: 0.6875rem;
  }
  .leading-none {
    --tw-leading: 1;
    line-height: 1;
  }
  .leading-relaxed {
    --tw-leading: var(--leading-relaxed);
    line-height: var(--leading-relaxed);
  }
  .leading-snug {
    --tw-leading: var(--leading-snug);
    line-height: var(--leading-snug);
  }
  .font-bold {
    --tw-font-weight: var(--font-weight-bold);
    font-weight: var(--font-weight-bold);
  }
  .font-medium {
    --tw-font-weight: var(--font-weight-medium);
    font-weight: var(--font-weight-medium);
  }
  .tracking-wide {
    --tw-tracking: var(--tracking-wide);
    letter-spacing: var(--tracking-wide);
  }
  .text-balance {
    text-wrap: balance;
  }
  .text-nowrap {
    text-wrap: nowrap;
  }
  .text-pretty {
    text-wrap: pretty;
  }
  .text-wrap {
    text-wrap: wrap;
  }
  .break-normal {
    overflow-wrap: normal;
    word-break: normal;
  }
  .break-words {
    overflow-wrap: break-word;
  }
  .break-all {
    word-break: break-all;
  }
  .break-keep {
    word-break: keep-all;
  }
  .overflow-ellipsis {
    text-overflow: ellipsis;
  }
  .text-clip {
    text-overflow: clip;
  }
  .text-ellipsis {
    text-overflow: ellipsis;
  }
  .hyphens-auto {
    -webkit-hyphens: auto;
    hyphens: auto;
  }
  .hyphens-manual {
    -webkit-hyphens: manual;
    hyphens: manual;
  }
  .hyphens-none {
    -webkit-hyphens: none;
    hyphens: none;
  }
  .whitespace-break-spaces {
    white-space: break-spaces;
  }
  .whitespace-normal {
    white-space: normal;
  }
  .whitespace-nowrap {
    white-space: nowrap;
  }
  .whitespace-pre {
    white-space: pre;
  }
  .whitespace-pre-line {
    white-space: pre-line;
  }
  .whitespace-pre-wrap {
    white-space: pre-wrap;
  }
  .file-input-accent {
    --btn-color: var(--color-accent);
    &::file-selector-button {
      color: var(--color-accent-content);
    }
    &, &:focus, &:focus-within {
      --input-color: var(--color-accent);
    }
  }
  .file-input-error {
    --btn-color: var(--color-error);
    &::file-selector-button {
      color: var(--color-error-content);
    }
    &, &:focus, &:focus-within {
      --input-color: var(--color-error);
    }
  }
  .file-input-info {
    --btn-color: var(--color-info);
    &::file-selector-button {
      color: var(--color-info-content);
    }
    &, &:focus, &:focus-within {
      --input-color: var(--color-info);
    }
  }
  .file-input-neutral {
    --btn-color: var(--color-neutral);
    &::file-selector-button {
      color: var(--color-neutral-content);
    }
    &, &:focus, &:focus-within {
      --input-color: var(--color-neutral);
    }
  }
  .file-input-primary {
    --btn-color: var(--color-primary);
    &::file-selector-button {
      color: var(--color-primary-content);
    }
    &, &:focus, &:focus-within {
      --input-color: var(--color-primary);
    }
  }
  .file-input-secondary {
    --btn-color: var(--color-secondary);
    &::file-selector-button {
      color: var(--color-secondary-content);
    }
    &, &:focus, &:focus-within {
      --input-color: var(--color-secondary);
    }
  }
  .file-input-success {
    --btn-color: var(--color-success);
    &::file-selector-button {
      color: var(--color-success-content);
    }
    &, &:focus, &:focus-within {
      --input-color: var(--color-success);
    }
  }
  .file-input-warning {
    --btn-color: var(--color-warning);
    &::file-selector-button {
      color: var(--color-warning-content);
    }
    &, &:focus, &:focus-within {
      --input-color: var(--color-warning);
    }
  }
  .checkbox-accent {
    color: var(--color-accent-content);
    --input-color: var(--color-accent);
  }
  .checkbox-error {
    color: var(--color-error-content);
    --input-color: var(--color-error);
  }
  .checkbox-info {
    color: var(--color-info-content);
    --input-color: var(--color-info);
  }
  .checkbox-neutral {
    color: var(--color-neutral-content);
    --input-color: var(--color-neutral);
  }
  .checkbox-primary {
    color: var(--color-primary-content);
    --input-color: var(--color-primary);
  }
  .checkbox-secondary {
    color: var(--color-secondary-content);
    --input-color: var(--color-secondary);
  }
  .checkbox-success {
    color: var(--color-success-content);
    --input-color: var(--color-success);
  }
  .checkbox-warning {
    color: var(--color-warning-content);
    --input-color: var(--color-warning);
  }
  .link-accent {
    color: var(--color-accent);
    @media (hover: hover) {
      &:hover {
        color: color-mix(in oklab, var(--color-accent) 80%, #000);
      }
    }
  }
  .link-error {
    color: var(--color-error);
    @media (hover: hover) {
      &:hover {
        color: color-mix(in oklab, var(--color-error) 80%, #000);
      }
    }
  }
  .link-info {
    color: var(--color-info);
    @media (hover: hover) {
      &:hover {
        color: color-mix(in oklab, var(--color-info) 80%, #000);
      }
    }
  }
  .link-neutral {
    color: var(--color-neutral);
    @media (hover: hover) {
      &:hover {
        color: color-mix(in oklab, var(--color-neutral) 80%, #000);
      }
    }
  }
  .link-primary {
    color: var(--color-primary);
    @media (hover: hover) {
      &:hover {
        color: color-mix(in oklab, var(--color-primary) 80%, #000);
      }
    }
  }
  .link-secondary {
    color: var(--color-secondary);
    @media (hover: hover) {
      &:hover {
        color: color-mix(in oklab, var(--color-secondary) 80%, #000);
      }
    }
  }
  .link-success {
    color: var(--color-success);
    @media (hover: hover) {
      &:hover {
        color: color-mix(in oklab, var(--color-success) 80%, #000);
      }
    }
  }
  .link-warning {
    color: var(--color-warning);
    @media (hover: hover) {
      &:hover {
        color: color-mix(in oklab, var(--color-warning) 80%, #000);
      }
    }
  }
  .range-accent {
    color: var(--color-accent);
    --range-thumb: var(--color-accent-content);
  }
  .range-error {
    color: var(--color-error);
    --range-thumb: var(--color-error-content);
  }
  .range-info {
    color: var(--color-info);
    --range-thumb: var(--color-info-content);
  }
  .range-neutral {
    color: var(--color-neutral);
    --range-thumb: var(--color-neutral-content);
  }
  .range-primary {
    color: var(--color-primary);
    --range-thumb: var(--color-primary-content);
  }
  .range-secondary {
    color: var(--color-secondary);
    --range-thumb: var(--color-secondary-content);
  }
  .range-success {
    color: var(--color-success);
    --range-thumb: var(--color-success-content);
  }
  .range-warning {
    color: var(--color-warning);
    --range-thumb: var(--color-warning-content);
  }
  .tooltip-accent {
    --tt-bg: var(--color-accent);
    > .tooltip-content, &[data-tip]:before {
      color: var(--color-accent-content);
    }
  }
  .tooltip-error {
    --tt-bg: var(--color-error);
    > .tooltip-content, &[data-tip]:before {
      color: var(--color-error-content);
    }
  }
  .tooltip-info {
    --tt-bg: var(--color-info);
    > .tooltip-content, &[data-tip]:before {
      color: var(--color-info-content);
    }
  }
  .tooltip-primary {
    --tt-bg: var(--color-primary);
    > .tooltip-content, &[data-tip]:before {
      color: var(--color-primary-content);
    }
  }
  .tooltip-secondary {
    --tt-bg: var(--color-secondary);
    > .tooltip-content, &[data-tip]:before {
      color: var(--color-secondary-content);
    }
  }
  .tooltip-success {
    --tt-bg: var(--color-success);
    > .tooltip-content, &[data-tip]:before {
      color: var(--color-success-content);
    }
  }
  .tooltip-warning {
    --tt-bg: var(--color-warning);
    > .tooltip-content, &[data-tip]:before {
      color: var(--color-warning-content);
    }
  }
  .\[color\:red\] {
    color: red;
  }
  .\[color\:red\]\/50 {
    color: color-mix(in oklab, red 50%, transparent);
  }
  .\[color\:red\]\/50\! {
    color: color-mix(in oklab, red 50%, transparent) !important;
  }
  .progress-accent {
    color: var(--color-accent);
  }
  .progress-error {
    color: var(--color-error);
  }
  .progress-info {
    color: var(--color-info);
  }
  .progress-neutral {
    color: var(--color-neutral);
  }
  .progress-primary {
    color: var(--color-primary);
  }
  .progress-secondary {
    color: var(--color-secondary);
  }
  .progress-success {
    color: var(--color-success);
  }
  .progress-warning {
    color: var(--color-warning);
  }
  .text-black {
    color: var(--color-black);
  }
  .text-gray-300 {
    color: var(--color-gray-300);
  }
  .text-gray-500 {
    color: var(--color-gray-500);
  }
  .text-gray-600 {
    color: var(--color-gray-600);
  }
  .text-gray-700 {
    color: var(--color-gray-700);
  }
  .text-gray-900 {
    color: var(--color-gray-900);
  }
  .text-white {
    color: var(--color-white);
  }
  .capitalize {
    text-transform: capitalize;
  }
  .lowercase {
    text-transform: lowercase;
  }
  .normal-case {
    text-transform: none;
  }
  .uppercase {
    text-transform: uppercase;
  }
  .italic {
    font-style: italic;
  }
  .not-italic {
    font-style: normal;
  }
  .font-stretch-condensed {
    font-stretch: condensed;
  }
  .font-stretch-expanded {
    font-stretch: expanded;
  }
  .font-stretch-extra-condensed {
    font-stretch: extra-condensed;
  }
  .font-stretch-extra-expanded {
    font-stretch: extra-expanded;
  }
  .font-stretch-normal {
    font-stretch: normal;
  }
  .font-stretch-semi-condensed {
    font-stretch: semi-condensed;
  }
  .font-stretch-semi-expanded {
    font-stretch: semi-expanded;
  }
  .font-stretch-ultra-condensed {
    font-stretch: ultra-condensed;
  }
  .font-stretch-ultra-expanded {
    font-stretch: ultra-expanded;
  }
  .diagonal-fractions {
    --tw-numeric-fraction: diagonal-fractions;
    font-variant-numeric: var(--tw-ordinal,) var(--tw-slashed-zero,) var(--tw-numeric-figure,) var(--tw-numeric-spacing,) var(--tw-numeric-fraction,);
  }
  .lining-nums {
    --tw-numeric-figure: lining-nums;
    font-variant-numeric: var(--tw-ordinal,) var(--tw-slashed-zero,) var(--tw-numeric-figure,) var(--tw-numeric-spacing,) var(--tw-numeric-fraction,);
  }
  .oldstyle-nums {
    --tw-numeric-figure: oldstyle-nums;
    font-variant-numeric: var(--tw-ordinal,) var(--tw-slashed-zero,) var(--tw-numeric-figure,) var(--tw-numeric-spacing,) var(--tw-numeric-fraction,);
  }
  .ordinal {
    --tw-ordinal: ordinal;
    font-variant-numeric: var(--tw-ordinal,) var(--tw-slashed-zero,) var(--tw-numeric-figure,) var(--tw-numeric-spacing,) var(--tw-numeric-fraction,);
  }
  .proportional-nums {
    --tw-numeric-spacing: proportional-nums;
    font-variant-numeric: var(--tw-ordinal,) var(--tw-slashed-zero,) var(--tw-numeric-figure,) var(--tw-numeric-spacing,) var(--tw-numeric-fraction,);
  }
  .slashed-zero {
    --tw-slashed-zero: slashed-zero;
    font-variant-numeric: var(--tw-ordinal,) var(--tw-slashed-zero,) var(--tw-numeric-figure,) var(--tw-numeric-spacing,) var(--tw-numeric-fraction,);
  }
  .stacked-fractions {
    --tw-numeric-fraction: stacked-fractions;
    font-variant-numeric: var(--tw-ordinal,) var(--tw-slashed-zero,) var(--tw-numeric-figure,) var(--tw-numeric-spacing,) var(--tw-numeric-fraction,);
  }
  .tabular-nums {
    --tw-numeric-spacing: tabular-nums;
    font-variant-numeric: var(--tw-ordinal,) var(--tw-slashed-zero,) var(--tw-numeric-figure,) var(--tw-numeric-spacing,) var(--tw-numeric-fraction,);
  }
  .normal-nums {
    font-variant-numeric: normal;
  }
  .btn-link {
    text-decoration-line: underline;
    outline-color: currentColor;
    --btn-border: #0000;
    --btn-bg: #0000;
    --btn-fg: var(--color-primary);
    --btn-noise: none;
    --btn-shadow: "";
    &:is(.btn-active, :hover, :active:focus, :focus-visible) {
      text-decoration-line: underline;
      --btn-border: #0000;
      --btn-bg: #0000;
    }
    @media (hover: none) {
      &:hover:not(.btn-active, :active, :focus-visible, :disabled, [disabled], .btn-disabled) {
        text-decoration-line: none;
      }
    }
  }
  .link-hover {
    text-decoration-line: none;
    &:hover {
      @media (hover: hover) {
        text-decoration-line: underline;
      }
    }
  }
  .line-through {
    text-decoration-line: line-through;
  }
  .no-underline {
    text-decoration-line: none;
  }
  .overline {
    text-decoration-line: overline;
  }
  .underline {
    text-decoration-line: underline;
  }
  .decoration-dashed {
    text-decoration-style: dashed;
  }
  .decoration-dotted {
    text-decoration-style: dotted;
  }
  .decoration-double {
    text-decoration-style: double;
  }
  .decoration-solid {
    text-decoration-style: solid;
  }
  .decoration-wavy {
    text-decoration-style: wavy;
  }
  .decoration-auto {
    text-decoration-thickness: auto;
  }
  .decoration-from-font {
    text-decoration-thickness: from-font;
  }
  .underline-offset-auto {
    text-underline-offset: auto;
  }
  .antialiased {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }
  .subpixel-antialiased {
    -webkit-font-smoothing: auto;
    -moz-osx-font-smoothing: auto;
  }
  .accent-auto {
    accent-color: auto;
  }
  .scheme-dark {
    color-scheme: dark;
  }
  .scheme-light {
    color-scheme: light;
  }
  .scheme-light-dark {
    color-scheme: light dark;
  }
  .scheme-normal {
    color-scheme: normal;
  }
  .scheme-only-dark {
    color-scheme: only dark;
  }
  .scheme-only-light {
    color-scheme: only light;
  }
  .swap-active {
    .swap-off {
      opacity: 0%;
    }
    .swap-on {
      opacity: 100%;
    }
  }
  .opacity-50 {
    opacity: 50%;
  }
  .mix-blend-plus-darker {
    mix-blend-mode: plus-darker;
  }
  .mix-blend-plus-lighter {
    mix-blend-mode: plus-lighter;
  }
  .shadow {
    --tw-shadow: 0 1px 3px 0 var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 1px 2px -1px var(--tw-shadow-color, rgb(0 0 0 / 0.1));
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }
  .shadow-sm {
    --tw-shadow: 0 1px 3px 0 var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 1px 2px -1px var(--tw-shadow-color, rgb(0 0 0 / 0.1));
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }
  .ring {
    --tw-ring-shadow: var(--tw-ring-inset,) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentColor);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }
  .inset-ring {
    --tw-inset-ring-shadow: inset 0 0 0 1px var(--tw-inset-ring-color, currentColor);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }
  .shadow-initial {
    --tw-shadow-color: initial;
  }
  .inset-shadow-initial {
    --tw-inset-shadow-color: initial;
  }
  .outline-hidden {
    --tw-outline-style: none;
    outline-style: none;
    @media (forced-colors: active) {
      outline: 2px solid transparent;
      outline-offset: 2px;
    }
  }
  .outline {
    outline-style: var(--tw-outline-style);
    outline-width: 1px;
  }
  .btn-ghost {
    &:not(.btn-active, :hover, :active:focus, :focus-visible) {
      --btn-shadow: "";
      --btn-bg: #0000;
      --btn-border: #0000;
      --btn-noise: none;
      &:not(:disabled, [disabled], .btn-disabled) {
        outline-color: currentColor;
        --btn-fg: currentColor;
      }
    }
    @media (hover: none) {
      &:hover:not(.btn-active, :active, :focus-visible, :disabled, [disabled], .btn-disabled) {
        --btn-shadow: "";
        --btn-bg: #0000;
        --btn-border: #0000;
        --btn-noise: none;
        --btn-fg: currentColor;
      }
    }
  }
  .blur {
    --tw-blur: blur(8px);
    filter: var(--tw-blur,) var(--tw-brightness,) var(--tw-contrast,) var(--tw-grayscale,) var(--tw-hue-rotate,) var(--tw-invert,) var(--tw-saturate,) var(--tw-sepia,) var(--tw-drop-shadow,);
  }
  .blur-none {
    --tw-blur:  ;
    filter: var(--tw-blur,) var(--tw-brightness,) var(--tw-contrast,) var(--tw-grayscale,) var(--tw-hue-rotate,) var(--tw-invert,) var(--tw-saturate,) var(--tw-sepia,) var(--tw-drop-shadow,);
  }
  .brightness-0 {
    --tw-brightness: brightness(0%);
    filter: var(--tw-blur,) var(--tw-brightness,) var(--tw-contrast,) var(--tw-grayscale,) var(--tw-hue-rotate,) var(--tw-invert,) var(--tw-saturate,) var(--tw-sepia,) var(--tw-drop-shadow,);
  }
  .drop-shadow {
    --tw-drop-shadow: drop-shadow(0 1px 2px rgb(0 0 0 / 0.1)) drop-shadow( 0 1px 1px rgb(0 0 0 / 0.06));
    filter: var(--tw-blur,) var(--tw-brightness,) var(--tw-contrast,) var(--tw-grayscale,) var(--tw-hue-rotate,) var(--tw-invert,) var(--tw-saturate,) var(--tw-sepia,) var(--tw-drop-shadow,);
  }
  .drop-shadow-none {
    --tw-drop-shadow:  ;
    filter: var(--tw-blur,) var(--tw-brightness,) var(--tw-contrast,) var(--tw-grayscale,) var(--tw-hue-rotate,) var(--tw-invert,) var(--tw-saturate,) var(--tw-sepia,) var(--tw-drop-shadow,);
  }
  .grayscale {
    --tw-grayscale: grayscale(100%);
    filter: var(--tw-blur,) var(--tw-brightness,) var(--tw-contrast,) var(--tw-grayscale,) var(--tw-hue-rotate,) var(--tw-invert,) var(--tw-saturate,) var(--tw-sepia,) var(--tw-drop-shadow,);
  }
  .invert {
    --tw-invert: invert(100%);
    filter: var(--tw-blur,) var(--tw-brightness,) var(--tw-contrast,) var(--tw-grayscale,) var(--tw-hue-rotate,) var(--tw-invert,) var(--tw-saturate,) var(--tw-sepia,) var(--tw-drop-shadow,);
  }
  .sepia {
    --tw-sepia: sepia(100%);
    filter: var(--tw-blur,) var(--tw-brightness,) var(--tw-contrast,) var(--tw-grayscale,) var(--tw-hue-rotate,) var(--tw-invert,) var(--tw-saturate,) var(--tw-sepia,) var(--tw-drop-shadow,);
  }
  .filter {
    filter: var(--tw-blur,) var(--tw-brightness,) var(--tw-contrast,) var(--tw-grayscale,) var(--tw-hue-rotate,) var(--tw-invert,) var(--tw-saturate,) var(--tw-sepia,) var(--tw-drop-shadow,);
  }
  .backdrop-blur {
    --tw-backdrop-blur: blur(8px);
    -webkit-backdrop-filter: var(--tw-backdrop-blur,) var(--tw-backdrop-brightness,) var(--tw-backdrop-contrast,) var(--tw-backdrop-grayscale,) var(--tw-backdrop-hue-rotate,) var(--tw-backdrop-invert,) var(--tw-backdrop-opacity,) var(--tw-backdrop-saturate,) var(--tw-backdrop-sepia,);
    backdrop-filter: var(--tw-backdrop-blur,) var(--tw-backdrop-brightness,) var(--tw-backdrop-contrast,) var(--tw-backdrop-grayscale,) var(--tw-backdrop-hue-rotate,) var(--tw-backdrop-invert,) var(--tw-backdrop-opacity,) var(--tw-backdrop-saturate,) var(--tw-backdrop-sepia,);
  }
  .backdrop-blur-none {
    --tw-backdrop-blur:  ;
    -webkit-backdrop-filter: var(--tw-backdrop-blur,) var(--tw-backdrop-brightness,) var(--tw-backdrop-contrast,) var(--tw-backdrop-grayscale,) var(--tw-backdrop-hue-rotate,) var(--tw-backdrop-invert,) var(--tw-backdrop-opacity,) var(--tw-backdrop-saturate,) var(--tw-backdrop-sepia,);
    backdrop-filter: var(--tw-backdrop-blur,) var(--tw-backdrop-brightness,) var(--tw-backdrop-contrast,) var(--tw-backdrop-grayscale,) var(--tw-backdrop-hue-rotate,) var(--tw-backdrop-invert,) var(--tw-backdrop-opacity,) var(--tw-backdrop-saturate,) var(--tw-backdrop-sepia,);
  }
  .backdrop-grayscale {
    --tw-backdrop-grayscale: grayscale(100%);
    -webkit-backdrop-filter: var(--tw-backdrop-blur,) var(--tw-backdrop-brightness,) var(--tw-backdrop-contrast,) var(--tw-backdrop-grayscale,) var(--tw-backdrop-hue-rotate,) var(--tw-backdrop-invert,) var(--tw-backdrop-opacity,) var(--tw-backdrop-saturate,) var(--tw-backdrop-sepia,);
    backdrop-filter: var(--tw-backdrop-blur,) var(--tw-backdrop-brightness,) var(--tw-backdrop-contrast,) var(--tw-backdrop-grayscale,) var(--tw-backdrop-hue-rotate,) var(--tw-backdrop-invert,) var(--tw-backdrop-opacity,) var(--tw-backdrop-saturate,) var(--tw-backdrop-sepia,);
  }
  .backdrop-invert {
    --tw-backdrop-invert: invert(100%);
    -webkit-backdrop-filter: var(--tw-backdrop-blur,) var(--tw-backdrop-brightness,) var(--tw-backdrop-contrast,) var(--tw-backdrop-grayscale,) var(--tw-backdrop-hue-rotate,) var(--tw-backdrop-invert,) var(--tw-backdrop-opacity,) var(--tw-backdrop-saturate,) var(--tw-backdrop-sepia,);
    backdrop-filter: var(--tw-backdrop-blur,) var(--tw-backdrop-brightness,) var(--tw-backdrop-contrast,) var(--tw-backdrop-grayscale,) var(--tw-backdrop-hue-rotate,) var(--tw-backdrop-invert,) var(--tw-backdrop-opacity,) var(--tw-backdrop-saturate,) var(--tw-backdrop-sepia,);
  }
  .backdrop-sepia {
    --tw-backdrop-sepia: sepia(100%);
    -webkit-backdrop-filter: var(--tw-backdrop-blur,) var(--tw-backdrop-brightness,) var(--tw-backdrop-contrast,) var(--tw-backdrop-grayscale,) var(--tw-backdrop-hue-rotate,) var(--tw-backdrop-invert,) var(--tw-backdrop-opacity,) var(--tw-backdrop-saturate,) var(--tw-backdrop-sepia,);
    backdrop-filter: var(--tw-backdrop-blur,) var(--tw-backdrop-brightness,) var(--tw-backdrop-contrast,) var(--tw-backdrop-grayscale,) var(--tw-backdrop-hue-rotate,) var(--tw-backdrop-invert,) var(--tw-backdrop-opacity,) var(--tw-backdrop-saturate,) var(--tw-backdrop-sepia,);
  }
  .backdrop-filter {
    -webkit-backdrop-filter: var(--tw-backdrop-blur,) var(--tw-backdrop-brightness,) var(--tw-backdrop-contrast,) var(--tw-backdrop-grayscale,) var(--tw-backdrop-hue-rotate,) var(--tw-backdrop-invert,) var(--tw-backdrop-opacity,) var(--tw-backdrop-saturate,) var(--tw-backdrop-sepia,);
    backdrop-filter: var(--tw-backdrop-blur,) var(--tw-backdrop-brightness,) var(--tw-backdrop-contrast,) var(--tw-backdrop-grayscale,) var(--tw-backdrop-hue-rotate,) var(--tw-backdrop-invert,) var(--tw-backdrop-opacity,) var(--tw-backdrop-saturate,) var(--tw-backdrop-sepia,);
  }
  .transition {
    transition-property: color, background-color, border-color, outline-color, text-decoration-color, fill, stroke, --tw-gradient-from, --tw-gradient-via, --tw-gradient-to, opacity, box-shadow, transform, translate, scale, rotate, filter, -webkit-backdrop-filter, backdrop-filter;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }
  .transition-\[max-height\] {
    transition-property: max-height;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }
  .transition-all {
    transition-property: all;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }
  .transition-colors {
    transition-property: color, background-color, border-color, outline-color, text-decoration-color, fill, stroke, --tw-gradient-from, --tw-gradient-via, --tw-gradient-to;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }
  .transition-opacity {
    transition-property: opacity;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }
  .transition-shadow {
    transition-property: box-shadow;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }
  .transition-transform {
    transition-property: transform, translate, scale, rotate;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }
  .transition-none {
    transition-property: none;
  }
  .transition-discrete {
    transition-behavior: allow-discrete;
  }
  .transition-normal {
    transition-behavior: normal;
  }
  .duration-100 {
    --tw-duration: 100ms;
    transition-duration: 100ms;
  }
  .duration-200 {
    --tw-duration: 200ms;
    transition-duration: 200ms;
  }
  .duration-400 {
    --tw-duration: 400ms;
    transition-duration: 400ms;
  }
  .duration-700 {
    --tw-duration: 700ms;
    transition-duration: 700ms;
  }
  .ease-in {
    --tw-ease: var(--ease-in);
    transition-timing-function: var(--ease-in);
  }
  .ease-in-out {
    --tw-ease: var(--ease-in-out);
    transition-timing-function: var(--ease-in-out);
  }
  .ease-linear {
    --tw-ease: linear;
    transition-timing-function: linear;
  }
  .ease-out {
    --tw-ease: var(--ease-out);
    transition-timing-function: var(--ease-out);
  }
  .will-change-auto {
    will-change: auto;
  }
  .will-change-contents {
    will-change: contents;
  }
  .will-change-scroll {
    will-change: scroll-position;
  }
  .will-change-transform {
    will-change: transform;
  }
  .contain-inline-size {
    --tw-contain-size: inline-size;
    contain: var(--tw-contain-size,) var(--tw-contain-layout,) var(--tw-contain-paint,) var(--tw-contain-style,);
  }
  .contain-layout {
    --tw-contain-layout: layout;
    contain: var(--tw-contain-size,) var(--tw-contain-layout,) var(--tw-contain-paint,) var(--tw-contain-style,);
  }
  .contain-paint {
    --tw-contain-paint: paint;
    contain: var(--tw-contain-size,) var(--tw-contain-layout,) var(--tw-contain-paint,) var(--tw-contain-style,);
  }
  .contain-size {
    --tw-contain-size: size;
    contain: var(--tw-contain-size,) var(--tw-contain-layout,) var(--tw-contain-paint,) var(--tw-contain-style,);
  }
  .contain-style {
    --tw-contain-style: style;
    contain: var(--tw-contain-size,) var(--tw-contain-layout,) var(--tw-contain-paint,) var(--tw-contain-style,);
  }
  .contain-content {
    contain: content;
  }
  .contain-none {
    contain: none;
  }
  .contain-strict {
    contain: strict;
  }
  .content-none {
    --tw-content: none;
    content: none;
  }
  .forced-color-adjust-auto {
    forced-color-adjust: auto;
  }
  .forced-color-adjust-none {
    forced-color-adjust: none;
  }
  .tabs-bottom {
    --tabs-height: auto;
    --tabs-direction: row;
    .tab {
      --tab-order: 1;
      --tab-border: var(--border) 0 0 0;
      --tab-radius-ss: 0;
      --tab-radius-se: 0;
      --tab-radius-es: min(var(--radius-field), var(--tab-radius-min));
      --tab-radius-ee: min(var(--radius-field), var(--tab-radius-min));
      --tab-border-colors: var(--tab-border-color) #0000 #0000 #0000;
      --tab-paddings: 0 var(--tab-p) var(--border) var(--tab-p);
      --tab-corner-width: calc(100% + min(var(--radius-field), var(--tab-radius-min)) * 2);
      --tab-corner-height: min(var(--radius-field), var(--tab-radius-min));
      --tab-corner-position: top left, top right;
      &:is(.tab-active, [aria-selected="true"]):not(.tab-disabled, [disabled]), &:is(input:checked), &:is(label:has(:checked)) {
        --tab-border: 0 var(--border) var(--border) var(--border);
        --tab-border-colors: #0000 var(--tab-border-color) var(--tab-border-color)
        var(--tab-border-color);
        --tab-paddings: var(--border) calc(var(--tab-p) - var(--border)) 0
        calc(var(--tab-p) - var(--border));
        --tab-inset: 0 auto auto auto;
        --radius-start: radial-gradient(
        circle at bottom left,
        #0000 var(--tab-grad),
        var(--tab-border-color) calc(var(--tab-grad) + 0.25px),
        var(--tab-border-color) calc(var(--tab-grad) + var(--border)),
        var(--tab-bg) calc(var(--tab-grad) + var(--border) + 0.25px)
      );
        --radius-end: radial-gradient(
        circle at bottom right,
        #0000 var(--tab-grad),
        var(--tab-border-color) calc(var(--tab-grad) + 0.25px),
        var(--tab-border-color) calc(var(--tab-grad) + var(--border)),
        var(--tab-bg) calc(var(--tab-grad) + var(--border) + 0.25px)
      );
      }
    }
    &:has(.tab-content) {
      > .tab:first-child {
        &:not(.tab-active, [aria-selected="true"]) {
          --tab-border-colors: #0000 var(--tab-border-color) var(--tab-border-color)
          var(--tab-border-color);
        }
      }
    }
    .tab-content {
      --tabcontent-order: 0;
      --tabcontent-margin: 0 0 calc(-1 * var(--border)) 0;
      --tabcontent-radius-ss: var(--radius-box);
      --tabcontent-radius-se: var(--radius-box);
      --tabcontent-radius-es: 0;
      --tabcontent-radius-ee: var(--radius-box);
    }
    > :checked, > :is(label:has(:checked)), > :is(.tab-active, [aria-selected="true"]) {
      & + .tab-content:not(:nth-child(2)) {
        --tabcontent-radius-es: var(--radius-box);
      }
    }
  }
  .tabs-top {
    --tabs-height: auto;
    --tabs-direction: row;
    .tab {
      --tab-order: 0;
      --tab-border: 0 0 var(--border) 0;
      --tab-radius-ss: min(var(--radius-field), var(--tab-radius-min));
      --tab-radius-se: min(var(--radius-field), var(--tab-radius-min));
      --tab-radius-es: 0;
      --tab-radius-ee: 0;
      --tab-paddings: var(--border) var(--tab-p) 0 var(--tab-p);
      --tab-border-colors: #0000 #0000 var(--tab-border-color) #0000;
      --tab-corner-width: calc(100% + min(var(--radius-field), var(--tab-radius-min)) * 2);
      --tab-corner-height: min(var(--radius-field), var(--tab-radius-min));
      --tab-corner-position: top left, top right;
      &:is(.tab-active, [aria-selected="true"]):not(.tab-disabled, [disabled]), &:is(input:checked), &:is(label:has(:checked)) {
        --tab-border: var(--border) var(--border) 0 var(--border);
        --tab-border-colors: var(--tab-border-color) var(--tab-border-color) #0000
        var(--tab-border-color);
        --tab-paddings: 0 calc(var(--tab-p) - var(--border)) var(--border)
        calc(var(--tab-p) - var(--border));
        --tab-inset: auto auto 0 auto;
        --radius-start: radial-gradient(
        circle at top left,
        #0000 var(--tab-grad),
        var(--tab-border-color) calc(var(--tab-grad) + 0.25px),
        var(--tab-border-color) calc(var(--tab-grad) + var(--border)),
        var(--tab-bg) calc(var(--tab-grad) + var(--border) + 0.25px)
      );
        --radius-end: radial-gradient(
        circle at top right,
        #0000 var(--tab-grad),
        var(--tab-border-color) calc(var(--tab-grad) + 0.25px),
        var(--tab-border-color) calc(var(--tab-grad) + var(--border)),
        var(--tab-bg) calc(var(--tab-grad) + var(--border) + 0.25px)
      );
      }
    }
    &:has(.tab-content) {
      > .tab:first-child {
        &:not(.tab-active, [aria-selected="true"]) {
          --tab-border-colors: var(--tab-border-color) var(--tab-border-color) #0000
          var(--tab-border-color);
        }
      }
    }
    .tab-content {
      --tabcontent-order: 1;
      --tabcontent-margin: calc(-1 * var(--border)) 0 0 0;
      --tabcontent-radius-ss: 0;
      --tabcontent-radius-se: var(--radius-box);
      --tabcontent-radius-es: var(--radius-box);
      --tabcontent-radius-ee: var(--radius-box);
    }
    :checked, label:has(:checked), :is(.tab-active, [aria-selected="true"]) {
      & + .tab-content {
        &:nth-child(1), &:nth-child(n + 3) {
          --tabcontent-radius-ss: var(--radius-box);
        }
      }
    }
  }
  .btn-outline {
    &:not( .btn-active, :hover, :active:focus, :focus-visible, :disabled, [disabled], .btn-disabled, :checked ) {
      --btn-shadow: "";
      --btn-bg: #0000;
      --btn-fg: var(--btn-color);
      --btn-border: var(--btn-color);
      --btn-noise: none;
    }
    @media (hover: none) {
      &:hover:not( .btn-active, :active, :focus-visible, :disabled, [disabled], .btn-disabled, :checked ) {
        --btn-shadow: "";
        --btn-bg: #0000;
        --btn-fg: var(--btn-color);
        --btn-border: var(--btn-color);
        --btn-noise: none;
      }
    }
  }
  .btn-soft {
    &:not(.btn-active, :hover, :active:focus, :focus-visible, :disabled, [disabled], .btn-disabled) {
      --btn-shadow: "";
      --btn-fg: var(--btn-color, var(--color-base-content));
      --btn-bg: color-mix(
      in oklab,
      var(--btn-color, var(--color-base-content)) 8%,
      var(--color-base-100)
    );
      --btn-border: color-mix(
      in oklab,
      var(--btn-color, var(--color-base-content)) 10%,
      var(--color-base-100)
    );
      --btn-noise: none;
    }
    @media (hover: none) {
      &:hover:not(.btn-active, :active, :focus-visible, :disabled, [disabled], .btn-disabled) {
        --btn-shadow: "";
        --btn-fg: var(--btn-color, var(--color-base-content));
        --btn-bg: color-mix(
        in oklab,
        var(--btn-color, var(--color-base-content)) 8%,
        var(--color-base-100)
      );
        --btn-border: color-mix(
        in oklab,
        var(--btn-color, var(--color-base-content)) 10%,
        var(--color-base-100)
      );
        --btn-noise: none;
      }
    }
  }
  .indicator-end {
    --indicator-s: auto;
    --indicator-e: 0;
    --indicator-x: 50%;
    [dir="rtl"] & {
      --indicator-s: 0;
      --indicator-e: auto;
      --indicator-x: -50%;
    }
  }
  .indicator-start {
    --indicator-s: 0;
    --indicator-e: auto;
    --indicator-x: -50%;
    [dir="rtl"] & {
      --indicator-s: auto;
      --indicator-e: 0;
      --indicator-x: 50%;
    }
  }
  .indicator-center {
    --indicator-s: 50%;
    --indicator-e: 50%;
    --indicator-x: -50%;
    [dir="rtl"] & {
      --indicator-x: 50%;
    }
  }
  .mask-half-1 {
    mask-size: 200%;
    mask-position: left;
    mask-position: left;
    &:where(:dir(rtl), [dir="rtl"], [dir="rtl"] *) {
      mask-position: right;
    }
  }
  .mask-half-2 {
    mask-size: 200%;
    mask-position: right;
    mask-position: right;
    &:where(:dir(rtl), [dir="rtl"], [dir="rtl"] *) {
      mask-position: left;
    }
  }
  .btn-lg {
    --fontsize: 1.125rem;
    --btn-p: 1.25rem;
    --size: calc(var(--size-field, 0.25rem) * 12);
  }
  .btn-md {
    --fontsize: 0.875rem;
    --btn-p: 1rem;
    --size: calc(var(--size-field, 0.25rem) * 10);
  }
  .btn-sm {
    --fontsize: 0.75rem;
    --btn-p: 0.75rem;
    --size: calc(var(--size-field, 0.25rem) * 8);
  }
  .btn-xl {
    --fontsize: 1.375rem;
    --btn-p: 1.5rem;
    --size: calc(var(--size-field, 0.25rem) * 14);
  }
  .btn-xs {
    --fontsize: 0.6875rem;
    --btn-p: 0.5rem;
    --size: calc(var(--size-field, 0.25rem) * 6);
  }
  .card-lg {
    .card-body {
      --card-p: 2rem;
      --card-fs: 1rem;
    }
    .card-title {
      --cardtitle-fs: 1.25rem;
    }
  }
  .card-md {
    .card-body {
      --card-p: 1.5rem;
      --card-fs: 0.875rem;
    }
    .card-title {
      --cardtitle-fs: 1.125rem;
    }
  }
  .card-sm {
    .card-body {
      --card-p: 1rem;
      --card-fs: 0.75rem;
    }
    .card-title {
      --cardtitle-fs: 1rem;
    }
  }
  .card-xl {
    .card-body {
      --card-p: 2.5rem;
      --card-fs: 1.125rem;
    }
    .card-title {
      --cardtitle-fs: 1.375rem;
    }
  }
  .card-xs {
    .card-body {
      --card-p: 0.5rem;
      --card-fs: 0.6875rem;
    }
    .card-title {
      --cardtitle-fs: 0.875rem;
    }
  }
  .indicator-bottom {
    --indicator-t: auto;
    --indicator-b: 0;
    --indicator-y: 50%;
  }
  .indicator-middle {
    --indicator-t: 50%;
    --indicator-b: 50%;
    --indicator-y: -50%;
  }
  .indicator-top {
    --indicator-t: 0;
    --indicator-b: auto;
    --indicator-y: -50%;
  }
  .badge-accent {
    --badge-color: var(--color-accent);
    --badge-fg: var(--color-accent-content);
  }
  .badge-error {
    --badge-color: var(--color-error);
    --badge-fg: var(--color-error-content);
  }
  .badge-info {
    --badge-color: var(--color-info);
    --badge-fg: var(--color-info-content);
  }
  .badge-neutral {
    --badge-color: var(--color-neutral);
    --badge-fg: var(--color-neutral-content);
  }
  .badge-primary {
    --badge-color: var(--color-primary);
    --badge-fg: var(--color-primary-content);
  }
  .badge-secondary {
    --badge-color: var(--color-secondary);
    --badge-fg: var(--color-secondary-content);
  }
  .badge-success {
    --badge-color: var(--color-success);
    --badge-fg: var(--color-success-content);
  }
  .badge-warning {
    --badge-color: var(--color-warning);
    --badge-fg: var(--color-warning-content);
  }
  .btn-accent {
    --btn-color: var(--color-accent);
    --btn-fg: var(--color-accent-content);
  }
  .btn-error {
    --btn-color: var(--color-error);
    --btn-fg: var(--color-error-content);
  }
  .btn-info {
    --btn-color: var(--color-info);
    --btn-fg: var(--color-info-content);
  }
  .btn-neutral {
    --btn-color: var(--color-neutral);
    --btn-fg: var(--color-neutral-content);
  }
  .btn-primary {
    --btn-color: var(--color-primary);
    --btn-fg: var(--color-primary-content);
  }
  .btn-secondary {
    --btn-color: var(--color-secondary);
    --btn-fg: var(--color-secondary-content);
  }
  .btn-success {
    --btn-color: var(--color-success);
    --btn-fg: var(--color-success-content);
  }
  .btn-warning {
    --btn-color: var(--color-warning);
    --btn-fg: var(--color-warning-content);
  }
  .outline-dashed {
    --tw-outline-style: dashed;
    outline-style: dashed;
  }
  .outline-dotted {
    --tw-outline-style: dotted;
    outline-style: dotted;
  }
  .outline-double {
    --tw-outline-style: double;
    outline-style: double;
  }
  .outline-none {
    --tw-outline-style: none;
    outline-style: none;
  }
  .outline-solid {
    --tw-outline-style: solid;
    outline-style: solid;
  }
  .timeline-snap-icon {
    > li {
      --timeline-col-start: 0.5rem;
      --timeline-row-start: minmax(0, 1fr);
    }
  }
  .\[default\:\\u202F\$\{N\(\`\$\{u\}\`\)\}\] {
    default: \u202F${N(`${u}`)};
  }
  .backface-hidden {
    backface-visibility: hidden;
  }
  .backface-visible {
    backface-visibility: visible;
  }
  .divide-x-reverse {
    :where(& > :not(:last-child)) {
      --tw-divide-x-reverse: 1;
    }
  }
  .duration-initial {
    --tw-duration: initial;
  }
  .ease-initial {
    --tw-ease: initial;
  }
  .input-accent {
    &, &:focus, &:focus-within {
      --input-color: var(--color-accent);
    }
  }
  .input-error {
    &, &:focus, &:focus-within {
      --input-color: var(--color-error);
    }
  }
  .input-info {
    &, &:focus, &:focus-within {
      --input-color: var(--color-info);
    }
  }
  .input-neutral {
    &, &:focus, &:focus-within {
      --input-color: var(--color-neutral);
    }
  }
  .input-primary {
    &, &:focus, &:focus-within {
      --input-color: var(--color-primary);
    }
  }
  .input-secondary {
    &, &:focus, &:focus-within {
      --input-color: var(--color-secondary);
    }
  }
  .input-success {
    &, &:focus, &:focus-within {
      --input-color: var(--color-success);
    }
  }
  .input-warning {
    &, &:focus, &:focus-within {
      --input-color: var(--color-warning);
    }
  }
  .loading-ball {
    mask-image: url("data:image/svg+xml,%3Csvg width='24' height='24' viewBox='0 0 24 24' xmlns='http://www.w3.org/2000/svg'%3E%3Cellipse cx='12' cy='5' rx='4' ry='4'%3E%3Canimate attributeName='cy' values='5;20;20.5;20;5' keyTimes='0;0.469;0.5;0.531;1' dur='.8s' repeatCount='indefinite' keySplines='.33,0,.66,.33;.33,.66,.66,1'/%3E%3Canimate attributeName='rx' values='4;4;4.8;4;4' keyTimes='0;0.469;0.5;0.531;1' dur='.8s' repeatCount='indefinite'/%3E%3Canimate attributeName='ry' values='4;4;3;4;4' keyTimes='0;0.469;0.5;0.531;1' dur='.8s' repeatCount='indefinite'/%3E%3C/ellipse%3E%3C/svg%3E");
  }
  .loading-bars {
    mask-image: url("data:image/svg+xml,%3Csvg width='24' height='24' viewBox='0 0 24 24' xmlns='http://www.w3.org/2000/svg'%3E%3Crect x='1' y='1' width='6' height='22'%3E%3Canimate attributeName='y' values='1;5;1' keyTimes='0;0.938;1' dur='.8s' repeatCount='indefinite'/%3E%3Canimate attributeName='height' values='22;14;22' keyTimes='0;0.938;1' dur='.8s' repeatCount='indefinite'/%3E%3Canimate attributeName='opacity' values='1;0.2;1' keyTimes='0;0.938;1' dur='.8s' repeatCount='indefinite'/%3E%3C/rect%3E%3Crect x='9' y='1' width='6' height='22'%3E%3Canimate attributeName='y' values='1;5;1' keyTimes='0;0.938;1' dur='.8s' repeatCount='indefinite' begin='-0.65s'/%3E%3Canimate attributeName='height' values='22;14;22' keyTimes='0;0.938;1' dur='.8s' repeatCount='indefinite' begin='-0.65s'/%3E%3Canimate attributeName='opacity' values='1;0.2;1' keyTimes='0;0.938;1' dur='.8s' repeatCount='indefinite' begin='-0.65s'/%3E%3C/rect%3E%3Crect x='17' y='1' width='6' height='22'%3E%3Canimate attributeName='y' values='1;5;1' keyTimes='0;0.938;1' dur='.8s' repeatCount='indefinite' begin='-0.5s'/%3E%3Canimate attributeName='height' values='22;14;22' keyTimes='0;0.938;1' dur='.8s' repeatCount='indefinite' begin='-0.5s'/%3E%3Canimate attributeName='opacity' values='1;0.2;1' keyTimes='0;0.938;1' dur='.8s' repeatCount='indefinite' begin='-0.5s'/%3E%3C/rect%3E%3C/svg%3E");
  }
  .loading-dots {
    mask-image: url("data:image/svg+xml,%3Csvg width='24' height='24' viewBox='0 0 24 24' xmlns='http://www.w3.org/2000/svg'%3E%3Ccircle cx='4' cy='12' r='3'%3E%3Canimate attributeName='cy' values='12;6;12;12' keyTimes='0;0.286;0.571;1' dur='1.05s' repeatCount='indefinite' keySplines='.33,0,.66,.33;.33,.66,.66,1'/%3E%3C/circle%3E%3Ccircle cx='12' cy='12' r='3'%3E%3Canimate attributeName='cy' values='12;6;12;12' keyTimes='0;0.286;0.571;1' dur='1.05s' repeatCount='indefinite' keySplines='.33,0,.66,.33;.33,.66,.66,1' begin='0.1s'/%3E%3C/circle%3E%3Ccircle cx='20' cy='12' r='3'%3E%3Canimate attributeName='cy' values='12;6;12;12' keyTimes='0;0.286;0.571;1' dur='1.05s' repeatCount='indefinite' keySplines='.33,0,.66,.33;.33,.66,.66,1' begin='0.2s'/%3E%3C/circle%3E%3C/svg%3E");
  }
  .loading-infinity {
    mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink' style='shape-rendering:auto;' width='200px' height='200px' viewBox='0 0 100 100' preserveAspectRatio='xMidYMid'%3E%3Cpath fill='none' stroke='black' stroke-width='10' stroke-dasharray='205.271 51.318' d='M24.3 30C11.4 30 5 43.3 5 50s6.4 20 19.3 20c19.3 0 32.1-40 51.4-40C88.6 30 95 43.3 95 50s-6.4 20-19.3 20C56.4 70 43.6 30 24.3 30z' stroke-linecap='round' style='transform:scale(0.8);transform-origin:50px 50px'%3E%3Canimate attributeName='stroke-dashoffset' repeatCount='indefinite' dur='2s' keyTimes='0;1' values='0;256.589'/%3E%3C/path%3E%3C/svg%3E");
  }
  .loading-ring {
    mask-image: url("data:image/svg+xml,%3Csvg width='44' height='44' viewBox='0 0 44 44' xmlns='http://www.w3.org/2000/svg' stroke='white'%3E%3Cg fill='none' fill-rule='evenodd' stroke-width='2'%3E%3Ccircle cx='22' cy='22' r='1'%3E%3Canimate attributeName='r' begin='0s' dur='1.8s' values='1;20' calcMode='spline' keyTimes='0;1' keySplines='0.165,0.84,0.44,1' repeatCount='indefinite'/%3E%3Canimate attributeName='stroke-opacity' begin='0s' dur='1.8s' values='1;0' calcMode='spline' keyTimes='0;1' keySplines='0.3,0.61,0.355,1' repeatCount='indefinite'/%3E%3C/circle%3E%3Ccircle cx='22' cy='22' r='1'%3E%3Canimate attributeName='r' begin='-0.9s' dur='1.8s' values='1;20' calcMode='spline' keyTimes='0;1' keySplines='0.165,0.84,0.44,1' repeatCount='indefinite'/%3E%3Canimate attributeName='stroke-opacity' begin='-0.9s' dur='1.8s' values='1;0' calcMode='spline' keyTimes='0;1' keySplines='0.3,0.61,0.355,1' repeatCount='indefinite'/%3E%3C/circle%3E%3C/g%3E%3C/svg%3E");
  }
  .loading-spinner {
    mask-image: url("data:image/svg+xml,%3Csvg width='24' height='24' stroke='black' viewBox='0 0 24 24' xmlns='http://www.w3.org/2000/svg'%3E%3Cg transform-origin='center'%3E%3Ccircle cx='12' cy='12' r='9.5' fill='none' stroke-width='3' stroke-linecap='round'%3E%3CanimateTransform attributeName='transform' type='rotate' from='0 12 12' to='360 12 12' dur='2s' repeatCount='indefinite'/%3E%3Canimate attributeName='stroke-dasharray' values='0,150;42,150;42,150' keyTimes='0;0.475;1' dur='1.5s' repeatCount='indefinite'/%3E%3Canimate attributeName='stroke-dashoffset' values='0;-16;-59' keyTimes='0;0.475;1' dur='1.5s' repeatCount='indefinite'/%3E%3C/circle%3E%3C/g%3E%3C/svg%3E");
  }
  .mask-circle {
    mask-image: url("data:image/svg+xml,%3csvg width='200' height='200' xmlns='http://www.w3.org/2000/svg'%3e%3ccircle fill='black' cx='100' cy='100' r='100' fill-rule='evenodd'/%3e%3c/svg%3e");
  }
  .mask-decagon {
    mask-image: url("data:image/svg+xml,%3csvg width='192' height='200' xmlns='http://www.w3.org/2000/svg'%3e%3cpath fill='black' d='m96 0 58.779 19.098 36.327 50v61.804l-36.327 50L96 200l-58.779-19.098-36.327-50V69.098l36.327-50z' fill-rule='evenodd'/%3e%3c/svg%3e");
  }
  .mask-diamond {
    mask-image: url("data:image/svg+xml,%3csvg width='200' height='200' xmlns='http://www.w3.org/2000/svg'%3e%3cpath fill='black' d='m100 0 100 100-100 100L0 100z' fill-rule='evenodd'/%3e%3c/svg%3e");
  }
  .mask-heart {
    mask-image: url("data:image/svg+xml,%3csvg width='200' height='185' xmlns='http://www.w3.org/2000/svg'%3e%3cpath d='M100 184.606a15.384 15.384 0 0 1-8.653-2.678C53.565 156.28 37.205 138.695 28.182 127.7 8.952 104.264-.254 80.202.005 54.146.308 24.287 24.264 0 53.406 0c21.192 0 35.869 11.937 44.416 21.879a2.884 2.884 0 0 0 4.356 0C110.725 11.927 125.402 0 146.594 0c29.142 0 53.098 24.287 53.4 54.151.26 26.061-8.956 50.122-28.176 73.554-9.023 10.994-25.383 28.58-63.165 54.228a15.384 15.384 0 0 1-8.653 2.673Z' fill='black' fill-rule='nonzero'/%3e%3c/svg%3e");
  }
  .mask-hexagon {
    mask-image: url("data:image/svg+xml,%3csvg width='182' height='201' xmlns='http://www.w3.org/2000/svg'%3e%3cpath d='M.3 65.486c0-9.196 6.687-20.063 14.211-25.078l61.86-35.946c8.36-5.016 20.899-5.016 29.258 0l61.86 35.946c8.36 5.015 14.211 15.882 14.211 25.078v71.055c0 9.196-6.687 20.063-14.211 25.079l-61.86 35.945c-8.36 4.18-20.899 4.18-29.258 0L14.51 161.62C6.151 157.44.3 145.737.3 136.54V65.486Z' fill='black' fill-rule='nonzero'/%3e%3c/svg%3e");
  }
  .mask-hexagon-2 {
    mask-image: url("data:image/svg+xml,%3csvg width='200' height='182' xmlns='http://www.w3.org/2000/svg'%3e%3cpath d='M64.786 181.4c-9.196 0-20.063-6.687-25.079-14.21L3.762 105.33c-5.016-8.36-5.016-20.9 0-29.259l35.945-61.86C44.723 5.851 55.59 0 64.786 0h71.055c9.196 0 20.063 6.688 25.079 14.211l35.945 61.86c4.18 8.36 4.18 20.899 0 29.258l-35.945 61.86c-4.18 8.36-15.883 14.211-25.079 14.211H64.786Z' fill='black' fill-rule='nonzero'/%3e%3c/svg%3e");
  }
  .mask-pentagon {
    mask-image: url("data:image/svg+xml,%3csvg width='192' height='181' xmlns='http://www.w3.org/2000/svg'%3e%3cpath fill='black' d='m96 0 95.106 69.098-36.327 111.804H37.22L.894 69.098z' fill-rule='evenodd'/%3e%3c/svg%3e");
  }
  .mask-squircle {
    mask-image: url("data:image/svg+xml,%3csvg width='200' height='200' xmlns='http://www.w3.org/2000/svg'%3e%3cpath d='M100 0C20 0 0 20 0 100s20 100 100 100 100-20 100-100S180 0 100 0Z'/%3e%3c/svg%3e");
  }
  .mask-star {
    mask-image: url("data:image/svg+xml,%3csvg width='192' height='180' xmlns='http://www.w3.org/2000/svg'%3e%3cpath fill='black' d='m96 137.263-58.779 42.024 22.163-68.389L.894 68.481l72.476-.243L96 0l22.63 68.238 72.476.243-58.49 42.417 22.163 68.389z' fill-rule='evenodd'/%3e%3c/svg%3e");
  }
  .mask-star-2 {
    mask-image: url("data:image/svg+xml,%3csvg width='192' height='180' xmlns='http://www.w3.org/2000/svg'%3e%3cpath fill='black' d='m96 153.044-58.779 26.243 7.02-63.513L.894 68.481l63.117-13.01L96 0l31.989 55.472 63.117 13.01-43.347 47.292 7.02 63.513z' fill-rule='evenodd'/%3e%3c/svg%3e");
  }
  .mask-triangle {
    mask-image: url("data:image/svg+xml,%3csvg width='174' height='149' xmlns='http://www.w3.org/2000/svg'%3e%3cpath fill='black' d='m87 148.476-86.603.185L43.86 74.423 87 0l43.14 74.423 43.463 74.238z' fill-rule='evenodd'/%3e%3c/svg%3e");
  }
  .mask-triangle-2 {
    mask-image: url("data:image/svg+xml,%3csvg width='174' height='150' xmlns='http://www.w3.org/2000/svg'%3e%3cpath fill='black' d='m87 .738 86.603-.184-43.463 74.238L87 149.214 43.86 74.792.397.554z' fill-rule='evenodd'/%3e%3c/svg%3e");
  }
  .mask-triangle-3 {
    mask-image: url("data:image/svg+xml,%3csvg width='150' height='174' xmlns='http://www.w3.org/2000/svg'%3e%3cpath fill='black' d='m149.369 87.107.185 86.603-74.239-43.463L.893 87.107l74.422-43.14L149.554.505z' fill-rule='evenodd'/%3e%3c/svg%3e");
  }
  .mask-triangle-4 {
    mask-image: url("data:image/svg+xml,%3csvg width='150' height='174' xmlns='http://www.w3.org/2000/svg'%3e%3cpath fill='black' d='M.631 87.107.446.505l74.239 43.462 74.422 43.14-74.422 43.14L.446 173.71z' fill-rule='evenodd'/%3e%3c/svg%3e");
  }
  .perspective-none {
    perspective: none;
  }
  .perspective-origin-bottom {
    perspective-origin: bottom;
  }
  .perspective-origin-bottom-left {
    perspective-origin: bottom left;
  }
  .perspective-origin-bottom-right {
    perspective-origin: bottom right;
  }
  .perspective-origin-center {
    perspective-origin: center;
  }
  .perspective-origin-left {
    perspective-origin: left;
  }
  .perspective-origin-right {
    perspective-origin: right;
  }
  .perspective-origin-top {
    perspective-origin: top;
  }
  .perspective-origin-top-left {
    perspective-origin: top left;
  }
  .perspective-origin-top-right {
    perspective-origin: top right;
  }
  .radio-accent {
    --input-color: var(--color-accent);
  }
  .radio-error {
    --input-color: var(--color-error);
  }
  .radio-info {
    --input-color: var(--color-info);
  }
  .radio-neutral {
    --input-color: var(--color-neutral);
  }
  .radio-primary {
    --input-color: var(--color-primary);
  }
  .radio-secondary {
    --input-color: var(--color-secondary);
  }
  .radio-success {
    --input-color: var(--color-success);
  }
  .radio-warning {
    --input-color: var(--color-warning);
  }
  .range-lg {
    --range-thumb-size: calc(var(--size-selector, 0.25rem) * 7);
  }
  .range-md {
    --range-thumb-size: calc(var(--size-selector, 0.25rem) * 6);
  }
  .range-sm {
    --range-thumb-size: calc(var(--size-selector, 0.25rem) * 5);
  }
  .range-xl {
    --range-thumb-size: calc(var(--size-selector, 0.25rem) * 8);
  }
  .range-xs {
    --range-thumb-size: calc(var(--size-selector, 0.25rem) * 4);
  }
  .ring-inset {
    --tw-ring-inset: inset;
  }
  .select-accent {
    &, &:focus, &:focus-within {
      --input-color: var(--color-accent);
    }
  }
  .select-error {
    &, &:focus, &:focus-within {
      --input-color: var(--color-error);
    }
  }
  .select-info {
    &, &:focus, &:focus-within {
      --input-color: var(--color-info);
    }
  }
  .select-neutral {
    &, &:focus, &:focus-within {
      --input-color: var(--color-neutral);
    }
  }
  .select-primary {
    &, &:focus, &:focus-within {
      --input-color: var(--color-primary);
    }
  }
  .select-secondary {
    &, &:focus, &:focus-within {
      --input-color: var(--color-secondary);
    }
  }
  .select-success {
    &, &:focus, &:focus-within {
      --input-color: var(--color-success);
    }
  }
  .select-warning {
    &, &:focus, &:focus-within {
      --input-color: var(--color-warning);
    }
  }
  .textarea-accent {
    &, &:focus, &:focus-within {
      --input-color: var(--color-accent);
    }
  }
  .textarea-error {
    &, &:focus, &:focus-within {
      --input-color: var(--color-error);
    }
  }
  .textarea-info {
    &, &:focus, &:focus-within {
      --input-color: var(--color-info);
    }
  }
  .textarea-neutral {
    &, &:focus, &:focus-within {
      --input-color: var(--color-neutral);
    }
  }
  .textarea-primary {
    &, &:focus, &:focus-within {
      --input-color: var(--color-primary);
    }
  }
  .textarea-secondary {
    &, &:focus, &:focus-within {
      --input-color: var(--color-secondary);
    }
  }
  .textarea-success {
    &, &:focus, &:focus-within {
      --input-color: var(--color-success);
    }
  }
  .textarea-warning {
    &, &:focus, &:focus-within {
      --input-color: var(--color-warning);
    }
  }
  .toggle-accent {
    &:checked, &[aria-checked="true"] {
      --input-color: var(--color-accent);
    }
  }
  .toggle-error {
    &:checked, &[aria-checked="true"] {
      --input-color: var(--color-error);
    }
  }
  .toggle-info {
    &:checked, &[aria-checked="true"] {
      --input-color: var(--color-info);
    }
  }
  .toggle-lg {
    &:is([type="checkbox"]), &:has([type="checkbox"]) {
      --size: calc(var(--size-selector, 0.25rem) * 7);
    }
  }
  .toggle-md {
    &:is([type="checkbox"]), &:has([type="checkbox"]) {
      --size: calc(var(--size-selector, 0.25rem) * 6);
    }
  }
  .toggle-neutral {
    &:checked, &[aria-checked="true"] {
      --input-color: var(--color-neutral);
    }
  }
  .toggle-primary {
    &:checked, &[aria-checked="true"] {
      --input-color: var(--color-primary);
    }
  }
  .toggle-secondary {
    &:checked, &[aria-checked="true"] {
      --input-color: var(--color-secondary);
    }
  }
  .toggle-sm {
    &:is([type="checkbox"]), &:has([type="checkbox"]) {
      --size: calc(var(--size-selector, 0.25rem) * 5);
    }
  }
  .toggle-success {
    &:checked, &[aria-checked="true"] {
      --input-color: var(--color-success);
    }
  }
  .toggle-warning {
    &:checked, &[aria-checked="true"] {
      --input-color: var(--color-warning);
    }
  }
  .toggle-xl {
    &:is([type="checkbox"]), &:has([type="checkbox"]) {
      --size: calc(var(--size-selector, 0.25rem) * 8);
    }
  }
  .toggle-xs {
    &:is([type="checkbox"]), &:has([type="checkbox"]) {
      --size: calc(var(--size-selector, 0.25rem) * 4);
    }
  }
  .transform-3d {
    transform-style: preserve-3d;
  }
  .transform-border {
    transform-box: border-box;
  }
  .transform-content {
    transform-box: content-box;
  }
  .transform-fill {
    transform-box: fill-box;
  }
  .transform-flat {
    transform-style: flat;
  }
  .transform-stroke {
    transform-box: stroke-box;
  }
  .transform-view {
    transform-box: view-box;
  }
  .group-hover\:bg-white {
    &:is(:where(.group):hover *) {
      @media (hover: hover) {
        background-color: var(--color-white);
      }
    }
  }
  .hover\:bg-gray-50 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-gray-50);
      }
    }
  }
  .hover\:bg-gray-100 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-gray-100);
      }
    }
  }
  .hover\:bg-gray-200 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-gray-200);
      }
    }
  }
  .hover\:text-gray-900 {
    &:hover {
      @media (hover: hover) {
        color: var(--color-gray-900);
      }
    }
  }
  .hover\:shadow-lg {
    &:hover {
      @media (hover: hover) {
        --tw-shadow: 0 10px 15px -3px var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 4px 6px -4px var(--tw-shadow-color, rgb(0 0 0 / 0.1));
        box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
      }
    }
  }
  .focus\:outline-none {
    &:focus {
      --tw-outline-style: none;
      outline-style: none;
    }
  }
  .sm\:min-h-0 {
    @media (width >= 40rem) {
      min-height: calc(var(--spacing) * 0);
    }
  }
  .sm\:w-28 {
    @media (width >= 40rem) {
      width: calc(var(--spacing) * 28);
    }
  }
  .sm\:grid-cols-2 {
    @media (width >= 40rem) {
      grid-template-columns: repeat(2, minmax(0, 1fr));
    }
  }
  .md\:mb-0 {
    @media (width >= 48rem) {
      margin-bottom: calc(var(--spacing) * 0);
    }
  }
  .md\:ml-16 {
    @media (width >= 48rem) {
      margin-left: calc(var(--spacing) * 16);
    }
  }
  .md\:block {
    @media (width >= 48rem) {
      display: block;
    }
  }
  .md\:contents {
    @media (width >= 48rem) {
      display: contents;
    }
  }
  .md\:flex {
    @media (width >= 48rem) {
      display: flex;
    }
  }
  .md\:hidden {
    @media (width >= 48rem) {
      display: none;
    }
  }
  .md\:h-auto {
    @media (width >= 48rem) {
      height: auto;
    }
  }
  .md\:w-\[552px\] {
    @media (width >= 48rem) {
      width: 552px;
    }
  }
  .md\:w-auto {
    @media (width >= 48rem) {
      width: auto;
    }
  }
  .md\:grid-cols-4 {
    @media (width >= 48rem) {
      grid-template-columns: repeat(4, minmax(0, 1fr));
    }
  }
  .md\:flex-row {
    @media (width >= 48rem) {
      flex-direction: row;
    }
  }
  .md\:justify-start {
    @media (width >= 48rem) {
      justify-content: flex-start;
    }
  }
  .md\:border-0 {
    @media (width >= 48rem) {
      border-style: var(--tw-border-style);
      border-width: 0px;
    }
  }
  .md\:bg-transparent {
    @media (width >= 48rem) {
      background-color: transparent;
    }
  }
  .md\:p-4 {
    @media (width >= 48rem) {
      padding: calc(var(--spacing) * 4);
    }
  }
  .md\:p-8 {
    @media (width >= 48rem) {
      padding: calc(var(--spacing) * 8);
    }
  }
  .md\:text-left {
    @media (width >= 48rem) {
      text-align: left;
    }
  }
  .md\:text-3xl {
    @media (width >= 48rem) {
      font-size: var(--text-3xl);
      line-height: var(--tw-leading, var(--text-3xl--line-height));
    }
  }
  .md\:text-base {
    @media (width >= 48rem) {
      font-size: var(--text-base);
      line-height: var(--tw-leading, var(--text-base--line-height));
    }
  }
  .lg\:order-1 {
    @media (width >= 64rem) {
      order: 1;
    }
  }
  .lg\:order-2 {
    @media (width >= 64rem) {
      order: 2;
    }
  }
  .lg\:order-first {
    @media (width >= 64rem) {
      order: -9999;
    }
  }
  .lg\:order-last {
    @media (width >= 64rem) {
      order: 9999;
    }
  }
  .lg\:mt-0 {
    @media (width >= 64rem) {
      margin-top: calc(var(--spacing) * 0);
    }
  }
  .lg\:mt-8 {
    @media (width >= 64rem) {
      margin-top: calc(var(--spacing) * 8);
    }
  }
  .lg\:mb-32 {
    @media (width >= 64rem) {
      margin-bottom: calc(var(--spacing) * 32);
    }
  }
  .lg\:block {
    @media (width >= 64rem) {
      display: block;
    }
  }
  .lg\:hidden {
    @media (width >= 64rem) {
      display: none;
    }
  }
  .lg\:h-\[444px\] {
    @media (width >= 64rem) {
      height: 444px;
    }
  }
  .lg\:h-\[456px\] {
    @media (width >= 64rem) {
      height: 456px;
    }
  }
  .lg\:min-h-full {
    @media (width >= 64rem) {
      min-height: 100%;
    }
  }
  .lg\:w-1\/2 {
    @media (width >= 64rem) {
      width: calc(1/2 * 100%);
    }
  }
  .lg\:w-1\/3 {
    @media (width >= 64rem) {
      width: calc(1/3 * 100%);
    }
  }
  .lg\:w-1\/4 {
    @media (width >= 64rem) {
      width: calc(1/4 * 100%);
    }
  }
  .lg\:w-4\/5 {
    @media (width >= 64rem) {
      width: calc(4/5 * 100%);
    }
  }
  .lg\:w-auto {
    @media (width >= 64rem) {
      width: auto;
    }
  }
  .lg\:max-w-1\/2 {
    @media (width >= 64rem) {
      max-width: calc(1/2 * 100%);
    }
  }
  .lg\:max-w-5\/12 {
    @media (width >= 64rem) {
      max-width: calc(5/12 * 100%);
    }
  }
  .lg\:max-w-\[392px\] {
    @media (width >= 64rem) {
      max-width: 392px;
    }
  }
  .lg\:max-w-\[474px\] {
    @media (width >= 64rem) {
      max-width: 474px;
    }
  }
  .lg\:max-w-\[500px\] {
    @media (width >= 64rem) {
      max-width: 500px;
    }
  }
  .lg\:max-w-\[530px\] {
    @media (width >= 64rem) {
      max-width: 530px;
    }
  }
  .lg\:min-w-\[350px\] {
    @media (width >= 64rem) {
      min-width: 350px;
    }
  }
  .lg\:grid-cols-2 {
    @media (width >= 64rem) {
      grid-template-columns: repeat(2, minmax(0, 1fr));
    }
  }
  .lg\:grid-cols-3 {
    @media (width >= 64rem) {
      grid-template-columns: repeat(3, minmax(0, 1fr));
    }
  }
  .lg\:flex-row {
    @media (width >= 64rem) {
      flex-direction: row;
    }
  }
  .lg\:justify-center {
    @media (width >= 64rem) {
      justify-content: center;
    }
  }
  .lg\:justify-start {
    @media (width >= 64rem) {
      justify-content: flex-start;
    }
  }
  .lg\:gap-4 {
    @media (width >= 64rem) {
      gap: calc(var(--spacing) * 4);
    }
  }
  .lg\:gap-8 {
    @media (width >= 64rem) {
      gap: calc(var(--spacing) * 8);
    }
  }
  .lg\:gap-11 {
    @media (width >= 64rem) {
      gap: calc(var(--spacing) * 11);
    }
  }
  .lg\:gap-16 {
    @media (width >= 64rem) {
      gap: calc(var(--spacing) * 16);
    }
  }
  .lg\:gap-32 {
    @media (width >= 64rem) {
      gap: calc(var(--spacing) * 32);
    }
  }
  .lg\:gap-44 {
    @media (width >= 64rem) {
      gap: calc(var(--spacing) * 44);
    }
  }
  .lg\:px-6 {
    @media (width >= 64rem) {
      padding-inline: calc(var(--spacing) * 6);
    }
  }
  .lg\:px-8 {
    @media (width >= 64rem) {
      padding-inline: calc(var(--spacing) * 8);
    }
  }
  .lg\:px-16 {
    @media (width >= 64rem) {
      padding-inline: calc(var(--spacing) * 16);
    }
  }
  .lg\:py-8 {
    @media (width >= 64rem) {
      padding-block: calc(var(--spacing) * 8);
    }
  }
  .lg\:text-left {
    @media (width >= 64rem) {
      text-align: left;
    }
  }
  .lg\:text-3xl {
    @media (width >= 64rem) {
      font-size: var(--text-3xl);
      line-height: var(--tw-leading, var(--text-3xl--line-height));
    }
  }
  .lg\:text-5xl {
    @media (width >= 64rem) {
      font-size: var(--text-5xl);
      line-height: var(--tw-leading, var(--text-5xl--line-height));
    }
  }
  .lg\:text-7xl {
    @media (width >= 64rem) {
      font-size: var(--text-7xl);
      line-height: var(--tw-leading, var(--text-7xl--line-height));
    }
  }
  .lg\:text-base {
    @media (width >= 64rem) {
      font-size: var(--text-base);
      line-height: var(--tw-leading, var(--text-base--line-height));
    }
  }
  .lg\:text-lg {
    @media (width >= 64rem) {
      font-size: var(--text-lg);
      line-height: var(--tw-leading, var(--text-lg--line-height));
    }
  }
  .lg\:text-xl {
    @media (width >= 64rem) {
      font-size: var(--text-xl);
      line-height: var(--tw-leading, var(--text-xl--line-height));
    }
  }
  .\[\&_\*\]\:border-gray-300 {
    & * {
      border-color: var(--color-gray-300);
    }
  }
}
@layer base {
  :root {
    --rmred: #ea1a1a;
    --rmred-light: #fca5a5;
    --rmred-dark: #b91c1c;
    --rmgray: #323336;
    --rmgray-light: #525359;
  }
  * {
    font-family: "Segoe UI", sans-serif;
  }
  .text-rmred {
    color: var(--rmred);
  }
  .text-rmred-light {
    color: var(--rmred-light);
  }
  .text-rmred-dark {
    color: var(--rmred-dark);
  }
  .bg-rmred {
    background-color: var(--rmred);
  }
  .bg-rmred-light {
    background-color: var(--rmred-light);
  }
  .bg-rmred-dark {
    background-color: var(--rmred-dark);
  }
  .bg-rmgray {
    background-color: var(--rmgray);
  }
  .bg-rmgray-light {
    background-color: var(--rmgray-light);
  }
  .hover\:bg-rmred:hover {
    background-color: var(--rmred);
  }
  .hover\:bg-rmred-light:hover {
    background-color: var(--rmred-light);
  }
  .hover\:bg-rmred-dark:hover {
    background-color: var(--rmred-dark);
  }
  .hover\:bg-rmgray-light:hover {
    background-color: var(--rmgray-light);
  }
  .border-rmred {
    border-color: var(--rmred);
  }
  .border-rmred-light {
    border-color: var(--rmred-light);
  }
  .border-rmred-dark {
    border-color: var(--rmred-dark);
  }
  #blog-content p, #blog-content li {
    margin-bottom: calc(var(--spacing) * 4);
    font-size: var(--text-lg);
    line-height: var(--tw-leading, var(--text-lg--line-height));
    color: var(--color-slate-800);
  }
  #blog-content h2 {
    font-size: var(--text-2xl);
    line-height: var(--tw-leading, var(--text-2xl--line-height));
    --tw-font-weight: var(--font-weight-bold);
    font-weight: var(--font-weight-bold);
    @media (width >= 48rem) {
      font-size: var(--text-2xl);
      line-height: var(--tw-leading, var(--text-2xl--line-height));
    }
  }
}
@layer base {
  :where(:root),:root:has(input.theme-controller[value=light]:checked),[data-theme=light] {
    color-scheme: light;
    --color-base-100: oklch(100% 0 0);
    --color-base-200: oklch(98% 0 0);
    --color-base-300: oklch(95% 0 0);
    --color-base-content: oklch(21% 0.006 285.885);
    --color-primary: oklch(45% 0.24 277.023);
    --color-primary-content: oklch(93% 0.034 272.788);
    --color-secondary: oklch(65% 0.241 354.308);
    --color-secondary-content: oklch(94% 0.028 342.258);
    --color-accent: oklch(77% 0.152 181.912);
    --color-accent-content: oklch(38% 0.063 188.416);
    --color-neutral: oklch(14% 0.005 285.823);
    --color-neutral-content: oklch(92% 0.004 286.32);
    --color-info: oklch(74% 0.16 232.661);
    --color-info-content: oklch(29% 0.066 243.157);
    --color-success: oklch(76% 0.177 163.223);
    --color-success-content: oklch(37% 0.077 168.94);
    --color-warning: oklch(82% 0.189 84.429);
    --color-warning-content: oklch(41% 0.112 45.904);
    --color-error: oklch(71% 0.194 13.428);
    --color-error-content: oklch(27% 0.105 12.094);
    --radius-selector: 0.5rem;
    --radius-field: 0.25rem;
    --radius-box: 0.5rem;
    --size-selector: 0.25rem;
    --size-field: 0.25rem;
    --border: 1px;
    --depth: 1;
    --noise: 0;
  }
}
@layer base {
  @media (prefers-color-scheme: dark) {
    :root {
      color-scheme: dark;
      --color-base-100: oklch(25.33% 0.016 252.42);
      --color-base-200: oklch(23.26% 0.014 253.1);
      --color-base-300: oklch(21.15% 0.012 254.09);
      --color-base-content: oklch(97.807% 0.029 256.847);
      --color-primary: oklch(58% 0.233 277.117);
      --color-primary-content: oklch(96% 0.018 272.314);
      --color-secondary: oklch(65% 0.241 354.308);
      --color-secondary-content: oklch(94% 0.028 342.258);
      --color-accent: oklch(77% 0.152 181.912);
      --color-accent-content: oklch(38% 0.063 188.416);
      --color-neutral: oklch(14% 0.005 285.823);
      --color-neutral-content: oklch(92% 0.004 286.32);
      --color-info: oklch(74% 0.16 232.661);
      --color-info-content: oklch(29% 0.066 243.157);
      --color-success: oklch(76% 0.177 163.223);
      --color-success-content: oklch(37% 0.077 168.94);
      --color-warning: oklch(82% 0.189 84.429);
      --color-warning-content: oklch(41% 0.112 45.904);
      --color-error: oklch(71% 0.194 13.428);
      --color-error-content: oklch(27% 0.105 12.094);
      --radius-selector: 0.5rem;
      --radius-field: 0.25rem;
      --radius-box: 0.5rem;
      --size-selector: 0.25rem;
      --size-field: 0.25rem;
      --border: 1px;
      --depth: 1;
      --noise: 0;
    }
  }
}
@layer base {
  :root:has(input.theme-controller[value=light]:checked),[data-theme=light] {
    color-scheme: light;
    --color-base-100: oklch(100% 0 0);
    --color-base-200: oklch(98% 0 0);
    --color-base-300: oklch(95% 0 0);
    --color-base-content: oklch(21% 0.006 285.885);
    --color-primary: oklch(45% 0.24 277.023);
    --color-primary-content: oklch(93% 0.034 272.788);
    --color-secondary: oklch(65% 0.241 354.308);
    --color-secondary-content: oklch(94% 0.028 342.258);
    --color-accent: oklch(77% 0.152 181.912);
    --color-accent-content: oklch(38% 0.063 188.416);
    --color-neutral: oklch(14% 0.005 285.823);
    --color-neutral-content: oklch(92% 0.004 286.32);
    --color-info: oklch(74% 0.16 232.661);
    --color-info-content: oklch(29% 0.066 243.157);
    --color-success: oklch(76% 0.177 163.223);
    --color-success-content: oklch(37% 0.077 168.94);
    --color-warning: oklch(82% 0.189 84.429);
    --color-warning-content: oklch(41% 0.112 45.904);
    --color-error: oklch(71% 0.194 13.428);
    --color-error-content: oklch(27% 0.105 12.094);
    --radius-selector: 0.5rem;
    --radius-field: 0.25rem;
    --radius-box: 0.5rem;
    --size-selector: 0.25rem;
    --size-field: 0.25rem;
    --border: 1px;
    --depth: 1;
    --noise: 0;
  }
}
@layer base {
  :root:has(input.theme-controller[value=dark]:checked),[data-theme=dark] {
    color-scheme: dark;
    --color-base-100: oklch(25.33% 0.016 252.42);
    --color-base-200: oklch(23.26% 0.014 253.1);
    --color-base-300: oklch(21.15% 0.012 254.09);
    --color-base-content: oklch(97.807% 0.029 256.847);
    --color-primary: oklch(58% 0.233 277.117);
    --color-primary-content: oklch(96% 0.018 272.314);
    --color-secondary: oklch(65% 0.241 354.308);
    --color-secondary-content: oklch(94% 0.028 342.258);
    --color-accent: oklch(77% 0.152 181.912);
    --color-accent-content: oklch(38% 0.063 188.416);
    --color-neutral: oklch(14% 0.005 285.823);
    --color-neutral-content: oklch(92% 0.004 286.32);
    --color-info: oklch(74% 0.16 232.661);
    --color-info-content: oklch(29% 0.066 243.157);
    --color-success: oklch(76% 0.177 163.223);
    --color-success-content: oklch(37% 0.077 168.94);
    --color-warning: oklch(82% 0.189 84.429);
    --color-warning-content: oklch(41% 0.112 45.904);
    --color-error: oklch(71% 0.194 13.428);
    --color-error-content: oklch(27% 0.105 12.094);
    --radius-selector: 0.5rem;
    --radius-field: 0.25rem;
    --radius-box: 0.5rem;
    --size-selector: 0.25rem;
    --size-field: 0.25rem;
    --border: 1px;
    --depth: 1;
    --noise: 0;
  }
}
@layer base {
  @property --radialprogress {
    syntax: "<percentage>";
    inherits: true;
    initial-value: 0%;
  }
}
@layer base {
  :root {
    scrollbar-color: color-mix(in oklch, currentColor 35%, #0000) #0000;
  }
}
@layer base {
  :root {
    --fx-noise: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg'%3E%3Cfilter id='a'%3E%3CfeTurbulence type='fractalNoise' baseFrequency='1.34' numOctaves='4' stitchTiles='stitch'%3E%3C/feTurbulence%3E%3C/filter%3E%3Crect width='100%25' height='100%25' filter='url(%23a)' opacity='0.2'%3E%3C/rect%3E%3C/svg%3E");
  }
}
@layer base {
  :root:has( .modal-open, .modal[open], .modal:target, .modal-toggle:checked, .drawer:not([class*="drawer-open"]) > .drawer-toggle:checked ) {
    overflow: hidden;
  }
}
@layer base {
  :where( :root:has( .modal-open, .modal[open], .modal:target, .modal-toggle:checked, .drawer:not(.drawer-open) > .drawer-toggle:checked ) ) {
    scrollbar-gutter: stable;
    background-image: linear-gradient(var(--color-base-100), var(--color-base-100));
    --root-bg: color-mix(in srgb, var(--color-base-100), oklch(0% 0 0) 40%);
  }
  :where(.modal[open], .modal-open, .modal-toggle:checked + .modal):not(.modal-start, .modal-end) {
    scrollbar-gutter: stable;
  }
}
@layer base {
  :root, [data-theme] {
    background-color: var(--root-bg, var(--color-base-100));
    color: var(--color-base-content);
  }
}
@keyframes radio {
  0% {
    padding: 5px;
  }
  50% {
    padding: 3px;
  }
}
@keyframes skeleton {
  0% {
    background-position: 150%;
  }
  100% {
    background-position: -50%;
  }
}
@keyframes progress {
  50% {
    background-position-x: -115%;
  }
}
@keyframes toast {
  0% {
    scale: 0.9;
    opacity: 0;
  }
  100% {
    scale: 1;
    opacity: 1;
  }
}
@keyframes dropdown {
  0% {
    opacity: 0;
  }
}
@keyframes rating {
  0%, 40% {
    scale: 1.1;
    filter: brightness(1.05) contrast(1.05);
  }
}
@property --tw-translate-x {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}
@property --tw-translate-y {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}
@property --tw-translate-z {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}
@property --tw-scale-x {
  syntax: "*";
  inherits: false;
  initial-value: 1;
}
@property --tw-scale-y {
  syntax: "*";
  inherits: false;
  initial-value: 1;
}
@property --tw-scale-z {
  syntax: "*";
  inherits: false;
  initial-value: 1;
}
@property --tw-rotate-x {
  syntax: "*";
  inherits: false;
  initial-value: rotateX(0);
}
@property --tw-rotate-y {
  syntax: "*";
  inherits: false;
  initial-value: rotateY(0);
}
@property --tw-rotate-z {
  syntax: "*";
  inherits: false;
  initial-value: rotateZ(0);
}
@property --tw-skew-x {
  syntax: "*";
  inherits: false;
  initial-value: skewX(0);
}
@property --tw-skew-y {
  syntax: "*";
  inherits: false;
  initial-value: skewY(0);
}
@property --tw-pan-x {
  syntax: "*";
  inherits: false;
}
@property --tw-pan-y {
  syntax: "*";
  inherits: false;
}
@property --tw-pinch-zoom {
  syntax: "*";
  inherits: false;
}
@property --tw-scroll-snap-strictness {
  syntax: "*";
  inherits: false;
  initial-value: proximity;
}
@property --tw-space-y-reverse {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}
@property --tw-space-x-reverse {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}
@property --tw-divide-x-reverse {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}
@property --tw-border-style {
  syntax: "*";
  inherits: false;
  initial-value: solid;
}
@property --tw-divide-y-reverse {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}
@property --tw-leading {
  syntax: "*";
  inherits: false;
}
@property --tw-font-weight {
  syntax: "*";
  inherits: false;
}
@property --tw-tracking {
  syntax: "*";
  inherits: false;
}
@property --tw-ordinal {
  syntax: "*";
  inherits: false;
}
@property --tw-slashed-zero {
  syntax: "*";
  inherits: false;
}
@property --tw-numeric-figure {
  syntax: "*";
  inherits: false;
}
@property --tw-numeric-spacing {
  syntax: "*";
  inherits: false;
}
@property --tw-numeric-fraction {
  syntax: "*";
  inherits: false;
}
@property --tw-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}
@property --tw-shadow-color {
  syntax: "*";
  inherits: false;
}
@property --tw-inset-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}
@property --tw-inset-shadow-color {
  syntax: "*";
  inherits: false;
}
@property --tw-ring-color {
  syntax: "*";
  inherits: false;
}
@property --tw-ring-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}
@property --tw-inset-ring-color {
  syntax: "*";
  inherits: false;
}
@property --tw-inset-ring-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}
@property --tw-ring-inset {
  syntax: "*";
  inherits: false;
}
@property --tw-ring-offset-width {
  syntax: "<length>";
  inherits: false;
  initial-value: 0px;
}
@property --tw-ring-offset-color {
  syntax: "*";
  inherits: false;
  initial-value: #fff;
}
@property --tw-ring-offset-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}
@property --tw-outline-style {
  syntax: "*";
  inherits: false;
  initial-value: solid;
}
@property --tw-blur {
  syntax: "*";
  inherits: false;
}
@property --tw-brightness {
  syntax: "*";
  inherits: false;
}
@property --tw-contrast {
  syntax: "*";
  inherits: false;
}
@property --tw-grayscale {
  syntax: "*";
  inherits: false;
}
@property --tw-hue-rotate {
  syntax: "*";
  inherits: false;
}
@property --tw-invert {
  syntax: "*";
  inherits: false;
}
@property --tw-opacity {
  syntax: "*";
  inherits: false;
}
@property --tw-saturate {
  syntax: "*";
  inherits: false;
}
@property --tw-sepia {
  syntax: "*";
  inherits: false;
}
@property --tw-drop-shadow {
  syntax: "*";
  inherits: false;
}
@property --tw-backdrop-blur {
  syntax: "*";
  inherits: false;
}
@property --tw-backdrop-brightness {
  syntax: "*";
  inherits: false;
}
@property --tw-backdrop-contrast {
  syntax: "*";
  inherits: false;
}
@property --tw-backdrop-grayscale {
  syntax: "*";
  inherits: false;
}
@property --tw-backdrop-hue-rotate {
  syntax: "*";
  inherits: false;
}
@property --tw-backdrop-invert {
  syntax: "*";
  inherits: false;
}
@property --tw-backdrop-opacity {
  syntax: "*";
  inherits: false;
}
@property --tw-backdrop-saturate {
  syntax: "*";
  inherits: false;
}
@property --tw-backdrop-sepia {
  syntax: "*";
  inherits: false;
}
@property --tw-duration {
  syntax: "*";
  inherits: false;
}
@property --tw-ease {
  syntax: "*";
  inherits: false;
}
@property --tw-contain-size {
  syntax: "*";
  inherits: false;
}
@property --tw-contain-layout {
  syntax: "*";
  inherits: false;
}
@property --tw-contain-paint {
  syntax: "*";
  inherits: false;
}
@property --tw-contain-style {
  syntax: "*";
  inherits: false;
}
