<div class="fixed inset-0 z-50">
  <!-- Dark overlay with click handler to close -->
  <div
    class="fixed inset-0 bg-black/50 pointer-events-auto"
    hx-get=""
    hx-target="#modal"
    hx-swap="innerHTML"
    hx-trigger="click"
  ></div>

  <!-- Modal container -->
  <div
    class="fixed inset-0 flex items-center justify-center pointer-events-none"
  >
    <div
      class="bg-white sm:rounded-lg shadow-xl sm:w-9/12 w-full h-full sm:max-h-[90vh] sm:min-h-[70vh] overflow-y-auto pointer-events-auto relative flex flex-col"
    >
      <!-- Modal Header -->
      <div
        class="sticky top-0 px-6 py-4 border-b bg-white z-10 flex justify-between items-center"
      >
        <div>
          <h3 class="text-lg font-semibold text-gray-900">Write Message</h3>
        </div>
        <button
          class="p-2 hover:bg-gray-100 rounded-full"
          hx-get=""
          hx-target="#modal"
          hx-swap="innerHTML"
        >
          <svg
            class="w-5 h-5 text-gray-500"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M6 18L18 6M6 6l12 12"
            />
          </svg>
        </button>
      </div>

      <!-- Modal Body -->
      <div class="px-6 py-4 flex-1">
        <fieldset class="fieldset">
          <legend class="fieldset-legend">To</legend>
          <label class="input w-full">
            <img src="../src/assets/search.svg" class="w-4 h-4" />
            <input
              type="search"
              class="grow"
              placeholder="Find tenant names, emails, or addresses"
            />
          </label>
        </fieldset>

        <fieldset class="fieldset">
          <legend class="fieldset-legend">Message Subject</legend>
          <select class="w-full bg-white border border-gray-300 rounded-md p-2 focus:outline-none focus:ring-2 focus:ring-slate-200">
            <option disabled selected>Select a message option</option>
            <option>Payment Reminder</option>
            <option>Rent Reminder</option>
            <option>
              (Check with Fred for more, these are email templates)
            </option>
          </select>
        </fieldset>
        <fieldset class="fieldset">
          <legend class="fieldset-legend">Message Preview</legend>
          <div class="w-full bg-white border border-gray-300 p-4 rounded-md">
            this is a preview template of the email
          </div>
        </fieldset>
      </div>

      <!-- Modal Footer -->
      <div
        class="sticky bottom-0 left-0 right-0 px-6 py-4 bg-gray-50 sm:rounded-b-lg flex justify-between gap-2 border-t mt-auto"
      >
        <button
          class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50"
          hx-get=""
          hx-target="#modal"
          hx-swap="innerHTML"
        >
          Cancel
        </button>
        <button
          class="px-4 py-2 text-sm font-medium text-white bg-slate-800 rounded-md hover:bg-slate-900"
        >
          Send Message
        </button>
      </div>
    </div>
  </div>
</div> 