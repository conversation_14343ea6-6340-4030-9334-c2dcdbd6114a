{{> header }}
<body class="min-h-screen bg-gray-200">
    <div class="flex min-h-screen">
      {{> navbar}}
      <main class="w-full bg-slate-100 px-4 lg:px-8 lg:ml-64 pb-20">
        <div class="max-w-[1440px] w-full mx-auto">
            <div class="flex-1 mt-20 lg:mt-8 pb-0 w-full">
                <h1 class="text-3xl">My Account</h1>
            </div>
            <!-- Tab Container -->
            <div class="flex flex-col sm:flex-row gap-6 mt-8">
                <!-- Desktop Tab Navigation -->
                <div class="hidden sm:block w-full sm:w-1/4 bg-white rounded-lg border border-gray-200 max-h-fit">
                    <nav class="flex flex-col" aria-label="Tabs">
                        <button 
                            class="tablinks min-h-16 py-4 text-sm font-medium text-left px-6 border-l-2 border-black bg-gray-50 text-slate-800 hover:bg-gray-50 hover:border-gray-300 hover:text-gray-600 active-tab" 
                            data-tab="Account"
                        >
                            Account Settings
                        </button>
                        <button 
                            class="tablinks min-h-16 py-4 text-sm font-medium text-left px-6 border-l-2 border-black hover:bg-gray-50 hover:border-gray-300 hover:text-gray-600" 
                            data-tab="Billing"
                        >
                            Billing
                        </button>
                        <!-- 注释掉 System Admin 相关tab按钮和option -->
                        <!-- <li class="..." data-tab="SysAdmin">System Admin</li> -->
                        <button 
                            class="tablinks min-h-16 py-4 text-sm font-medium text-left px-6 border-l-2 border-black hover:bg-gray-50 hover:border-gray-300 hover:text-gray-600" 
                            data-tab="Plans"
                        >
                            Plans
                        </button>
                        <button 
                            class="tablinks min-h-16 py-4 text-sm font-medium text-left px-6 border-l-2 border-black hover:bg-gray-50 hover:border-gray-300 hover:text-gray-600" 
                            data-tab="Logout"
                        >
                            Logout
                        </button>
                    </nav>
                </div>

                <!-- Mobile Dropdown -->
                <div class="relative w-full sm:hidden mb-4">
                    <select 
                        class="w-full p-4 bg-white border border-gray-200 rounded-lg appearance-none cursor-pointer text-sm font-medium"
                        id="mobile-tab-select"
                    >
                        <option value="Account">Account Settings</option>
                        <option value="Billing">Billing</option>
                        <!-- 注释掉 System Admin 相关tab按钮和option -->
                        <!-- <option value="SysAdmin">System Admin</option> -->
                        <option value="Plans">Plans</option>
                        <option value="Logout">Logout</option>
                    </select>
                    <!-- Dropdown Arrow -->
                    <div class="absolute inset-y-0 right-0 flex items-center px-4 pointer-events-none">
                        <svg class="w-4 h-4 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"/>
                        </svg>
                    </div>
                </div>
                
                <!-- Tab content -->
                <div class="md:w-3/4 w-full">
                    <!-- Account Tab -->
                    <div id="Account" class="tabcontent hidden bg-white p-6 rounded-lg border border-gray-200 mb-16">
                        <div class="block">
                            <h2 class="text-lg mb-2">Account Information</h2>
                            <div id="name-field">
                                <!-- Account info will be rendered here -->
                                <div class="flex items-center justify-center p-8">
                                    <span>Loading account information...</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Organization Tab -->
                    <div id="Organization" class="tabcontent hidden bg-white p-6 rounded-lg border border-gray-200 mb-16">
                        <div class="block">
                            <div class="flex justify-between items-center mb-2">
                                <h2 class="text-lg">Organization</h2>
                            </div>
                            <div class="flex flex-col md:flex-row gap-2 items-start w-full justify-between px-0 md:px-2 py-8 text-sm border-b">
                                <h3 class="font-semibold w-full md:w-1/4">Account Type</h3>
                                <div class="w-full md:w-1/2 flex gap-2"><p>Basic Account</p></div>
                                <a href="#" class="font-bold text-blue-500 hover:underline w-full md:w-1/5 text-left md:text-right mt-2 md:mt-0">Upgrade Account</a>
                            </div>
                            <div class="flex flex-col md:flex-row gap-2 items-start w-full justify-between px-0 md:px-2 py-8 text-sm border-b">
                                <h3 class="font-semibold w-full md:w-1/4">Organization Name</h3>
                                <div class="w-full md:w-1/2 flex gap-2"><p>123 landlord inc.</p></div>
                                <a href="#" class="font-bold text-blue-500 hover:underline w-full md:w-1/5 text-left md:text-right mt-2 md:mt-0">Update</a>
                            </div>
                            <div class="flex flex-col md:flex-row gap-2 items-start w-full justify-between px-0 md:px-2 py-8 text-sm border-b">
                                <h3 class="font-semibold w-full md:w-1/4">Business Email</h3>
                                <div class="w-full md:w-1/2 flex gap-2"><p><EMAIL></p></div>
                                <a href="#" class="font-bold text-blue-500 hover:underline w-full md:w-1/5 text-left md:text-right mt-2 md:mt-0">Update</a>
                            </div>
                            <div class="flex flex-col md:flex-row gap-2 items-start w-full justify-between px-0 md:px-2 py-8 text-sm border-b">
                                <h3 class="font-semibold w-full md:w-1/4">Business Address</h3>
                                <div class="w-full md:w-1/2">
                                    <p>1234 Main St, Unit 108</p>
                                    <p>Anytown, CA 91234</p>
                                    <p>USA</p>
                                </div>
                                <a href="#" class="font-bold text-blue-500 hover:underline w-full md:w-1/5 text-left md:text-right mt-2 md:mt-0">Update</a>
                            </div>
                            <!-- Team Section Header -->
                            <div class="flex flex-col md:flex-row gap-2 items-start w-full justify-between px-0 md:px-2 py-8 text-sm">
                                <h2 class="text-lg">Team</h2>
                                <button 
                                    class="bg-slate-800 text-white px-4 py-2 rounded text-sm mt-4 sm:mt-0"
                                    onclick="addNewTeamMember()"
                                >
                                    Invite Member
                                </button>
                            </div>
                            <!-- Team List Container -->
                            <div id="teamList">
                                <!-- Existing team member 1 - The Boss -->
                                <div class="flex flex-col md:flex-row gap-2 w-full justify-between px-0 md:px-2 py-8 text-sm items-center">
                                    <div class="md:w-2/5 w-full block">
                                        <h3 class="font-semibold w-full">The Boss</h3>
                                        <div class="w-full md:w-1/2 flex gap-2"><p class="text-gray-500"><EMAIL></p></div>
                                    </div>
                                    <div class="relative w-full md:w-2/5">
                                        <select
                                            class="p-2 w-full focus:outline-gray-100 border border-gray-200 pr-8 disabled:bg-gray-50 disabled:text-gray-700 disabled:cursor-not-allowed appearance-none"
                                            id="role"
                                            disabled
                                        >    
                                            <option>Owner</option>
                                        </select>
                                        <svg 
                                            class="w-4 h-4 absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-500 pointer-events-none" 
                                            fill="none" 
                                            stroke="currentColor" 
                                            viewBox="0 0 24 24"
                                        >
                                            <path 
                                                stroke-linecap="round" 
                                                stroke-linejoin="round" 
                                                stroke-width="2" 
                                                d="M19 9l-7 7-7-7"
                                            />
                                        </svg>
                                    </div>
                                </div>
                                <!-- Team Member 2 - Normal select -->
                                <div class="flex flex-col md:flex-row gap-2 w-full justify-between px-0 md:px-2 py-8 text-sm items-center">
                                    <div class="md:w-2/5 w-full block">
                                        <h3 class="font-semibold w-full">Firstname Lastname here</h3>
                                        <div class="w-full md:w-1/2 flex gap-2"><p class="text-gray-500"><EMAIL></p></div>
                                    </div>
                                    <div class="relative w-full md:w-2/5">
                                        <select
                                            class="p-2 w-full focus:outline-gray-100 border border-gray-200 pr-8 appearance-none"
                                            id="role"
                                        >    
                                            <option>Admin</option>
                                            <option>Member</option>
                                            <option>Remove</option>
                                        </select>
                                        <svg 
                                            class="w-4 h-4 absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-500" 
                                            fill="none" 
                                            stroke="currentColor" 
                                            viewBox="0 0 24 24"
                                        >
                                            <path 
                                                stroke-linecap="round" 
                                                stroke-linejoin="round" 
                                                stroke-width="2" 
                                                d="M19 9l-7 7-7-7"
                                            />
                                        </svg>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Billing Tab -->
                    <div id="Billing" class="tabcontent hidden bg-white p-6 rounded-lg border border-gray-200 mb-16">
                        <div class="block">
                            <h2 class="text-lg mb-2">Billing Information</h2>
                            
                            <div id="billing-address-section" class="flex flex-col md:flex-row gap-2 items-start w-full justify-between px-0 md:px-2 py-8 text-sm border-b">
                                <h3 class="font-semibold w-full md:w-1/4">Billing Address</h3>
                                <div class="w-full md:w-1/2" id="billing-address-content">
                                </div>
                            </div>
                            
                            <div id="payment-method-section" class="flex flex-col md:flex-row gap-2 items-start w-full justify-between px-0 md:px-2 py-8 text-sm border-b">
                                <h3 class="font-semibold w-full md:w-1/4">Payment Method</h3>
                                <div class="w-full md:w-1/2" id="payment-method-content">
                                </div>
                            </div>

                            <!-- Billing History section -->
                            <div class="mt-8">
                                <h2 class="text-lg mb-2">Billing History</h2>
                                <!-- Desktop view -->
                                <div class="hidden md:grid grid-cols-4 text-sm font-medium text-gray-500 border-b border-gray-100 mb-2">
                                    <div class="p-4 w-1/4">Date</div>
                                    <div class="p-4 w-1/4">Amount</div>
                                    <div class="p-4 w-1/4">Card</div>
                                    <div class="p-4 w-1/4">Invoice</div>
                                </div>
                                <div id="billing-history-table" class="hidden md:block"></div>
                                <!-- Mobile view -->
                                <div id="billing-history-mobile" class="md:hidden flex flex-col gap-2"></div>
                            </div>
                        </div>
                    </div>

                    <!-- SysAdmin Tab -->
                    <!--
                    <div id="SysAdmin" class="tabcontent hidden bg-white p-6 rounded-lg border border-gray-200 mb-16">
                        <div class="block">
                            <div class="flex flex-col md:flex-row gap-2 items-start w-full justify-between px-0 md:px-2 py-8 text-sm border-b">
                                <h3 class="font-semibold w-full md:w-1/4">Metro2 Report</h3>
                                <div class="w-full md:w-1/2">
                                    <label for="month-select" class="block mb-2">Select Month: (only up to the previous month)</label>
                                    <input type="month" id="month-select" class="w-full border border-gray-300 rounded px-2 py-1">
                                </div>
                                <div class="w-full md:w-1/5 flex justify-start md:justify-end mt-2 md:mt-0">
                                    <button id="generate-metro2-btn" class="bg-slate-800 text-white px-4 py-2 rounded text-sm">
                                        Generate
                                    </button>
                                </div>
                            </div>
                            <div class="flex flex-col md:flex-row gap-2 items-start w-full justify-between px-0 md:px-2 py-8 text-sm border-b">
                                <h3 class="font-semibold w-full md:w-1/4">Product Theme</h3>
                                <div class="w-full md:w-1/2">
                                    <p>Slate (Default)</p>
                                </div>
                                <a href="#" class="font-bold text-blue-500 hover:underline w-full md:w-1/5 text-left md:text-right mt-2 md:mt-0">Change</a>
                            </div>
                            <div class="flex flex-col md:flex-row gap-2 items-start w-full justify-between px-0 md:px-2 py-8 text-sm border-b">
                                <h3 class="font-semibold w-full md:w-1/4">Mailbox</h3>
                                <div class="w-full md:w-1/2">
                                    <p>Enable mailbox feature for all users</p>
                                </div>
                                <div class="w-full md:w-1/5 flex justify-start md:justify-end mt-2 md:mt-0">
                                    <div
                                        hx-get="/components/toggle-switch.html"
                                        hx-trigger="revealed"
                                        hx-swap="innerHTML"
                                    ></div>
                                </div>
                            </div>
                            <div class="flex flex-col md:flex-row gap-2 items-start w-full justify-between px-0 md:px-2 py-8 text-sm border-b">
                                <h3 class="font-semibold w-full md:w-1/4">Forum</h3>
                                <div class="w-full md:w-1/2">
                                    <p>Enable the reporting board feature for all users</p>
                                </div>
                                <div class="w-full md:w-1/5 flex justify-start md:justify-end mt-2 md:mt-0">
                                    <div
                                        hx-get="/components/toggle-switch.html"
                                        hx-trigger="revealed"
                                        hx-swap="innerHTML"
                                    ></div>
                                </div>
                            </div>
                            <div class="flex flex-col md:flex-row gap-2 items-start w-full justify-between px-0 md:px-2 py-8 text-sm border-b">
                                <h3 class="font-semibold w-full md:w-1/4">Apply Changes</h3>
                                <div class="w-full md:w-1/2">
                                    <p>Apply these changes to global users</p>
                                </div>
                                <div class="w-full md:w-1/5 flex justify-start md:justify-end mt-2 md:mt-0">
                                    <button class="bg-slate-800 text-white px-4 py-2 rounded text-sm"
                                    >Apply Changes</button>
                                </div>
                            </div>
                        </div>
                    </div>
                    -->

                    <!-- Plans Tab -->
                    <div id="Plans" class="tabcontent hidden bg-white p-6 rounded-lg border border-gray-200 mb-16">
                        <div class="block">
                            <h2 class="text-lg mb-4">Plans</h2>
                            <div class="flex flex-col xl:flex-row gap-4">
                                <div class="flex flex-col justify-between bg-white w-full rounded-md py-4 px-4 md:px-8 gap-8 border border-blue-100 shadow-xl shadow-blue-400/10">
                                    <div class="flex flex-col gap-1">
                                        <span class="w-full">Investor</span>
                                        <span id="plan-price" class="text-xl w-full font-bold">$19.99</span>
                                        <span class="text-sm text-gray-400 w-full mb-2">Per month, billed monthly</span>
                                        <hr class="border-gray-200">
                                        <ul class="md:text-sm text-xs">
                                            <li class="mt-4 flex flex-row gap-2 items-center">
                                                <div class="w-4 h-4"><svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="size-4"><path stroke-linecap="round" stroke-linejoin="round" d="M9 12.75 11.25 15 15 9.75M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z" /></svg></div>
                                                Free 20 reports a month to Equifax
                                            </li>
                                            <li class="mt-2 flex flex-row gap-2 items-center">
                                                <div class="w-4 h-4"><svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="size-4"><path stroke-linecap="round" stroke-linejoin="round" d="M9 12.75 11.25 15 15 9.75M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z" /></svg></div>
                                                Unlimited lease tracking
                                            </li>
                                            <li class="mt-2 flex flex-row gap-2 items-center">
                                                <div class="w-4 h-4"><svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="size-4"><path stroke-linecap="round" stroke-linejoin="round" d="M9 12.75 11.25 15 15 9.75M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z" /></svg></div>
                                                Unlimited properties tracking
                                            </li>
                                            <li class="mt-2 flex flex-row gap-2 items-center">
                                                <div class="w-4 h-4"><svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="size-4"><path stroke-linecap="round" stroke-linejoin="round" d="M9 12.75 11.25 15 15 9.75M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z" /></svg></div>
                                                Automated tenant notifications
                                            </li>
                                        </ul>
                                    </div>
                                    <div class="flex flex-col items-center gap-2 w-full" id="plans-current-plan-section" style="display:none;">
                                        <button class="bg-blue-100 text-blue-700 px-4 py-2 rounded font-semibold mb-2 cursor-default" disabled>Current Plan</button>
                                        <button class="bg-white border border-gray-300 text-gray-500 px-4 py-2 rounded font-semibold hover:bg-gray-100">Cancel Plan</button>
                                    </div>
                                    <button class="bg-slate-800 text-white px-6 py-2 rounded text-sm w-full max-w-[200px] mx-auto" id="subscribe-btn">Subscribe</button>
                                </div>
                                <div class="flex flex-col justify-between bg-white w-full rounded-md py-4 px-4 md:px-8 gap-8 border border-gray-100">
                                    <div class="flex flex-col gap-1">
                                        <span class="w-full">Property Manager</span>
                                        <span class="text-xl w-full font-bold">$29.99</span>
                                        <span class="text-sm text-gray-400 w-full mb-2">Per month, billed monthly</span>
                                        <hr class="border-gray-200">
                                        <ul class="md:text-sm text-xs">
                                            <li class="mt-4 flex flex-row gap-2 items-center">
                                                <div class="w-4 h-4"><svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="size-4"><path stroke-linecap="round" stroke-linejoin="round" d="M9 12.75 11.25 15 15 9.75M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z" /></svg></div>
                                                Unlimited reports to Equifax
                                            </li>
                                            <li class="mt-2 flex flex-row gap-2 items-center">
                                                <div class="w-4 h-4"><svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="size-4"><path stroke-linecap="round" stroke-linejoin="round" d="M9 12.75 11.25 15 15 9.75M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z" /></svg></div>
                                                Unlock team management
                                            </li>
                                            <li class="mt-2 flex flex-row gap-2 items-center">
                                                <div class="w-4 h-4"><svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="size-4"><path stroke-linecap="round" stroke-linejoin="round" d="M9 12.75 11.25 15 15 9.75M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z" /></svg></div>
                                                Role & access management
                                            </li>
                                            <li class="mt-2 flex flex-row gap-2 items-center">
                                                <div class="w-4 h-4"><svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="size-4"><path stroke-linecap="round" stroke-linejoin="round" d="M9 12.75 11.25 15 15 9.75M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z" /></svg></div>
                                                Assign agents to leases/properties
                                            </li>
                                        </ul>
                                    </div>
                                    <div class="flex flex-col items-center gap-2 w-full mt-4">
                                        <button class="bg-gray-400 text-white px-6 py-2 rounded text-sm w-full max-w-[200px] mx-auto cursor-not-allowed" disabled>Coming Soon</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Checkout Tab -->
                    <div id="checkout" class="tabcontent hidden bg-white p-6 rounded-lg border border-gray-200 mb-16">
                        <div class="block">
                            <h2 class="text-lg mb-4">Upgrade Account</h2>
                            <div class="flex flex-col md:flex-row gap-2 justify-between">
                                <div id="monthly-billing" class="flex flex-col rounded-md border border-blue-300 bg-blue-50 p-4 w-full cursor-pointer selected-billing">
                                    <span class="font-bold ">Monthly billing</span>
                                    <span class="text-gray-400 text-sm mt-2">$19.99 per month</span>
                                </div>
                                <div id="annual-billing" class="flex flex-col rounded-md border border-gray-300 p-4 w-full cursor-pointer">
                                    <span class="font-bold flex flex-row justify-between">Annual billing <div class="badge badge-soft badge-primary text-xs">Save 20%</div></span>
                                    <span class="text-gray-400 text-sm mt-2">$15.99 per month</span>
                                </div>
                            </div>
                            <h2 class="text-lg mt-8 mb-4">Summary</h2>
                            <hr class=" border-gray-300 mb-4">
                            <div id="summary-upgrade-account" class="flex flex-row justify-between mt-4 mb-8">
                                <div class="flex flex-col">
                                    <span class="text-sm font-medium">Upgrade account to Investor</span>
                                    <span id="summary-renewal-date" class="text-sm text-gray-400 mb-2">Your plan will renew on January 1, 2026</span>
                                </div>
                                <span id="summary-base-price" class="text-sm font-medium">$19.99</span>
                            </div>
                            <div class="flex flex-row justify-between mt-4 mb-8">
                                <div class="flex flex-col">
                                    <span class="text-sm font-medium">20 X Rent reports to Equifax</span>
                                    <span class="text-sm text-gray-400 mb-2">Monthly free reports as part of the investor plan</span>
                                </div>
                                <span class="text-sm font-medium">$0.00</span>
                            </div>
                            <div id="summary-annual-discount" class="flex flex-row justify-between mt-4 mb-8" style="display: none;">
                                <div class="flex flex-col">
                                    <span class="text-sm font-medium">Annual billing discount</span>
                                    <span id="annual-discount-percent" class="text-sm text-gray-400 mb-2">20% discount</span>
                                </div>
                                <span id="annual-discount-amount" class="text-sm font-medium">$-48.00</span>
                            </div>
                            <div id="summary-promo-discount" class="flex flex-row justify-between mt-4 mb-8" style="display: none;">
                                <div class="flex flex-col">
                                    <span class="text-sm font-medium">Referral discount</span>
                                    <span id="promo-discount-percent" class="text-sm text-gray-400 mb-2">15% discount</span>
                                </div>
                                <span id="promo-discount-amount" class="text-sm font-medium">$-0.00</span>
                            </div>
                            <!-- Total Discount Summary -->
                            <div id="total-discount-summary" class="flex flex-row justify-between mt-4 mb-2 pt-4 border-t border-gray-200" style="display: none;">
                                <div class="flex flex-col">
                                    <span class="text-sm font-medium">Total Discount</span>
                                    <span id="total-discount-percent" class="text-sm text-gray-400 mb-2">28% off</span>
                                </div>
                                <span id="total-discount-amount" class="text-sm font-medium text-green-600">$-67.17</span>
                            </div>

                            <div class="flex flex-col-reverse md:flex-row my-4 justify-between items-center md:items-end gap-4">
                                <button class="bg-slate-800 text-white px-8 py-3 rounded text-base font-bold w-full md:w-auto" id="confirm-pay-btn">Confirm and pay</button>
                                <span class="text-lg font-bold text-right w-full md:w-auto">Total: <span id="summary-total-price" class="text-lg font-bold ml-4">$19.99</span></span>
                            </div>
                        </div>
                    </div>

                    <!-- Logout Tab -->
                    <div id="Logout" class="tabcontent hidden bg-white p-6 rounded-lg border border-gray-200 mb-16">
                        <div class="block">
                            <h2 class="text-lg mb-6">Log Out</h2>
                            <p class="mb-6">Are you sure you want to log out of your account?</p>
                            <button 
                                id="logout-btn"
                                class="bg-slate-800 text-white px-6 py-3 rounded text-sm hover:bg-slate-700"
                            >
                                Confirm Logout
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
      </main>
    </div>
    <!-- Modal Container -->
    <div id="modal" class="relative"></div>
    {{> footer }}
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            var subscribeBtn = document.getElementById('subscribe-btn');
            if (subscribeBtn) {
                subscribeBtn.onclick = function() {
                    var tabcontent = document.getElementsByClassName('tabcontent');
                    for (var i = 0; i < tabcontent.length; i++) {
                        tabcontent[i].classList.add('hidden');
                        tabcontent[i].classList.remove('block');
                    }
                    var checkoutTab = document.getElementById('checkout');
                    checkoutTab.classList.remove('hidden');
                    checkoutTab.classList.add('block');
                };
            }
        });
    </script>
    <style>
    .selected-billing {
        border: 1.5px solid #2563eb !important;
        background: #e0edff !important;
    }
    </style>
<script type="module" src="./main.js"></script>