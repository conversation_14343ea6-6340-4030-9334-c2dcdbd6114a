// Import dependencies
import $ from 'jquery'
import { userApi, authApi } from '../../services'
import { checkAuthStatusAndRedirect, performLogout } from '../../services/util'
import Mustache from 'mustache'
import accountInfoTemplate from '../../templates/account-info.mst?raw'
import errorMessagesTemplate from '../../templates/error-messages.mst?raw'
import teamMemberTemplate from '../../templates/team-member.mst?raw'
import { renderSidebarNav } from '../../services/sidebar'

// Store user data
let userData = null;
let planData = null;
let monthlyPlanData = null;
let annualPlanData = null;
let currentPlanType = 'month'; // 默认月付
let hasActiveSubscription = false;
let currentUserSub = null;
let currentPlanInfo = null;
let allPlans = [];

// Global function to close modal and restore scrolling
window.closeModal = function() {
  document.getElementById('modal').innerHTML = '';
  // 恢复背景滚动
  document.body.style.overflow = '';
  document.documentElement.style.overflow = '';
};

// Page initialization
$(document).ready(async function() {
    // await checkAuthStatusAndRedirect();

    // 拉取最新用户信息并渲染侧边栏
    try {
        const userDataSidebar = await userApi.getUserInfo();
        await renderSidebarNav(userDataSidebar);
    } catch (e) {
    }

    await loadUserAccount();
    await fetchPlans();
    await fetchUserSubscription();
    renderInvestorCard();
    renderCheckoutDynamic(); // 立即渲染价格和续费日期

    // Hide Plans section for tenant view
    handlePlansVisibility();
    
    // Initialize tab handlers
    $('.tablinks').on('click', function(event) {
        const tabName = $(this).data('tab');
        handleTabChange(event, tabName);
    });

    // Handle mobile dropdown
    $('#mobile-tab-select').on('change', function(event) {
        handleTabChange(event, this.value);
    });

    // Check for URL hash and activate corresponding tab
    const hash = window.location.hash.substring(1); // Remove the # symbol
    if (hash && $(`button[data-tab="${hash}"]`).length > 0) {
        // If hash matches a tab, activate it
        $(`button[data-tab="${hash}"]`).trigger('click');
    } else {
        // Set first tab as active by default
        $('.tablinks').first().trigger('click');
    }

    initMetro2Download();

    // Handle view type change
    $(document).on('click', '#change-view-btn', async function() {
        try {
            const newViewType = userData.viewType === 'landlord' ? 'tenant' : 'landlord';
            await userApi.updateViewType(newViewType);
            await loadUserAccount(); // 重新拉取并渲染用户信息
            
            // 更新本地存储的用户数据
            userData.viewType = newViewType;
            localStorage.setItem('user', JSON.stringify(userData));

            // Update Plans visibility after view type change
            handlePlansVisibility();

            // 显示成功消息
            const successToastHtml = Mustache.render(errorMessagesTemplate, {
                successToast: true,
                message: `Successfully switched to ${newViewType} view`
            });
            $(successToastHtml).appendTo('body').delay(3000).fadeOut(function() {
                $(this).remove();
            });
            
            // 刷新页面以更新导航栏和其他视图相关的内容
            window.location.reload();
        } catch (error) {
            const errorToastHtml = Mustache.render(errorMessagesTemplate, { 
                errorToast: true, 
                message: 'Failed to update view type: ' + (error.message || 'Unknown error')
            });
            $(errorToastHtml).appendTo('body').delay(3000).fadeOut(function() {
                $(this).remove();
            });
        }
    });

    // 推荐码Apply按钮事件
    $(document).on('click', '#apply-referral-code-btn', async function() {
        const code = $('#apply-referral-code-input').val().trim();
        if (!code) {
            $('#apply-referral-code-fail').text('Please enter a referral code').removeClass('hidden');
            $('#apply-referral-code-success').addClass('hidden');
            return;
        }
        try {
            const res = await fetch('/v1/referral-code-applications', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ code })
            });
            if (res.ok) {
                $('#apply-referral-code-success').removeClass('hidden');
                $('#apply-referral-code-fail').addClass('hidden');
            } else {
                const data = await res.json();
                $('#apply-referral-code-fail').text(data.error || 'Referral code is invalid or has already been used').removeClass('hidden');
                $('#apply-referral-code-success').addClass('hidden');
            }
        } catch (e) {
            $('#apply-referral-code-fail').text('Network error, please try again').removeClass('hidden');
            $('#apply-referral-code-success').addClass('hidden');
        }
    });

    await fetchPlans();
    renderCheckoutDynamic();

    // 订阅类型切换（年付/月付）
    $(document).on('click', '#monthly-billing, #annual-billing', function() {
        $('#monthly-billing, #annual-billing').removeClass('selected-billing border-blue-300 bg-blue-50').addClass('border-gray-300');
        $(this).addClass('selected-billing border-blue-300 bg-blue-50').removeClass('border-gray-300');
        // 切换类型
        if (this.id === 'annual-billing') {
            currentPlanType = 'year';
        } else {
            currentPlanType = 'month';
        }
        renderCheckoutDynamic();
    });

    renderSubscriptionUI();

    // 添加地址输入格式化功能
    setupAddressFormatting();
});

// 地址输入格式化功能
function setupAddressFormatting() {
    // 为除了Province和Postal Code外的字段添加首字母大写功能（不包括country因为它是固定的）
    $(document).on('input', '#new-street, #new-unit, #new-city', function() {
        const input = $(this);
        const value = input.val();

        // 将每个单词的首字母大写
        const formattedValue = value.replace(/\b\w/g, function(char) {
            return char.toUpperCase();
        });

        if (value !== formattedValue) {
            const cursorPosition = input[0].selectionStart;
            input.val(formattedValue);
            // 恢复光标位置
            input[0].setSelectionRange(cursorPosition, cursorPosition);
        }
    });

    // Province字段：全部大写
    $(document).on('input', '#new-province', function() {
        const input = $(this);
        const value = input.val();
        const formattedValue = value.toUpperCase();

        if (value !== formattedValue) {
            const cursorPosition = input[0].selectionStart;
            input.val(formattedValue);
            // 恢复光标位置
            input[0].setSelectionRange(cursorPosition, cursorPosition);
        }
    });

    // Postal Code字段：全部大写，并添加空格格式化 (X0X 0X0)
    $(document).on('input', '#new-postal-code', function() {
        const input = $(this);
        let value = input.val().replace(/\s/g, '').toUpperCase(); // 移除空格并转大写

        // 限制只能输入字母和数字
        value = value.replace(/[^A-Z0-9]/g, '');

        // 加拿大邮政编码格式：X0X 0X0
        if (value.length > 3) {
            value = value.substring(0, 3) + ' ' + value.substring(3, 6);
        }

        // 限制最大长度为7个字符（包括空格）
        if (value.length > 7) {
            value = value.substring(0, 7);
        }

        const cursorPosition = input[0].selectionStart;
        input.val(value);

        // 调整光标位置，考虑添加的空格
        let newCursorPosition = cursorPosition;
        if (cursorPosition === 4 && value.length >= 4) {
            newCursorPosition = 4; // 在空格后
        }
        input[0].setSelectionRange(newCursorPosition, newCursorPosition);
    });
}

// Handle tab changes
function handleTabChange(evt, tabName) {
    // Hide all tab content
    $('.tabcontent').addClass('hidden').removeClass('block');

    // Remove active class from all tab buttons
    $('.tablinks').each(function() {
        $(this)
            .removeClass('bg-gray-50 border-black text-slate-800 active-tab')
            .addClass('border-transparent')
            .removeClass('hover:border-black')
            .addClass('hover:border-gray-300');
    });

    // Show the selected tab content
    $(`#${tabName}`).removeClass('hidden').addClass('block');

    // Update mobile dropdown selection
    $('#mobile-tab-select').val(tabName);

    // If event has currentTarget (button click), add active class
    if (evt.currentTarget.tagName === 'BUTTON') {
        $(evt.currentTarget)
            .addClass('bg-gray-50 border-black text-slate-800 active-tab')
            .removeClass('border-transparent hover:border-gray-300')
            .addClass('hover:border-black');
    }
    // If using dropdown, find and activate corresponding button
    else if (evt.currentTarget.tagName === 'SELECT') {
        $(`.tablinks[data-tab="${tabName}"]`)
            .addClass('bg-gray-50 border-black text-slate-800 active-tab')
            .removeClass('border-transparent hover:border-gray-300')
            .addClass('hover:border-black');
    }

    if (tabName === 'Billing') {
        loadBillingHistory();
        loadPaymentInfo();
    }
}

// Team member functionality
function addNewTeamMember() {
    const renderedHtml = Mustache.render(teamMemberTemplate);
    $('#teamList').prepend(renderedHtml);
}

// Handle invite button click
$(document).on('click', '.send-invite-btn', function() {
    $(this).text('Remove Invitation');
});

// Load user account data
async function loadUserAccount() {
    try {
        // Get user data
        const response = await userApi.getUserInfo();
        userData = response;
        window.currentUser = userData;
        localStorage.setItem('user', JSON.stringify(userData));

        // Format address for display
        let formattedAddress = '';
        if (userData.address) {
            const addr = userData.address;
            formattedAddress = [
                addr.street,
                addr.unit,
                addr.city,
                addr.prov,
                addr.country,
                addr.zipCode
            ].filter(Boolean).join(', ');
        }

        // Split name into first and last name for display
        const fullName = userData.name || userData.username || '';
        const nameParts = fullName.trim().split(' ');
        const firstName = nameParts[0] || '';
        const lastName = nameParts.slice(1).join(' ') || '';

        // Render template with data
        const viewType = ((userData.viewType === undefined || userData.viewType === null || userData.viewType === '') ? 'landlord' : userData.viewType).toLowerCase().trim();
        const isLandlord = viewType === 'landlord';
        const switchToView = isLandlord ? 'Tenant' : 'Landlord';
        const currentProvince = userData.address ? userData.address.prov || '' : '';

        const renderedHtml = Mustache.render(accountInfoTemplate, {
            name: userData.name || userData.username,
            firstName: firstName,
            lastName: lastName,
            email: userData.email,
            address: formattedAddress,
            addressStreet: userData.address ? userData.address.street || '' : '',
            addressUnit: userData.address ? userData.address.unit || '' : '',
            addressCity: userData.address ? userData.address.city || '' : '',
            addressProv: currentProvince,
            addressCountry: 'Canada', // Always set to Canada
            addressZipCode: userData.address ? userData.address.zipCode || '' : '',
            phoneNumber: userData.phoneNumber || '',
            accountStatus: userData.accountStatus || 'Unverified',
            isVerified: userData.accountStatus === 'Verified',
            viewType,
            isLandlord,
            switchToView,
            // Province selection flags
            isAB: currentProvince === 'AB',
            isBC: currentProvince === 'BC',
            isMB: currentProvince === 'MB',
            isNB: currentProvince === 'NB',
            isNL: currentProvince === 'NL',
            isNS: currentProvince === 'NS',
            isON: currentProvince === 'ON',
            isPE: currentProvince === 'PE',
            isQC: currentProvince === 'QC',
            isSK: currentProvince === 'SK',
            isNT: currentProvince === 'NT',
            isNU: currentProvince === 'NU',
            isYT: currentProvince === 'YT'
        });

        // Update account info sections
        $('#name-field').html(renderedHtml);

        // Load modal templates
        const modalResponse = await fetch('/components/modal-medium.html');
        const modalHtml = await modalResponse.text();
        $('body').append(modalHtml);

    } catch (error) {
        const errorHtml = Mustache.render(errorMessagesTemplate, { accountError: true });
        $('#name-field').html(errorHtml);
    }
}

// Initialize Metro2 download button
function initMetro2Download() {
    //only up to the previous month when generate metro2
    let date = new Date();
    date.setMonth(date.getMonth() - 1);
    const maxMonth = date.toISOString().slice(0, 7); // "YYYY-MM"
    $('#month-select').attr('max', maxMonth);

    $(document).on('click', '#generate-metro2-btn', async function(event) {
        event.preventDefault();
        const button = $(this);
        const originalContent = button.html();
        const monthSelect = $('#month-select');
        const selectedMonth = monthSelect.val(); // Format: YYYY-MM
        if (!selectedMonth) {
            // Show error message using template
            const errorToastHtml = Mustache.render(errorMessagesTemplate, { 
                errorToast: true, 
                message: 'Please select a month first'
            });
            $(errorToastHtml).appendTo('body').delay(3000).fadeOut(function() {
                $(this).remove();
            });
            return;
        }

        try {
            // Show loading state
            button.prop('disabled', true);
            const spinnerHtml = Mustache.render(errorMessagesTemplate, { spinnerIcon: true });
            button.html(spinnerHtml);

            // Make API call with the selected month
            const response = await fetch('/v1/metro2/generate', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    month: selectedMonth
                })
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            // Get filename from Content-Disposition header or use default
            const contentDisposition = response.headers.get('Content-Disposition');
            let filename = 'RM-Metro2-' + new Date().toISOString().slice(0,19).replace(/[-:]/g, '').replace('T', '-') + '.txt';
            if (contentDisposition) {
                const matches = /filename=(.+)/.exec(contentDisposition);
                if (matches && matches[1]) {
                    filename = matches[1].replace(/["']/g, '');
                }
            }

            // Download the file
            const blob = await response.blob();
            const downloadUrl = window.URL.createObjectURL(blob);
            const link = document.createElement('a');
            link.href = downloadUrl;
            link.download = filename;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            window.URL.revokeObjectURL(downloadUrl);

            // Show success message using template
            const successToastHtml = Mustache.render(errorMessagesTemplate, { 
                successToast: true, 
                message: 'Metro2 report downloaded successfully'
            });
            $(successToastHtml).appendTo('body').delay(3000).fadeOut(function() {
                $(this).remove();
            });

        } catch (error) {
            
            // Show error message using template
            const errorToastHtml = Mustache.render(errorMessagesTemplate, { 
                errorToast: true, 
                message: 'Failed to generate Metro2 report: ' + (error.message || 'Unknown error')
            });
            $(errorToastHtml).appendTo('body').delay(3000).fadeOut(function() {
                $(this).remove();
            });
        } finally {
            // Restore button state
            button.prop('disabled', false);
            button.html(originalContent);
        }
    });
}

// Handle name update button click
$(document).on('click', '.name-edit-btn', function() {
    $(this).closest('div').find('.name-edit-btns').removeClass('hidden');
    $(this).addClass('hidden');
    $(this).closest('div').siblings('div').find('.name-display').addClass('hidden');
    $(this).closest('div').siblings('div').find('.name-edit').removeClass('hidden');

    // 清除之前的错误信息
    clearNameErrors();

    // 确保输入框有正确的初始值
    const originalName = userData && userData.name ? userData.name : '';
    const nameParts = originalName.trim().split(' ');
    const firstName = nameParts[0] || '';
    const lastName = nameParts.slice(1).join(' ') || '';

    $('#new-first-name-input').val(firstName);
    $('#new-last-name-input').val(lastName);
});

// 实时清除错误信息当用户开始输入时
$(document).on('input', '#new-first-name-input', function() {
    if ($(this).val().trim()) {
        $('#first-name-error').addClass('hidden');
        $(this).removeClass('border-red-500').addClass('border-gray-300');
    }
});

$(document).on('input', '#new-last-name-input', function() {
    if ($(this).val().trim()) {
        $('#last-name-error').addClass('hidden');
        $(this).removeClass('border-red-500').addClass('border-gray-300');
    }
});

// Handle address update button click
$(document).on('click', '.address-edit-btn', function() {
    $(this).closest('div').find('.address-edit-btns').removeClass('hidden');
    $(this).addClass('hidden');
    $(this).closest('div').siblings('div').find('.address-display').addClass('hidden');
    $(this).closest('div').siblings('div').find('.address-edit').removeClass('hidden');

    // 确保输入框有正确的初始值
    if (userData && userData.address) {
        $('#new-street').val(userData.address.street || '');
        $('#new-unit').val(userData.address.unit || '');
        $('#new-city').val(userData.address.city || '');
        $('#new-province').val(userData.address.prov || '');
        $('#new-country').val('Canada'); // Always set to Canada
        $('#new-postal-code').val(userData.address.zipCode || '');
    } else {
        // If no address data, set default values
        $('#new-country').val('Canada');
    }
});

// Handle phone update button click
$(document).on('click', '.phone-edit-btn', function() {
    $(this).closest('div').find('.phone-edit-btns').removeClass('hidden');
    $(this).addClass('hidden');
    $(this).closest('div').siblings('div').find('.phone-display').addClass('hidden');
    $(this).closest('div').siblings('div').find('.phone-edit').removeClass('hidden');

    // 确保输入框有正确的初始值
    const originalPhone = userData && userData.phoneNumber ? userData.phoneNumber : '';
    $('#new-phone-input').val(originalPhone);
});

// Handle name update cancellation
$(document).on('click', '#cancel-name-update', function() {
    resetNameEdit();
});

// Handle address update cancellation
$(document).on('click', '#cancel-address-update', function() {
    resetAddressEdit();
});

// Handle phone update cancellation
$(document).on('click', '#cancel-phone-update', function() {
    resetPhoneEdit();
});

// Handle name update confirmation
$(document).on('click', '#confirm-name-update', async function() {
    const firstName = $('#new-first-name-input').val().trim();
    const lastName = $('#new-last-name-input').val().trim();

    // 清除之前的错误信息
    clearNameErrors();

    // 验证输入
    let hasErrors = false;

    if (!firstName) {
        showNameFieldError('first-name', 'First name is required');
        hasErrors = true;
    }

    if (!lastName) {
        showNameFieldError('last-name', 'Last name is required');
        hasErrors = true;
    }

    if (hasErrors) {
        return;
    }

    // 合并first name和last name
    const newName = `${firstName} ${lastName}`;

    // 验证名称长度（可选：添加更多验证规则）
    if (newName.length < 2) {
        showNameFieldError('first-name', 'Name must be at least 2 characters long');
        return;
    }

    try {
        await userApi.updateUser({ name: newName });
    } catch (error) {
    }

    // 无论API调用结果如何，都刷新页面数据
    // 因为实际测试显示数据已经保存成功
    // 保存当前滚动位置
    const scrollPosition = window.pageYOffset || document.documentElement.scrollTop;

    // 重新加载用户数据而不是整个页面
    await loadUserAccount();

    // 重置编辑状态
    resetNameEdit();

    // 恢复滚动位置
    window.scrollTo(0, scrollPosition);
});

// Handle address update confirmation
$(document).on('click', '#confirm-address-update', async function() {
    const addressData = {
        street: $('#new-street').val().trim(),
        unit: $('#new-unit').val().trim(),
        city: $('#new-city').val().trim(),
        prov: $('#new-province').val().trim(),
        country: 'Canada', // Always set to Canada
        zipCode: $('#new-postal-code').val().trim()
    };

    // 验证必填字段
    if (!addressData.street || !addressData.city || !addressData.prov || !addressData.zipCode) {
        alert('Please fill in all required address fields (Street, City, Province, Postal Code)');
        return;
    }

    // 验证省份是否为有效的加拿大省份
    const validProvinces = ['AB', 'BC', 'MB', 'NB', 'NL', 'NS', 'ON', 'PE', 'QC', 'SK', 'NT', 'NU', 'YT'];
    if (!validProvinces.includes(addressData.prov)) {
        alert('Please select a valid Canadian province');
        return;
    }

    // 验证字段长度
    if (addressData.street.length < 3) {
        alert('Street address must be at least 3 characters long');
        return;
    }

    if (addressData.city.length < 2) {
        alert('City must be at least 2 characters long');
        return;
    }

    if (addressData.zipCode.length < 3) {
        alert('Postal code must be at least 3 characters long');
        return;
    }

    try {
        await userApi.updateUser({ address: addressData });
    } catch (error) {
    }

    // 无论API调用结果如何，都刷新页面数据
    // 因为实际测试显示数据已经保存成功
    // 保存当前滚动位置
    const scrollPosition = window.pageYOffset || document.documentElement.scrollTop;

    // 重新加载用户数据而不是整个页面
    await loadUserAccount();

    // 重置编辑状态
    resetAddressEdit();

    // 恢复滚动位置
    window.scrollTo(0, scrollPosition);
});

// Handle phone update confirmation
$(document).on('click', '#confirm-phone-update', async function() {
    const newPhone = $('#new-phone-input').val().trim();

    // 验证输入
    if (!newPhone) {
        alert('Please enter a phone number');
        return;
    }

    // 验证电话号码格式：必须是10位数字
    const phoneRegex = /^\d{10}$/;
    if (!phoneRegex.test(newPhone)) {
        alert('Phone number must be exactly 10 digits');
        return;
    }

    try {
        await userApi.updateUser({ phoneNumber: newPhone });
    } catch (error) {
    }

    // 无论API调用结果如何，都刷新页面数据
    // 因为实际测试显示数据已经保存成功
    // 保存当前滚动位置
    const scrollPosition = window.pageYOffset || document.documentElement.scrollTop;

    // 重新加载用户数据而不是整个页面
    await loadUserAccount();

    // 重置编辑状态
    resetPhoneEdit();

    // 恢复滚动位置
    window.scrollTo(0, scrollPosition);
});

// Reset name edit state
function resetNameEdit() {
    $('.name-edit-btn').removeClass('hidden');
    $('.name-edit-btns').addClass('hidden');
    $('.name-display').removeClass('hidden');
    $('.name-edit').addClass('hidden');
    // 清除错误信息
    clearNameErrors();
    // 重置输入框的值为原始值
    const originalName = userData && userData.name ? userData.name : '';
    const nameParts = originalName.trim().split(' ');
    const firstName = nameParts[0] || '';
    const lastName = nameParts.slice(1).join(' ') || '';

    $('#new-first-name-input').val(firstName);
    $('#new-last-name-input').val(lastName);
}

// 显示姓名字段错误信息
function showNameFieldError(fieldType, message) {
    const errorId = fieldType + '-error';
    const inputId = fieldType === 'first-name' ? 'new-first-name-input' : 'new-last-name-input';

    $(`#${errorId}`).text(message).removeClass('hidden');
    $(`#${inputId}`).addClass('border-red-500').removeClass('border-gray-300');
}

// 清除姓名字段错误信息
function clearNameErrors() {
    $('#first-name-error, #last-name-error').addClass('hidden');
    $('#new-first-name-input, #new-last-name-input').removeClass('border-red-500').addClass('border-gray-300');
}

// Reset address edit state
function resetAddressEdit() {
    $('.address-edit-btn').removeClass('hidden');
    $('.address-edit-btns').addClass('hidden');
    $('.address-display').removeClass('hidden');
    $('.address-edit').addClass('hidden');
    // 重置地址输入框的值为原始值
    if (userData && userData.address) {
        $('#new-street').val(userData.address.street || '');
        $('#new-unit').val(userData.address.unit || '');
        $('#new-city').val(userData.address.city || '');
        $('#new-province').val(userData.address.prov || '');
        $('#new-country').val('Canada'); // Always set to Canada
        $('#new-postal-code').val(userData.address.zipCode || '');
    } else {
        // 如果没有地址数据，清空所有输入框但保持Canada
        $('#new-street').val('');
        $('#new-unit').val('');
        $('#new-city').val('');
        $('#new-province').val('');
        $('#new-country').val('Canada');
        $('#new-postal-code').val('');
    }
}

// Reset phone edit state
function resetPhoneEdit() {
    $('.phone-edit-btn').removeClass('hidden');
    $('.phone-edit-btns').addClass('hidden');
    $('.phone-display').removeClass('hidden');
    $('.phone-edit').addClass('hidden');
    // 重置电话输入框的值为原始值
    const originalPhone = userData && userData.phoneNumber ? userData.phoneNumber : '';
    $('#new-phone-input').val(originalPhone);
}

// Handle email update
$(document).on('click', '#change-email-btn', async function() {
    const newEmail = $('#new-email-input').val();
    if (!newEmail) return;

    try {
        await userApi.updateUser({ email: newEmail });
        await loadUserAccount();
        $('#modal').empty();
    } catch (error) {
        alert('Failed to update email');
    }
});

// Handle password edit button click
$(document).on('click', '.password-edit-btn', function() {
    $(this).closest('div').find('.password-edit-btns').removeClass('hidden');
    $(this).addClass('hidden');
    $(this).closest('div').siblings('div').find('.password-display').addClass('hidden');
    $(this).closest('div').siblings('div').find('.password-edit').removeClass('hidden');
});

// Handle password update cancellation
$(document).on('click', '#cancel-password-update', function() {
    resetPasswordEdit();
});

// Handle password update confirmation
$(document).on('click', '#confirm-password-update', async function() {
    const currentPassword = $('#current-password').val();
    const newPassword = $('#new-password').val();
    const confirmPassword = $('#confirm-password').val();

    if (!currentPassword || !newPassword || !confirmPassword) {
        alert('Please fill in all password fields');
        return;
    }
    if (newPassword !== confirmPassword) {
        alert('New passwords do not match');
        return;
    }
    try {
        await userApi.updatePassword(currentPassword, newPassword);
        resetPasswordEdit();
        await loadUserAccount();
        // 清空输入框
        $('#current-password').val('');
        $('#new-password').val('');
        $('#confirm-password').val('');
        alert('Password updated successfully');
    } catch (error) {
        alert(error.message || 'Failed to update password');
    }
});

// Reset password edit state
function resetPasswordEdit() {
    $('.password-edit-btn').removeClass('hidden');
    $('.password-edit-btns').addClass('hidden');
    $('.password-display').removeClass('hidden');
    $('.password-edit').addClass('hidden');
}

// Handle account verification
$(document).on('click', '#start-verification-btn', async function() {
    try {
        await userApi.startVerification();
        await loadUserAccount();
        alert('Verification process started. Please check your email.');
    } catch (error) {
        alert('Failed to start verification process');
    }
});

// Handle account closure
$(document).on('click', '#confirm-close-account-btn', async function() {
    try {
        await userApi.closeAccount();

        // Clear local userData reference
        userData = null;

        // Use the centralized logout function to clean up and redirect
        await performLogout(authApi);
    } catch (error) {
        alert('Failed to close account');
    }
});

// Handle logout button click
$(document).on('click', '#logout-btn', async function() {
    // Clear local userData reference
    userData = null;

    // Use the centralized logout function
    await performLogout(authApi);
});

$(document).on('click', '#confirm-pay-btn', async function() {
    // 根据当前选中的 plan 类型，动态选择 priceId 和 prdId
    let plan = currentPlanType === 'year' ? annualPlanData : monthlyPlanData;
    if (!plan) {
        alert('no plan selected');
        return;
    }
    // 字段名严格校验
    const priceId = plan.prcId || plan.priceId;
    const prdId = plan._id || plan.prdId;
    if (!priceId || !prdId) {
        alert('plan parameters missing, please refresh the page');
        return;
    }
    // 使用一个特殊的URL来处理支付成功后关闭tab的逻辑
    const successUrl = window.location.origin + '/pages/payment-close-tab/';
    const cancelUrl = 'https://stripe.com';
    try {
        const res = await fetch('/v1/checkout/session', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ priceId, prdId, successUrl, cancelUrl })
        });
        const data = await res.json();
        if (res.ok && data.url) {
            window.open(data.url, '_blank');
        } else {
            alert(data.error || 'Failed to create checkout session');
        }
    } catch (e) {
        alert('Network error, please try again.');
    }
});

// 页面初始化时拉取订阅计划
async function fetchPlans() {
    try {
        const res = await fetch('/v1/subplans');
        if (!res.ok) throw new Error('failed to fetch plans');
        const data = await res.json();
        allPlans = data;

        // 根据 allPlans 找到月付和年付的 plan
        if (Array.isArray(allPlans)) {
            monthlyPlanData = allPlans.find(p => p.intrvl === 'month');
            annualPlanData = allPlans.find(p => p.intrvl === 'year');
        }

        // 默认选中月付
        planData = monthlyPlanData;

    } catch (e) {
        planData = null;
    }

    // 更新UI
    if (currentPlanInfo) {
        // 如果用户已有订阅，则在"Plans"区域显示"Current Plan"
        $('#plans-current-plan-section').show();
        // 隐藏 Plans tab 的所有 Subscribe 按钮（兼容多按钮情况）
        $('#Plans .bg-slate-800, #Plans .btn, #Plans button:contains("Subscribe")').hide();
    } else {
        $('#plans-current-plan-section').hide();
        $('#Plans .bg-slate-800, #Plans .btn, #Plans button:contains("Subscribe")').show();
    }

    // 更新总览里的价格信息，只更新第一个匹配到的plan
    if (Array.isArray(allPlans) && allPlans.length > 0) {
        const plan = allPlans.find(p => p.nm === 'normal monthly plan' && p.intrvl === 'month') || allPlans[0];
        const cycle = plan.intrvl === 'month' ? 'Per month, billed monthly' : 'Per year, billed annually';

        const planPriceEl = $('#plan-price');
        if (plan.originalPrc && plan.originalPrc > 0) {
            const originalPrice = (plan.originalPrc / 100).toFixed(2);
            const newPrice = (plan.prc / 100).toFixed(2);
            planPriceEl.html(
                `<span class="text-gray-500 line-through">$${originalPrice}</span>` +
                `<span class="text-red-500 font-bold ml-2">$${newPrice}</span>`
            );
        } else {
            planPriceEl.text(`$${(plan.prc / 100).toFixed(2)}`);
        }

        // 只更新第一个卡片（Investor）的计费周期，不影响Property Manager卡片
        $("#Plans .text-sm.text-gray-400.w-full.mb-2").first().text(cycle);
    }
}

// 动态渲染checkout价格和续费日期
function renderCheckoutDynamic() {
    let selectedPlan = currentPlanType === 'month' ? monthlyPlanData : annualPlanData;
    if (!selectedPlan) return;

    // 价格渲染
    if (monthlyPlanData) {
        const priceDollar = (monthlyPlanData.prc / 100).toFixed(2);
        $("#monthly-billing .text-gray-400").text(`$${priceDollar} per month`);
    }
    if (annualPlanData) {

        // 年付计划显示最终折扣后的月均价格
        // 使用 originalPrc 作为基础价格，如果没有则使用 prc
        let basePrice = (annualPlanData.originalPrc || annualPlanData.prc) / 100;

        // 计算折扣乘数 - 使用乘算逻辑
        let discountMultiplier = 0.8; // 年费20%折扣 = 0.8倍

        // 如果有推荐码折扣，再乘以推荐码折扣
        if (annualPlanData.originalPrc && annualPlanData.originalPrc > annualPlanData.prc && annualPlanData.discountPercent) {
            discountMultiplier = discountMultiplier * (1 - annualPlanData.discountPercent / 100);
        }

        // 最终年费价格 - 使用乘算
        let finalAnnualPrice = basePrice * discountMultiplier;

        // 月均价格
        const monthlyPriceDollar = (finalAnnualPrice / 12).toFixed(2);
        $("#annual-billing .text-gray-400").text(`$${monthlyPriceDollar} per month`);
    }

    // 计算各种价格和折扣
    calculateAndDisplayPricing(selectedPlan);

    // 续费日期
    const now = new Date();
    if (currentPlanType === 'year') {
        now.setFullYear(now.getFullYear() + 1);
    } else {
        now.setMonth(now.getMonth() + 1);
    }
    const renewDate = now.toLocaleDateString('en-US', { year: 'numeric', month: 'long', day: 'numeric' });
    $("#summary-renewal-date").text(`Your plan will renew on ${renewDate}`);
}

// 计算和显示详细的价格信息
function calculateAndDisplayPricing(selectedPlan) {
    if (!selectedPlan) return;

    let basePrice = 0;
    let annualDiscount = 0;
    let promoDiscount = 0;
    let totalPrice = 0;

    // 计算基础价格（原始价格）
    if (currentPlanType === 'year') {
        // 年付情况：基础价格使用年费计划的原始价格
        if (selectedPlan.originalPrc && selectedPlan.originalPrc > 0) {
            basePrice = selectedPlan.originalPrc / 100;
        } else {
            basePrice = selectedPlan.prc / 100;
        }

        // 年费固定20%折扣 - 显示实际折扣金额
        annualDiscount = Math.round(basePrice * 0.2 * 100) / 100; // 20%固定折扣，精确到分

        $("#summary-annual-discount").show();
        $("#annual-discount-amount").text(`$-${annualDiscount.toFixed(2)}`);
        $("#annual-discount-percent").text("20% discount");

    } else {
        // 月付情况：使用月费价格
        if (selectedPlan.originalPrc && selectedPlan.originalPrc > 0) {
            basePrice = selectedPlan.originalPrc / 100;
        } else {
            basePrice = selectedPlan.prc / 100;
        }

        // 隐藏年费折扣行
        $("#summary-annual-discount").hide();
    }

    // 计算推荐码折扣 - 使用乘算逻辑
    let discountMultiplier = 1.0; // 总折扣乘数

    if (selectedPlan.originalPrc && selectedPlan.originalPrc > selectedPlan.prc && selectedPlan.discountPercent) {
        if (currentPlanType === 'year') {
            // 年费：先应用年费折扣(0.8)，再应用推荐码折扣
            discountMultiplier = 0.8 * (1 - selectedPlan.discountPercent / 100);
            // 计算推荐码折扣金额：基于已应用年费折扣后的价格
            const priceAfterAnnual = basePrice * 0.8;
            promoDiscount = Math.round(priceAfterAnnual * (selectedPlan.discountPercent / 100) * 100) / 100;
        } else {
            // 月费：只应用推荐码折扣
            discountMultiplier = 1 - selectedPlan.discountPercent / 100;
            promoDiscount = (selectedPlan.originalPrc - selectedPlan.prc) / 100;
        }

        // 显示推荐码折扣行
        $("#summary-promo-discount").show();
        $("#promo-discount-amount").text(`$-${promoDiscount.toFixed(2)}`);
        $("#promo-discount-percent").text(`${selectedPlan.discountPercent}% discount`);
    } else {
        // 隐藏推荐码折扣行
        $("#summary-promo-discount").hide();
        if (currentPlanType === 'year') {
            discountMultiplier = 0.8; // 只有年费折扣
        }
    }

    // 计算最终总价 - 使用乘算
    totalPrice = Math.round(basePrice * discountMultiplier * 100) / 100;

    // 计算并显示总折扣信息
    const totalDiscountAmount = basePrice - totalPrice;
    const totalDiscountPercent = Math.round((totalDiscountAmount / basePrice) * 100);

    if (totalDiscountAmount > 0) {
        $("#total-discount-summary").show();
        $("#total-discount-amount").text(`$-${totalDiscountAmount.toFixed(2)}`);
        $("#total-discount-percent").text(`${totalDiscountPercent}% off`);
    } else {
        $("#total-discount-summary").hide();
    }

    // 更新显示
    $("#summary-base-price").text(`$${basePrice.toFixed(2)}`);
    $("#summary-total-price").text(`$${totalPrice.toFixed(2)}`);

    // 兼容旧的选择器（保持向后兼容）
    $("#checkout .flex-row.justify-between.mt-4.mb-8").first().find('span.text-sm.font-medium').last().text(`$${basePrice.toFixed(2)}`);
    $("#checkout .flex-col-reverse.md\\:flex-row .text-lg.font-bold.ml-4").text(`$${totalPrice.toFixed(2)}`);
}

// 订阅按钮点击时切换tab并渲染动态内容
$(document).on('click', '#subscribe-btn', function() {
    renderCheckoutDynamic();
});

// 拉取当前用户订阅状态
async function fetchUserSubscription() {
    try {
        const res = await fetch('/v1/usersub');
        if (!res.ok) throw new Error('failed to fetch user subscription');
        const data = await res.json();
        // 找到 status 为 active 的记录
        currentUserSub = Array.isArray(data) ? data.find(item => item.sts && item.sts === 'active') : null;
        let planId = currentUserSub ? currentUserSub.planId : null;
        // 查找时要求 id 匹配且 subplan.sts 为 'active'
        if (planId && Array.isArray(allPlans)) {
            currentPlanInfo = allPlans.find(
                p => (String(p._id || p.id || p.planId).trim() === String(planId).trim()) && p.sts === 'active'
            );
        } else {
            currentPlanInfo = null;
        }
        // 只有 usersub 存在且对应 subplan 也为 active，才算已订阅
        hasActiveSubscription = !!(currentUserSub && currentPlanInfo);
    } catch (e) {
        hasActiveSubscription = false;
        currentUserSub = null;
        currentPlanInfo = null;
    }
}

function renderSubscriptionUI() {
    if (hasActiveSubscription) {
        $('#checkout .flex-col-reverse.md\\:flex-row').hide();
        $('#current-plan-section').show();
        // Plans 区域
        $('#plans-current-plan-section').show();
        $('#subscribe-btn').hide();
        // 隐藏 Plans tab 的所有 Subscribe 按钮（兼容多按钮情况）
        $('#Plans .bg-slate-800, #Plans .btn, #Plans button:contains("Subscribe")').hide();
    } else {
        $('#checkout .flex-col-reverse.md\\:flex-row').show();
        $('#current-plan-section').hide();
        $('#plans-current-plan-section').hide();
        $('#subscribe-btn').show();
        $('#Plans .bg-slate-800, #Plans .btn, #Plans button:contains("Subscribe")').show();
    }
}

// 渲染 Investor 卡片内容
function renderInvestorCard() {
    // 只渲染卡片内容，不控制按钮显示
    let plan = currentPlanInfo || monthlyPlanData;
    if (!plan) return;
    // 价格 - 只更新第一个卡片（Investor）
    $("#Plans .text-xl.w-full.font-bold").first().text(`$${(plan.prc / 100).toFixed(2)}`);
    // 计费周期 - 只更新第一个卡片（Investor）
    let cycle = plan.intrvl === 'year' ? 'Per year, billed annually' : 'Per month, billed monthly';
    $("#Plans .text-sm.text-gray-400.w-full.mb-2").first().text(cycle);
    // 权益列表（如需更细致可扩展）
    // ...如需动态渲染权益可在此扩展...
}

// 取消订阅弹窗和逻辑
$(document).on('click', '#plans-current-plan-section button:contains("Cancel Plan")', function() {
    if (!currentUserSub) return;
    // 弹窗确认
    if (window.confirm('Are you sure you want to cancel your plan?')) {
        cancelCurrentPlan();
    }
});

async function cancelCurrentPlan() {
    if (!currentUserSub) return;
    try {
        const res = await fetch(`/v1/subscriptions/${encodeURIComponent(currentUserSub.stripeSubId)}`, {
            method: 'DELETE'
        });
        const data = await res.json();
        if (res.ok) {
            alert('Plan cancelled successfully');
            window.location.reload();
        } else {
            alert(data.error || 'Failed to cancel plan, please try again');
        }
    } catch (e) {
        alert('Network error, failed to cancel plan');
    }
}

// 拉取当前用户近一年billhst
async function fetchUserBillHst() {
    const res = await fetch('/v1/user/billhst?year=1');
    if (!res.ok) throw new Error('Failed to fetch billing history');
    return await res.json();
}

// 渲染桌面端账单历史
function renderBillingHistoryTable(billList) {
    const table = document.getElementById('billing-history-table');
    if (!table) return;
    if (!billList || billList.length === 0) {
        table.innerHTML = '<div class="p-4 text-center text-gray-400 col-span-4">No billing history found</div>';
        return;
    }
    table.innerHTML = billList.map(bill => `
        <div class="grid grid-cols-4 text-sm border-b border-gray-100">
            <div class="p-4 w-1/4 whitespace-nowrap">${bill.ts ? new Date(bill.ts).toLocaleDateString('en-US', { year: 'numeric', month: 'short', day: '2-digit' }) : '-'}</div>
            <div class="p-4 w-1/4">${bill.amt ? ('$' + (bill.amt * 0.01).toFixed(2) + (bill.cur ? bill.cur.toUpperCase() : '')) : '-'}</div>
            <div class="p-4 w-1/4">${bill.crdL4 ? '****' + bill.crdL4 : '-'}</div>
            <div class="p-4 w-1/4 whitespace-nowrap">
                <span class="underline cursor-pointer" onclick="resendBill('${bill._id || bill.id || bill.bhid}')">Resend bill</span>
            </div>
        </div>
    `).join('');
}

// 渲染移动端账单历史
function renderBillingHistoryMobile(billList) {
    const mobile = document.getElementById('billing-history-mobile');
    if (!mobile) return;
    if (!billList || billList.length === 0) {
        mobile.innerHTML = '<div class="p-4 text-center text-gray-400">No billing history found</div>';
        return;
    }
    mobile.innerHTML = billList.map(bill => `
        <div class="flex flex-row justify-between bg-white p-4 border border-gray-100 rounded-md">
            <div class="flex flex-col gap-1">
                <span class="text-sm text-gray-400">${bill.ts ? new Date(bill.ts).toLocaleDateString('en-US', { year: 'numeric', month: 'short', day: '2-digit' }) : '-'}</span>
                <span class="text-lg h-full content-center">${bill.amt ? ('$' + (bill.amt * 0.01).toFixed(2) + (bill.cur ? bill.cur.toUpperCase() : '')) : '-'}</span>
            </div>
            <div class="flex flex-col gap-1">
                <span class="text-sm text-gray-400 text-right">${bill.crdL4 ? '****' + bill.crdL4 : '-'}</span>
                <button class="btn btn-ghost" onclick="resendBill('${bill._id || bill.id || bill.bhid}')">Resend bill</button>
            </div>
        </div>
    `).join('');
}

// Resend bill 事件
window.resendBill = async function(billId) {
    if (!billId) return;
    try {
        const res = await fetch('/v1/user/resend-invoice', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ billId })
        });
        const data = await res.json();
        if (res.ok) {
            alert('invoice email has been resent');
        } else {
            alert('send failed: ' + (data.error || 'unknown error'));
        }
    } catch (e) {
        alert('send failed: ' + e.message);
    }
}

// 加载账单历史
async function loadBillingHistory() {
    try {
        const billList = await fetchUserBillHst();
        renderBillingHistoryTable(billList);
        renderBillingHistoryMobile(billList);
    } catch (e) {
        const table = document.getElementById('billing-history-table');
        const mobile = document.getElementById('billing-history-mobile');
        if (table) table.innerHTML = `<div class='p-4 text-center text-red-500 col-span-4'>${e.message}</div>`;
        if (mobile) mobile.innerHTML = `<div class='p-4 text-center text-red-500'>${e.message}</div>`;
    }
}

// 加载支付信息
async function loadPaymentInfo() {
    try {
        const paymentInfo = await userApi.getPaymentInfo();
        renderPaymentInfo(paymentInfo);
    } catch (e) {
    }
}

// 渲染支付信息
function renderPaymentInfo(paymentInfo) {
    const billingAddressSection = document.getElementById('billing-address-section');
    const billingAddressContent = document.getElementById('billing-address-content');
    const paymentMethodSection = document.getElementById('payment-method-section');
    const paymentMethodContent = document.getElementById('payment-method-content');
    

    
    // 处理账单地址
    if (paymentInfo && paymentInfo.billingAddress) {
        const address = paymentInfo.billingAddress;
        const addressLines = [
            address.line1,
            address.line2,
            `${address.city}, ${address.state} ${address.postalCode}`,
            address.country
        ].filter(Boolean);
        
        billingAddressContent.innerHTML = addressLines.map(line => `<p class="text-gray-600">${line}</p>`).join('');
        billingAddressSection.style.display = 'flex';
    } else {
        billingAddressSection.style.display = 'none';
    }
    
    // 处理支付方式
    if (paymentInfo && paymentInfo.paymentMethod) {
        const { cardBrand, lastFour, expMonth, expYear } = paymentInfo.paymentMethod;
        const cardInfo = [
            `${cardBrand} **** ${lastFour}`,
            `Expires ${expMonth}/${expYear}`
        ].filter(Boolean);
        
        paymentMethodContent.innerHTML = cardInfo.map(info => `<p class="text-gray-600">${info}</p>`).join('');
        paymentMethodSection.style.display = 'flex';
    } else {
        paymentMethodSection.style.display = 'none';
    }
}

// Handle Plans section visibility based on view type
function handlePlansVisibility() {
    const currentUserData = window.currentUser || JSON.parse(localStorage.getItem('user') || '{}');
    const viewType = (currentUserData.viewType || '').toLowerCase();

    if (viewType === 'tenant') {
        // Hide Plans tab button for tenant view
        $('button[data-tab="Plans"]').hide();
        // Also hide from mobile dropdown
        $('#mobile-tab-select option[value="Plans"]').hide();
        // Ensure Plans content is hidden and add hidden class
        $('#Plans').addClass('hidden').removeClass('block');
    } else {
        // Show Plans tab button for landlord view
        $('button[data-tab="Plans"]').show();
        // Also show in mobile dropdown
        $('#mobile-tab-select option[value="Plans"]').show();
        // Don't force show Plans content - let tab switching logic handle it
        // The content visibility will be controlled by the tab switching logic
    }
}