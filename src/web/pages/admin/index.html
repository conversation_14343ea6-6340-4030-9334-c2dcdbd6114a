<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Dashboard</title>
    <link href="/styles/output.css" rel="stylesheet" />
</head>
<body class="min-h-screen bg-gray-200">
  <div class="flex h-screen">
    <!-- 侧边栏 -->
    <aside class="w-20 lg:w-64 bg-white border-r border-gray-200 flex flex-col justify-between py-6">
      <div>
        <a href="/pages/admin/index.html" class="flex items-center px-4 py-3 hover:bg-gray-100">
          <span class="text-base font-medium text-gray-800">Dashboard</span>
        </a>
      </div>
      <div class="mb-2">
        <a href="/pages/admin_account.html" class="flex items-center px-4 py-3 hover:bg-gray-100">
          <span class="text-base font-medium text-gray-800">Admin account</span>
        </a>
      </div>
    </aside>
    <!-- 主体内容 -->
    <main class="h-screen w-full bg-slate-100 overflow-auto px-4 lg:px-16">
      <div class="max-w-[1440px] w-full mx-auto mb-32">
        <div class="flex-1 mt-20 lg:mt-8 pb-0 w-full">
          <h1 class="text-2xl font-bold mb-6">Admin</h1>
          <!-- 搜索栏 -->
          <div class="mt-8 w-full">
            <div class="flex gap-2">
              <input
                type="text"
                id="admin-search"
                placeholder="Search user email, ID, name, or organization"
                class="p-2 px-10 border border-1 rounded text-sm flex-1 w-full sm:max-w-[300px]"
                style="
                  background-image: url('data:image/svg+xml,%3Csvg xmlns=\'http://www.w3.org/2000/svg\' class=\'h-4 w-4\' viewBox=\'0 0 24 24\' fill=\'none\' stroke=\'%236B7280\' stroke-width=\'2\'%3E%3Cpath stroke-linecap=\'round\' stroke-linejoin=\'round\' d=\'M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z\'%3E%3C/path%3E%3C/svg%3E');
                  background-position: 8px 8px;
                  background-repeat: no-repeat;
                  background-size: 20px;"
              />
              <button
                id="searchBtn"
                class="hidden sm:block bg-slate-800 hover:bg-slate-900 transition text-white px-4 py-2 rounded text-sm whitespace-nowrap"
              >
                Search
              </button>
            </div>
          </div>
          <!-- 表格 -->
          <div class="hidden lg:block">
            <table class="w-full text-sm rounded-md overflow-hidden mt-4 bg-white" style="box-shadow: 0 0 0 1px rgb(203 213 225)">
              <thead>
                <tr class="bg-slate-200 text-left text-black uppercase text-xs border-b border-slate-300">
                  <th class="p-4 w-1/12">
                    <span class="flex items-center gap-2">ID
                      <img src="/assets/selector.svg" alt="Sort" class="w-4 h-4" />
                    </span>
                  </th>
                  <th class="p-4 w-1/6">
                    <span class="flex items-center gap-2">Name
                      <img src="/assets/selector.svg" alt="Sort" class="w-4 h-4" />
                    </span>
                  </th>
                  <th class="p-4 w-1/6">
                    <span class="flex items-center gap-2">Email
                      <img src="/assets/selector.svg" alt="Sort" class="w-4 h-4" />
                    </span>
                  </th>
                  <th class="p-4 w-1/6">
                    <span class="flex items-center gap-2">Account Type
                      <img src="/assets/selector.svg" alt="Sort" class="w-4 h-4" />
                    </span>
                  </th>
                  <th class="p-4 w-1/6">
                    <span class="flex items-center gap-2">Status
                      <img src="/assets/selector.svg" alt="Sort" class="w-4 h-4" />
                    </span>
                  </th>
                  <th class="p-4 w-1/6">
                    <span class="flex items-center gap-2">Organization
                      <img src="/assets/selector.svg" alt="Sort" class="w-4 h-4" />
                    </span>
                  </th>
                </tr>
              </thead>
              <tbody id="userTableBody">
                <!-- 用户数据将由JS渲染 -->
                <tr>
                  <td colspan="6" class="p-4 text-center">暂无数据</td>
                </tr>
              </tbody>
            </table>
            <!-- 分页 -->
            <div id="admin-pagination" class="mt-4">
              <!-- 分页内容由JS渲染 -->
            </div>
          </div>
        </div>
      </div>
    </main>
  </div>
  <script type="module" src="./main.js"></script>
</body>
</html> 