<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin - Lease List</title>
    <link href="/styles/output.css" rel="stylesheet" />
</head>
<body class="min-h-screen bg-gray-200">
  <div class="flex h-screen">
    <main class="h-screen w-full bg-slate-100 overflow-auto px-4 lg:px-16">
      <div class="max-w-[1440px] w-full mx-auto mb-32">
        <div class="flex-1 mt-20 lg:mt-8 pb-0 w-full">
          <a href="#" id="back-btn" class="text-sm text-black underline mb-4 inline-block">Back To User Details</a>
          <div class="flex flex-row justify-between items-center mb-6">
            <h1 class="text-2xl font-bold" id="lease-title">Leases</h1>
          </div>
          <div class="mt-8 w-full">
            <div class="flex gap-2 items-center">
              <input
                type="text"
                id="search-input"
                placeholder="Search for a property name or address"
                class="p-2 px-10 border border-1 rounded text-sm flex-1 w-full sm:max-w-[300px]"
                style="background-image: url('data:image/svg+xml,%3Csvg xmlns=\'http://www.w3.org/2000/svg\' class=\'h-4 w-4\' viewBox=\'0 0 24 24\' fill=\'none\' stroke=\'%236B7280\' stroke-width=\'2\'%3E%3Cpath stroke-linecap=\'round\' stroke-linejoin=\'round\' d=\'M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z\'%3E%3C/path%3E%3C/svg%3E');background-position: 8px 8px;background-repeat: no-repeat;background-size: 20px;"
              />
              <button id="search-btn" class="hidden sm:block bg-slate-800 hover:bg-slate-900 transition text-white px-4 py-2 rounded text-sm whitespace-nowrap">Search</button>
              <div class="flex-1"></div>
              <button id="create-lease-btn" class="bg-black text-white px-4 py-2 rounded text-sm whitespace-nowrap ml-auto">Create Lease</button>
            </div>
          </div>
          <div class="hidden lg:block">
            <table class="w-full text-sm rounded-md overflow-hidden mt-4 bg-white" style="box-shadow: 0 0 0 1px rgb(203 213 225)">
              <thead>
                <tr class="bg-slate-200 text-left text-black uppercase text-xs border-b border-slate-300">
                  <th class="p-4 w-1/4 cursor-pointer">
                    <span class="flex items-center gap-2">Leaseholders
                      <img src="/assets/selector.svg" alt="Sort" class="w-4 h-4" />
                    </span>
                  </th>
                  <th class="p-4 w-1/6 cursor-pointer">
                    <span class="flex items-center gap-2">Unit
                      <img src="/assets/selector.svg" alt="Sort" class="w-4 h-4" />
                    </span>
                  </th>
                  <th class="p-4 w-1/6 cursor-pointer">
                    <span class="flex items-center gap-2">Last Payment
                      <img src="/assets/selector.svg" alt="Sort" class="w-4 h-4" />
                    </span>
                  </th>
                  <th class="p-4 w-1/6 cursor-pointer">
                    <span class="flex items-center gap-2">Payment Status
                      <img src="/assets/selector.svg" alt="Sort" class="w-4 h-4" />
                    </span>
                  </th>
                  <th class="p-4 w-1/6 cursor-pointer">
                    <span class="flex items-center gap-2">Owing balance
                      <img src="/assets/selector.svg" alt="Sort" class="w-4 h-4" />
                    </span>
                  </th>
                </tr>
              </thead>
              <tbody id="lease-group-tbody">
                <!-- 分组渲染：每个property为一组，组行+子lease行 -->
              </tbody>
            </table>
            <!-- 分页栏完全复用admin首页 -->
            <div id="admin-pagination" class="mt-4">
              <!-- 分页内容由JS渲染 -->
            </div>
          </div>
        </div>
      </div>
    </main>
  </div>
  <script type="module" src="./main.js"></script>
</body>
</html> 