// admin/lease_all/main.js

function getQueryParam(name) {
  const url = new URL(window.location.href);
  return url.searchParams.get(name);
}

const PAGE_SIZE = 50; // 后端分页大小
let leases = [];
let groupedLeases = [];
let filteredGroups = [];
let currentPage = 1;
let totalPages = 1;
let totalRecords = 0;
let userId = null;
let userName = '';
let sortKey = 'propertyName';
let sortAsc = true;
let adminProperties = [];

async function fetchLeases(userId, page = 1, limit = 50) {
  const params = new URLSearchParams({
    page: page,
    limit: limit
  });
  const res = await fetch(`/v1/admin/users/${encodeURIComponent(userId)}/leases?${params.toString()}`);
  if (res.status === 403 || res.status === 401) {
    throw new Error('Permission denied');
  }
  if (!res.ok) throw new Error('Failed to fetch leases');
  const data = await res.json();

  // 处理新的分页格式
  if (data.data && Array.isArray(data.data)) {
    return {
      leases: data.data,
      total: data.total || 0,
      page: data.page || 1,
      totalPages: data.totalPages || 1
    };
  }
  // 兼容旧格式
  if (Array.isArray(data)) {
    return {
      leases: data,
      total: data.length,
      page: 1,
      totalPages: 1
    };
  }
  return {
    leases: [],
    total: 0,
    page: 1,
    totalPages: 1
  };
}

async function fetchUser(userId) {
  const res = await fetch(`/v1/admin/users/${encodeURIComponent(userId)}`);
  if (!res.ok) return null;
  return await res.json();
}

function formatAddress(addr) {
  if (!addr) return '';
  if (typeof addr === 'string') return addr;
  const parts = [addr.street, addr.unit, addr.city, addr.prov, addr.country, addr.zipCode || addr.zip];
  return parts.filter(Boolean).join(', ');
}

function groupLeasesByProperty(leases) {
  const map = {};
  for (const lease of leases) {
    const addrStr = lease.propertyAddress || '';
    const groupKey = addrStr ? `${addrStr}${lease.propertyName ? ', ' + lease.propertyName : ''}` : (lease.propertyName || '-');
    if (!map[groupKey]) map[groupKey] = { property: groupKey, address: lease.propertyAddress, leases: [], totalOwing: 0 };
    map[groupKey].leases.push(lease);
    map[groupKey].totalOwing += lease.owingBalance || 0;
  }
  // 转为数组并排序
  let arr = Object.values(map);
  arr.sort((a, b) => sortAsc ? (a.property.localeCompare(b.property)) : (b.property.localeCompare(a.property)));
  return arr;
}

function renderTable() {
  const tbody = document.getElementById('lease-group-tbody');
  tbody.innerHTML = '';
  // 分页
  const start = (currentPage - 1) * PAGE_SIZE;
  const end = start + PAGE_SIZE;
  const pageGroups = filteredGroups.slice(start, end);
  if (pageGroups.length === 0) {
    tbody.innerHTML = '<tr><td colspan="5" class="p-4 text-center text-gray-400">No leases found</td></tr>';
    return;
  }
  for (const group of pageGroups) {
    // 分组行
    const groupId = 'group-' + btoa(encodeURIComponent(group.property)).replace(/=/g, '');
    tbody.innerHTML += `
      <tr class="bg-gray-50 cursor-pointer group-row" data-group="${groupId}">
        <td class="p-4 font-semibold text-black">
          <span class="mr-2 text-lg">&#9654;</span>
          ${group.property} <span class="text-gray-500 ml-2">(${group.leases.length})</span>
        </td>
        <td></td>
        <td></td>
        <td></td>
        <td class="p-4 pl-12 font-bold ${group.totalOwing > 0 ? 'text-red-500' : ''}">$${group.totalOwing.toFixed(2)}</td>
      </tr>
    `;
    // 子lease行
    for (const lease of group.leases) {
      const tenantNames = (lease.currentTenants && lease.currentTenants.length)
        ? lease.currentTenants.map(t => `${t.firstName || ''} ${t.lastName || ''}`.trim()).join(', ')
        : '-';
      const unit = lease.roomName || '-';
      const lastPayment = lease.lastPaymentDate ? formatDate(lease.lastPaymentDate) : '-';
      const paymentStatus = lease.status ? (lease.status === 'active' ? 'On-Time' : lease.status.charAt(0).toUpperCase() + lease.status.slice(1)) : '-';
      const owing = lease.owingBalance != null ? `$${lease.owingBalance.toFixed(2)}` : '-';
      tbody.innerHTML += `<tr class="lease-row" data-group="${groupId}" data-lease-id="${lease.id}" style="display:none;">
        <td class="p-4 pl-12">${tenantNames}</td>
        <td class="p-4">${unit}</td>
        <td class="p-4">${lastPayment}</td>
        <td class="p-4">${paymentStatus}</td>
        <td class="p-4">${owing}</td>
      </tr>`;
    }
  }
  // 展开/收起分组，默认收起
  document.querySelectorAll('.group-row').forEach(row => {
    row.onclick = function() {
      const group = this.getAttribute('data-group');
      const rows = document.querySelectorAll(`.lease-row[data-group='${group}']`);
      const icon = this.querySelector('span');
      if (rows.length && rows[0].style.display !== 'none') {
        rows.forEach(r => r.style.display = 'none');
        if (icon) icon.innerHTML = '&#9654;';
      } else {
        rows.forEach(r => r.style.display = '');
        if (icon) icon.innerHTML = '&#9660;';
      }
    };
  });
  // 为每个lease行添加点击事件
  document.querySelectorAll('.lease-row').forEach(row => {
    row.onclick = function() {
      const leaseId = this.getAttribute('data-lease-id');
      if (leaseId) {
        window.location.href = `/pages/admin/lease_detail/index.html?lease_id=${encodeURIComponent(leaseId)}&id=${encodeURIComponent(userId)}`;
      }
    };
  });
}

function renderPagination() {
  const pagDiv = document.getElementById('admin-pagination');

  // 生成页码按钮
  let pageButtons = '';
  const maxPageBtn = 5;
  let start = Math.max(1, currentPage - 2);
  let end = Math.min(totalPages, start + maxPageBtn - 1);
  if (end - start < maxPageBtn - 1) {
    start = Math.max(1, end - maxPageBtn + 1);
  }

  if (start > 1) {
    pageButtons += `<button class="px-2 py-1 rounded border bg-white mx-1" data-page="1">1</button>`;
    if (start > 2) pageButtons += '<span class="mx-1 text-gray-400">...</span>';
  }
  for (let i = start; i <= end; i++) {
    if (i === currentPage) {
      pageButtons += `<button class="px-2 py-1 rounded border font-bold bg-slate-200 mx-1" disabled>${i}</button>`;
    } else {
      pageButtons += `<button class="px-2 py-1 rounded border bg-white mx-1" data-page="${i}">${i}</button>`;
    }
  }
  if (end < totalPages) {
    if (end < totalPages - 1) pageButtons += '<span class="mx-1 text-gray-400">...</span>';
    pageButtons += `<button class="px-2 py-1 rounded border bg-white mx-1" data-page="${totalPages}">${totalPages}</button>`;
  }

  pagDiv.innerHTML = `
    <div class="flex justify-between items-center gap-4 px-4 py-2 bg-white border border-gray-300 rounded-lg">
      <span class="text-sm text-gray-500">Page ${currentPage} of ${totalPages} (${totalRecords} total)</span>
      <div class="flex items-center gap-1">
        <button id="prevPage" class="bg-slate-100 border border-slate-300 rounded px-2 py-1" ${currentPage === 1 ? 'disabled' : ''}>&lt;</button>
        ${pageButtons}
        <button id="nextPage" class="bg-slate-100 border border-slate-300 rounded px-2 py-1" ${currentPage === totalPages ? 'disabled' : ''}>&gt;</button>
      </div>
    </div>
  `;

  // 绑定事件
  document.getElementById('prevPage').onclick = async function() {
    if (currentPage > 1) {
      currentPage--;
      await loadData();
    }
  };
  document.getElementById('nextPage').onclick = async function() {
    if (currentPage < totalPages) {
      currentPage++;
      await loadData();
    }
  };

  // 页码数字点击
  Array.from(pagDiv.querySelectorAll('button[data-page]')).forEach(btn => {
    btn.onclick = async function() {
      const page = parseInt(this.getAttribute('data-page'));
      if (page !== currentPage) {
        currentPage = page;
        await loadData();
      }
    };
  });
}

function formatDate(ts) {
  if (!ts) return '-';
  let d;
  if (typeof ts === 'string') {
    d = new Date(ts);
  } else if (typeof ts === 'number') {
    d = ts > 1e12 ? new Date(ts) : new Date(ts * 1000);
  } else {
    return '-';
  }
  if (isNaN(d.getTime())) return '-';
  return d.toISOString().slice(0, 10);
}

function doSearch() {
  const q = document.getElementById('search-input').value.trim().toLowerCase();
  if (!q) {
    filteredGroups = groupedLeases;
  } else {
    filteredGroups = groupedLeases.filter(g => {
      // 分别对property address和name做模糊匹配
      const arr = g.property.split(',');
      const addrStr = arr.slice(0, -1).join(',').toLowerCase();
      const nameStr = arr.length > 1 ? arr[arr.length - 1].toLowerCase() : '';
      return addrStr.includes(q) || nameStr.includes(q);
    });
  }
  currentPage = 1;
  renderTable();
  renderPagination();
}

// 获取当前user的property列表
async function fetchAdminProperties(userId) {
  const res = await fetch(`/v1/admin/users/${encodeURIComponent(userId)}/properties`);
  if (!res.ok) return [];
  return await res.json();
}

// 获取property的所有房间
async function fetchAdminRooms(propertyId) {
  const res = await fetch(`/v1/admin/properties/${encodeURIComponent(propertyId)}`);
  if (!res.ok) return [];
  const data = await res.json();
  return data.rooms || [];
}

// 打开弹窗时加载property下拉
async function initCreateLeaseModal() {
  const propertySelect = document.getElementById('property-select');
  const roomSelect = document.getElementById('room-select');
  if (!propertySelect) return;
  propertySelect.innerHTML = '<option value="">Loading properties...</option>';
  propertySelect.disabled = true;
  adminProperties = await fetchAdminProperties(userId);
  if (!adminProperties.length) {
    propertySelect.innerHTML = '<option value="">No properties found</option>';
    return;
  }
  propertySelect.innerHTML = '<option value="">Select a property</option>' + adminProperties.map(p => `<option value="${p.id}">${p.name || p.address?.street || p.id}</option>`).join('');
  propertySelect.disabled = false;
  // 监听property选择
  propertySelect.onchange = async function() {
    const propId = this.value;
    roomSelect.innerHTML = '<option value="">Loading units...</option>';
    roomSelect.disabled = true;
    if (!propId) {
      roomSelect.innerHTML = '<option value="">Select a property first</option>';
      return;
    }
    const rooms = await fetchAdminRooms(propId);
    const vacantRooms = rooms.filter(r => r.status === 'vacant');
    if (!vacantRooms.length) {
      roomSelect.innerHTML = '<option value="">No vacant unit available</option>';
      return;
    }
    roomSelect.innerHTML = '<option value="">Select a room</option>' + vacantRooms.map(r => `<option value="${r.id}">${r.name || r.id}</option>`).join('');
    roomSelect.disabled = false;
  };
}

// 校验必填项
function validateLeaseForm() {
  let valid = true;
  const requiredIds = ['property-select', 'room-select', 'start-date', 'rent-amount'];
  requiredIds.forEach(id => {
    const el = document.getElementById(id);
    if (!el || !el.value) {
      el?.classList.add('border-red-500');
      valid = false;
    } else {
      el.classList.remove('border-red-500');
    }
  });
  return valid;
}

// 绑定弹窗事件
function bindCreateLeaseModalEvents() {
  // 打开弹窗后初始化下拉
  setTimeout(initCreateLeaseModal, 0);
  // 事件委托：监听document上的弹窗按钮点击
  document.addEventListener('click', async function(e) {
    const target = e.target;
    if (target && target.id === 'create-lease-btn') {
      e.preventDefault();
      if (!validateLeaseForm()) {
        alert('Please fill in all required fields');
        return;
      }
      // 收集表单数据
      const propertyId = document.getElementById('property-select').value;
      const roomId = document.getElementById('room-select').value;
      const startDate = document.getElementById('start-date').value;
      const rentAmount = parseFloat(document.getElementById('rent-amount').value) || 0;
      const additionalFees = parseFloat(document.getElementById('additional-fees').value) || 0;
      const keyDeposit = parseFloat(document.getElementById('key-deposit').value) || 0;
      const rentDeposit = parseFloat(document.getElementById('rent-deposit').value) || 0;
      const otherDeposits = parseFloat(document.getElementById('other-deposits').value) || 0;
      // 取move-in日期的日作为rentDueDay
      let rentDueDay = 1;
      if (startDate) {
        // 避免时区问题：直接从日期字符串解析日期部分
        // 格式: "2025-05-01" -> 获取 "01" -> 转换为数字 1
        const dateParts = startDate.split('-');
        if (dateParts.length === 3) {
          const day = parseInt(dateParts[2], 10);
          if (!isNaN(day) && day >= 1 && day <= 31) {
            rentDueDay = day;
          }
        }
      }
      const leaseData = {
        userId: userId,
        propertyId: propertyId,
        roomId: roomId,
        startDate: startDate,
        rentDueDay: rentDueDay,
        rentAmount: rentAmount,
        additionalMonthlyFees: additionalFees,
        keyDeposit: keyDeposit,
        rentDeposit: rentDeposit,
        otherDeposits: otherDeposits,
        status: 'active'
      };
      // 提交到后端
      const btn = target;
      btn.disabled = true;
      btn.textContent = 'Creating...';
      try {
        const res = await fetch('/v1/admin/leases', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(leaseData)
        });
        if (!res.ok) {
          let msg = 'Create lease failed';
          try {
            const err = await res.json();
            if (err && err.error) msg = err.error;
          } catch {}
          throw new Error(msg);
        }
        document.getElementById('modal').innerHTML = '';
        // 刷新租约列表
        await loadData();
      } catch (err) {
        alert(err.message || 'Create lease failed');
      } finally {
        btn.disabled = false;
        btn.textContent = 'Create Lease';
      }
    }
  });
}

async function loadData() {
  try {
    const result = await fetchLeases(userId, currentPage, PAGE_SIZE);
    leases = result.leases;
    totalRecords = result.total;
    totalPages = result.totalPages;

    groupedLeases = groupLeasesByProperty(leases);
    filteredGroups = groupedLeases;
    renderTable();
    renderPagination();
  } catch (e) {
    document.getElementById('lease-group-tbody').innerHTML = `<tr><td colspan="5" class="p-4 text-center text-red-500">${e.message}</td></tr>`;
    document.getElementById('admin-pagination').innerHTML = '';
  }
}

window.onload = async function() {
  userId = getQueryParam('id');
  if (!userId) {
    alert('No user id.');
    return;
  }
  // 获取用户信息
  const user = await fetchUser(userId);
  userName = user ? user.username || user.email || user.id : userId;
  document.getElementById('lease-title').textContent = `Leases - ${userName}`;
  document.getElementById('back-btn').onclick = function() {
    window.location.href = `/pages/admin/user_detail/index.html?id=${encodeURIComponent(userId)}`;
  };
  await loadData();
  document.getElementById('search-btn').onclick = doSearch;
  document.getElementById('search-input').onkeydown = function(e) { if (e.key === 'Enter') doSearch(); };
  // 排序（仅property名，后续可扩展）
  document.querySelectorAll('th').forEach((th, idx) => {
    th.onclick = function() {
      // 只允许property列排序
      if (idx === 0) {
        sortAsc = !sortAsc;
        groupedLeases = groupLeasesByProperty(leases);
        doSearch();
      }
    };
  });

  // 新增：点击"Create Lease"弹出弹窗
  document.getElementById('create-lease-btn').onclick = function() {
    fetch('./modal-create-lease.html')
      .then(res => res.text())
      .then(html => {
        document.getElementById('modal')?.remove();
        const modalDiv = document.createElement('div');
        modalDiv.id = 'modal';
        modalDiv.innerHTML = html;
        document.body.appendChild(modalDiv);
        bindCreateLeaseModalEvents();
      });
  };
}; 