<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Lease Record - Admin</title>
    <link href="/styles/output.css" rel="stylesheet" />

</head>
<body class="min-h-screen bg-gray-200">
  <div class="flex min-h-screen">
    <main class="w-full md:px-8 bg-slate-100 pb-20">
      <!-- Sticky bar-->
      <div class="fixed bottom-0 right-0 bg-white border w-full z-50">
        <div class="flex justify-between py-4 px-2 md:px-8">
          <button id="download-metro2" class="bg-white border text-black px-4 py-2 rounded hover:bg-gray-50">
            <img src="/assets/download.svg" alt="" class="w-4 h-4" />
          </button>
          <button id="save-lease-btn" class="bg-slate-800 text-white px-4 py-2 rounded">Save Changes</button>
        </div>
      </div>
      <!-- Page Content-->
      <div class="max-w-[1440px] w-full mx-auto px-4">
        <div class="w-full mt-20 md:mt-8 pb-0">
          <a class="text-sm underline" href="../lease_all/index.html" id="back-btn">Back To Leases</a>
        </div>
        <div class="w-full mt-8 pb-0">
          <h1 class="text-2xl">Lease Record</h1>
        </div>
        <!-- Lease Details-->
        <div class="flex flex-col lg:flex-row bg-white rounded border mt-4 w-full p-2 md:p-6 text-sm">
          <div id="lease-detail-info" class="flex-1"></div>
          <div class="flex flex-wrap flex-1 lg:ml-8 mt-8 lg:mt-0 w-full h-full max-w-md">
            <div class="flex flex-col bg-gray-200 p-4 rounded w-full mb-4 gap-4">
              <div class="flex justify-between items-center mb-2">
                <span class="font-bold">Lease Documents</span>
                <span id="document-count" class="text-sm text-gray-600">0 files</span>
              </div>
              <div id="file-download" class="space-y-2 max-h-40 overflow-y-auto">
                <div class="py-8 flex flex-col items-center justify-center w-full border-2 border-dashed rounded-md border-gray-500 gap-2">
                  <span class="text-gray-500 text-sm">No files uploaded</span>
                </div>
              </div>
            </div>
            <div class="flex bg-gray-200 p-4 rounded w-full flex-wrap">
              <span class="font-bold w-full">Options</span>
              <div class="flex flex-col gap-4 py-4 w-full">
                <div class="flex items-center justify-between w-full">
                  <label for="rent-reporting" class="flex items-center gap-2 font-medium text-gray-700 w-full">
                    Rent Reporting
                    <div class="group relative inline-block">
                      <img src="/assets/help-circle.svg" alt="" class="hover:cursor-help" />
                      <span class="invisible group-hover:visible opacity-0 group-hover:opacity-100 border border-gray-300 transition bg-white text-black text-sm rounded-md p-2 absolute z-50 -top-16 left-1/2 -translate-x-1/2 w-48 text-center">
                        Enable rent reporting to help tenants build their credit score
                      </span>
                    </div>
                  </label>
                  <label class="relative inline-flex items-center cursor-pointer">
                    <input type="checkbox" class="sr-only peer" />
                    <div class="w-11 h-6 bg-gray-100 border border-gray-300 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                  </label>
                </div>
                <div class="flex items-center justify-between w-full">
                  <label for="auto-pay" class="flex items-center gap-2 font-medium text-gray-700 w-full">
                    Auto-Pay
                    <div class="group relative inline-block">
                      <img src="/assets/help-circle.svg" alt="" class="hover:cursor-help" />
                      <span class="invisible group-hover:visible opacity-0 group-hover:opacity-100 border border-gray-300 transition bg-white text-black text-sm rounded-md p-2 absolute z-50 -top-16 left-1/2 -translate-x-1/2 w-48 text-center">
                        Automatically creates a rent log at the start of each period. You can edit or delete it if needed.
                      </span>
                    </div>
                  </label>
                  <label class="relative inline-flex items-center cursor-pointer">
                    <input type="checkbox" class="sr-only peer" />
                    <div class="w-11 h-6 bg-gray-100 border border-gray-300 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                  </label>
                </div>
              </div>
            </div>
          </div>
        </div>
        <!-- Tenants & Payments-->
        <div class="flex flex-col lg:flex-row w-full mt-8 gap-8">
          <div class="w-full lg:w-1/2">
            <div class="w-full inline-flex justify-between mb-2 items-baseline">
              <h2 class="text-lg font-bold w-full">Tenants</h2>
            </div>

            <!-- Tenant Type Toggle -->
            <div class="w-full mb-2">
              <div class="min-h-[32px] flex items-center">
                <button
                  id="tenant-toggle-btn-admin-view"
                  class="text-sm text-gray-600 hover:text-gray-800 underline cursor-pointer"
                >
                  Show past tenants
                </button>
              </div>
            </div>

            <div class="w-full">
              <div id="tenant-list">
                <div class="flex items-center justify-center p-8">
                  <span>Loading tenants...</span>
                </div>
              </div>
            </div>
          </div>
          <div class="flex flex-wrap w-full lg:w-1/2 max-h-fit">
            <div class="w-full inline-flex justify-between mb-2 items-baseline">
              <h2 class="text-lg font-bold w-full">Payments</h2>
            </div>

            <!-- Spacer to align with tenant toggle area -->
            <div class="w-full mb-2">
              <div class="min-h-[32px] flex items-center"></div> <!-- Same height as tenant toggle buttons -->
            </div>

            <div class="w-full">
              <div id="payment-list">
                <div class="flex items-center justify-center p-8">
                  <span>Loading payments...</span>
                </div>
              </div>
            </div>
          </div>
        </div>
        <!-- Notes-->
        <div class="w-full mt-8">
          <label class="w-full" for="unit" required>
            <h2 class="text-lg font-semibold text-gray-900">Tenant Notes</h2>
            <p class="text-sm text-gray-500 mb-4">A place to keep notes for internal purposes. Visible to only you and your team.</p>
          </label>
          <textarea class="p-4 w-full border border-gray-300 rounded-md focus:outline-none h-48 resize-none" id="unit" placeholder="Add notes about the tenant here"></textarea>
        </div>
        <!-- Delete-->
        <div class="w-full mt-8 mb-4">
          <h2 class="text-lg font-semibold text-gray-900 mb-4">Delete Lease</h2>
          <button id="delete-lease-btn" class="bg-slate-800 hover:bg-slate-900 transition text-white px-4 py-2 rounded text-sm text-nowrap mb-32">Delete Lease</button>
        </div>
      </div>
    </main>
  </div>
  <div id="modal" class=""></div>
  <script type="module" src="./main.js"></script>
</body>
</html> 