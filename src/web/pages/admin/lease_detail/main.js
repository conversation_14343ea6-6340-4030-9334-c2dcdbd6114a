// src/web/pages/admin/lease_detail/main.js

// Admin view tenant切换相关变量
let currentTenantViewAdminView = 'current'; // 'current' 或 'past'
let currentLeaseDataAdminView = null; // 存储当前lease数据

function getLeaseIdFromUrl() {
  const params = new URLSearchParams(window.location.search);
  return params.get('lease_id');
}

function getUserIdFromUrl() {
  const params = new URLSearchParams(window.location.search);
  return params.get('id');
}

async function fetchLeaseDetail(leaseId) {
  const res = await fetch(`/v1/admin/leases/${encodeURIComponent(leaseId)}`);
  if (!res.ok) throw new Error('Failed to fetch lease detail');
  return await res.json();
}

async function fetchPayments(leaseId) {
  const res = await fetch(`/v1/admin/leases/${encodeURIComponent(leaseId)}/payments`);
  if (!res.ok) throw new Error('Failed to fetch payments');
  return await res.json();
}

function renderLeaseDetail(lease) {
  // Property Details
  const html = `
    <div class="flex flex-col gap-4">
      <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label class="block text-gray-700 mb-1">Property</label>
          <input class="w-full border rounded p-2 bg-gray-100" value="${lease.propertyAddress || ''}" disabled />
        </div>
        <div>
          <label class="block text-gray-700 mb-1">Unit</label>
          <input class="w-full border rounded p-2 bg-gray-100" value="${lease.roomName || ''}" disabled />
        </div>
        <div>
          <label class="block text-gray-700 mb-1">Rent Due</label>
          <input id="lease-rent-due" class="w-full border rounded p-2" type="number" min="1" max="31" value="${lease.rentDueDay || ''}" />
        </div>
        <div>
          <label class="block text-gray-700 mb-1">Monthly Rent</label>
          <input id="lease-rent-amount" class="w-full border rounded p-2" type="number" min="0" step="0.01" value="${lease.rentAmount || 0}" />
        </div>
        <div>
          <label class="block text-gray-700 mb-1">Additional Fees</label>
          <input id="lease-additional-fees" class="w-full border rounded p-2" type="number" min="0" step="0.01" value="${lease.additionalMonthlyFees || 0}" />
        </div>
        <div>
          <label class="block text-gray-700 mb-1">Lease Status</label>
          <input id="lease-status" class="w-full border rounded p-2" value="${lease.status || ''}" />
        </div>
        <div>
          <label class="block text-gray-700 mb-1">Key Deposit</label>
          <input id="lease-key-deposit" class="w-full border rounded p-2" type="number" min="0" step="0.01" value="${lease.keyDeposit || 0}" />
        </div>
        <div>
          <label class="block text-gray-700 mb-1">Rent Deposit</label>
          <input id="lease-rent-deposit" class="w-full border rounded p-2" type="number" min="0" step="0.01" value="${lease.rentDeposit || 0}" />
        </div>
        <div>
          <label class="block text-gray-700 mb-1">Other Deposits</label>
          <input id="lease-other-deposits" class="w-full border rounded p-2" type="number" min="0" step="0.01" value="${lease.otherDeposits || 0}" />
        </div>
        <div>
          <label class="block text-gray-700 mb-1">Start Date</label>
          <input id="lease-start-date" class="w-full border rounded p-2" type="date" value="${lease.startDate ? lease.startDate.slice(0,10) : ''}" />
        </div>
        <div>
          <label class="block text-gray-700 mb-1">End Date</label>
          <input id="lease-end-date" class="w-full border rounded p-2" type="date" value="${lease.endDate ? lease.endDate.slice(0,10) : ''}" />
        </div>
      </div>
    </div>
  `;
  document.getElementById('lease-detail-info').innerHTML = html;

  // Options
  document.querySelector('input[type="checkbox"][class*="peer"]').checked = !!lease.rentReporting;
  document.querySelectorAll('input[type="checkbox"][class*="peer"]')[1].checked = !!lease.autoPay;
}

function renderTenants(tenants) {
  window.currentTenants = tenants || [];
  const list = tenants && tenants.length ? tenants.map(t => `
    <div class="flex items-center justify-between border-b py-2">
      <span>${[t.firstName, t.lastName].filter(Boolean).join(' ') || '-'}</span>
      <span class="text-gray-500 text-sm">${t.email || ''}</span>
    </div>
  `).join('') : '<div class="p-4 text-gray-400">No tenants</div>';
  document.getElementById('tenant-list').innerHTML = list;
}

// Admin view专用的tenant渲染函数
function renderTenantsAdminView() {
  if (!currentLeaseDataAdminView) return;

  const tenants = currentTenantViewAdminView === 'current'
    ? (currentLeaseDataAdminView.currentTenants || [])
    : (currentLeaseDataAdminView.pastTenants || []);

  const list = tenants && tenants.length ? tenants.map(t => `
    <div class="flex items-center justify-between border-b py-2">
      <span>${[t.firstName, t.lastName].filter(Boolean).join(' ') || '-'}</span>
      <span class="text-gray-500 text-sm">${t.email || ''}</span>
    </div>
  `).join('') : '<div class="p-4 text-gray-400">No tenants</div>';

  document.getElementById('tenant-list').innerHTML = list;
  window.currentTenants = tenants || [];
}

// 初始化admin view的tenant切换按钮
function initTenantToggleAdminView() {
  // 处理单个切换按钮点击
  document.addEventListener('click', function(e) {
    if (e.target.id === 'tenant-toggle-btn-admin-view') {
      // 切换状态
      currentTenantViewAdminView = currentTenantViewAdminView === 'current' ? 'past' : 'current';
      updateTenantToggleUIAdminView();
      renderTenantsAdminView();
    }
  });
}

// 更新admin view的tenant切换按钮UI状态
function updateTenantToggleUIAdminView() {
  const toggleBtn = document.getElementById('tenant-toggle-btn-admin-view');
  if (toggleBtn) {
    if (currentTenantViewAdminView === 'current') {
      toggleBtn.textContent = 'Show past tenants';
    } else {
      toggleBtn.textContent = 'Show current tenants';
    }
  }
}

function renderPayments(payments) {
  const list = payments && payments.length ? `
    <table class="w-full text-sm">
      <thead><tr><th class="text-left">Date</th><th class="text-left">Amount</th><th class="text-left">Remaining Balance</th></tr></thead>
      <tbody>
        ${payments.map(p => `<tr><td>${p.date || '-'}</td><td>$${p.amount != null ? p.amount.toFixed(2) : '-'}</td><td>${p.remainingBalance != null ? '$' + p.remainingBalance.toFixed(2) : '-'}</td></tr>`).join('')}
      </tbody>
    </table>
  ` : '<div class="p-4 text-gray-400">No payments</div>';
  document.getElementById('payment-list').innerHTML = list;
}

// 格式化文件大小
function formatFileSize(bytes) {
  if (bytes === 0) return '0 Bytes';
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

function renderFileDownload(data) {
  const fileDiv = document.getElementById('file-download');
  const countDiv = document.getElementById('document-count');
  if (!fileDiv) return;

  // 处理多文档显示（过滤掉deleted和final_deleted状态的文件）
  const activeDocuments = (data.documents || []).filter(doc =>
    doc.status !== 'deleted' && doc.status !== 'final_deleted'
  );

  if (activeDocuments.length > 0) {
    // 更新文档计数
    if (countDiv) {
      countDiv.textContent = `${activeDocuments.length} files`;
    }

    // 渲染文档列表
    const documentsHtml = activeDocuments.map(doc => `
      <a href="/v1/leases/download/${doc.id}" target="_blank" class="flex items-center gap-2 hover:bg-gray-100 bg-gray-50 px-3 py-1.5 rounded-md text-gray-700 block">
        <svg class="w-4 h-4 text-gray-500 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z" />
        </svg>
        <div class="flex-1 min-w-0">
          <div class="text-sm font-medium text-gray-900 truncate">${doc.fileName}</div>
          <div class="text-xs text-gray-500">${formatFileSize(doc.fileSize || 0)}</div>
        </div>
      </a>
    `).join('');
    fileDiv.innerHTML = documentsHtml;
  } else if (data.documents && data.documents.length > 0) {
    // 如果有文档但都是deleted/final_deleted状态，显示无文件
    if (countDiv) {
      countDiv.textContent = '0 files';
    }
    fileDiv.innerHTML = `
      <div class="py-8 flex flex-col items-center justify-center w-full border-2 border-dashed rounded-md border-gray-500 gap-2">
        <span class="text-gray-500 text-sm">No files uploaded</span>
      </div>
    `;
  } else {
    // 兼容旧的单文件显示
    const fileId = data.id || data.fileId || data.fileID;
    if (data.fileName && fileId) {
      if (countDiv) {
        countDiv.textContent = '1 file';
      }
      fileDiv.innerHTML = `
        <a href="/v1/leases/download/${fileId}" target="_blank" class="flex items-center gap-2 hover:bg-gray-100 bg-gray-50 px-3 py-1.5 rounded-md text-gray-700 block">
          <svg class="w-4 h-4 text-gray-500 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z" />
          </svg>
          <div class="flex-1 min-w-0">
            <div class="text-sm font-medium text-gray-900 truncate">${data.fileName}</div>
            <div class="text-xs text-gray-500">${formatFileSize(data.fileSize || 0)}</div>
          </div>
        </a>
      `;
    } else {
      if (countDiv) {
        countDiv.textContent = '0 files';
      }
      fileDiv.innerHTML = `
        <div class="py-8 flex flex-col items-center justify-center w-full border-2 border-dashed rounded-md border-gray-500 gap-2">
          <span class="text-gray-500 text-sm">No files uploaded</span>
        </div>
      `;
    }
  }
}

window.onload = async function() {
  const leaseId = getLeaseIdFromUrl();
  const userId = getUserIdFromUrl();
  if (!leaseId) {
    alert('No lease id.');
    return;
  }
  try {
    const lease = await fetchLeaseDetail(leaseId);
    renderLeaseDetail(lease);

    // 存储lease数据供admin view使用
    currentLeaseDataAdminView = lease;
    renderTenantsAdminView(); // 使用新的渲染函数

    // 拉取payments（如有接口）
    let payments = [];
    try {
      payments = await fetchPayments(leaseId);
    } catch (err) {
    }
    renderPayments(payments.items || payments || []);
    // Notes
    document.getElementById('unit').value = lease.notes || '';

    // ====== 只读处理 ======
    setTimeout(() => {
      // 所有input/textarea设为readonly
      document.querySelectorAll('input, textarea').forEach(el => {
        if (el.type !== 'file') {
          el.setAttribute('readonly', 'readonly');
          el.classList.add('bg-gray-100');
        }
      });
      // 所有checkbox设为disabled
      document.querySelectorAll('input[type="checkbox"]').forEach(el => {
        el.setAttribute('disabled', 'disabled');
      });
      // 隐藏delete按钮和说明
      const delBtn = document.getElementById('delete-lease-btn');
      if (delBtn) delBtn.style.display = 'none';
      const delTitle = Array.from(document.querySelectorAll('h2')).find(h2 => h2.textContent.includes('Delete Lease'));
      if (delTitle) delTitle.style.display = 'none';
    }, 0);
    // ====== 只读处理 END ======

    // 修改Back To Leases按钮跳转
    const backBtn = document.getElementById('back-btn');
    if (backBtn && userId) {
      backBtn.href = `/pages/admin/lease_all/index.html?id=${encodeURIComponent(userId)}`;
    }

    // 保存按钮事件
    const saveBtn = document.getElementById('save-lease-btn');
    if (saveBtn) {
      saveBtn.onclick = async function() {
      const updates = {
        rentAmount: parseFloat(document.getElementById('lease-rent-amount').value) || 0,
        additionalMonthlyFees: parseFloat(document.getElementById('lease-additional-fees').value) || 0,
        keyDeposit: parseFloat(document.getElementById('lease-key-deposit').value) || 0,
        rentDeposit: parseFloat(document.getElementById('lease-rent-deposit').value) || 0,
        otherDeposits: parseFloat(document.getElementById('lease-other-deposits').value) || 0,
        status: document.getElementById('lease-status').value,
        rentDueDay: parseInt(document.getElementById('lease-rent-due').value) || 1,
        autoPay: document.querySelectorAll('input[type="checkbox"][class*="peer"]')[1].checked,
        rentReporting: document.querySelector('input[type="checkbox"][class*="peer"]').checked,
        startDate: document.getElementById('lease-start-date').value,
        endDate: document.getElementById('lease-end-date').value,
      };
      try {
        const res = await fetch(`/v1/admin/leases/${encodeURIComponent(leaseId)}`, {
          method: 'PUT',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(updates)
        });
        if (!res.ok) throw new Error('Save failed');
        alert('Saved successfully');
        location.reload();
      } catch (err) {
        alert(err.message || 'Save failed');
      }
      };
    }

    // Add Tenant弹窗绑定（仅在按钮存在时）
    const addTenantBtn = document.getElementById('add-tenant-btn');
    if (addTenantBtn) {
      addTenantBtn.onclick = function() {
        fetch('modal-add-tenant.html')
          .then(res => res.text())
          .then(html => {
            document.getElementById('modal').innerHTML = html;
          });
      };
    }

    renderFileDownload(lease);

    // 初始化admin view的tenant切换功能
    initTenantToggleAdminView();
  } catch (e) {
    document.getElementById('lease-detail-info').innerText = e.message;
  }
};

// ========== Add Tenant Modal ========== //
function validateAdminTenantForm() {
  let valid = true;
  const requiredIds = [
    'first-name',
    'last-name',
    'email',
    'dateBirth',
  ];
  requiredIds.forEach(id => {
    const el = document.getElementById(id);
    if (!el.value.trim()) {
      el.classList.add('border-red-500');
      valid = false;
    } else {
      el.classList.remove('border-red-500');
    }
  });
  return valid;
}

document.addEventListener('click', async function(e) {
  if (e.target && e.target.id === 'submit-add-tenant') {
    e.preventDefault();
    if (!validateAdminTenantForm()) {
      alert('Please fill in all required fields');
      return;
    }
    const leaseId = getLeaseIdFromUrl();
    const tenantData = {
      firstNm: document.getElementById('first-name').value.trim(),
      midNm: document.getElementById('middle-name').value.trim(),
      lastNm: document.getElementById('last-name').value.trim(),
      email: document.getElementById('email').value.trim(),
      dob: document.getElementById('dateBirth').value,
      phoneNumber: document.getElementById('phone-number').value.trim(),
      notes: document.getElementById('unit').value.trim(),
      leaseId: leaseId,
      usrId: getUserIdFromUrl(),
      ssn: document.getElementById('SIN').value.trim(),
    };
    try {
      const res = await fetch(`/v1/admin/leases/${encodeURIComponent(leaseId)}/tenants`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(tenantData)
      });
      const resText = await res.text();
      if (!res.ok) throw new Error('Failed to add tenant: ' + resText);
      alert('Tenant added successfully');
      document.getElementById('modal').innerHTML = '';
      location.reload();
    } catch (err) {
      alert(err.message || 'Failed to add tenant');
    }
  }
  // Tenant行点击（div结构）
  if (e.target && e.target.closest('#tenant-list .flex.items-center.justify-between')) {
    const div = e.target.closest('#tenant-list .flex.items-center.justify-between');
    if (div) {
      // 假设租客数据已缓存到window.currentTenants，按顺序渲染
      const idx = Array.from(document.querySelectorAll('#tenant-list .flex.items-center.justify-between')).indexOf(div);
      const tenant = (window.currentTenants && window.currentTenants[idx]) || {};
      // 拉取modal-add-tenant.html结构
      fetch('/pages/lease_detail/modal-add-tenant.html')
        .then(res => res.text())
        .then(html => {
          html = html.replace('Add Tenant', 'Tenant Detail');
          document.getElementById('modal').innerHTML = html;
          setTimeout(() => {
            // 填充表单
            document.getElementById('first-name').value = tenant.firstName || '';
            document.getElementById('middle-name').value = tenant.middleName || '';
            document.getElementById('last-name').value = tenant.lastName || '';
            document.getElementById('email').value = tenant.email || '';
            document.getElementById('phone-number').value = tenant.phoneNumber || '';
            // 删除notes、生日、sin
            const sinDiv = document.getElementById('SIN')?.parentElement;
            if (sinDiv) sinDiv.remove();
            const dobDiv = document.getElementById('dateBirth')?.parentElement?.parentElement;
            if (dobDiv) dobDiv.remove();
            // 优化：循环等待label出现后再删除
            function removeNotesLabel() {
              const notesLabel = document.querySelector('label[for="unit"]');
              if (notesLabel) {
                notesLabel.remove();
              } else {
                setTimeout(removeNotesLabel, 30);
              }
            }
            removeNotesLabel();
            const notesArea = document.getElementById('unit');
            if (notesArea) notesArea.remove();
            // 只保留Cancel按钮，删除Save按钮
            const saveBtn = document.getElementById('submit-add-tenant');
            if (saveBtn) saveBtn.remove();
            // 所有input设为readonly
            ['first-name','middle-name','last-name','email','phone-number'].forEach(id => {
              const el = document.getElementById(id);
              if (el) {
                el.setAttribute('readonly', 'readonly');
                el.classList.add('bg-gray-100');
              }
            });
          }, 0);
        });
    }
  }
  // Payment行点击
  if (e.target && e.target.closest('#payment-list tr')) {
    const tr = e.target.closest('#payment-list tr');
    if (tr && !tr.classList.contains('text-center')) {
      // 获取payment数据（通过表格内容）
      const tds = tr.querySelectorAll('td');
      const date = tds[0]?.textContent.trim() || '';
      const amount = tds[1]?.textContent.trim().replace('$','') || '';
      const balance = tds[2]?.textContent.trim().replace('$','') || '';
      fetch('/pages/lease_detail/modal-add-payment.html')
        .then(res => res.text())
        .then(html => {
          html = html.replace('Add Payment', 'Payment Detail');
          document.getElementById('modal').innerHTML = html;
          setTimeout(() => {
            // 填充表单
            document.getElementById('payment-date').value = date ? (new Date(date).toISOString().slice(0,10)) : '';
            document.getElementById('amount-paid').value = amount;
            document.getElementById('remaining-balance').value = balance;
            // 删除Save按钮
            const saveBtn = document.getElementById('submit-add-payment');
            if (saveBtn) saveBtn.remove();
            // 所有input设为readonly
            ['payment-date','amount-paid','remaining-balance'].forEach(id => {
              const el = document.getElementById(id);
              if (el) {
                el.setAttribute('readonly', 'readonly');
                el.classList.add('bg-gray-100');
              }
            });
          }, 0);
        });
    }
  }
}); 