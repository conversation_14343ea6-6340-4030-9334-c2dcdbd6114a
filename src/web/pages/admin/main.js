const API_URL = '/v1/admin/users';
const PAGE_SIZE = 10;

let currentPage = 1;
let total = 0;
let users = [];
let searchKeyword = '';
let sortField = '';
let sortOrder = 'asc'; // 'asc' or 'desc'

async function fetchUsers(page = 1, keyword = '') {
  // 传递分页和搜索参数
  const params = new URLSearchParams({
    page: page,
    limit: PAGE_SIZE,
    keyword: keyword
  });
  const res = await fetch(`${API_URL}?${params.toString()}`);
  if (res.status === 403) {
    alert('Permission denied: Only admin can view all users.');
    return { users: [], total: 0 };
  }
  if (!res.ok) return { users: [], total: 0 };

  const data = await res.json();
  // 处理新的分页格式
  if (data.data && Array.isArray(data.data)) {
    return {
      users: data.data,
      total: data.total || 0,
      page: data.page || 1,
      totalPages: data.totalPages || 1
    };
  }
  // 兼容旧格式
  if (Array.isArray(data)) {
    return { users: data, total: data.length };
  }
  return { users: [], total: 0 };
}

function sortUsers(users, field, order) {
  return users.slice().sort((a, b) => {
    let va = a[field] || '';
    let vb = b[field] || '';
    if (typeof va === 'string') va = va.toLowerCase();
    if (typeof vb === 'string') vb = vb.toLowerCase();
    if (va < vb) return order === 'asc' ? -1 : 1;
    if (va > vb) return order === 'asc' ? 1 : -1;
    return 0;
  });
}

function filterUsers(users, keyword) {
  if (!keyword) return users;
  keyword = keyword.toLowerCase();
  return users.filter(u =>
    (u.id && u.id.toLowerCase().includes(keyword)) ||
    (u.email && u.email.toLowerCase().includes(keyword)) ||
    (u.username && u.username.toLowerCase().includes(keyword))
  );
}

function renderTable(users) {
  const tbody = document.getElementById('userTableBody');
  tbody.innerHTML = '';
  if (!users.length) {
    tbody.innerHTML = '<tr><td colspan="6" class="text-center p-4 text-gray-400">No data</td></tr>';
    return;
  }
  // 后端已处理分页和搜索，直接渲染
  let displayUsers = users;
  if (sortField) {
    displayUsers = sortUsers(users, sortField, sortOrder);
  }
  displayUsers.forEach(user => {
    // Account Type 处理
    let accountType = user.accountType ? user.accountType.toLowerCase() : '';
    let accountTypeLabel = '';
    if (accountType === 'vip') {
      accountTypeLabel = '<span class="inline-block px-4 py-1 rounded-xl bg-indigo-50 text-indigo-600 font-medium">VIP Account</span>';
    } else {
      accountTypeLabel = '<span class="inline-block px-4 py-1 rounded-xl bg-gray-100 text-gray-900 font-medium">Basic Account</span>';
    }
    // Status 处理
    let statusLabel = '';
    if (user.status === 'active') {
      statusLabel = '<span class="text-green-600">Active</span>';
    } else {
      statusLabel = '<span class="text-red-600 font-semibold">Suspended</span>';
    }
    tbody.innerHTML += `
      <tr class="bg-white hover:bg-slate-100 text-left group cursor-pointer text-gray-700" data-user-id="${user.id}">
        <td class="p-4"><div class="overflow-x-auto whitespace-nowrap max-w-[140px] scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100 rounded">${user.id || ''}</div></td>
        <td class="p-4"><div class="overflow-x-auto whitespace-nowrap max-w-[140px] scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100 rounded">${user.username || ''}</div></td>
        <td class="p-4"><div class="overflow-x-auto whitespace-nowrap max-w-[200px] scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100 rounded">${user.email || ''}</div></td>
        <td class="p-4"><div class="overflow-x-auto whitespace-nowrap max-w-[140px] scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100 rounded">${accountTypeLabel}</div></td>
        <td class="p-4"><div class="overflow-x-auto whitespace-nowrap max-w-[140px] scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100 rounded">${statusLabel}</div></td>
        <td class="p-4"><div class="overflow-x-auto whitespace-nowrap max-w-[180px] scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100 rounded">${user.organizationId || ''}</div></td>
      </tr>
    `;
  });
  // 行点击跳转
  Array.from(tbody.querySelectorAll('tr[data-user-id]')).forEach(tr => {
    tr.onclick = function() {
      const userId = this.getAttribute('data-user-id');
      window.location.href = `/pages/admin/user_detail/index.html?id=${encodeURIComponent(userId)}`;
    };
  });
}

function renderPagination() {
  const totalPages = Math.max(1, Math.ceil(total / PAGE_SIZE));
  const info = document.getElementById('admin-pagination');

  // 页码按钮生成逻辑
  let pageButtons = '';
  const maxPageBtn = 5; // 最多显示5个页码按钮
  let start = Math.max(1, currentPage - 2);
  let end = Math.min(totalPages, start + maxPageBtn - 1);
  if (end - start < maxPageBtn - 1) {
    start = Math.max(1, end - maxPageBtn + 1);
  }

  if (start > 1) {
    pageButtons += `<button class="px-2 py-1 rounded border bg-white mx-1" data-page="1">1</button>`;
    if (start > 2) pageButtons += '<span class="mx-1 text-gray-400">...</span>';
  }
  for (let i = start; i <= end; i++) {
    if (i === currentPage) {
      pageButtons += `<button class="px-2 py-1 rounded border font-bold bg-slate-200 mx-1" disabled>${i}</button>`;
    } else {
      pageButtons += `<button class="px-2 py-1 rounded border bg-white mx-1" data-page="${i}">${i}</button>`;
    }
  }
  if (end < totalPages) {
    if (end < totalPages - 1) pageButtons += '<span class="mx-1 text-gray-400">...</span>';
    pageButtons += `<button class="px-2 py-1 rounded border bg-white mx-1" data-page="${totalPages}">${totalPages}</button>`;
  }

  info.innerHTML = `
    <div class="flex justify-between items-center gap-4 px-4 py-2 bg-white border border-gray-300 rounded-lg">
      <span class="text-sm text-gray-500">Page ${currentPage} of ${totalPages}</span>
      <div class="flex items-center gap-1">
        <button id="prevPage" class="bg-slate-100 border border-slate-300 rounded px-2 py-1" ${currentPage === 1 ? 'disabled' : ''}>&lt;</button>
        ${pageButtons}
        <button id="nextPage" class="bg-slate-100 border border-slate-300 rounded px-2 py-1" ${currentPage === totalPages ? 'disabled' : ''}>&gt;</button>
      </div>
    </div>
  `;
  document.getElementById('prevPage').onclick = async function() {
    if (currentPage > 1) {
      currentPage--;
      await loadData();
    }
  };
  document.getElementById('nextPage').onclick = async function() {
    if (currentPage < totalPages) {
      currentPage++;
      await loadData();
    }
  };
  // 页码数字点击
  Array.from(info.querySelectorAll('button[data-page]')).forEach(btn => {
    btn.onclick = async function() {
      const page = parseInt(this.getAttribute('data-page'));
      if (page !== currentPage) {
        currentPage = page;
        await loadData();
      }
    };
  });
}

// 表头排序事件绑定
function bindSortEvents() {
  const ths = document.querySelectorAll('thead th');
  const fieldMap = ['id', 'username', 'email', 'accountType', 'status', 'organizationId'];
  ths.forEach((th, idx) => {
    const img = th.querySelector('img');
    if (img) {
      img.style.cursor = 'pointer';
      img.onclick = function(e) {
        e.stopPropagation();
        if (sortField === fieldMap[idx]) {
          sortOrder = sortOrder === 'asc' ? 'desc' : 'asc';
        } else {
          sortField = fieldMap[idx];
          sortOrder = 'asc';
        }
        renderTable(users);
        renderPagination();
      };
    }
  });
}

document.getElementById('searchBtn').onclick = async function() {
  const input = document.getElementById('admin-search');
  searchKeyword = input.value.trim();
  currentPage = 1;
  await loadData(); // 重新从后端获取数据
};

// 添加 Enter 键搜索支持
document.getElementById('admin-search').onkeydown = function(e) {
  if (e.key === 'Enter') {
    document.getElementById('searchBtn').click();
  }
};

async function loadData() {
  const data = await fetchUsers(currentPage, searchKeyword);
  users = data.users || [];
  total = data.total || users.length;
  renderTable(users);
  renderPagination();
  bindSortEvents();
}

window.onload = loadData; 