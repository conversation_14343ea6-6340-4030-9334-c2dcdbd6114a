// admin/payment_all/main.js

function getQueryParam(name) {
  const url = new URL(window.location.href);
  return url.searchParams.get(name);
}

const PAGE_SIZE = 50; // 后端分页大小
let payments = [];
let filteredPayments = [];
let currentPage = 1;
let totalPages = 1;
let totalRecords = 0;
let userId = null;
let userName = '';
let sortKey = 'paymentId';
let sortAsc = true;
let billHst = [];
const TABLE_FIELDS = [
  { key: '_id', label: 'Payment ID' },
  { key: 'dt', label: 'Date' },
  { key: 'amt', label: 'Amount' },
  { key: 'method', label: 'Method' },
  { key: 'stat', label: 'Status' },
  { key: 'billingAddress', label: 'Billing Address' },
];

async function fetchPayments(userId, page = 1, limit = 50) {
  const params = new URLSearchParams({
    page: page,
    limit: limit
  });
  const res = await fetch(`/v1/admin/users/${encodeURIComponent(userId)}/payments?${params.toString()}`);
  if (res.status === 403 || res.status === 401) {
    throw new Error('Permission denied');
  }
  if (!res.ok) throw new Error('Failed to fetch payments');
  const data = await res.json();

  // 处理新的分页格式
  if (data.data && Array.isArray(data.data)) {
    return {
      payments: data.data,
      total: data.total || 0,
      page: data.page || 1,
      totalPages: data.totalPages || 1
    };
  }
  // 兼容旧格式
  if (Array.isArray(data)) {
    return {
      payments: data,
      total: data.length,
      page: 1,
      totalPages: 1
    };
  }
  return {
    payments: [],
    total: 0,
    page: 1,
    totalPages: 1
  };
}

async function fetchUser(userId) {
  const res = await fetch(`/v1/admin/users/${encodeURIComponent(userId)}`);
  if (!res.ok) return null;
  return await res.json();
}

async function fetchBillHst(userId) {
  const res = await fetch(`/v1/admin/users/${encodeURIComponent(userId)}/billhst`);
  if (res.status === 403 || res.status === 401) {
    throw new Error('Permission denied');
  }
  if (!res.ok) throw new Error('Failed to fetch bill history');
  return await res.json();
}

function formatDate(dt) {
  if (!dt) return '-';
  const d = new Date(dt);
  if (isNaN(d.getTime())) return '-';
  return d.toLocaleDateString('en-US', { year: 'numeric', month: 'short', day: '2-digit' });
}

function renderTable() {
  const tbody = document.getElementById('payment-list-tbody');
  tbody.innerHTML = '';
  // 分页
  const start = (currentPage - 1) * PAGE_SIZE;
  const end = start + PAGE_SIZE;
  const pagePays = filteredPayments.slice(start, end);
  if (pagePays.length === 0 && billHst.length === 0) {
    tbody.innerHTML = '<tr><td colspan="6" class="p-4 text-center text-gray-400">No payments found</td></tr>';
    return;
  }
  const combinedData = [...pagePays, ...billHst];
  for (const pay of combinedData) {
    tbody.innerHTML += `<tr>
      <td class="p-4">
        <div>${pay._id || pay.pmId || '-'}</div>
        <div class="text-xs text-gray-500">${pay.billingAddress || ''}</div>
      </td>
      <td class="p-4">${formatDate(pay.dt || pay.ts)}</td>
      <td class="p-4">${pay.status || pay.sts || '-'}</td>
      <td class="p-4">${pay.amt != null ? `$${(pay.amt * 0.01).toFixed(2)}` : '-'}</td>
      <td class="p-4">-</td>
      <td class="p-4">-</td>
    </tr>`;
  }
}

function renderPagination() {
  const pagDiv = document.getElementById('admin-pagination');

  // 生成页码按钮
  let pageButtons = '';
  const maxPageBtn = 5;
  let start = Math.max(1, currentPage - 2);
  let end = Math.min(totalPages, start + maxPageBtn - 1);
  if (end - start < maxPageBtn - 1) {
    start = Math.max(1, end - maxPageBtn + 1);
  }

  if (start > 1) {
    pageButtons += `<button class="px-2 py-1 rounded border bg-white mx-1" data-page="1">1</button>`;
    if (start > 2) pageButtons += '<span class="mx-1 text-gray-400">...</span>';
  }
  for (let i = start; i <= end; i++) {
    if (i === currentPage) {
      pageButtons += `<button class="px-2 py-1 rounded border font-bold bg-slate-200 mx-1" disabled>${i}</button>`;
    } else {
      pageButtons += `<button class="px-2 py-1 rounded border bg-white mx-1" data-page="${i}">${i}</button>`;
    }
  }
  if (end < totalPages) {
    if (end < totalPages - 1) pageButtons += '<span class="mx-1 text-gray-400">...</span>';
    pageButtons += `<button class="px-2 py-1 rounded border bg-white mx-1" data-page="${totalPages}">${totalPages}</button>`;
  }

  pagDiv.innerHTML = `
    <div class="flex justify-between items-center gap-4 px-4 py-2 bg-white border border-gray-300 rounded-lg">
      <span class="text-sm text-gray-500">Page ${currentPage} of ${totalPages} (${totalRecords} total)</span>
      <div class="flex items-center gap-1">
        <button id="prevPage" class="bg-slate-100 border border-slate-300 rounded px-2 py-1" ${currentPage === 1 ? 'disabled' : ''}>&lt;</button>
        ${pageButtons}
        <button id="nextPage" class="bg-slate-100 border border-slate-300 rounded px-2 py-1" ${currentPage === totalPages ? 'disabled' : ''}>&gt;</button>
      </div>
    </div>
  `;

  // 绑定事件
  document.getElementById('prevPage').onclick = async function() {
    if (currentPage > 1) {
      currentPage--;
      await loadData();
    }
  };
  document.getElementById('nextPage').onclick = async function() {
    if (currentPage < totalPages) {
      currentPage++;
      await loadData();
    }
  };

  // 页码数字点击
  Array.from(pagDiv.querySelectorAll('button[data-page]')).forEach(btn => {
    btn.onclick = async function() {
      const page = parseInt(this.getAttribute('data-page'));
      if (page !== currentPage) {
        currentPage = page;
        await loadData();
      }
    };
  });
}

function doSearch() {
  const q = document.getElementById('search-input').value.trim().toLowerCase();
  if (!q) {
    filteredPayments = payments;
  } else {
    filteredPayments = payments.filter(p => {
      const addrStr = (p.billingAddress || '').toLowerCase();
      return addrStr.includes(q);
    });
  }
  currentPage = 1;
  renderTable();
  renderPagination();
}

async function loadData() {
  try {
    const result = await fetchPayments(userId, currentPage, PAGE_SIZE);
    payments = result.payments;
    totalRecords = result.total;
    totalPages = result.totalPages;

    // 格式化billingAddress字段，拼接propertyName/roomName
    payments.forEach(p => {
      if (p.propertyName && p.roomName) {
        p.billingAddress = `${p.propertyName} - ${p.roomName} - ${p.billingAddress || ''}`;
      }
    });

    filteredPayments = payments;

    // 获取 billHst 数据（只在第一页时获取）
    if (currentPage === 1) {
      billHst = await fetchBillHst(userId);
      billHst = (billHst || []).map(bill => ({
        ts: bill.ts,
        amt: bill.amt,
        sts: bill.sts,
        pmId: bill.pmId
      }));
    }

    renderTable();
    renderPagination();
  } catch (e) {
    document.getElementById('payment-list-tbody').innerHTML = `<tr><td colspan="6" class="p-4 text-center text-red-500">${e.message}</td></tr>`;
    document.getElementById('admin-pagination').innerHTML = '';
  }
}

window.onload = async function() {
  userId = getQueryParam('id');
  if (!userId) {
    alert('No user id.');
    return;
  }
  // 获取用户信息
  const user = await fetchUser(userId);
  userName = user ? user.username || user.email || user.id : userId;
  document.getElementById('payment-title').textContent = `Payment History - ${userName}`;
  document.getElementById('back-btn').onclick = function() {
    window.location.href = `/pages/admin/user_detail/index.html?id=${encodeURIComponent(userId)}`;
  };
  await loadData();
  document.getElementById('search-btn').onclick = doSearch;
  document.getElementById('search-input').onkeydown = function(e) { if (e.key === 'Enter') doSearch(); };
  // 激活所有表头排序
  document.querySelectorAll('th').forEach((th, idx) => {
    th.onclick = function() {
      sortAsc = (sortKey !== TABLE_FIELDS[idx].key) ? true : !sortAsc;
      sortKey = TABLE_FIELDS[idx].key;
      payments.sort((a, b) => {
        let aKey = a[sortKey] || '';
        let bKey = b[sortKey] || '';
        // 金额特殊处理
        if (sortKey === 'amt') {
          aKey = Number(aKey) || 0;
          bKey = Number(bKey) || 0;
          return sortAsc ? aKey - bKey : bKey - aKey;
        }
        // 日期特殊处理
        if (sortKey === 'dt') {
          aKey = new Date(aKey).getTime() || 0;
          bKey = new Date(bKey).getTime() || 0;
          return sortAsc ? aKey - bKey : bKey - aKey;
        }
        return sortAsc ? String(aKey).localeCompare(String(bKey)) : String(bKey).localeCompare(String(aKey));
      });
      doSearch();
    };
  });
}; 