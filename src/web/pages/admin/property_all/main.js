// admin/property_all/main.js

function getQueryParam(name) {
  const url = new URL(window.location.href);
  return url.searchParams.get(name);
}

const PAGE_SIZE = 50; // 后端分页大小
let properties = [];
let filteredProperties = [];
let currentPage = 1;
let totalPages = 1;
let totalRecords = 0;
let userId = null;
let userName = '';
let sortKey = 'name';
let sortAsc = true;

const CANADA_PROVINCES = [
  { name: 'Alberta', code: 'AB' },
  { name: 'British Columbia', code: 'BC' },
  { name: 'Manitoba', code: 'MB' },
  { name: 'New Brunswick', code: 'NB' },
  { name: 'Newfoundland and Labrador', code: 'NL' },
  { name: 'Nova Scotia', code: 'NS' },
  { name: 'Ontario', code: 'ON' },
  { name: 'Prince Edward Island', code: 'PE' },
  { name: 'Quebec', code: 'QC' },
  { name: 'Saskatchewan', code: 'SK' },
  { name: 'Northwest Territories', code: 'NT' },
  { name: '<PERSON><PERSON>vu<PERSON>', code: 'NU' },
  { name: 'Yukon', code: 'YT' }
];

async function fetchProperties(userId, page = 1, limit = 50) {
  const params = new URLSearchParams({
    page: page,
    limit: limit
  });
  const res = await fetch(`/v1/admin/users/${encodeURIComponent(userId)}/properties?${params.toString()}`);
  if (res.status === 403 || res.status === 401) {
    throw new Error('Permission denied');
  }
  if (!res.ok) throw new Error('Failed to fetch properties');
  const data = await res.json();

  // 处理新的分页格式
  if (data.data && Array.isArray(data.data)) {
    return {
      properties: data.data,
      total: data.total || 0,
      page: data.page || 1,
      totalPages: data.totalPages || 1
    };
  }
  // 兼容旧格式
  if (Array.isArray(data)) {
    return {
      properties: data,
      total: data.length,
      page: 1,
      totalPages: 1
    };
  }
  return {
    properties: [],
    total: 0,
    page: 1,
    totalPages: 1
  };
}

async function fetchUser(userId) {
  const res = await fetch(`/v1/admin/users/${encodeURIComponent(userId)}`);
  if (!res.ok) return null;
  return await res.json();
}

function formatAddress(addr) {
  if (!addr) return '';
  if (typeof addr === 'string') return addr;
  const parts = [addr.street, addr.unit, addr.city, addr.prov, addr.country, addr.zipCode || addr.zip];
  return parts.filter(Boolean).join(', ');
}

function renderTable() {
  const tbody = document.getElementById('property-list-tbody');
  tbody.innerHTML = '';
  // 分页
  const start = (currentPage - 1) * PAGE_SIZE;
  const end = start + PAGE_SIZE;
  const pageProps = filteredProperties.slice(start, end);
  if (pageProps.length === 0) {
    tbody.innerHTML = '<tr><td colspan="4" class="p-4 text-center text-gray-400">No properties found</td></tr>';
    return;
  }
  for (const prop of pageProps) {
    const addrStr = formatAddress(prop.address);
    const propName = addrStr ? `${addrStr}${prop.name ? ', ' + prop.name : ''}` : (prop.name || '-');
    const owing = prop.owingBalance != null ? `$${prop.owingBalance.toFixed(2)}` : '-';
    tbody.innerHTML += `<tr class="property-row" data-id="${prop.id}">
      <td class="p-4">${propName}</td>
      <td class="p-4">${prop.totalRooms != null ? prop.totalRooms : '-'}</td>
      <td class="p-4">${prop.vacantRooms != null ? prop.vacantRooms : '-'}</td>
      <td class="p-4${prop.owingBalance > 0 ? ' text-red-500 font-bold' : ''}">${owing}</td>
    </tr>`;
  }
  // 行点击跳转
  document.querySelectorAll('.property-row').forEach(row => {
    row.onclick = function() {
      const id = this.getAttribute('data-id');
      window.location.href = `/pages/admin/property_detail/index.html?id=${encodeURIComponent(id)}&userId=${encodeURIComponent(userId)}`;
    };
  });
}

function renderPagination() {
  const pagDiv = document.getElementById('admin-pagination');

  // 生成页码按钮
  let pageButtons = '';
  const maxPageBtn = 5;
  let start = Math.max(1, currentPage - 2);
  let end = Math.min(totalPages, start + maxPageBtn - 1);
  if (end - start < maxPageBtn - 1) {
    start = Math.max(1, end - maxPageBtn + 1);
  }

  if (start > 1) {
    pageButtons += `<button class="px-2 py-1 rounded border bg-white mx-1" data-page="1">1</button>`;
    if (start > 2) pageButtons += '<span class="mx-1 text-gray-400">...</span>';
  }
  for (let i = start; i <= end; i++) {
    if (i === currentPage) {
      pageButtons += `<button class="px-2 py-1 rounded border font-bold bg-slate-200 mx-1" disabled>${i}</button>`;
    } else {
      pageButtons += `<button class="px-2 py-1 rounded border bg-white mx-1" data-page="${i}">${i}</button>`;
    }
  }
  if (end < totalPages) {
    if (end < totalPages - 1) pageButtons += '<span class="mx-1 text-gray-400">...</span>';
    pageButtons += `<button class="px-2 py-1 rounded border bg-white mx-1" data-page="${totalPages}">${totalPages}</button>`;
  }

  pagDiv.innerHTML = `
    <div class="flex justify-between items-center gap-4 px-4 py-2 bg-white border border-gray-300 rounded-lg">
      <span class="text-sm text-gray-500">Page ${currentPage} of ${totalPages} (${totalRecords} total)</span>
      <div class="flex items-center gap-1">
        <button id="prevPage" class="bg-slate-100 border border-slate-300 rounded px-2 py-1" ${currentPage === 1 ? 'disabled' : ''}>&lt;</button>
        ${pageButtons}
        <button id="nextPage" class="bg-slate-100 border border-slate-300 rounded px-2 py-1" ${currentPage === totalPages ? 'disabled' : ''}>&gt;</button>
      </div>
    </div>
  `;

  // 绑定事件
  document.getElementById('prevPage').onclick = async function() {
    if (currentPage > 1) {
      currentPage--;
      await loadData();
    }
  };
  document.getElementById('nextPage').onclick = async function() {
    if (currentPage < totalPages) {
      currentPage++;
      await loadData();
    }
  };

  // 页码数字点击
  Array.from(pagDiv.querySelectorAll('button[data-page]')).forEach(btn => {
    btn.onclick = async function() {
      const page = parseInt(this.getAttribute('data-page'));
      if (page !== currentPage) {
        currentPage = page;
        await loadData();
      }
    };
  });
}

function doSearch() {
  const q = document.getElementById('search-input').value.trim().toLowerCase();
  if (!q) {
    filteredProperties = properties;
  } else {
    filteredProperties = properties.filter(p => {
      const addrStr = formatAddress(p.address).toLowerCase();
      const nameStr = (p.name || '').toLowerCase();
      return addrStr.includes(q) || nameStr.includes(q);
    });
  }
  currentPage = 1;
  renderTable();
  renderPagination();
}

async function loadData() {
  try {
    const result = await fetchProperties(userId, currentPage, PAGE_SIZE);
    properties = result.properties;
    totalRecords = result.total;
    totalPages = result.totalPages;

    filteredProperties = properties;
    renderTable();
    renderPagination();
  } catch (e) {
    document.getElementById('property-list-tbody').innerHTML = `<tr><td colspan="4" class="p-4 text-center text-red-500">${e.message}</td></tr>`;
    document.getElementById('admin-pagination').innerHTML = '';
  }
}

window.onload = async function() {
  userId = getQueryParam('id');
  if (!userId) {
    alert('No user id.');
    return;
  }
  // 获取用户信息
  const user = await fetchUser(userId);
  userName = user ? user.username || user.email || user.id : userId;
  document.getElementById('property-title').textContent = `Properties - ${userName}`;
  document.getElementById('back-btn').onclick = function() {
    window.location.href = `/pages/admin/user_detail/index.html?id=${encodeURIComponent(userId)}`;
  };
  await loadData();
  document.getElementById('search-btn').onclick = doSearch;
  document.getElementById('search-input').onkeydown = function(e) { if (e.key === 'Enter') doSearch(); };
  // 排序（仅property name，后续可扩展）
  document.querySelectorAll('th').forEach((th, idx) => {
    th.onclick = function() {
      if (idx === 0) {
        sortAsc = !sortAsc;
        properties.sort((a, b) => {
          const addrA = formatAddress(a.address);
          const keyA = addrA ? `${addrA}${a.name ? ', ' + a.name : ''}` : (a.name || '-');
          const addrB = formatAddress(b.address);
          const keyB = addrB ? `${addrB}${b.name ? ', ' + b.name : ''}` : (b.name || '-');
          return sortAsc ? keyA.localeCompare(keyB) : keyB.localeCompare(keyA);
        });
        doSearch();
      }
    };
  });
};

// 弹窗HTML模板
const createPropertyModalHtml = `
<div class="fixed inset-0 z-50">
  <div class="fixed inset-0 bg-black bg-opacity-50 pointer-events-auto" onclick="document.getElementById('modal').innerHTML = ''"></div>
  <div class="fixed inset-0 flex items-center justify-center pointer-events-none">
    <div class="bg-white sm:rounded-lg shadow-xl sm:w-9/12 w-full h-full sm:max-h-[80vh] sm:min-h-[70vh] overflow-hidden pointer-events-auto relative">
      <div class="sticky top-0 px-6 py-4 border-b bg-white z-10 flex justify-between items-center">
        <div>
          <h3 class="text-lg font-semibold text-gray-900">Create Property</h3>
          <p>Ensure all information is 100% accurate.</p>
        </div>
        <button class="p-2 hover:bg-gray-100 rounded-full" onclick="document.getElementById('modal').innerHTML = ''">
          <svg class="w-5 h-5 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
      </div>
      <div class="px-6 py-4 overflow-auto" style="height: calc(100% - 140px)">
        <form id="create-property-form">
          <div class="flex flex-col gap-4">
            <div class="w-full">
              <label class="mb-2">Property Name <span class="text-red-500">*</span></label>
              <input class="p-2 py-4 w-full border border-gray-300 rounded-md focus:outline-none" id="modal-property-name" placeholder="Property name" required />
            </div>
            <div class="flex flex-col md:flex-row gap-4">
              <div class="flex-1">
                <label class="mb-2">Address <span class="text-red-500">*</span></label>
                <input class="p-2 py-4 w-full border border-gray-300 rounded-md focus:outline-none" id="modal-address" placeholder="Address" required />
              </div>
              <div class="flex-1">
                <label class="mb-2">City/Town <span class="text-red-500">*</span></label>
                <input class="p-2 py-4 w-full border border-gray-300 rounded-md focus:outline-none" id="modal-city" placeholder="City/Town" required />
              </div>
              <div class="flex-1">
                <label class="mb-2">Unit</label>
                <input class="p-2 py-4 w-full border border-gray-300 rounded-md focus:outline-none" id="modal-unit-number" placeholder="Unit" />
              </div>
            </div>
            <div class="flex flex-col md:flex-row gap-4">
              <div class="flex-1">
                <label class="mb-2">Country <span class="text-red-500">*</span></label>
                <input class="p-2 py-4 w-full border border-gray-300 rounded-md focus:outline-none" id="modal-country" placeholder="Country" value="Canada" required />
              </div>
              <div class="flex-1">
                <label class="mb-2">Province <span class="text-red-500">*</span></label>
                <select class="p-2 py-4 w-full border border-gray-300 rounded-md focus:outline-none" id="modal-province" required>
                  <option value="">Select Province</option>
                  ${CANADA_PROVINCES.map(p => `<option value="${p.code}">${p.name}</option>`).join('')}
                </select>
              </div>
              <div class="flex-1">
                <label class="mb-2">Postal Code <span class="text-red-500">*</span></label>
                <input class="p-2 py-4 w-full border border-gray-300 rounded-md focus:outline-none" id="modal-postal-code" placeholder="Postal Code" required />
              </div>
            </div>
            <div class="w-full mt-4">
              <label class="mb-2">Property Notes</label>
              <textarea class="p-2 w-full border border-gray-300 rounded-md focus:outline-none" id="modal-property-notes" placeholder="A place to keep notes for internal purposes. Visible to only you and your team."></textarea>
            </div>
          </div>
        </form>
      </div>
      <div class="absolute bottom-0 left-0 right-0 px-6 py-4 bg-gray-50 sm:rounded-b-lg flex justify-between gap-2 border-t">
        <button class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50" id="modal-cancel-btn">Cancel</button>
        <button id="modal-create-property-btn" class="px-4 py-2 text-sm font-medium text-white bg-slate-800 rounded-md hover:bg-slate-900" disabled>Create Property</button>
      </div>
    </div>
  </div>
</div>`;

// 绑定弹窗事件
window.addEventListener('DOMContentLoaded', function() {
  const modalDiv = document.getElementById('modal');
  document.getElementById('create-property-btn').onclick = function() {
    modalDiv.innerHTML = createPropertyModalHtml;
    bindCreatePropertyModal();
  };
});

function bindCreatePropertyModal() {
  const form = document.getElementById('create-property-form');
  const requiredIds = [
    'modal-property-name',
    'modal-address',
    'modal-city',
    'modal-country',
    'modal-province',
    'modal-postal-code'
  ];
  const createBtn = document.getElementById('modal-create-property-btn');
  const cancelBtn = document.getElementById('modal-cancel-btn');

  function checkValid() {
    let valid = true;
    for (const id of requiredIds) {
      const v = document.getElementById(id).value.trim();
      if (!v) valid = false;
    }
    createBtn.disabled = !valid;
  }
  requiredIds.forEach(id => {
    document.getElementById(id).addEventListener('input', checkValid);
  });
  checkValid();

  cancelBtn.onclick = function(e) {
    e.preventDefault();
    document.getElementById('modal').innerHTML = '';
  };

  createBtn.onclick = async function(e) {
    e.preventDefault();
    if (createBtn.disabled) return;
    // 收集数据
    const data = {
      name: document.getElementById('modal-property-name').value.trim(),
      address: {
        street: document.getElementById('modal-address').value.trim(),
        city: document.getElementById('modal-city').value.trim(),
        unit: document.getElementById('modal-unit-number').value.trim(),
        country: document.getElementById('modal-country').value.trim(),
        prov: document.getElementById('modal-province').value.trim().toUpperCase(),
        zipCode: document.getElementById('modal-postal-code').value.trim(),
      },
      notes: document.getElementById('modal-property-notes').value.trim(),
      userId: userId,
      propertyType: "",
      status: "",
      totalRooms: 0,
      vacantRooms: 0,
    };
    // 提交到后端
    try {
      const res = await fetch(`/v1/admin/properties`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(data)
      });
      if (!res.ok) throw new Error('Create failed');
      const newProperty = await res.json();
      // 创建默认房间
      if (newProperty && newProperty.id) {
        try {
          const roomData = {
            name: 'Default Room',
            status: 'vacant',
            type: 'studio',
            notes: ''
          };
          await fetch(`/v1/admin/properties/${encodeURIComponent(newProperty.id)}/rooms`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(roomData)
          });
        } catch (roomError) {
        }
      }
      document.getElementById('modal').innerHTML = '';
      // 刷新列表
      await loadData();
    } catch (err) {
      alert(err.message || 'Create failed');
    }
  };
} 