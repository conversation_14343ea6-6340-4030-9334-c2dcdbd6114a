<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Property Detail - Admin</title>
    <link href="/styles/output.css" rel="stylesheet" />
</head>
<body class="min-h-screen bg-gray-200">
  <div class="flex min-h-screen">
    <main class="w-full md:px-8 bg-slate-100 pb-20">
      <!-- Sticky bar-->
      <div class="fixed bottom-0 right-0 bg-white border w-full z-50">
        <div class="flex justify-between py-4 px-2 md:px-8">
          <button id="download-property-btn" class="bg-white border text-black px-4 py-2 rounded">
            <img src="/assets/download.svg" alt="" class="w-4 h-4" />
          </button>
          <button id="save-property-btn" class="bg-slate-800 text-white px-4 py-2 rounded">
            Save Changes
          </button>
        </div>
      </div>
      <!-- Page Content-->
      <div class="max-w-[1440px] w-full mx-auto md:mt-24 lg:mt-4">
        <div class="w-full md:mt-8 pb-0">
          <a class="text-sm underline" href="../property_all/index.html" id="back-btn">Back To Properties</a>
        </div>
        <div class="w-full mt-8 pb-0">
          <h1 class="text-2xl">Property Details</h1>
        </div>
        <!-- Property Details-->
        <div class="flex flex-col lg:flex-row bg-white rounded border mt-4 w-full p-2 md:p-6 text-sm">
          <div class="flex flex-1 w-full">
            <div id="property-detail-info" class="w-full"></div>
          </div>
          <div class="flex flex-col gap-2 w-full max-w-md lg:ml-8 mt-8 lg:mt-0">
            <div class="flex flex-col bg-gray-200 p-4 rounded w-full mb-4 gap-4">
              <div class="flex justify-between items-center mb-2">
                <span class="font-bold">Property Documents</span>
                <span id="document-count" class="text-sm text-gray-600">0 files</span>
              </div>
              <div id="file-download" class="space-y-2 max-h-40 overflow-y-auto">
                <div class="py-8 flex flex-col items-center justify-center w-full border-2 border-dashed rounded-md border-gray-500 gap-2">
                  <span class="text-gray-500 text-sm">No files uploaded</span>
                </div>
              </div>
            </div>
          </div>
        </div>
        <!-- Rooms-->
        <div class="flex flex-col lg:flex-row w-full mt-8 gap-8">
          <div class="w-full">
            <div class="w-full inline-flex justify-between mb-2 items-baseline">
              <h2 class="text-lg font-bold w-full mb-4">Rooms</h2>
              <button class="bg-slate-800 hover:bg-slate-900 transition text-white px-4 py-2 rounded text-sm text-nowrap" id="create-room-btn">Create Room</button>
            </div>
            <table class="w-full rounded-md overflow-hidden">
              <tr class="bg-slate-200 text-left text-xs text-black uppercase">
                <th class="p-4 w-4/12"><span class="flex items-center gap-2">Room Name<img src="/assets/selector.svg" class="w-4 h-4" alt=""/></span></th>
                <th class="p-4"><span class="flex items-center gap-2">Status<img src="/assets/selector.svg" class="w-4 h-4" alt=""/></span></th>
                <th class="p-4"><span class="flex items-center gap-2">Owing Balance<img src="/assets/selector.svg" class="w-4 h-4" alt=""/></span></th>
              </tr>
              <tbody id="room-list">
                <tr><td colspan="4"><div class="flex items-center justify-center p-8"><span>Loading rooms...</span></div></td></tr>
              </tbody>
            </table>
          </div>
        </div>
        <!-- Notes-->
        <div class="w-full mt-8">
          <label class="w-full" for="unit" required>
            <h2 class="text-lg font-semibold text-gray-900">Property Notes</h2>
            <p class="text-sm text-gray-500 mb-4">A place to keep notes for internal purposes. Visible to only you and your team.</p>
          </label>
          <textarea class="p-4 w-full border border-gray-300 rounded-md focus:outline-none h-48 resize-none" id="unit" placeholder="Add notes about the property here"></textarea>
        </div>
        <!-- Delete-->
        <div class="w-full mt-8 mb-4">
          <h2 class="text-lg font-semibold text-gray-900 mb-4">Delete Property</h2>
          <button id="delete-property-btn" class="bg-slate-800 hover:bg-slate-900 transition text-white px-4 py-2 rounded text-sm text-nowrap mb-32">Delete Property</button>
        </div>
      </div>
    </main>
  </div>
  <div id="modal" class=""></div>
  <script type="module" src="./main.js"></script>
</body>
</html> 