let propertyId = null;
let propertyData = null;

function getQueryParam(name) {
  const url = new URL(window.location.href);
  return url.searchParams.get(name);
}

window.onload = async function() {
  propertyId = getQueryParam('id');
  const userId = getQueryParam('userId') || getQueryParam('id'); // 兼容参数名
  if (!propertyId) {
    alert('No property id.');
    window.location.href = '/pages/admin/property_all/index.html';
    return;
  }
  try {
    await loadPropertyDetail(propertyId);
    setTimeout(() => {
      document.querySelectorAll('input, textarea').forEach(el => {
        if (el.type !== 'file') {
          el.setAttribute('readonly', 'readonly');
          el.classList.add('bg-gray-100');
        }
      });
      document.querySelectorAll('input[type="checkbox"]').forEach(el => {
        el.setAttribute('disabled', 'disabled');
      });
      const delBtn = document.getElementById('delete-property-btn');
      if (delBtn) delBtn.style.display = 'none';
      const delTitle = Array.from(document.querySelectorAll('h2')).find(h2 => h2.textContent.includes('Delete Property'));
      if (delTitle) delTitle.style.display = 'none';
    }, 0);
    document.getElementById('save-property-btn').onclick = saveProperty;
    document.getElementById('delete-property-btn').onclick = deleteProperty;
    document.getElementById('back-btn').onclick = function(e) {
      e.preventDefault();
      if (userId) {
        window.location.href = `/pages/admin/property_all/index.html?id=${encodeURIComponent(userId)}`;
      } else {
        window.location.href = '/pages/admin/property_all/index.html';
      }
    };
    document.getElementById('create-room-btn').onclick = function() {
      showCreateRoomModal();
    };
  } catch (e) {
    alert(e.message || 'No permission or property not found.');
    window.location.href = '/pages/admin/property_all/index.html';
  }
};

async function loadPropertyDetail(id) {
  const res = await fetch(`/v1/admin/properties/${encodeURIComponent(id)}`);
  if (res.status === 401 || res.status === 403) throw new Error('No permission');
  if (!res.ok) throw new Error('Failed to fetch property detail');
  propertyData = await res.json();
  renderPropertyForm(propertyData);
  renderRoomList(propertyData.rooms || []);
  document.getElementById('unit').value = propertyData.notes || '';
  renderFileDownload(propertyData);
}

function renderPropertyForm(data) {
  const addr = data.address || {};
  document.getElementById('property-detail-info').innerHTML = `
    <div class="flex flex-col gap-4 w-full">
      <label>Property Name
        <input id="property-name" class="border rounded p-2 w-full" value="${data.name || ''}" />
      </label>
      <div class="flex gap-2">
        <label class="flex-1">Address
          <input id="address-street" class="border rounded p-2 w-full" value="${addr.street || ''}" />
        </label>
        <label class="flex-1">Unit
          <input id="address-unit" class="border rounded p-2 w-full" value="${addr.unit || ''}" />
        </label>
      </div>
      <label>City/Town
        <input id="address-city" class="border rounded p-2 w-full" value="${addr.city || ''}" />
      </label>
      <div class="flex gap-2">
        <label class="flex-1">Country
          <input id="address-country" class="border rounded p-2 w-full" value="${addr.country || ''}" />
        </label>
        <label class="flex-1">Province
          <input id="address-prov" class="border rounded p-2 w-full" value="${addr.prov || ''}" />
        </label>
        <label class="flex-1">Postal Code
          <input id="address-zip" class="border rounded p-2 w-full" value="${addr.zipCode || addr.zip || ''}" />
        </label>
      </div>
    </div>
  `;
}

function renderRoomList(rooms) {
  const tbody = document.getElementById('room-list');
  if (!rooms.length) {
    tbody.innerHTML = '<tr><td colspan="4"><div class="flex items-center justify-center p-8"><span>No units</span></div></td></tr>';
    return;
  }
  tbody.innerHTML = rooms.map(room => `<tr class="room-row" data-id="${room.id}"><td class="p-4">${room.name || '-'}</td><td class="p-4">${room.status || '-'}</td><td class="p-4">${room.owingBalance != null ? '$' + room.owingBalance : '-'}</td></tr>`).join('');
  document.querySelectorAll('.room-row').forEach(row => {
    row.onclick = function() {
      const roomId = this.getAttribute('data-id');
      const room = rooms.find(r => r.id === roomId);
      showEditRoomModal(room);
    };
  });
}

function showEditRoomModal(room) {
  const modalHtml = `
    <div class="fixed inset-0 z-50">
      <div class="fixed inset-0 bg-black bg-opacity-50 pointer-events-auto" onclick="document.getElementById('modal').innerHTML = ''"></div>
      <div class="fixed inset-0 flex items-center justify-center pointer-events-none">
        <div class="bg-white sm:rounded-lg shadow-xl sm:w-9/12 w-full h-full sm:max-h-[80vh] sm:min-h-[70vh] overflow-hidden pointer-events-auto relative">
          <div class="sticky top-0 px-6 py-4 border-b bg-white z-10 flex justify-between items-center">
            <div>
              <h3 class="text-lg font-semibold text-gray-900">Edit Room</h3>
              <p>Ensure all information is 100% accurate.</p>
            </div>
            <button class="p-2 hover:bg-gray-100 rounded-full" onclick="document.getElementById('modal').innerHTML = ''">
              <svg class="w-5 h-5 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
          <div class="px-6 py-4 overflow-auto" style="height: calc(100% - 180px)">
            <form id="edit-room-form">
              <div class="flex flex-col gap-4">
                <div class="w-full">
                  <label class="mb-2">Room Name <span class="text-red-500">*</span></label>
                  <input class="p-2 py-4 w-full border border-gray-300 rounded-md focus:outline-none" id="edit-room-name" placeholder="Room name" value="${room.name || ''}" required />
                </div>
                <div class="w-full mt-4">
                  <label class="mb-2">Room Notes</label>
                  <textarea class="p-2 w-full border border-gray-300 rounded-md focus:outline-none" id="edit-room-notes" placeholder="A place to keep notes for internal purposes. Visible to only you and your team.">${room.notes || ''}</textarea>
                </div>
              </div>
            </form>
            <div class="w-full mt-8">
              <h2 class="text-lg font-semibold text-gray-900 mb-2">Delete Room</h2>
              <button class="px-4 py-2 text-sm font-medium text-black bg-white border border-gray-300 rounded-md hover:bg-gray-50" id="delete-room-btn">Delete Room</button>
            </div>
          </div>
          <div class="absolute bottom-0 left-0 right-0 px-6 py-4 bg-gray-50 sm:rounded-b-lg flex justify-between gap-2 border-t">
            <button class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50" id="edit-cancel-room-btn">Cancel</button>
            <button id="edit-save-room-btn" class="px-4 py-2 text-sm font-medium text-white bg-slate-800 rounded-md hover:bg-slate-900">Save</button>
          </div>
        </div>
      </div>
    </div>
  `;
  document.getElementById('modal').innerHTML = modalHtml;
  bindEditRoomModal(room);
}

function bindEditRoomModal(room) {
  const form = document.getElementById('edit-room-form');
  const saveBtn = document.getElementById('edit-save-room-btn');
  const cancelBtn = document.getElementById('edit-cancel-room-btn');
  const deleteBtn = document.getElementById('delete-room-btn');
  saveBtn.disabled = false;
  cancelBtn.onclick = function(e) {
    e.preventDefault();
    document.getElementById('modal').innerHTML = '';
  };
  saveBtn.onclick = async function(e) {
    e.preventDefault();
    if (saveBtn.disabled) return;
    const data = {
      name: document.getElementById('edit-room-name').value.trim(),
      notes: document.getElementById('edit-room-notes').value.trim(),
      status: room.status || 'vacant',
      type: room.type || 'studio'
    };
    try {
      const res = await fetch(`/v1/admin/properties/${encodeURIComponent(propertyId)}/rooms/${encodeURIComponent(room.id)}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(data)
      });
      if (!res.ok) throw new Error('Save failed');
      document.getElementById('modal').innerHTML = '';
      await loadPropertyDetail(propertyId);
    } catch (err) {
      alert(err.message || 'Save failed');
    }
  };
  deleteBtn.onclick = async function(e) {
    e.preventDefault();
    if (!confirm('Are you sure you want to delete this room?')) return;
    try {
      const res = await fetch(`/v1/admin/properties/${encodeURIComponent(propertyId)}/rooms/${encodeURIComponent(room.id)}`, {
        method: 'DELETE'
      });
      if (!res.ok) throw new Error('Delete failed');
      document.getElementById('modal').innerHTML = '';
      await loadPropertyDetail(propertyId);
    } catch (err) {
      alert(err.message || 'Delete failed');
    }
  };
}

async function saveProperty() {
  if (!propertyId) return;
  const updates = {
    name: document.getElementById('property-name').value,
    address: {
      street: document.getElementById('address-street').value,
      unit: document.getElementById('address-unit').value,
      city: document.getElementById('address-city').value,
      country: document.getElementById('address-country').value,
      prov: document.getElementById('address-prov').value,
      zipCode: document.getElementById('address-zip').value,
    },
    notes: document.getElementById('unit').value,
  };
  const res = await fetch(`/v1/admin/properties/${encodeURIComponent(propertyId)}`, {
    method: 'PUT',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(updates),
  });
  if (!res.ok) {
    alert('Save failed');
    return;
  }
  alert('Saved successfully');
}

async function deleteProperty() {
  if (!propertyId) return;
  if (!confirm('Are you sure you want to delete this property?')) return;
  const res = await fetch(`/v1/admin/properties/${encodeURIComponent(propertyId)}`, { method: 'DELETE' });
  if (!res.ok) {
    alert('Delete failed');
    return;
  }
  alert('Deleted successfully');
  window.location.href = '/pages/admin/property_all/index.html';
}

function showCreateRoomModal() {
  const modalHtml = `
    <div class="fixed inset-0 z-50">
      <div class="fixed inset-0 bg-black bg-opacity-50 pointer-events-auto" onclick="document.getElementById('modal').innerHTML = ''"></div>
      <div class="fixed inset-0 flex items-center justify-center pointer-events-none">
        <div class="bg-white sm:rounded-lg shadow-xl sm:w-9/12 w-full h-full sm:max-h-[80vh] sm:min-h-[70vh] overflow-hidden pointer-events-auto relative">
          <div class="sticky top-0 px-6 py-4 border-b bg-white z-10 flex justify-between items-center">
            <div>
              <h3 class="text-lg font-semibold text-gray-900">Create Room</h3>
              <p>Ensure all information is 100% accurate.</p>
            </div>
            <button class="p-2 hover:bg-gray-100 rounded-full" onclick="document.getElementById('modal').innerHTML = ''">
              <svg class="w-5 h-5 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
          <div class="px-6 py-4 overflow-auto" style="height: calc(100% - 140px)">
            <form id="create-room-form">
              <div class="flex flex-col gap-4">
                <div class="w-full">
                  <label class="mb-2">Room Name <span class="text-red-500">*</span></label>
                  <input class="p-2 py-4 w-full border border-gray-300 rounded-md focus:outline-none" id="modal-room-name" placeholder="Room name" required />
                </div>
                <div class="w-full mt-4">
                  <label class="mb-2">Room Notes</label>
                  <textarea class="p-2 w-full border border-gray-300 rounded-md focus:outline-none" id="modal-room-notes" placeholder="A place to keep notes for internal purposes. Visible to only you and your team."></textarea>
                </div>
              </div>
            </form>
          </div>
          <div class="absolute bottom-0 left-0 right-0 px-6 py-4 bg-gray-50 sm:rounded-b-lg flex justify-between gap-2 border-t">
            <button class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50" id="modal-cancel-room-btn">Cancel</button>
            <button id="modal-create-room-btn" class="px-4 py-2 text-sm font-medium text-white bg-slate-800 rounded-md hover:bg-slate-900">Create Room</button>
          </div>
        </div>
      </div>
    </div>
  `;
  document.getElementById('modal').innerHTML = modalHtml;
  bindCreateRoomModal();
}

function bindCreateRoomModal() {
  const form = document.getElementById('create-room-form');
  const createBtn = document.getElementById('modal-create-room-btn');
  const cancelBtn = document.getElementById('modal-cancel-room-btn');
  createBtn.disabled = false;
  cancelBtn.onclick = function(e) {
    e.preventDefault();
    document.getElementById('modal').innerHTML = '';
  };
  createBtn.onclick = async function(e) {
    e.preventDefault();
    if (createBtn.disabled) return;
    const data = {
      name: document.getElementById('modal-room-name').value.trim(),
      notes: document.getElementById('modal-room-notes').value.trim(),
      status: 'vacant',
      type: 'studio'
    };
    try {
      const res = await fetch(`/v1/admin/properties/${encodeURIComponent(propertyId)}/rooms`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(data)
      });
      if (!res.ok) throw new Error('Create room failed');
      document.getElementById('modal').innerHTML = '';
      await loadPropertyDetail(propertyId);
    } catch (err) {
      alert(err.message || 'Create room failed');
    }
  };
}

// 格式化文件大小
function formatFileSize(bytes) {
  if (bytes === 0) return '0 Bytes';
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

function renderFileDownload(data) {
  const fileDiv = document.getElementById('file-download');
  const countDiv = document.getElementById('document-count');
  if (!fileDiv) return;

  // 处理多文档显示（过滤掉deleted和final_deleted状态的文件）
  const activeDocuments = (data.documents || []).filter(doc =>
    doc.status !== 'deleted' && doc.status !== 'final_deleted'
  );

  if (activeDocuments.length > 0) {
    // 更新文档计数
    if (countDiv) {
      countDiv.textContent = `${activeDocuments.length} files`;
    }

    // 渲染文档列表
    const documentsHtml = activeDocuments.map(doc => `
      <a href="/v1/properties/download/${doc.id}" target="_blank" class="flex items-center gap-2 hover:bg-gray-100 bg-gray-50 px-3 py-1.5 rounded-md text-gray-700 block">
        <svg class="w-4 h-4 text-gray-500 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z" />
        </svg>
        <div class="flex-1 min-w-0">
          <div class="text-sm font-medium text-gray-900 truncate">${doc.fileName}</div>
          <div class="text-xs text-gray-500">${formatFileSize(doc.fileSize || 0)}</div>
        </div>
      </a>
    `).join('');
    fileDiv.innerHTML = documentsHtml;
  } else {
    // 如果没有有效文档（包括所有文档都是deleted/final_deleted状态的情况）
    if (countDiv) {
      countDiv.textContent = '0 files';
    }
    fileDiv.innerHTML = `
      <div class="py-8 flex flex-col items-center justify-center w-full border-2 border-dashed rounded-md border-gray-500 gap-2">
        <span class="text-gray-500 text-sm">No files uploaded</span>
      </div>
    `;
  }
}