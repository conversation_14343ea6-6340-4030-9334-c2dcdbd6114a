<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Admin Logs</title>
  <link href="/styles/output.css" rel="stylesheet" />
</head>
<body class="min-h-screen bg-gray-200">
  <div class="flex h-screen">
    <main class="h-screen w-full bg-slate-100 overflow-auto lg:px-16 md:px-8 px-4">
      <div class="max-w-[1440px] w-full mx-auto mb-32">
        <div class="w-full mt-20 md:mt-8 pb-0">
          <a class="text-sm underline" id="back-to-user" href="#">← Back to User Details</a>
        </div>
        <div class="flex-1 mt-20 lg:mt-8 pb-0 w-full">
          <h1 class="text-2xl" id="admin-log-title">Admin Logs</h1>
          <div class="block">
            <table class="table-fixed w-full text-sm rounded-md overflow-hidden mt-4" style="box-shadow: 0 0 0 1px rgb(203 213 225)">
              <thead>
                <tr class="bg-slate-200 text-left text-black uppercase text-xs border-b border-slate-300">
                  <th class="p-4 w-1/3"><span class="flex items-center gap-2">Action</span></th>
                  <th class="p-4 w-1/4"><span class="flex items-center gap-2">Changes</span></th>
                  <th class="p-4 w-1/4"><span class="flex items-center gap-2">Date</span></th>
                  <th class="p-4 w-1/4"><span class="flex items-center gap-2">Admin</span></th>
                </tr>
              </thead>
              <tbody id="admin-log-tbody">
                <!-- 动态插入日志数据 -->
              </tbody>
            </table>
            <div class="hidden lg:flex justify-between items-center gap-4 px-4 py-2 mt-4 bg-white border border-gray-300 rounded-lg" id="admin-log-pagination">
              <span class="text-sm text-gray-500" id="admin-log-count">Showing 0 of 0 entries</span>
              <div class="join" id="admin-log-pages">
                <!-- 分页按钮 -->
              </div>
            </div>
          </div>
        </div>
      </div>
    </main>
  </div>
  <script>
    function getQueryParam(name) {
      const url = new URL(window.location.href);
      return url.searchParams.get(name);
    }
    document.getElementById('back-to-user').onclick = function() {
      const uid = getQueryParam('id');
      window.location.href = `/pages/admin/user_detail/index.html?id=${encodeURIComponent(uid)}`;
    };
    const uid = getQueryParam('id');
    if (uid) {
      document.getElementById('admin-log-title').textContent = `Admin Logs for User ${uid}`;
      fetch(`/v1/admin/users/${encodeURIComponent(uid)}`)
        .then(res => res.json())
        .then(user => {
          if (user && user.username) {
            document.getElementById('admin-log-title').textContent = `Admin Logs for ${user.username}`;
          } else if (user && user.name) {
            document.getElementById('admin-log-title').textContent = `Admin Logs for ${user.name}`;
          }
        })
        .catch(() => {
        });
      fetch(`/v1/admin/logs?userId=${encodeURIComponent(uid)}`)
        .then(res => res.json())
        .then(async logs => {
          const tbody = document.getElementById('admin-log-tbody');
          tbody.innerHTML = '';
          if (Array.isArray(logs) && logs.length > 0) {
            const adminIds = Array.from(new Set(logs.map(log => log.adminId).filter(Boolean)));
            let adminMap = {};
            if (adminIds.length > 0) {
              const res = await fetch(`/v1/admin/usernames?ids=${adminIds.join(',')}`);
              adminMap = await res.json();
            }
            logs.forEach(log => {
              const tr = document.createElement('tr');
              tr.innerHTML = `
                <td class="p-4">${log.act || ''}</td>
                <td class="p-4">${log.chg || ''}</td>
                <td class="p-4">${log.ts ? new Date(log.ts).toLocaleString() : ''}</td>
                <td class="p-4">${adminMap[log.adminId] || log.adminId || ''}</td>
              `;
              tbody.appendChild(tr);
            });
            document.getElementById('admin-log-count').textContent = `Showing ${logs.length} entr${logs.length === 1 ? 'y' : 'ies'}`;
          } else {
            document.getElementById('admin-log-count').textContent = 'Showing 0 of 0 entries';
          }
        });
    }
  </script>
</body>
</html> 