<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>User Details</title>
  <link href="/styles/output.css" rel="stylesheet" />
</head>
<body class="min-h-screen bg-gray-200">
  <div class="flex h-screen">
    <main class="w-full overflow-y-auto lg:px-16 md:px-8 px-4 bg-slate-100">
      <div class="max-w-[1440px] w-full mx-auto md:mt-24 lg:mt-4 relative">
        <div class="w-full mt-20 md:mt-8 pb-0 flex justify-between items-center">
          <a class="text-sm underline" href="/pages/admin/index.html">Back To Admin Dashboard</a>
        </div>
        <div class="w-full mt-8 pb-0 flex justify-between items-center">
          <h1 class="text-2xl">User Details</h1>
          <a href="#" id="admin-logs-btn" class="btn bg-white border border-gray-300 rounded px-4 py-2 text-base font-medium text-gray-900 hover:bg-gray-100 transition shadow-none">Admin Logs</a>
        </div>
        <div class="flex flex-col bg-white rounded border border-gray-300 mt-4 w-full p-2 gap-8 md:p-6 text-sm">
          <!-- Account information -->
          <div class="flex flex-1 flex-wrap gap-4">
            <h2 class="text-lg font-bold w-full mb-4">Account Information</h2>
            <div class="flex flex-row w-full">
              <div class="flex flex-row w-1/2">
                <div class="font-bold w-36">Name:</div>
                <span id="user-name">-</span><span class="ml-1 italic text-gray-500" id="user-id"></span>
              </div>
              <div class="flex flex-row w-1/2">
                <div class="font-bold w-36">Organization:</div>
                <span id="user-org">-</span><span class="ml-1 italic text-gray-500" id="user-org-role"></span>
              </div>
            </div>
            <div class="flex flex-row w-full">
              <div class="flex flex-row w-1/2">
                <div class="font-bold w-36">Email:</div>
                <span id="user-email">-</span>
              </div>
              <div class="flex flex-row w-1/2">
                <div class="font-bold w-36">Phone Number:</div>
                <span id="user-phone">-</span><span class="ml-1 italic text-gray-500" id="user-phone-role"></span>
              </div>
            </div>
            <div class="flex flex-row w-full">
              <div class="flex flex-row w-1/2">
                <div class="font-bold w-36">Account Verified:</div>
                <span id="user-verified">-</span>
              </div>
            </div>
            <div class="flex flex-row w-full items-center mb-2 mt-4">
              <div class="font-bold w-36">Account Status</div>
              <select class="select w-60 max-w-xs ml-2" id="user-status">
                <option value="active">Active</option>
                <option value="suspended">Suspended</option>
                <option value="disabled">Disabled</option>
              </select>
            </div>
            <div class="flex flex-row w-full">
              <div class="flex flex-row w-1/2">
                <div class="font-bold w-36">Billing Address:</div>
                <div class="flex flex-col" id="user-address">
                  <span>-</span>
                </div>
              </div>
              <div class="flex flex-row w-1/2">
                <div class="font-bold w-36">Payment Method:</div>
                <span id="user-card">-</span>
              </div>
            </div>
            <button class="btn mt-4 bg-white border border-gray-300 rounded px-4 py-2 text-base font-medium text-gray-900 hover:bg-gray-100 transition shadow-none" id="billing-history-btn">Billing History</button>
          </div>
          <!-- Organization information -->
          <div id="org-info-block" class="flex flex-1 flex-wrap">
            <h2 class="text-lg font-bold w-full mb-4">Organization Information</h2>
            <div id="org-info-content">
              <div class="flex flex-row w-full">
                <div class="flex flex-row w-1/2">
                  <div class="font-bold w-36">Account Verified:</div>
                  <span id="org-verified">No</span>
                </div>
              </div>
            </div>
          </div>
          <!-- Recent Properties & Leases -->
          <div class="flex flex-col lg:flex-row w-full mt-8 gap-8">
            <!-- Recent Properties -->
            <div class="w-full lg:w-1/2">
              <div class="w-full inline-flex justify-between mb-2 items-baseline">
                <h2 class="text-lg font-bold w-full mb-4">Recent Properties</h2>
                <button class="bg-slate-800 hover:bg-slate-900 transition text-white px-4 py-2 rounded text-sm text-nowrap" id="view-all-properties-btn">View All Properties</button>
              </div>
              <div class="w-full">
                <table class="w-full rounded-md overflow-hidden text-xs" style="box-shadow: 0 0 0 1px rgb(203 213 225)">
                  <thead>
                    <tr class="bg-slate-200 text-black uppercase border-b border-slate-300">
                      <th class="p-4 w-4/12 rounded-tl-md">Date Created</th>
                      <th class="p-4">Property Name</th>
                      <th class="p-4">Address</th>
                    </tr>
                  </thead>
                  <tbody id="recent-properties-tbody">
                    <!-- recent properties -->
                  </tbody>
                </table>
              </div>
            </div>
            <!-- Recent Leases -->
            <div class="w-full lg:w-1/2">
              <div class="w-full inline-flex justify-between mb-2 items-baseline">
                <h2 class="text-lg font-bold w-full mb-4">Recent Leases</h2>
                <button class="bg-slate-800 hover:bg-slate-900 transition text-white px-4 py-2 rounded text-sm text-nowrap" id="view-all-leases-btn">View All Leases</button>
              </div>
              <div class="w-full">
                <table class="w-full rounded-md overflow-hidden text-xs shadow-xl" style="box-shadow: 0 0 0 1px rgb(203 213 225)">
                  <thead>
                    <tr class="bg-slate-200 text-black uppercase border-b border-slate-300">
                      <th class="p-4 w-4/12 rounded-tl-md">Date Created</th>
                      <th class="p-4">Primary tenant</th>
                      <th class="p-4">Building</th>
                      <th class="p-4">Unit</th>
                    </tr>
                  </thead>
                  <tbody id="recent-leases-tbody">
                    <!-- recent leases -->
                  </tbody>
                </table>
              </div>
            </div>
          </div>
          <!-- Delete User -->
          <div class="w-full mt-8 mb-4">
            <h2 class="text-lg font-semibold text-gray-900 mb-4">Delete User</h2>
            <button class="bg-slate-800 hover:bg-slate-900 transition text-white px-4 py-2 rounded text-sm text-nowrap mb-32" id="delete-user-btn">Delete User</button>
          </div>
        </div>
      </div>
    </main>
  </div>
  <script type="module" src="./main.js"></script>
  <script>
    window.addEventListener('load', function() {
      if (typeof getQueryParam === 'function') {
        const id = getQueryParam('id');
        const adminLogsBtn = document.getElementById('admin-logs-btn');
        if (adminLogsBtn && id) {
          adminLogsBtn.href = `/pages/admin/user_detail/admin_log.html?id=${encodeURIComponent(id)}`;
        }
      } else {
        const urlParams = new URLSearchParams(window.location.search);
        const id = urlParams.get('id');
        const adminLogsBtn = document.getElementById('admin-logs-btn');
        if (adminLogsBtn && id) {
          adminLogsBtn.href = `/pages/admin/user_detail/admin_log.html?id=${encodeURIComponent(id)}`;
        }
      }
    });
  </script>
</body>
</html> 