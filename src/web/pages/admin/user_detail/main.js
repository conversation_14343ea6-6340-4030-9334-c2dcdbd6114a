function getQueryParam(name) {
  const url = new URL(window.location.href);
  return url.searchParams.get(name);
}

async function fetchUserDetail(id) {
  const res = await fetch(`/v1/admin/users/${encodeURIComponent(id)}`);
  if (!res.ok) {
    document.getElementById('user-name').textContent = 'User not found or permission denied.';
    return null;
  }
  return await res.json();
}

async function fetchUserProperties(id) {
  const res = await fetch(`/v1/admin/users/${encodeURIComponent(id)}/properties`);
  if (!res.ok) return [];
  const data = await res.json();

  // 处理新的分页格式
  if (data.data && Array.isArray(data.data)) {
    return data.data;
  }
  // 兼容旧格式
  if (Array.isArray(data)) {
    return data;
  }
  return [];
}

async function fetchUserLeases(id) {
  const res = await fetch(`/v1/admin/users/${encodeURIComponent(id)}/leases`);
  if (!res.ok) return [];
  const data = await res.json();

  // 处理新的分页格式
  if (data.data && Array.isArray(data.data)) {
    return data.data;
  }
  // 兼容旧格式
  if (Array.isArray(data)) {
    return data;
  }
  return [];
}

// TODO: 这里应补充fetchRecentProperties/fetchRecentLeases等API

function formatDate(ts) {
  if (!ts) return '-';
  let d;
  if (typeof ts === 'string') {
    d = new Date(ts);
  } else if (typeof ts === 'number') {
    // 判断是秒还是毫秒
    d = ts > 1e12 ? new Date(ts) : new Date(ts * 1000);
  } else {
    return '-';
  }
  if (isNaN(d.getTime())) return '-';
  return d.toISOString().slice(0, 10);
}

function formatAddress(addr) {
  if (!addr) return '-';
  // addr可能是字符串或对象
  if (typeof addr === 'string') return addr;
  const parts = [addr.street, addr.unit, addr.city, addr.prov, addr.country, addr.zipCode || addr.zip];
  return parts.filter(Boolean).join(', ');
}

async function renderUserDetail(user, id) {
  if (!user) return;
  document.getElementById('user-name').textContent = user.username || '-';
  document.getElementById('user-id').textContent = user.id ? `(${user.id})` : '';
  document.getElementById('user-org').textContent = user.organizationId || '-';
  document.getElementById('user-org-role').textContent = user.orgRole ? `(${user.orgRole})` : '';
  document.getElementById('user-email').textContent = user.email || '-';
  document.getElementById('user-phone').textContent = user.phoneNumber || '-';
  document.getElementById('user-phone-role').textContent = user.phoneRole ? `(${user.phoneRole})` : '';
  document.getElementById('user-verified').textContent = user.isVerified ? 'Yes' : 'No';
  document.getElementById('user-status').value = user.status || '-';
  // 地址
  const addr = user.address || {};
  document.getElementById('user-address').innerHTML = `<span>${addr.line1 || '-'}</span><span>${addr.line2 || ''}</span>`;
  document.getElementById('user-card').textContent = user.paymentMethod || '-';

  // 组织信息区块
  const orgBlock = document.getElementById('org-info-content');
  if (!user.organizationId) {
    orgBlock.innerHTML = `<div class='flex flex-row items-center'><div class='font-bold w-36'>Account Verified:</div><span id='org-verified'>No</span></div>`;
  } else {
    orgBlock.innerHTML = `
      <div class=\"flex flex-row w-full\">
        <div class=\"flex flex-row w-1/2\">
          <div class=\"font-bold w-36\">Account Type:</div>
          <span id=\"user-account-type\" class=\"inline-block px-4 py-1 rounded-xl bg-gray-100 text-gray-900 font-medium\">${user.accountType ? (user.accountType.toLowerCase() === 'vip' ? 'VIP Account' : 'Basic Account') : 'Basic Account'}</span>
        </div>
      </div>
      <div class=\"flex flex-row w-full\">
        <div class=\"flex flex-row w-1/2\">
          <div class=\"font-bold w-36\">Business Name:</div>
          <span id=\"org-name\">${user.organizationName || '-'}</span><span class=\"italic text-gray-500 ml-1\" id=\"org-verified\">${user.orgVerified ? '(Verified)' : '(Unverified)'}</span>
        </div>
        <div class=\"flex flex-row w-1/2\">
          <div class=\"font-bold w-36\">Date Created:</div>
          <span id=\"org-date\">${user.orgDateCreated || '-'}</span>
        </div>
      </div>
      <div class=\"flex flex-row w-full\">
        <div class=\"flex flex-row w-1/2\">
          <div class=\"font-bold w-36\">Owner:</div>
          <span id=\"org-owner\">${user.orgOwner || '-'}</span>
        </div>
        <div class=\"flex flex-row w-1/2\">
          <div class=\"font-bold w-36\">Phone Number:</div>
          <span id=\"org-phone\">${user.orgPhone || '-'}</span>
        </div>
      </div>
      <div class=\"flex flex-row w-full\">
        <div class=\"flex flex-row w-1/2\">
          <div class=\"font-bold w-36\">Business Email:</div>
          <span id=\"org-email\">${user.orgEmail || '-'}</span>
        </div>
      </div>
      <div class=\"flex flex-row w-full items-center mb-2 mt-4\">
        <div class=\"font-bold w-36\">Business Status</div>
        <select class=\"select ml-2 w-60\" id=\"org-status\" disabled>
          <option value=\"active\"${user.orgStatus === 'active' ? ' selected' : ''}>Active</option>
          <option value=\"suspended\"${user.orgStatus === 'suspended' ? ' selected' : ''}>Suspended</option>
          <option value=\"disabled\"${user.orgStatus === 'disabled' ? ' selected' : ''}>Disabled</option>
          <option value=\"-\"${user.orgStatus === '-' ? ' selected' : ''}>-</option>
        </select>
      </div>
      <div class=\"flex flex-row w-full gap-2 mt-2\">
        <button class=\"btn\" id=\"review-id-btn\">Review ID Verification</button>
        <button class=\"btn\" id=\"past-verifications-btn\">Past Verifications (<span id=\"past-verifications-count\">${user.pastVerificationsCount || 0}</span>)</button>
        <button class=\"btn ml-auto\" id=\"view-members-btn\">View All Members (<span id=\"members-count\">${user.membersCount || 0}</span>)</button>
      </div>
    `;
  }

  // recent properties
  const propTbody = document.getElementById('recent-properties-tbody');
  const properties = await fetchUserProperties(id);
  propTbody.innerHTML = properties.length ? properties.map(p => `<tr><td class="p-4">${formatDate(p._ts)}</td><td class="p-4">${p.name || '-'}</td><td class="p-4">${formatAddress(p.address)}</td></tr>`).join('') : '<tr><td colspan="3" class="p-4 text-center text-gray-400">No data</td></tr>';
  // recent leases
  const leaseTbody = document.getElementById('recent-leases-tbody');
  const leases = await fetchUserLeases(id);
  leaseTbody.innerHTML = leases.length ? leases.map(l => `<tr><td class="p-4">${formatDate(l._ts)}</td><td class="p-4">${l.tenantName || '-'} </td><td class="p-4">${l.propertyAddress || '-'}</td><td class="p-4">${l.roomName || '-'}</td></tr>`).join('') : '<tr><td colspan="4" class="p-4 text-center text-gray-400">No data</td></tr>';
}

function renderUserProperties(properties) {
  const propTbody = document.getElementById('recent-properties-tbody');
  propTbody.innerHTML = properties.length ? properties.map(p => `<tr><td class="p-4">${formatDate(p._ts)}</td><td class="p-4">${p.name || '-'} </td><td class="p-4">${formatAddress(p.address)}</td></tr>`).join('') : '<tr><td colspan="3" class="p-4 text-center text-gray-400">No data</td></tr>';
}

function renderUserLeases(leases) {
  const leaseTbody = document.getElementById('recent-leases-tbody');
  leaseTbody.innerHTML = leases.length ? leases.map(l => `<tr><td class="p-4">${formatDate(l._ts)}</td><td class="p-4">${l.tenantName || '-'} </td><td class="p-4">${l.propertyAddress || '-'}</td><td class="p-4">${l.roomName || '-'}</td></tr>`).join('') : '<tr><td colspan="4" class="p-4 text-center text-gray-400">No data</td></tr>';
}

window.onload = async function() {
  const id = getQueryParam('id');
  if (!id) {
    document.getElementById('user-name').textContent = 'No user id.';
    return;
  }
  const user = await fetchUserDetail(id);
  await renderUserDetail(user, id);

  // 绑定Account Status下拉菜单弹窗确认
  const statusSelect = document.getElementById('user-status');
  if (statusSelect) {
    let prevValue = statusSelect.value;
    statusSelect.addEventListener('focus', function() {
      prevValue = statusSelect.value;
    });
    statusSelect.addEventListener('change', async function(e) {
      const newValue = statusSelect.value;
      if (newValue === prevValue) return;
      if (confirm(`Are you sure you want to change account status from "${prevValue}" to "${newValue}"?`)) {
        // 用户确认，发起API请求
        try {
          // 获取当前用户id和admin id
          const userId = id;
          let adminId = localStorage.getItem('adminId');
          if (!adminId) {
            // 可选：从token解析adminId，或后端返回
            // adminId = ...
          }
          // 1. 更新用户状态
          const res = await fetch(`/v1/admin/users/${encodeURIComponent(userId)}`, {
            method: 'PUT',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ status: newValue })
          });
          if (!res.ok) throw new Error('Failed to update user status');
          // 2. 创建admin log
          const logRes = await fetch('/v1/admin/logs', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
              act: 'Edit user account status',
              chg: `${prevValue} -> ${newValue}`,
              userId: userId,
              adminId: adminId,
              ts: new Date().toISOString()
            })
          });
          if (!logRes.ok) {
            const errText = await logRes.text();
            alert('Admin log failed: ' + errText);
            throw new Error('Admin log failed: ' + errText);
          }
          prevValue = newValue;
        } catch (err) {
          alert('Failed to update status or log.');
          statusSelect.value = prevValue;
        }
      } else {
        // 用户取消，恢复原值
        statusSelect.value = prevValue;
      }
    });
  }

  // 渲染property表格
  const properties = await fetchUserProperties(id);
  renderUserProperties(properties);

  // 渲染lease表格
  const leases = await fetchUserLeases(id);
  renderUserLeases(leases);

  // 绑定View All Leases按钮跳转
  const viewAllLeasesBtn = document.getElementById('view-all-leases-btn');
  if (viewAllLeasesBtn) {
    viewAllLeasesBtn.onclick = function() {
      window.location.href = `/pages/admin/lease_all/index.html?id=${encodeURIComponent(id)}`;
    };
  }

  // 绑定View All Properties按钮跳转
  const viewAllPropertiesBtn = document.getElementById('view-all-properties-btn');
  if (viewAllPropertiesBtn) {
    viewAllPropertiesBtn.onclick = function() {
      window.location.href = `/pages/admin/property_all/index.html?id=${encodeURIComponent(id)}`;
    };
  }

  // 绑定Billing History按钮跳转
  const billingHistoryBtn = document.getElementById('billing-history-btn');
  if (billingHistoryBtn) {
    billingHistoryBtn.onclick = function() {
      window.location.href = `/pages/admin/payment_all/index.html?id=${encodeURIComponent(id)}`;
    };
  }
}; 