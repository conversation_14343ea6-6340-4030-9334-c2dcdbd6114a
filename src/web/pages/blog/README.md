# Dynamic Blog System

这个blog系统现在是完全动态的，不再需要硬编码文章列表。

## 系统架构

### 核心文件
- `src/web/services/blog.js` - 博客数据管理服务
- `src/web/services/blog-ui.js` - 共享UI组件
- `src/web/services/blog-admin.js` - 管理工具

### 主要功能

1. **动态文章列表** - Blog主页自动从数据源读取文章
2. **智能相关文章** - 每个文章页面自动显示相关文章
3. **统一数据管理** - 所有文章信息集中管理
4. **响应式布局** - 适配不同屏幕尺寸

## 如何添加新文章

### 1. 在blog.js中添加文章数据
```javascript
{
  id: 4,
  slug: 'new-article-slug',
  title: 'New Article Title',
  excerpt: 'Article description...',
  image: '/assets/article-image.jpg',
  date: 'January 20, 2025',
  readTime: '5 min read',
  tags: ['tag1', 'tag2'],
  featured: false
}
```

### 2. 创建文章目录
```
src/web/pages/blog/new-article-slug/
├── index.html
└── main.js
```

### 3. 使用模板创建main.js
```javascript
import { redirectLoggedInUsersToHome } from '../../../services/util.js';
import blogService from '../../../services/blog.js';
import { BlogUI } from '../../../services/blog-ui.js';

const currentSlug = 'new-article-slug';

async function loadRelatedArticles() {
  try {
    const container = document.getElementById('related-articles');
    if (!container) return;

    const relatedPosts = await blogService.getRelatedPosts(currentSlug, 4);
    
    if (relatedPosts.length === 0) {
      container.innerHTML = '<p class="text-gray-500">No related articles found.</p>';
      return;
    }

    const baseUrl = BlogUI.getBaseUrl();
    const articlesHTML = relatedPosts.map(post => 
      BlogUI.createRelatedArticleCard(post, baseUrl)
    ).join('');
    
    container.innerHTML = articlesHTML;
  } catch (error) {
    console.error('Error loading related articles:', error);
  }
}

document.addEventListener('DOMContentLoaded', async () => {
  try {
    await redirectLoggedInUsersToHome();
    await loadRelatedArticles();
  } catch (error) {
    console.error('Error during page initialization:', error);
  }
});
```

### 4. 在HTML中添加相关文章容器
```html
<div class="bg-white p-8 rounded-md flex flex-col gap-8">
  <span class="text-xl font-medium">Suggested Articles</span>
  <div id="related-articles" class="flex flex-col gap-4">
    <!-- Related articles will be loaded here dynamically -->
  </div>
</div>
```

## 系统特性

### 自动化功能
- ✅ 文章列表自动更新
- ✅ 相关文章智能推荐
- ✅ 分页自动计算
- ✅ 导航链接自动生成

### 智能相关文章算法
1. 优先显示有相同标签的文章
2. 其次显示最新发布的文章
3. 自动排除当前文章
4. 支持自定义显示数量

### 响应式设计
- 主页：网格布局，支持多种屏幕尺寸
- 文章页：侧边栏布局，移动端自适应
- 卡片：统一样式，hover效果

## 维护说明

### 添加新文章只需要：
1. 在`blog.js`中添加一条记录
2. 创建文章目录和文件
3. 系统自动处理其余所有事情

### 修改文章信息：
- 只需修改`blog.js`中的对应记录
- 所有页面会自动更新

### 样式修改：
- 主页卡片样式：修改`BlogUI.createBlogCard()`
- 相关文章样式：修改`BlogUI.createRelatedArticleCard()`
- 分页样式：修改`BlogUI.createPagination()`

这个系统完全解决了之前硬编码的问题，现在添加新文章非常简单，而且所有页面都会自动更新！
