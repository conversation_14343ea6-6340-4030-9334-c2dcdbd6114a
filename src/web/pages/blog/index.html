{{> landing-header}}
  <body>
    <!-- NAV BAR-->
    {{> landing-navbar}}

    <!-- Main content-->
    <main class="flex flex-col items-center min-h-screen w-full bg-white">
      <!-- Hero Banner -->
      <div
        class="flex w-full mt-0 lg:mt-0 py-8 lg:h-[456px] overflow-clip justify-center"
      >
        <div
          class="flex lg:flex-row flex-col items-center w-full max-w-[1248px] bg-white px-4 lg:px-16 justify-between gap-12"
        >
          <div class="flex flex-col gap-8 align-middle items-start w-full lg:w-1/2 lg:max-w-[580px]">
            <h1
              class="text-base lg:text-4xl text-left font-medium lg:leading-tight"
            >
              What Is Rent Reporting And Why Should Landlords Care?
            </h1>
            <p class="lg:text-lg text-sm lg:leading-relaxed">
              If you're a landlord with just one or two rental units, chances are you wear a lot of hats: property manager, maintenance coordinator, leasing agent, and more. With so much on your plate, adding something like rent reporting might not be top of mind.
            </p>
            <a href="./why-rent-reporting-matters-for-landlords/"
              ><button
                class="px-4 py-3 bg-rmred hover:bg-rmred-dark text-white rounded-md lg:text-base text-sm font-medium cursor-pointer transition-colors duration-100"
              >
                Read More
              </button></a
            >
          </div>
          <div
            class="w-full h-72 lg:w-1/2 lg:h-80 order-first lg:order-last overflow-hidden rounded-lg"
          >
            <img src="/assets/landlord-i1.jpg" class="object-cover w-full h-full" />
          </div>
        </div>
      </div>

      <div class="flex w-full mt-0 overflow-clip justify-center bg-gray-100">
        <div
          class="flex flex-col items-center w-full max-w-[1248px] px-4 lg:px-16 py-11 gap-8"
        >
          <span class="text-lg font-medium w-full">All Posts</span>
          <div id="blog-posts-container" class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8">
            <!-- Blog posts will be loaded here dynamically -->
          </div>
          <div id="pagination-container" class="join" style="display: none;">
            <!-- Pagination will be loaded here dynamically -->
          </div>
        </div>
      </div>
    </main>

    <!--Footer-->
    {{> landing-footer}}

    <style>
      .line-clamp-2 {
        display: -webkit-box;
        -webkit-line-clamp: 2;
        line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
      }
      .line-clamp-3 {
        display: -webkit-box;
        -webkit-line-clamp: 3;
        line-clamp: 3;
        -webkit-box-orient: vertical;
        overflow: hidden;
      }
    </style>
    <script type="module" src="./main.js"></script>
  </body>
</html>
