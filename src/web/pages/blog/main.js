import { redirectLoggedInUsersToHome } from '../../services/util.js';
import blogService from '../../services/blog.js';
import { BlogUI } from '../../services/blog-ui.js';

const POSTS_PER_PAGE = 6;
let currentPage = 1;

async function loadPage(page) {
  try {
    currentPage = page;

    // Get paginated posts from blog service
    const result = await blogService.getPostsPaginated(page, POSTS_PER_PAGE);

    const container = document.getElementById('blog-posts-container');
    const paginationContainer = document.getElementById('pagination-container');

    if (!container) {
      console.error('Blog posts container not found!');
      return;
    }

    if (!paginationContainer) {
      console.error('Pagination container not found!');
      return;
    }

    // Get base URL for navigation
    const baseUrl = BlogUI.getBaseUrl();

    // Load blog posts
    const cardsHTML = result.posts.map(post => BlogUI.createBlogCard(post, baseUrl)).join('');
    container.innerHTML = cardsHTML;

    // Load pagination
    const paginationHTML = BlogUI.createPagination(result.totalPosts, currentPage, POSTS_PER_PAGE);
    if (paginationHTML) {
      paginationContainer.innerHTML = paginationHTML;
      paginationContainer.style.display = 'flex';
    } else {
      paginationContainer.style.display = 'none';
    }

  } catch (error) {
    console.error('Error loading blog page:', error);
  }
}

// Make loadPage function globally available
window.loadPage = loadPage;

// Check if user is logged in and redirect to home if they are
document.addEventListener('DOMContentLoaded', async () => {
  try {
    await redirectLoggedInUsersToHome();

    // Load the first page of blog posts
    await loadPage(1);
  } catch (error) {
    console.error('Error during blog page initialization:', error);
  }
});
