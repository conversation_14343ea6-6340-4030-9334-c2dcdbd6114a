import { redirectLoggedInUsersToHome } from '../../../services/util.js';
import blogService from '../../../services/blog.js';
import { BlogUI } from '../../../services/blog-ui.js';

// Get current post slug from URL
const currentSlug = 'why-rent-reporting-matters-for-landlords';

async function loadRelatedArticles() {
  try {
    // Get sidebar container only
    const sidebarContainer = document.getElementById('related-articles');
    if (!sidebarContainer) return;

    // Get related posts from blog service
    const relatedPosts = await blogService.getRelatedPosts(currentSlug, 3);

    if (relatedPosts.length === 0) {
      sidebarContainer.innerHTML = '<p class="text-gray-500">No related articles found.</p>';
      return;
    }

    // Get base URL for navigation
    const baseUrl = BlogUI.getBaseUrl();

    // Generate HTML for related articles
    const articlesHTML = relatedPosts.map(post =>
      BlogUI.createRelatedArticleCard(post, baseUrl)
    ).join('');

    // Load articles into sidebar container
    sidebarContainer.innerHTML = articlesHTML;
  } catch (error) {
    console.error('Error loading related articles:', error);
  }
}

// Copy link functionality
function setupCopyLinkButton() {
  const copyLinkBtn = document.getElementById('copy-link-btn');
  if (copyLinkBtn) {
    copyLinkBtn.addEventListener('click', async () => {
      try {
        await navigator.clipboard.writeText(window.location.href);

        // Temporarily change button text to show success
        const originalText = copyLinkBtn.innerHTML;
        copyLinkBtn.innerHTML = `
          <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="size-6">
            <path stroke-linecap="round" stroke-linejoin="round" d="m4.5 12.75 6 6 9-13.5" />
          </svg>
          Copied!
        `;

        // Reset button text after 2 seconds
        setTimeout(() => {
          copyLinkBtn.innerHTML = originalText;
        }, 2000);
      } catch (err) {
        console.error('Failed to copy link:', err);
        // Fallback for older browsers
        const textArea = document.createElement('textarea');
        textArea.value = window.location.href;
        document.body.appendChild(textArea);
        textArea.select();
        document.execCommand('copy');
        document.body.removeChild(textArea);

        // Show success message
        const originalText = copyLinkBtn.innerHTML;
        copyLinkBtn.innerHTML = `
          <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="size-6">
            <path stroke-linecap="round" stroke-linejoin="round" d="m4.5 12.75 6 6 9-13.5" />
          </svg>
          Copied!
        `;

        setTimeout(() => {
          copyLinkBtn.innerHTML = originalText;
        }, 2000);
      }
    });
  }
}

// Check if user is logged in and redirect to home if they are
document.addEventListener('DOMContentLoaded', async () => {
  try {
    await redirectLoggedInUsersToHome();

    // Load related articles
    await loadRelatedArticles();

    // Setup copy link button
    setupCopyLinkButton();
  } catch (error) {
    console.error('Error during page initialization:', error);
  }
});
