<div class="fixed inset-0 z-50">
  <!-- 遮罩层 -->
  <div class="fixed inset-0 bg-black bg-opacity-50 pointer-events-auto" onclick="this.parentElement.remove()"></div>
  <!-- Modal 容器 -->
  <div class="fixed inset-0 flex items-center justify-center pointer-events-none">
    <div class="bg-white sm:rounded-lg shadow-xl max-w-[1120px] sm:w-9/12 w-full h-full sm:max-h-[80vh] sm:min-h-[70vh] overflow-hidden pointer-events-auto relative">
      <!-- Header -->
      <div class="sticky top-0 px-6 py-4 border-b bg-white z-10 flex justify-between items-center">
        <h3 class="text-lg font-semibold text-gray-900">Create Report</h3>
        <button class="p-2 hover:bg-gray-100 rounded-full" onclick="document.getElementById('modal').innerHTML = ''">
          <!-- 关闭图标 -->
          <svg class="w-5 h-5 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
          </svg>
        </button>
      </div>
      <!-- Body -->
      <div class="px-8 lg:px-16 py-4 overflow-auto h-full">
        <form id="create-report-form" class="space-y-4 mt-6" enctype="multipart/form-data">
          <div>
            <label class="block font-bold mb-1">Select Tenant/Lease</label>
            <input
              placeholder="Search by tenant name, email, phone or property"
              type="text"
              class="p-2 px-10 border border-1 rounded text-sm w-full"
              style="background-image: url('data:image/svg+xml,%3Csvg xmlns=\'http://www.w3.org/2000/svg\' class=\'h-4 w-4\' viewBox=\'0 0 24 24\' fill=\'none\' stroke=\'%236B7280\' stroke-width=\'2\'%3E%3Cpath stroke-linecap=\'round\' stroke-linejoin=\'round\' d=\'M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z\'%3E%3C/path%3E%3C/svg%3E');
                background-position: 8px 8px;
                background-repeat: no-repeat;
                background-size: 20px;"
            />
          </div>
          <div>
            <label class="block font-bold mb-1">Report Title</label>
            <input type="text" name="title" placeholder="Enter report title" class="w-full border rounded p-2" required />
          </div>
          <div>
            <label class="block font-bold mb-1">Detailed Content</label>
            <textarea name="details" placeholder="Enter detailed content" class="w-full border rounded p-2" rows="4" required></textarea>
          </div>
          <div>
            <label class="block font-bold mb-1">Purpose</label>
            <input type="text" name="purpose" placeholder="Enter purpose" class="w-full border rounded p-2" />
          </div>
          <div>
            <label class="block font-bold mb-1">Visibility</label>
            <select name="visibility" class="border rounded p-2 w-full">
              <option value="public">Public</option>
              <option value="private">Private</option>
            </select>
          </div>
          <div>
            <label class="block font-bold mb-1">Upload Files</label>
            <input type="file" name="files" multiple class="w-full" />
          </div>
        </form>
      </div>
      <!-- Footer -->
      <div class="absolute bottom-0 left-0 right-0 px-6 py-4 bg-gray-50 sm:rounded-b-lg flex justify-between gap-2 border-t">
        <button class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50"
          onclick="document.getElementById('modal').innerHTML = ''">Cancel</button>
        <button class="px-4 py-2 text-sm font-medium text-white bg-slate-800 rounded-md hover:bg-slate-900"
          type="submit" form="create-report-form">Submit</button>
      </div>
    </div>
  </div>
</div> 