// Import dependencies
import $ from 'jquery'
import { propertyApi, leaseApi, userApi } from '../../services'
import { checkAuthStatusAndRedirect } from '../../services/util'
import Mustache from 'mustache'
import accountSummaryTemplate from '../../templates/account-summary.mst?raw'
import errorMessagesTemplate from '../../templates/error-messages.mst?raw'
import { initCreateLeaseForm } from '../leases/main.js'
import { initCreatePropertyForm } from '../properties/main.js'
import { renderSidebarNav } from '../../services/sidebar'

// Page initialization
$(document).ready(async function() {
    // 检查是否带有邀请参数
    const urlParams = new URLSearchParams(window.location.search);
    if (urlParams.get('fromInvitation') === '1' && urlParams.get('code')) {
        window.invitationCode = urlParams.get('code');
        window.invitationEmail = urlParams.get('email') || '';
        // 也可以存到localStorage/sessionStorage
        sessionStorage.setItem('invitationCode', window.invitationCode);
        sessionStorage.setItem('invitationEmail', window.invitationEmail);
    }
    // await checkAuthStatusAndRedirect();
    await loadAccountSummary();
    await loadReferralCode();
    
    try {
        const userData = await userApi.getUserInfo();
        await renderSidebarNav(userData);
    } catch (e) {
        // 可以根据需要处理未登录等情况
    }
    
    // Initialize tab handlers
    $('#tab-container button').on('click', function() {
        const tabId = $(this).data('tab');
        switchTab(tabId);
    });
    
    // Set initial active tab
    switchTab('rent-reporting');

    $('#rent-reporting-create-property-btn').on('click', function() {
        window.location.href = '/pages/properties/?autoOpen=1';
    });
});

// Load account summary data
async function loadAccountSummary() {
    try {
        // Get properties and leases data
        const propertyResponse = await propertyApi.getProperties();
        const leaseResponse = await leaseApi.getLeases();

        // Calculate summary data
        const activeLeases = (leaseResponse.items || []).filter(l => (l.status || '').toLowerCase() === 'active').length;
        const totalVacantRooms = propertyResponse.items.reduce((total, property) => total + (property.vacantRooms || 0), 0);

        // Render template with data
        const renderedHtml = Mustache.render(accountSummaryTemplate, {
            activeLeases: activeLeases,
            vacantRooms: totalVacantRooms
        });

        // Update the container
        $('#account-summary-container').html(renderedHtml);

    } catch (error) {
        golog.Error('Failed to load account summary:', error);
        const errorHtml = Mustache.render(errorMessagesTemplate, { accountSummaryError: true });
        $('#account-summary-container').html(errorHtml);
    }
}

// Referral code logic
async function loadReferralCode() {
    try {
        // 1. Always fetch fresh user info from API to ensure accuracy
        let user = null;
        try {
            user = await userApi.getUserInfo();
            // Update localStorage with fresh data
            localStorage.setItem('user', JSON.stringify(user));
        } catch (e) {
            // Fallback to localStorage only if API fails
            try {
                user = JSON.parse(localStorage.getItem('user'));
            } catch (parseError) {
                golog.Error('Failed to parse stored user data:', parseError);
            }
        }
        // 2. Try to get referral code by creator
        let codeData = null;
        try {
            const res = await fetch(`/v1/referral-codes?creator=${encodeURIComponent(user.id)}`);
            if (res.ok) {
                const data = await res.json();
                if (data && data.items && data.items.length > 0) {
                    codeData = data.items[0];
                }
            }
        } catch (e) {}
        // 3. If not found, create one
        if (!codeData) {
            try {
                // 只有当前用户 id 不在 referral_code 数据库的 crtr_uid 字段时才新建
                // 再次确认接口返回为空才创建
                const resCheck = await fetch(`/v1/referral-codes?creator=${encodeURIComponent(user.id)}`);
                let alreadyExists = false;
                if (resCheck.ok) {
                    const data = await resCheck.json();
                    if (data && data.items && data.items.length > 0) {
                        alreadyExists = true;
                        codeData = data.items[0];
                    }
                }
                if (!alreadyExists) {
                    const res = await fetch('/v1/referral-codes', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' }
                    });
                    if (res.ok) {
                        codeData = await res.json();
                    }
                }
            } catch (e) {}
        }
        // 4. Render referral code
        if (codeData && codeData.code) {
            $('#referral-code-container').html(`
                <div class="flex flex-row gap-4 items-center">
                    <span class="text-xl font-mono" id="referral-code-value">${codeData.code}</span>
                    <button class="btn btn-sm btn-outline" id="copy-referral-code">Copy</button>
                </div>
                <p>Share your referral code with your contacts. When they sign up, you both get a discount on your next rent report!</p>
            `);
            $('#copy-referral-code').on('click', function() {
                const code = $('#referral-code-value').text();
                const button = $(this);

                // 尝试使用现代 Clipboard API
                if (navigator.clipboard && window.isSecureContext) {
                    navigator.clipboard.writeText(code).then(() => {
                        button.text('Copied!');
                        setTimeout(() => { button.text('Copy'); }, 1500);
                    }).catch(() => {
                        // 如果现代API失败，使用fallback方法
                        fallbackCopyTextToClipboard(code, button);
                    });
                } else {
                    // 使用fallback方法
                    fallbackCopyTextToClipboard(code, button);
                }
            });
        } else {
            $('#referral-code-container').html('<span class="text-red-500">Failed to load referral code.</span>');
        }
    } catch (err) {
        $('#referral-code-container').html('<span class="text-red-500">Failed to load referral code.</span>');
    }
}

// Fallback function for copying text to clipboard (works in non-HTTPS environments)
function fallbackCopyTextToClipboard(text, button) {
    // Create a temporary textarea element
    const textArea = document.createElement('textarea');
    textArea.value = text;

    // Make it invisible but still selectable
    textArea.style.position = 'fixed';
    textArea.style.left = '-999999px';
    textArea.style.top = '-999999px';
    textArea.setAttribute('readonly', '');

    document.body.appendChild(textArea);

    try {
        // Select the text
        textArea.select();
        textArea.setSelectionRange(0, 99999); // For mobile devices

        // Try to copy using the old execCommand method
        const successful = document.execCommand('copy');

        if (successful) {
            button.text('Copied!');
            setTimeout(() => { button.text('Copy'); }, 1500);
        } else {
            // If execCommand also fails, try to select the original text for manual copy
            selectReferralCodeText();
            button.text('Selected - Press Ctrl+C');
            setTimeout(() => { button.text('Copy'); }, 3000);
        }
    } catch (err) {
        golog.Error('Fallback copy failed:', err);
        // Try to select the original text for manual copy
        selectReferralCodeText();
        button.text('Selected - Press Ctrl+C');
        setTimeout(() => { button.text('Copy'); }, 3000);
    } finally {
        // Clean up
        document.body.removeChild(textArea);
    }
}

// Helper function to select the referral code text for manual copying
function selectReferralCodeText() {
    try {
        const codeElement = document.getElementById('referral-code-value');
        if (codeElement) {
            const range = document.createRange();
            range.selectNodeContents(codeElement);
            const selection = window.getSelection();
            selection.removeAllRanges();
            selection.addRange(range);
        }
    } catch (err) {
        golog.Error('Failed to select referral code text:', err);
    }
}

// Tutorial tab functionality
function switchTab(tabId) {
    // Hide all content sections using the unique container
    $('#tutorial-tab-content > div').addClass('tab-content-hidden');
    
    // Show selected content
    $(`#${tabId}`).removeClass('tab-content-hidden');
    
    // Reset all tabs to default state
    $('#tab-container button').each(function() {
        $(this).removeClass('tab-button-active').addClass('tab-button-default');
    });
    
    // Style active tab
    $(`button[data-tab="${tabId}"]`).removeClass('tab-button-default').addClass('tab-button-active');
}