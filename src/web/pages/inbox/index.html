{{> header }}
<body class="min-h-screen bg-gray-200">
    <div class="flex min-h-screen">
        {{> navbar}}
        <main class="w-full bg-slate-100 lg:px-16 md:px-8 px-4 lg:ml-64 pb-20">
            <div class="max-w-[1440px] w-full mx-auto mb-32">
                <!-- Header Section -->
                <div class="flex-1 mt-20 lg:mt-8 pb-0 w-full">
                    <h1 class="text-2xl">Inbox</h1>

                    <!-- 搜索框 -->
                    <div class="flex gap-2 mt-4">
                      <div class="flex items-center border border-gray-300 rounded-md bg-white px-3 py-2 sm:max-w-[300px]">
                        <svg
                          class="h-4 w-4 opacity-50"
                          xmlns="http://www.w3.org/2000/svg"
                          viewBox="0 0 24 24"
                        >
                          <g
                            stroke-linejoin="round"
                            stroke-linecap="round"
                            stroke-width="2.5"
                            fill="none"
                            stroke="currentColor"
                          >
                            <circle cx="11" cy="11" r="8"></circle>
                            <path d="m21 21-4.3-4.3"></path>
                          </g>
                        </svg>
                        <input
                          type="search"
                          class="grow outline-none border-none bg-transparent text-gray-900 placeholder-gray-400 ml-2"
                          placeholder="Search messages"
                        />
                      </div>
                      <button
                        class="hidden sm:block bg-slate-800 hover:bg-slate-900 transition text-white px-4 py-2 rounded text-sm whitespace-nowrap"
                      >
                        Search
                      </button>
                    </div>

                    <!-- tab切换和发送按钮 -->
                    <div class="flex md:flex-row flex-col gap-4 justify-between mt-4 w-full">
                      <div class="flex bg-white rounded-xl shadow-sm p-1 gap-2">
                        <button id="tab-inbox" class="px-6 py-3 rounded-lg font-medium text-base transition focus:outline-none bg-white shadow tab-active text-black">Inbox</button>
                        <button id="tab-sent" class="px-6 py-3 rounded-lg font-medium text-base transition focus:outline-none text-gray-400 bg-transparent">Sent Messages</button>
                      </div>
                      <div class="flex items-end">
                        <button
                          class="flex items-center whitespace-nowrap bg-slate-800 hover:bg-slate-900 transition text-white px-4 py-2 gap-2 rounded text-sm"
                          onclick="document.getElementById('modal').innerHTML = document.getElementById('send-message-template').innerHTML"
                        >
                          <svg
                            class="w-5 h-5"
                            fill="none"
                            stroke="currentColor"
                            viewBox="0 0 24 24"
                          >
                            <path
                              stroke-linecap="round"
                              stroke-linejoin="round"
                              stroke-width="2"
                              d="M12 4v16m8-8H4"
                            />
                          </svg>
                          Send Message
                        </button>
                      </div>
                    </div>
                </div>

                <!-- Inbox Table Section -->
                <div id="message-list" class="max-w-[1440px] w-full mx-auto mb-32">
                    <!-- Desktop View -->
                    <div class="hidden lg:block">
                        <table
                            class="w-full text-sm rounded-md overflow-hidden mt-4"
                            style="box-shadow: 0 0 0 1px rgb(203 213 225)"
                        >
                            <thead>
                                <tr
                                    class="bg-slate-200 text-left text-black uppercase text-xs border-b border-slate-300"
                                >
                                    <th class="p-4 w-5/12">
                                        <span class="flex items-center gap-2">Subject</span>
                                    </th>
                                    <th class="p-4 w-4/12">
                                        <span class="flex items-center gap-2">From</span>
                                    </th>
                                    <th class="p-4 w-2/12">
                                        <span class="flex items-center gap-2">Date</span>
                                    </th>
                                </tr>
                            </thead>
                            <tbody>
                                <!-- 消息列表将通过JavaScript动态加载 -->
                            </tbody>
                        </table>
                        <!-- Desktop Pagination -->
                        <div class="hidden lg:flex justify-between items-center gap-4 px-4 py-2 mt-4 bg-white border border-gray-300 rounded-lg">
                          <span class="text-sm text-gray-500">No messages</span>
                          <div class="join">
                            <button class="join-item btn">1</button>
                          </div>
                        </div>
                    </div>

                    <!-- Mobile/Tablet View -->
                    <div class="lg:hidden">
                        <div class="mt-4 rounded-md overflow-hidden" style="box-shadow: 0 0 0 1px rgb(203 213 225)">
                            <!-- 消息列表将通过JavaScript动态加载 -->
                        </div>
                        <!-- Mobile Pagination -->
                        <div class="flex justify-center items-center gap-4 px-4 py-2 mt-4 bg-white border rounded-lg">
                          <button class="p-2 border rounded-md">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
                            </svg>
                          </button>
                          <span class="text-sm">No messages</span>
                          <button class="p-2 border rounded-md">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                            </svg>
                          </button>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- Modal Container -->
    <div id="modal" class=""></div>

    <!-- Send Message Template -->
    <template id="send-message-template">
      <div class="fixed inset-0 z-50">
        <!-- Dark overlay with click handler to close -->
        <div
          class="fixed inset-0 bg-black/50 pointer-events-auto"
          onclick="document.getElementById('modal').innerHTML = ''"
        ></div>

        <!-- Modal container -->
        <div
          class="fixed inset-0 flex items-center justify-center pointer-events-none"
        >
          <div
            class="bg-white sm:rounded-lg shadow-xl sm:w-9/12 w-full h-full sm:max-h-[80vh] sm:min-h-[70vh] overflow-hidden pointer-events-auto relative"
          >
            <!-- Modal Header -->
            <div
              class="sticky top-0 px-6 py-4 border-b bg-white z-10 flex justify-between items-center"
            >
              <div>
                <h3 class="text-lg font-semibold text-gray-900">Write Message</h3>
              </div>
              <button
                class="p-2 hover:bg-gray-100 rounded-full"
                onclick="document.getElementById('modal').innerHTML = ''"
              >
                <svg
                  class="w-5 h-5 text-gray-500"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M6 18L18 6M6 6l12 12"
                  />
                </svg>
              </button>
            </div>

            <!-- Modal Body -->
            <div class="px-6 py-4 overflow-auto" style="height: calc(100% - 140px)">
              <!-- To -->
              <div class="mb-4">
                <div class="text-base font-medium text-gray-700 mb-2">To</div>
                <div class="flex flex-wrap gap-2 p-2 border border-gray-300 rounded bg-white relative">
                  <!-- 已选择的收件人标签 -->
                  <div id="selected-recipients" class="flex flex-wrap gap-2"></div>
                  <!-- 输入框容器 -->
                  <div class="flex-1 min-w-[200px] mt-2">
                    <div class="flex items-center gap-2">
                      <img src="/assets/search.svg" class="w-4 h-4 opacity-60" />
                      <input 
                        type="text" 
                        id="recipient-input"
                        class="flex-1 outline-none border-none bg-transparent text-gray-900 placeholder-gray-400" 
                        placeholder="Find tenant names, emails, or addresses" 
                        autocomplete="off"
                      />
                    </div>
                    <!-- 自动补全下拉列表 -->
                    <div id="autocomplete-results" class="hidden absolute left-0 right-0 mt-2 w-full bg-white border border-gray-300 rounded-md shadow-lg max-h-60 overflow-auto z-50"></div>
                  </div>
                </div>
              </div>

              <fieldset class="fieldset">
                <legend class="fieldset-legend">Message Subject</legend>
                <select class="w-full bg-white border border-gray-300 rounded-md p-2 focus:outline-none focus:ring-2 focus:ring-slate-200 mt-2 mb-4">
                  <option disabled selected>Select a message option</option>
                  <option>Rent Reminder</option>
                  <option>Late Payment Notice</option>
                  <option>Payment Received</option>
                  <option>Lease Renewal Notice</option>
                  <option>Maintenance Request Acknowledgment</option>
                  <option>Inspection Schedule Notice</option>
                  <option>Violation Warning</option>
                  <option>Move-Out Reminder</option>
                  <option>General Announcement</option>
                  <option>Contact Update Request</option>
                  <option>Other</option>
                </select>
              </fieldset>
              <fieldset class="fieldset">
                <legend class="fieldset-legend">Message Preview</legend>
                <div id="message-preview" class="w-full bg-white border border-gray-300 p-4 rounded-md min-h-[120px] resize-y outline-none mt-2" contenteditable="true" spellcheck="false" style="white-space:pre-line;"></div>
              </fieldset>
            </div>

            <!-- Modal Footer -->
            <div
              class="absolute bottom-0 left-0 right-0 px-6 py-4 bg-gray-50 sm:rounded-b-lg flex justify-between gap-2 border-t"
            >
              <button
                class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50"
                onclick="document.getElementById('modal').innerHTML = ''"
              >
                Cancel
              </button>
              <button
                id="send-message-btn"
                class="px-4 py-2 text-sm font-medium text-white bg-slate-800 rounded-md hover:bg-slate-900"
              >
                Send Message
              </button>
            </div>
          </div>
        </div>
      </div>
    </template>

{{> footer }}
<script type="module" src="../../services/inbox.js"></script>
<script type="module" src="./main.js"></script>