import { leaseApi } from '../../services';
import { userApi } from '../../services';
import { renderSidebarNav } from '../../services/sidebar';
window.leaseApi = leaseApi;

// 导入InboxService
import '../../services/inbox.js';

// 收件人自动完成功能
document.addEventListener('DOMContentLoaded', function() {
    const recipientSearch = document.getElementById('recipient-search');
    const recipientsContainer = document.getElementById('recipients-container');
    const recipientSuggestions = document.getElementById('recipient-suggestions');
    const selectedRecipients = new Set();

    if (recipientSearch) {
        // 处理收件人选择
        recipientSearch.addEventListener('keydown', function(e) {
            if (e.key === 'Enter' && this.value.trim()) {
                e.preventDefault();
                addRecipient(this.value.trim());
                this.value = '';
            }
        });

        // 处理收件人建议点击
        recipientSuggestions.addEventListener('click', function(e) {
            const suggestion = e.target.closest('.suggestion-item');
            if (suggestion) {
                const userId = suggestion.dataset.userId;
                const userName = suggestion.dataset.userName;
                const userEmail = suggestion.dataset.userEmail;
                
                if (!selectedRecipients.has(userId)) {
                    addRecipient(userName, userId, userEmail);
                    selectedRecipients.add(userId);
                }
                
                recipientSearch.value = '';
                recipientSuggestions.innerHTML = '';
            }
        });
    }

    // 添加收件人标签
    function addRecipient(name, id, email) {
        const badge = document.createElement('div');
        badge.className = 'inline-flex items-center px-2 py-1 rounded-full text-sm bg-indigo-100 text-indigo-800';
        badge.innerHTML = `
            ${name} ${email ? `<span class="text-indigo-600">(${email})</span>` : ''}
            <button type="button" class="ml-1 text-indigo-600 hover:text-indigo-800" onclick="this.parentElement.remove()">
                <i class="mdi mdi-close"></i>
            </button>
            <input type="hidden" name="recipients[]" value="${id || name}">
        `;
        recipientsContainer.insertBefore(badge, recipientSearch);
    }

    // 表单验证
    const messageForm = document.querySelector('form[hx-post="/api/messages/send"]');
    if (messageForm) {
        messageForm.addEventListener('submit', function(e) {
            const recipients = this.querySelectorAll('input[name="recipients[]"]');
            const subject = this.querySelector('#subject').value;
            const body = this.querySelector('#body').value;

            if (recipients.length === 0) {
                e.preventDefault();
                showError('Please select at least one recipient');
                return;
            }

            if (!subject) {
                e.preventDefault();
                showError('Please select a subject');
                return;
            }

            if (!body.trim()) {
                e.preventDefault();
                showError('Please enter a message');
                return;
            }
        });
    }

    // 获取DOM元素
    const inboxTab = document.querySelector('a[role="tab"]:first-child');
    const sentTab = document.querySelector('a[role="tab"]:last-child');
    const searchInput = document.querySelector('input[type="search"]');
    // 修复按钮查找方式
    let searchButton = null;
    let sendMessageButton = null;
    document.querySelectorAll('button').forEach(btn => {
        if (btn.textContent.trim() === 'Search') searchButton = btn;
        if (btn.textContent.trim() === 'Send Message') sendMessageButton = btn;
    });

    // Tab切换功能 - 移除重复的事件监听器，使用统一的setupInboxTabs函数

    // 搜索功能
    if (searchInput && searchButton) {
        searchButton.addEventListener('click', function() {
            const searchTerm = searchInput.value.trim();
            if (searchTerm) {
            }
        });
    }

    // 发送消息按钮
    if (sendMessageButton) {
        sendMessageButton.addEventListener('click', function() {
        });
    }
});

// 显示错误消息
function showError(message) {
    const errorContainer = document.createElement('div');
    errorContainer.className = 'fixed top-4 left-1/2 transform -translate-x-1/2 z-50 rounded-md bg-red-50 p-4 shadow-lg';
    errorContainer.innerHTML = `
        <div class="flex">
            <div class="flex-shrink-0">
                <i class="mdi mdi-alert-circle text-red-400"></i>
            </div>
            <div class="ml-3">
                <p class="text-sm font-medium text-red-800">${message}</p>
            </div>
        </div>
    `;

    document.body.appendChild(errorContainer);

    // 3秒后自动移除错误消息
    setTimeout(() => {
        errorContainer.remove();
    }, 3000);
}

// 显示成功消息
function showSuccess(message) {
    const successContainer = document.createElement('div');
    successContainer.className = 'fixed top-4 left-1/2 transform -translate-x-1/2 z-50 rounded-md bg-green-50 p-4 shadow-lg';
    successContainer.innerHTML = `
        <div class="flex">
            <div class="flex-shrink-0">
                <i class="mdi mdi-check-circle text-green-400"></i>
            </div>
            <div class="ml-3">
                <p class="text-sm font-medium text-green-800">${message}</p>
            </div>
        </div>
    `;
    document.body.appendChild(successContainer);

    setTimeout(() => {
        successContainer.remove();
    }, 3000);
}

// 处理消息发送成功
document.body.addEventListener('htmx:afterRequest', function(evt) {
    if (evt.detail.pathInfo.requestPath === '/api/messages/send' && evt.detail.successful) {
        showSuccess('Message sent successfully');
        // 关闭模态框
        document.getElementById('modal').innerHTML = '';
        document.getElementById('modal').classList.add('hidden');
        // 自动切换到Sent Messages标签
        const sentTab = document.getElementById('tab-sent');
        if (sentTab) sentTab.click();
        // 刷新已发送消息列表
        if (typeof refreshSentMessages === 'function') refreshSentMessages();
    }
});

// 自动补全功能
let selectedRecipients = new Set();
let debounceTimer;

// 统一动态渲染预览内容
async function updateMessagePreview(subject) {
  const previewDiv = document.querySelector('#message-preview');
  if (!previewDiv) return;

  const recipientInput = document.querySelector('input[name="recipients[]"]');
  let tenant = null;
  let recipientName = 'tenantName';
  let rentAmount = '';
  let dueDate = '';
  let hasRecipient = false;

  if (recipientInput && window.selectedTenantMap) {
    tenant = window.selectedTenantMap[recipientInput.value] || window.selectedTenantMap[recipientInput.dataset.userId] || window.selectedTenantMap[recipientInput.dataset.email];
    if (tenant) {
      recipientName = `${tenant.firstName || tenant.name || ''} ${tenant.lastName || ''}`.trim();
      hasRecipient = true;
    }
  }

  // 查 lease 信息
  let lease = null;
  if (tenant && tenant.leaseId && ['Rent Reminder', 'Late Payment Notice', 'Payment Received'].includes(subject)) {
    try {
      lease = await window.leaseApi.getLeaseById(tenant.leaseId);
      rentAmount = lease.rentAmount || '';
      if (lease.rentDueDay) {
        const now = new Date();
        const monthNames = ["Jan.", "Feb.", "Mar.", "Apr.", "May.", "Jun.", "Jul.", "Aug.", "Sep.", "Oct.", "Nov.", "Dec."];
        const month = monthNames[now.getMonth()];
        const day = lease.rentDueDay;
        const getDaySuffix = (day) => {
          if (day >= 11 && day <= 13) return 'th';
          switch (day % 10) {
            case 1: return 'st';
            case 2: return 'nd';
            case 3: return 'rd';
            default: return 'th';
          }
        };
        dueDate = `${month}${day}${getDaySuffix(day)}`;
      }
    } catch (e) {
    }
  }

  // 获取当前用户信息
  let currentUser = window.currentUser || {};
  if (localStorage.getItem('user')) {
    try {
      currentUser = { ...currentUser, ...JSON.parse(localStorage.getItem('user')) };
    } catch {}
  }
  const agentName = currentUser.usrNm || currentUser.name || currentUser.username || currentUser.email || 'agentName';
  const hasAgentName = !!(currentUser.usrNm || currentUser.name || currentUser.username || currentUser.email);

  // 特殊模板处理
  if (subject === 'Rent Reminder' && hasRecipient && rentAmount && dueDate) {
    previewDiv.innerHTML = `Dear <span class="text-black">${recipientName}</span>,<br><br>This is a friendly reminder that your monthly rent of $<span class="text-black">${rentAmount}</span> is due on <span class="text-black">${dueDate}</span>.<br>Please make your payment on time to avoid any late fees.<br><br>If you've already paid, you can disregard this message.<br><br>Thank you,<br><br><span class="${hasAgentName ? 'text-black' : 'text-red-500'}">${agentName}</span>`;
    const bodyInput = document.querySelector('textarea[name=\"body\"], input[name=\"body\"]');
    if (bodyInput) {
        bodyInput.value = previewDiv.innerHTML;
    }
    return;
  }

  if (subject === 'Late Payment Notice' && hasRecipient && rentAmount && dueDate) {
    previewDiv.innerHTML = `Dear <span class="text-black">${recipientName}</span>,<br><br>Our records show that your rent payment of $<span class="text-black">${rentAmount}</span> due on <span class="text-black">${dueDate}</span> has not yet been received.<br>Please make the payment as soon as possible to avoid further penalties.<br><br>If you have any questions, feel free to reach out.<br><br>Best regards,<br><span class="${hasAgentName ? 'text-black' : 'text-red-500'}">${agentName}</span>`;
    const bodyInput = document.querySelector('textarea[name=\"body\"], input[name=\"body\"]');
    if (bodyInput) {
        bodyInput.value = previewDiv.innerHTML;
    }
    return;
  }

  if (subject === 'Payment Received' && hasRecipient && rentAmount && dueDate) {
    previewDiv.innerHTML = `Dear <span class="text-black">${recipientName}</span>,<br><br>We have received your rent payment of $<span class="text-black">${rentAmount}</span> for the period starting <span class="text-black">${dueDate}</span>.<br>Thank you for your prompt payment.<br><br>Let us know if you have any questions.<br><br>Sincerely,<br><span class="${hasAgentName ? 'text-black' : 'text-red-500'}">${agentName}</span>`;
    const bodyInput = document.querySelector('textarea[name=\"body\"], input[name=\"body\"]');
    if (bodyInput) {
        bodyInput.value = previewDiv.innerHTML;
    }
    return;
  }

  // fallback 默认模板
  const defaultTemplates = {
    'Rent Reminder': `Dear <span class="text-red-500">${recipientName}</span>,<br><br>This is a friendly reminder that your monthly rent of $<span class="text-red-500">amount</span> is due on <span class="text-red-500">dueDate</span>.<br>Please make your payment on time to avoid any late fees.<br><br>If you've already paid, you can disregard this message.<br><br>Thank you,<br><br><span class="${hasAgentName ? 'text-black' : 'text-red-500'}">${agentName}</span>`,
    'Late Payment Notice': `Dear <span class="text-red-500">${recipientName}</span>,<br><br>Our records show that your rent payment of $<span class="text-red-500">amount</span> due on <span class="text-red-500">dueDate</span> has not yet been received.<br>Please make the payment as soon as possible to avoid further penalties.<br><br>If you have any questions, feel free to reach out.<br><br>Best regards,<br><span class="${hasAgentName ? 'text-black' : 'text-red-500'}">${agentName}</span>`,
    'Payment Received': `Dear <span class="text-red-500">${recipientName}</span>,<br><br>We have received your rent payment of $<span class="text-red-500">amount</span> for the period starting <span class="text-red-500">startDate</span>.<br>Thank you for your prompt payment.<br><br>Let us know if you have any questions.<br><br>Sincerely,<br><span class="${hasAgentName ? 'text-black' : 'text-red-500'}">${agentName}</span>`,
    'Other': `Dear <span class="text-red-500">${recipientName}</span>,<br><br>This is to inform you of the following:<br><br><span class="text-red-500">customMessageContent</span><br><br>Best regards,<br><span class="${hasAgentName ? 'text-black' : 'text-red-500'}">${agentName}</span>`
    // 你可以继续扩展其他模板
  };

  previewDiv.innerHTML = defaultTemplates[subject] || '<span class="text-gray-400">Please select a subject to preview the message template.</span>';
  const bodyInput = document.querySelector('textarea[name=\"body\"], input[name=\"body\"]');
  if (bodyInput) {
      bodyInput.value = previewDiv.innerHTML;
  }
}


// 修改selectRecipient和主题切换，统一调用updateMessagePreview
window.selectRecipient = async function(tenant) {
  if (!window.selectedTenantMap) window.selectedTenantMap = {};
  if (tenant.id) window.selectedTenantMap[tenant.id] = tenant;
  if (tenant.email) window.selectedTenantMap[tenant.email] = tenant;
  if (selectedRecipients.has(tenant.id)) {
    return;
  }
  selectedRecipients.add(tenant.id);
  let lease = null;
  let rentAmount = '';
  let dueDate = '';
  if (tenant.leaseId) {
    try {
      lease = await window.leaseApi.getLeaseById(tenant.leaseId);
      rentAmount = lease.rentAmount || '';
      // 计算dueDate
      if (lease.rentDueDay) {
        const now = new Date();
        const monthNames = ["Jan.", "Feb.", "Mar.", "Apr.", "May.", "Jun.", "Jul.", "Aug.", "Sep.", "Oct.", "Nov.", "Dec."];
        const month = monthNames[now.getMonth()];
        const day = lease.rentDueDay;
        // 英文序号后缀
        function getDaySuffix(day) {
          if (day >= 11 && day <= 13) return 'th';
          switch (day % 10) {
            case 1: return 'st';
            case 2: return 'nd';
            case 3: return 'rd';
            default: return 'th';
          }
        }
        dueDate = `${month}${day}${getDaySuffix(day)}`;
      }
    } catch (e) {
    }
  } else {
  }
  // 兼容弹窗和主页面
  const selectedContainer = document.getElementById('selected-recipients') || document.getElementById('recipients-container');
  const input = document.getElementById('recipient-input') || document.getElementById('recipient-search');
  if (!selectedContainer || !input) {
    return;
  }
  // 添加收件人标签
  const badge = document.createElement('div');
  badge.className = 'inline-flex items-center gap-1 px-2 py-1 bg-gray-100 rounded-full text-sm';

  // 构建显示名称，包含property和unit信息
  let displayName = `${tenant.firstName || tenant.name || ''} ${tenant.lastName || ''}`;
  if (tenant.propertyName && tenant.roomName) {
    displayName += ` (${tenant.propertyName} - ${tenant.roomName})`;
  } else if (tenant.propertyName) {
    displayName += ` (${tenant.propertyName})`;
  } else if (tenant.roomName) {
    displayName += ` (${tenant.roomName})`;
  }

  badge.innerHTML = `
    <span>${displayName}</span>
    <button
      class="hover:text-red-500"
      onclick="removeRecipient('${tenant.id}', this)">
      <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
      </svg>
    </button>
    <input type="hidden" name="recipients[]" value="${tenant.email}"
           data-first-name="${tenant.firstName}"
           data-last-name="${tenant.lastName}"
           data-lease-id="${tenant.leaseId || ''}"
           data-lease-status="${tenant.leaseStatus || ''}">
  `;
  selectedContainer.appendChild(badge);
  // 清空输入框并隐藏结果
  input.value = '';
  const results = document.getElementById('autocomplete-results') || document.getElementById('recipient-suggestions');
  if (results) results.classList.add('hidden');

  // 统一调用
  const subjectSelect = document.querySelector('select');
  if (subjectSelect && subjectSelect.value) {
    await updateMessagePreview(subjectSelect.value);
  } else if (subjectSelect) {
    // 如果主题还没选，监听主题变化后自动刷新
    subjectSelect.addEventListener('change', function() {
      updateMessagePreview(this.value);
    });
  }
};

// 处理收件人点击跳转到lease
window.handleRecipientClick = async function(leaseId, leaseStatus) {
  if (!leaseId || leaseId.trim() === '') {
    return;
  }

  try {
    // 实时检查lease的当前状态
    const lease = await window.leaseApi.getLeaseById(leaseId);

    // 检查lease状态
    if (lease.status === 'deleted') {
      // 显示确认弹窗
      const modalHtml = `
        <div class="fixed inset-0 z-50">
          <div class="fixed inset-0 bg-black bg-opacity-50"></div>
          <div class="fixed inset-0 flex items-center justify-center">
            <div class="bg-white rounded-lg p-6 max-w-md mx-4">
              <h3 class="text-lg font-semibold mb-4">Lease Not Available</h3>
              <p class="text-gray-600 mb-6">This lease has been deleted and is no longer accessible.</p>
              <div class="flex justify-end">
                <button
                  class="px-4 py-2 bg-slate-800 text-white rounded hover:bg-slate-900"
                  onclick="document.getElementById('modal').innerHTML = ''"
                >
                  OK
                </button>
              </div>
            </div>
          </div>
        </div>
      `;
      document.getElementById('modal').innerHTML = modalHtml;
      return;
    }

    // 跳转到lease detail页面
    window.location.href = `/pages/lease_detail/?id=${leaseId}`;

  } catch (error) {
    // 如果API调用失败，也显示错误弹窗
    const modalHtml = `
      <div class="fixed inset-0 z-50">
        <div class="fixed inset-0 bg-black bg-opacity-50"></div>
        <div class="fixed inset-0 flex items-center justify-center">
          <div class="bg-white rounded-lg p-6 max-w-md mx-4">
            <h3 class="text-lg font-semibold mb-4">Lease Not Available</h3>
            <p class="text-gray-600 mb-6">This lease is no longer accessible or has been deleted.</p>
            <div class="flex justify-end">
              <button
                class="px-4 py-2 bg-slate-800 text-white rounded hover:bg-slate-900"
                onclick="document.getElementById('modal').innerHTML = ''"
              >
                OK
              </button>
            </div>
          </div>
        </div>
      `;
    document.getElementById('modal').innerHTML = modalHtml;
  }
};

// 将函数定义在全局作用域
window.removeRecipient = function(tenantId, button) {
  selectedRecipients.delete(tenantId);
  button.parentElement.remove();
  
  // 如果已经选择了主题，更新预览
  const subjectSelect = document.querySelector('select');
  if (subjectSelect && subjectSelect.value) {
    const previewDiv = document.querySelector('#message-preview');
    if (previewDiv) {
      const templates = {
        'Rent Reminder': `Dear <span class="text-red-500">tenantName</span>,<br><br>This is a friendly reminder that your monthly rent of $<span class="text-red-500">amount</span> is due on <span class="text-red-500">dueDate</span>.<br>Please make your payment on time to avoid any late fees.<br><br>If you've already paid, you can disregard this message.<br><br>Thank you,<br><br><span class="text-red-500">agentName</span>`,
        'Late Payment Notice': `Dear <span class="text-red-500">tenantName</span>,<br><br>Our records show that your rent payment of $<span class="text-red-500">amount</span> due on <span class="text-red-500">dueDate</span> has not yet been received.<br>Please make the payment as soon as possible to avoid further penalties.<br><br>If you have any questions, feel free to reach out.<br><br>Best regards,<br><span class="text-red-500">agentName</span>`,
        'Payment Received': `Dear <span class="text-red-500">tenantName</span>,<br><br>We have received your rent payment of $<span class="text-red-500">amount</span> for the period starting <span class="text-red-500">startDate</span>.<br>Thank you for your prompt payment.<br><br>Let us know if you have any questions.<br><br>Sincerely,<br><span class="text-red-500">agentName</span>`,
        'Lease Renewal Notice': `Dear <span class="text-red-500">tenantName</span>,<br><br>Your current lease agreement is approaching its end date. We would like to discuss the possibility of renewal.<br><br>Please let us know if you're interested in renewing your lease.<br><br>Best regards,<br><span class="text-red-500">agentName</span>`,
        'Maintenance Request Acknowledgment': `Dear <span class="text-red-500">tenantName</span>,<br><br>We have received your maintenance request and will address it as soon as possible.<br><br>Our maintenance team will contact you to schedule a visit.<br><br>Thank you for your patience.<br><br>Best regards,<br><span class="text-red-500">agentName</span>`,
        'Inspection Schedule Notice': `Dear <span class="text-red-500">tenantName</span>,<br><br>We would like to schedule a routine property inspection on <span class="text-red-500">inspectionDate</span>.<br><br>Please let us know if this time works for you.<br><br>Best regards,<br><span class="text-red-500">agentName</span>`,
        'Violation Warning': `Dear <span class="text-red-500">tenantName</span>,<br><br>This letter serves as a formal notice regarding a violation of your lease agreement.<br><br>Please address this issue immediately to avoid further action.<br><br>Best regards,<br><span class="text-red-500">agentName</span>`,
        'Move-Out Reminder': `Dear <span class="text-red-500">tenantName</span>,<br><br>This is a reminder that your move-out date is scheduled for <span class="text-red-500">moveOutDate</span>.<br><br>Please ensure all items are removed and the property is left in good condition.<br><br>Best regards,<br><span class="text-red-500">agentName</span>`,
        'General Announcement': `Dear <span class="text-red-500">tenantName</span>,<br><br>We would like to inform you about some important updates regarding the property.<br><br><span class="text-red-500">announcementContent</span><br><br>Best regards,<br><span class="text-red-500">agentName</span>`,
        'Contact Update Request': `Dear <span class="text-red-500">tenantName</span>,<br><br>We need to update our records with your current contact information.<br><br>Please confirm if your current contact details are still accurate.<br><br>Best regards,<br><span class="text-red-500">agentName</span>`,
        'Other': `Dear <span class="text-red-500">tenantName</span>,<br><br>We hope you're doing well.<br>This message is to inform you of the following:<br><br><span class="text-red-500">customMessageContent</span><br><br>If you have any questions or concerns, please feel free to reach out to us.<br><br>Best regards,<br><span class="text-red-500">agentName</span>`
      };
      if (templates[subjectSelect.value]) {
        previewDiv.innerHTML = templates[subjectSelect.value];
      }
    }
  }
};

// 初始化自动补全功能
function initAutocomplete() {
  const input = document.getElementById('recipient-input');
  const resultsContainer = document.getElementById('autocomplete-results');
  const selectedContainer = document.getElementById('selected-recipients');


  if (!input || !resultsContainer || !selectedContainer) {
    return;
  }

  // 处理输入事件
  input.addEventListener('input', (e) => {
    const query = e.target.value.trim();
    
    // 清除之前的定时器
    clearTimeout(debounceTimer);
    
    // 设置新的定时器
    debounceTimer = setTimeout(() => {
      if (query.length >= 2) {
        searchTenants(query);
      } else {
        resultsContainer.classList.add('hidden');
      }
    }, 300);
  });

  // 处理点击外部事件
  document.addEventListener('click', (e) => {
    if (!input.contains(e.target) && !resultsContainer.contains(e.target)) {
      resultsContainer.classList.add('hidden');
    }
  });
}

// 监听模板克隆事件
document.addEventListener('click', function(e) {
  if (e.target.matches('button') && e.target.textContent.includes('Send Message')) {
    // 等待模板被克隆到DOM中
    setTimeout(() => {
      initAutocomplete();
    }, 100);
  }
});

// 搜索租户
async function searchTenants(query) {
  try {
    const response = await fetch(`/v1/tenants/search?q=${encodeURIComponent(query)}`);
    if (!response.ok) throw new Error('Failed to search tenants');
    const data = await response.json();
    displayResults(data.items || []);
  } catch (error) {
    displayResults([]);
  }
}

// 显示搜索结果
function displayResults(tenants) {
  const resultsContainer = document.getElementById('autocomplete-results');
  if (!resultsContainer) {
    return;
  }

  if (tenants.length === 0) {
    resultsContainer.innerHTML = '<div class="p-2 text-gray-500">No tenants found</div>';
  } else {
    // 不再去重，显示所有匹配的租户（包括重复的姓名）
    resultsContainer.innerHTML = tenants
      .map(tenant => {
        // 构建显示名称，包含property和unit信息
        let displayName = `${tenant.firstName} ${tenant.lastName}`;
        if (tenant.propertyName && tenant.roomName) {
          displayName += ` (${tenant.propertyName} - ${tenant.roomName})`;
        } else if (tenant.propertyName) {
          displayName += ` (${tenant.propertyName})`;
        } else if (tenant.roomName) {
          displayName += ` (${tenant.roomName})`;
        }

        return `
          <div
            class="p-2 hover:bg-gray-100 cursor-pointer flex items-center justify-between"
            onclick="selectRecipient(${JSON.stringify(tenant).replace(/"/g, '&quot;')})"
          >
            <div>
              <div class="font-medium">${displayName}</div>
              <div class="text-sm text-gray-500">${tenant.email}</div>
            </div>
          </div>
        `;
      })
      .join('');
  }

  resultsContainer.classList.remove('hidden');
}

// 删除页面加载时的自动初始化
// document.addEventListener('DOMContentLoaded', () => {
//   initAutocomplete();
// });

// 导入服务
import InboxService from '../../services/inbox.js';

// 监听modal内容变化，弹窗插入后绑定Message Subject事件
const modalContainer = document.getElementById('modal');
if (modalContainer) {
  const observer = new MutationObserver(() => {
    const subjectSelect = modalContainer.querySelector('select');
    const previewDiv = modalContainer.querySelector('#message-preview');
    const sendButton = modalContainer.querySelector('#send-message-btn');
    
    // 存储当前选择的收件人
    let selectedRecipient = null;

    // 监听收件人选择
    const recipientInputs = modalContainer.querySelectorAll('input[name="recipients[]"]');
    recipientInputs.forEach(input => {
      input.addEventListener('change', function() {
        if (this.checked) {
          selectedRecipient = {
            firstName: this.dataset.firstName,
            lastName: this.dataset.lastName
          };
        } else {
          selectedRecipient = null;
        }
        // 如果已经选择了主题，更新预览
        if (subjectSelect && subjectSelect.value) {
          updateMessagePreview(subjectSelect.value);
        }
      });
    });

    // 更新消息预览的函数
    async function updateMessagePreview(subject) {
      if (!previewDiv) return;
      // 获取当前选中的收件人
      const selectedRecipientInput = modalContainer.querySelector('input[name="recipients[]"]');
      let recipientName = 'tenantName';
      let hasRecipient = false;
      let rentAmount = '';
      let dueDate = '';
      let lease = null;
      let tenant = null;
      let email = '';
      if (selectedRecipientInput) {
        const firstName = selectedRecipientInput.dataset.firstName;
        const lastName = selectedRecipientInput.dataset.lastName;
        email = selectedRecipientInput.value;
        if (firstName || lastName) {
          recipientName = `${firstName || ''} ${lastName || ''}`.trim();
          hasRecipient = true;
        }
        // 尝试从window.selectedTenantMap获取租户对象
        if (window.selectedTenantMap) {
          tenant = window.selectedTenantMap[email];
        }
      }
      // 只要有收件人且主题为Rent Reminder、Late Payment Notice或Payment Received就查lease
      if (tenant && tenant.leaseId && (subject === 'Rent Reminder' || subject === 'Late Payment Notice' || subject === 'Payment Received')) {
        try {
          lease = await window.leaseApi.getLeaseById(tenant.leaseId);
          rentAmount = lease.rentAmount || '';
          if (lease.rentDueDay) {
            const now = new Date();
            const monthNames = ["Jan.", "Feb.", "Mar.", "Apr.", "May.", "Jun.", "Jul.", "Aug.", "Sep.", "Oct.", "Nov.", "Dec."];
            const month = monthNames[now.getMonth()];
            const day = lease.rentDueDay;
            function getDaySuffix(day) {
              if (day >= 11 && day <= 13) return 'th';
              switch (day % 10) {
                case 1: return 'st';
                case 2: return 'nd';
                case 3: return 'rd';
                default: return 'th';
              }
            }
            dueDate = `${month}${day}${getDaySuffix(day)}`;
          }
        } catch (e) {
        }
      }
      // 获取当前用户信息
      let currentUser = window.currentUser || {};
      const storedUser = localStorage.getItem('user');
      if (storedUser) {
        try {
          const parsedUser = JSON.parse(storedUser);
          currentUser = { ...currentUser, ...parsedUser };
        } catch (e) {
        }
      }
      const agentName = currentUser.usrNm || currentUser.name || currentUser.username || currentUser.email || 'agentName';
      const hasAgentName = !!(currentUser.usrNm || currentUser.name || currentUser.username || currentUser.email);
      // 渲染模板
      if (subject === 'Rent Reminder' && hasRecipient && rentAmount && dueDate) {
        previewDiv.innerHTML = `Dear <span class="text-black">${recipientName}</span>,<br><br>This is a friendly reminder that your monthly rent of $<span class="text-black">${rentAmount}</span> is due on <span class="text-black">${dueDate}</span>.<br>Please make your payment on time to avoid any late fees.<br><br>If you've already paid, you can disregard this message.<br><br>Thank you,<br><br><span class="${hasAgentName ? 'text-black' : 'text-red-500'}">${agentName}</span>`;
      } else if (subject === 'Late Payment Notice' && hasRecipient && rentAmount && dueDate) {
        previewDiv.innerHTML = `Dear <span class="text-black">${recipientName}</span>,<br><br>Our records show that your rent payment of $<span class="text-black">${rentAmount}</span> due on <span class="text-black">${dueDate}</span> has not yet been received.<br>Please make the payment as soon as possible to avoid further penalties.<br><br>If you have any questions, feel free to reach out.<br><br>Best regards,<br><span class="${hasAgentName ? 'text-black' : 'text-red-500'}">${agentName}</span>`;
      } else if (subject === 'Payment Received' && hasRecipient && rentAmount && dueDate) {
        previewDiv.innerHTML = `Dear <span class="text-black">${recipientName}</span>,<br><br>We have received your rent payment of $<span class="text-black">${rentAmount}</span> for the period starting <span class="text-black">${dueDate}</span>.<br>Thank you for your prompt payment.<br><br>Let us know if you have any questions.<br><br>Sincerely,<br><span class="${hasAgentName ? 'text-black' : 'text-red-500'}">${agentName}</span>`;
      } else {
        const templates = {
          'Rent Reminder': `Dear <span class="${hasRecipient ? 'text-black' : 'text-red-500'}">${recipientName}</span>,<br><br>This is a friendly reminder that your monthly rent of $<span class="text-red-500">amount</span> is due on <span class="text-red-500">dueDate</span>.<br>Please make your payment on time to avoid any late fees.<br><br>If you've already paid, you can disregard this message.<br><br>Thank you,<br><br><span class="${hasAgentName ? 'text-black' : 'text-red-500'}">${agentName}</span>`,
          'Late Payment Notice': `Dear <span class="${hasRecipient ? 'text-black' : 'text-red-500'}">${recipientName}</span>,<br><br>Our records show that your rent payment of $<span class="text-red-500">amount</span> due on <span class="text-red-500">dueDate</span> has not yet been received.<br>Please make the payment as soon as possible to avoid further penalties.<br><br>If you have any questions, feel free to reach out.<br><br>Best regards,<br><span class="${hasAgentName ? 'text-black' : 'text-red-500'}">${agentName}</span>`,
          'Payment Received': `Dear <span class="${hasRecipient ? 'text-black' : 'text-red-500'}">${recipientName}</span>,<br><br>We have received your rent payment of $<span class="text-red-500">amount</span> for the period starting <span class="text-red-500">startDate</span>.<br>Thank you for your prompt payment.<br><br>Let us know if you have any questions.<br><br>Sincerely,<br><span class="${hasAgentName ? 'text-black' : 'text-red-500'}">${agentName}</span>`,
          'Lease Renewal Notice': `Dear <span class="${hasRecipient ? 'text-black' : 'text-red-500'}">${recipientName}</span>,<br><br>Your current lease agreement is approaching its end date. We would like to discuss the possibility of renewal.<br><br>Please let us know if you're interested in renewing your lease.<br><br>Best regards,<br><span class="${hasAgentName ? 'text-black' : 'text-red-500'}">${agentName}</span>`,
          'Maintenance Request Acknowledgment': `Dear <span class="${hasRecipient ? 'text-black' : 'text-red-500'}">${recipientName}</span>,<br><br>We have received your maintenance request and will address it as soon as possible.<br><br>Our maintenance team will contact you to schedule a visit.<br><br>Thank you for your patience.<br><br>Best regards,<br><span class="${hasAgentName ? 'text-black' : 'text-red-500'}">${agentName}</span>`,
          'Inspection Schedule Notice': `Dear <span class="${hasRecipient ? 'text-black' : 'text-red-500'}">${recipientName}</span>,<br><br>We would like to schedule a routine property inspection on <span class="text-red-500">inspectionDate</span>.<br><br>Please let us know if this time works for you.<br><br>Best regards,<br><span class="${hasAgentName ? 'text-black' : 'text-red-500'}">${agentName}</span>`,
          'Violation Warning': `Dear <span class="${hasRecipient ? 'text-black' : 'text-red-500'}">${recipientName}</span>,<br><br>This letter serves as a formal notice regarding a violation of your lease agreement.<br><br>Please address this issue immediately to avoid further action.<br><br>Best regards,<br><span class="${hasAgentName ? 'text-black' : 'text-red-500'}">${agentName}</span>`,
          'Move-Out Reminder': `Dear <span class="${hasRecipient ? 'text-black' : 'text-red-500'}">${recipientName}</span>,<br><br>This is a reminder that your move-out date is scheduled for <span class="text-red-500">moveOutDate</span>.<br><br>Please ensure all items are removed and the property is left in good condition.<br><br>Best regards,<br><span class="${hasAgentName ? 'text-black' : 'text-red-500'}">${agentName}</span>`,
          'General Announcement': `Dear <span class="${hasRecipient ? 'text-black' : 'text-red-500'}">${recipientName}</span>,<br><br>We would like to inform you about some important updates regarding the property.<br><br><span class="text-red-500">announcementContent</span><br><br>Best regards,<br><span class="${hasAgentName ? 'text-black' : 'text-red-500'}">${agentName}</span>`,
          'Contact Update Request': `Dear <span class="${hasRecipient ? 'text-black' : 'text-red-500'}">${recipientName}</span>,<br><br>We need to update our records with your current contact information.<br><br>Please confirm if your current contact details are still accurate.<br><br>Best regards,<br><span class="${hasAgentName ? 'text-black' : 'text-red-500'}">${agentName}</span>`,
          'Other': `Dear <span class="${hasRecipient ? 'text-black' : 'text-red-500'}">${recipientName}</span>,<br><br>We hope you're doing well.<br>This message is to inform you of the following:<br><br><span class="text-red-500">customMessageContent</span><br><br>If you have any questions or concerns, please feel free to reach out to us.<br><br>Best regards,<br><span class="${hasAgentName ? 'text-black' : 'text-red-500'}">${agentName}</span>`
        };
        if (templates[subject]) {
          previewDiv.innerHTML = templates[subject];
        } else {
          previewDiv.innerHTML = '<span class="text-gray-400">Please select a subject to preview the message template.</span>';
        }
      }
    }

    if (subjectSelect && previewDiv && !subjectSelect._templateBound) {
      subjectSelect.addEventListener('change', function() {
        updateMessagePreview(this.value);
      });
      subjectSelect._templateBound = true;
    }

    // 检查消息内容是否包含红色字段
    function hasRedFields(element) {
        const redElements = element.querySelectorAll('.text-red-500');
        return redElements.length > 0;
    }

    // 修改发送消息按钮的事件处理
    if (sendButton && !sendButton._bound) {
        sendButton.addEventListener('click', async function() {
            const recipientInputs = Array.from(modalContainer.querySelectorAll('input[name="recipients[]"]'));
            const recipients = recipientInputs.map(input => input.value);
            const recipientNames = recipientInputs.map(input => {
                const firstName = input.dataset.firstName || '';
                const lastName = input.dataset.lastName || '';
                return `${firstName} ${lastName}`.trim() || input.value; // 如果没有姓名，使用email作为fallback
            });
            const recipientLeaseIds = recipientInputs.map(input => input.dataset.leaseId || '');
            const recipientLeaseStatuses = recipientInputs.map(input => input.dataset.leaseStatus || '');
            const subject = subjectSelect.value;
            const body = previewDiv.innerText;

            if (recipients.length === 0) {
                showError('Please select at least one recipient');
                return;
            }
            if (!subject) {
                showError('Please select a subject');
                return;
            }
            if (!body.trim()) {
                showError('Please enter a message');
                return;
            }

            // 检查消息内容是否包含红色字段
            if (hasRedFields(previewDiv)) {
                showError('Please complete all required fields in the message');
                return;
            }

            try {
                const response = await InboxService.sendMessage({
                    recipients,
                    recipientNames,
                    recipientLeaseIds,
                    recipientLeaseStatuses,
                    subject,
                    body
                });
                showSuccess('Message sent successfully');
                document.getElementById('modal').innerHTML = '';
                const sentTab = document.getElementById('tab-sent');
                if (sentTab) sentTab.click();
                if (typeof refreshSentMessages === 'function') refreshSentMessages();
            } catch (error) {
                showError(error.message || 'Failed to send message');
            }
        });
        sendButton._bound = true;
    }
  });
  observer.observe(modalContainer, { childList: true, subtree: true });
}

// tab切换功能
function setupInboxTabs() {
  const tabInbox = document.getElementById('tab-inbox');
  const tabSent = document.getElementById('tab-sent');
  if (tabInbox && tabSent) {
    tabInbox.addEventListener('click', function() {
      tabInbox.classList.add('bg-white', 'shadow', 'text-black', 'tab-active');
      tabInbox.classList.remove('text-gray-400', 'bg-transparent');
      tabSent.classList.remove('bg-white', 'shadow', 'text-black', 'tab-active');
      tabSent.classList.add('text-gray-400', 'bg-transparent');
      // 切换到收件箱内容
      refreshInboxMessages();
    });
    tabSent.addEventListener('click', function() {
      tabSent.classList.add('bg-white', 'shadow', 'text-black', 'tab-active');
      tabSent.classList.remove('text-gray-400', 'bg-transparent');
      tabInbox.classList.remove('bg-white', 'shadow', 'text-black', 'tab-active');
      tabInbox.classList.add('text-gray-400', 'bg-transparent');
      // 切换到已发送消息内容
      refreshSentMessages();
    });
  }
}

document.addEventListener('DOMContentLoaded', function() {
  setupInboxTabs();
  // 初始加载收件箱消息
  refreshInboxMessages();
});

// 渲染收件箱消息列表
async function refreshInboxMessages() {
  const messageList = document.getElementById('message-list');
  if (!messageList) return;

  // 清空内容
  messageList.innerHTML = '';
  // 加载 loading 状态
  messageList.innerHTML = '<div class="text-center text-gray-400 py-8">Loading...</div>';

  try {
    const messages = await InboxService.getInboxMessages();
    if (!messages || messages.length === 0) {
      messageList.innerHTML = `
        <!-- Desktop View -->
        <div class="hidden lg:block">
          <table class="w-full text-sm rounded-md overflow-hidden mt-4" style="box-shadow: 0 0 0 1px rgb(203 213 225)">
            <thead>
              <tr class="bg-slate-200 text-left text-black uppercase text-xs border-b border-slate-300">
                <th class="p-4 w-5/12"><span class="flex items-center gap-2">Subject</span></th>
                <th class="p-4 w-4/12"><span class="flex items-center gap-2">From</span></th>
                <th class="p-4 w-2/12"><span class="flex items-center gap-2">Date</span></th>
              </tr>
            </thead>
            <tbody>
              <tr class="bg-white text-center">
                <td colspan="3" class="p-4 text-gray-400">No messages</td>
              </tr>
            </tbody>
          </table>
        </div>
        <!-- Mobile View -->
        <div class="lg:hidden">
          <div class="mt-4 rounded-md overflow-hidden" style="box-shadow: 0 0 0 1px rgb(203 213 225)">
            <div class="text-center text-gray-400 py-8">No messages</div>
          </div>
        </div>
      `;
      return;
    }

    // 渲染消息列表
    let html = `
      <!-- Desktop View -->
      <div class="hidden lg:block">
        <table class="w-full text-sm rounded-md overflow-hidden mt-4" style="box-shadow: 0 0 0 1px rgb(203 213 225)">
          <thead>
            <tr class="bg-slate-200 text-left text-black uppercase text-xs border-b border-slate-300">
              <th class="p-4 w-5/12"><span class="flex items-center gap-2">Subject</span></th>
              <th class="p-4 w-4/12"><span class="flex items-center gap-2">From</span></th>
              <th class="p-4 w-2/12"><span class="flex items-center gap-2">Date</span></th>
            </tr>
          </thead>
          <tbody>
    `;

    messages.forEach(msg => {
      html += `
        <tr class="bg-white border-b border-slate-100 hover:bg-gray-50">
          <td class="p-4">${msg.subject || ''}</td>
          <td class="p-4">${msg.from || ''}</td>
          <td class="p-4">${msg.receivedAt ? new Date(msg.receivedAt).toLocaleString() : ''}</td>
        </tr>
      `;
    });

    html += `
          </tbody>
        </table>
      </div>
      <!-- Mobile View -->
      <div class="lg:hidden">
        <div class="mt-4 rounded-md overflow-hidden" style="box-shadow: 0 0 0 1px rgb(203 213 225)">
    `;

    messages.forEach(msg => {
      html += `
        <div class="bg-white border-b border-slate-100 p-4">
          <div class="font-semibold">${msg.subject || ''}</div>
          <div class="text-sm text-gray-500">${msg.from || ''}</div>
          <div class="text-xs text-gray-400 mt-1">${msg.receivedAt ? new Date(msg.receivedAt).toLocaleString() : ''}</div>
        </div>
      `;
    });

    html += `
        </div>
      </div>
    `;

    messageList.innerHTML = html;
  } catch (e) {
    // 即使加载失败，也显示空表格，不显示错误信息
    messageList.innerHTML = `
      <!-- Desktop View -->
      <div class="hidden lg:block">
        <table class="w-full text-sm rounded-md overflow-hidden mt-4" style="box-shadow: 0 0 0 1px rgb(203 213 225)">
          <thead>
            <tr class="bg-slate-200 text-left text-black uppercase text-xs border-b border-slate-300">
              <th class="p-4 w-5/12"><span class="flex items-center gap-2">Subject</span></th>
              <th class="p-4 w-4/12"><span class="flex items-center gap-2">From</span></th>
              <th class="p-4 w-2/12"><span class="flex items-center gap-2">Date</span></th>
            </tr>
          </thead>
          <tbody>
            <tr class="bg-white text-center">
              <td colspan="3" class="p-4 text-gray-400">No messages</td>
            </tr>
          </tbody>
        </table>
      </div>
      <!-- Mobile View -->
      <div class="lg:hidden">
        <div class="mt-4 rounded-md overflow-hidden" style="box-shadow: 0 0 0 1px rgb(203 213 225)">
          <div class="text-center text-gray-400 py-8">No messages</div>
        </div>
      </div>
    `;
  }
}

// 渲染已发送消息列表
async function refreshSentMessages() {
  const messageList = document.getElementById('message-list');
  if (!messageList) return;
  // 清空内容
  messageList.innerHTML = '';
  // 加载 loading 状态
  messageList.innerHTML = '<div class="text-center text-gray-400 py-8">Loading...</div>';
  try {
    const messages = await InboxService.getSentMessages();
    if (!messages || messages.length === 0) {
      messageList.innerHTML = `
        <!-- Desktop View -->
        <div class="hidden lg:block">
          <table class="w-full text-sm rounded-md overflow-hidden mt-4" style="box-shadow: 0 0 0 1px rgb(203 213 225)">
            <thead>
              <tr class="bg-slate-200 text-left text-black uppercase text-xs border-b border-slate-300">
                <th class="p-4 w-5/12"><span class="flex items-center gap-2">Subject</span></th>
                <th class="p-4 w-4/12"><span class="flex items-center gap-2">To</span></th>
                <th class="p-4 w-2/12"><span class="flex items-center gap-2">Date</span></th>
              </tr>
            </thead>
            <tbody>
              <tr class="bg-white text-center">
                <td colspan="3" class="p-4 text-gray-400">No messages</td>
              </tr>
            </tbody>
          </table>
        </div>
        <!-- Mobile View -->
        <div class="lg:hidden">
          <div class="mt-4 rounded-md overflow-hidden" style="box-shadow: 0 0 0 1px rgb(203 213 225)">
            <div class="text-center text-gray-400 py-8">No messages</div>
          </div>
        </div>
      `;
      return;
    }

    // 构建表格
    let html = `
      <!-- Desktop View -->
      <div class="hidden lg:block">
        <table class="w-full text-sm rounded-md overflow-hidden mt-4" style="box-shadow: 0 0 0 1px rgb(203 213 225)">
          <thead>
            <tr class="bg-slate-200 text-left text-black uppercase text-xs border-b border-slate-300">
              <th class="p-4 w-5/12"><span class="flex items-center gap-2">Subject</span></th>
              <th class="p-4 w-4/12"><span class="flex items-center gap-2">To</span></th>
              <th class="p-4 w-2/12"><span class="flex items-center gap-2">Date</span></th>
            </tr>
          </thead>
          <tbody>
    `;

    messages.forEach(msg => {
      // 处理收件人显示：对每个收件人，优先显示姓名，没有姓名则显示email
      let recipientDisplay = '';
      if (Array.isArray(msg.to)) {
        const recipients = msg.to.map((email, index) => {
          // 检查对应index的toName是否存在且不为空
          let displayName = '';
          if (msg.toName && Array.isArray(msg.toName) &&
              index < msg.toName.length &&
              msg.toName[index] &&
              msg.toName[index].trim() !== '') {
            displayName = msg.toName[index];
          } else {
            displayName = email; // 如果没有对应的姓名，使用email
          }

          // 检查是否有对应的leaseId，如果有则添加点击功能
          const leaseId = msg.recipientLeaseIds && Array.isArray(msg.recipientLeaseIds) &&
                         index < msg.recipientLeaseIds.length ? msg.recipientLeaseIds[index] : '';
          const leaseStatus = msg.recipientLeaseStatuses && Array.isArray(msg.recipientLeaseStatuses) &&
                             index < msg.recipientLeaseStatuses.length ? msg.recipientLeaseStatuses[index] : '';

          if (leaseId && leaseId.trim() !== '') {
            return `<span class="cursor-pointer text-blue-600 hover:text-blue-800 hover:underline"
                          onclick="handleRecipientClick('${leaseId}', '${leaseStatus}')">${displayName}</span>`;
          } else {
            return displayName;
          }
        });
        recipientDisplay = recipients.join(', ');
      } else {
        recipientDisplay = msg.to || '';
      }

      html += `
        <tr class="bg-white border-b border-slate-100 hover:bg-gray-50">
          <td class="p-4">${msg.subject || ''}</td>
          <td class="p-4">${recipientDisplay}</td>
          <td class="p-4">${msg.sentAt ? new Date(msg.sentAt).toLocaleString() : ''}</td>
        </tr>
      `;
    });

    html += `
          </tbody>
        </table>
      </div>
      <!-- Mobile View -->
      <div class="lg:hidden">
        <div class="mt-4 rounded-md overflow-hidden" style="box-shadow: 0 0 0 1px rgb(203 213 225)">
    `;

    messages.forEach(msg => {
      // 处理收件人显示：对每个收件人，优先显示姓名，没有姓名则显示email
      let recipientDisplay = '';
      if (Array.isArray(msg.to)) {
        const recipients = msg.to.map((email, index) => {
          // 检查对应index的toName是否存在且不为空
          let displayName = '';
          if (msg.toName && Array.isArray(msg.toName) &&
              index < msg.toName.length &&
              msg.toName[index] &&
              msg.toName[index].trim() !== '') {
            displayName = msg.toName[index];
          } else {
            displayName = email; // 如果没有对应的姓名，使用email
          }

          // 检查是否有对应的leaseId，如果有则添加点击功能
          const leaseId = msg.recipientLeaseIds && Array.isArray(msg.recipientLeaseIds) &&
                         index < msg.recipientLeaseIds.length ? msg.recipientLeaseIds[index] : '';
          const leaseStatus = msg.recipientLeaseStatuses && Array.isArray(msg.recipientLeaseStatuses) &&
                             index < msg.recipientLeaseStatuses.length ? msg.recipientLeaseStatuses[index] : '';

          if (leaseId && leaseId.trim() !== '') {
            return `<span class="cursor-pointer text-blue-600 hover:text-blue-800 hover:underline"
                          onclick="handleRecipientClick('${leaseId}', '${leaseStatus}')">${displayName}</span>`;
          } else {
            return displayName;
          }
        });
        recipientDisplay = recipients.join(', ');
      } else {
        recipientDisplay = msg.to || '';
      }

      html += `
        <div class="bg-white border-b border-slate-100 p-4">
          <div class="font-semibold">${msg.subject || ''}</div>
          <div class="text-sm text-gray-500">${recipientDisplay}</div>
          <div class="text-xs text-gray-400 mt-1">${msg.sentAt ? new Date(msg.sentAt).toLocaleString() : ''}</div>
        </div>
      `;
    });

    html += `
        </div>
      </div>
    `;

    messageList.innerHTML = html;
  } catch (e) {
    // 即使加载失败，也显示空表格，不显示错误信息
    messageList.innerHTML = `
      <!-- Desktop View -->
      <div class="hidden lg:block">
        <table class="w-full text-sm rounded-md overflow-hidden mt-4" style="box-shadow: 0 0 0 1px rgb(203 213 225)">
          <thead>
            <tr class="bg-slate-200 text-left text-black uppercase text-xs border-b border-slate-300">
              <th class="p-4 w-5/12"><span class="flex items-center gap-2">Subject</span></th>
              <th class="p-4 w-4/12"><span class="flex items-center gap-2">To</span></th>
              <th class="p-4 w-2/12"><span class="flex items-center gap-2">Date</span></th>
            </tr>
          </thead>
          <tbody>
            <tr class="bg-white text-center">
              <td colspan="3" class="p-4 text-gray-400">No messages</td>
            </tr>
          </tbody>
        </table>
      </div>
      <!-- Mobile View -->
      <div class="lg:hidden">
        <div class="mt-4 rounded-md overflow-hidden" style="box-shadow: 0 0 0 1px rgb(203 213 225)">
          <div class="text-center text-gray-400 py-8">No messages</div>
        </div>
      </div>
    `;
  }
}

// 监听主题选择变化，动态渲染Rent Reminder内容
const subjectSelect = document.querySelector('select');
if (subjectSelect) {
  subjectSelect.addEventListener('change', async function() {
    await updateMessagePreview(this.value);
  });
}

$(document).ready(async function() {
    try {
        const userData = await userApi.getUserInfo();
        await renderSidebarNav(userData);
    } catch (e) {
        // 可以根据需要处理未登录等情况
    }
    // ...原有初始化代码...
}); 