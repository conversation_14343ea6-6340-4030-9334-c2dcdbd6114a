import '../../styles/output.css';
import $ from 'jquery';

$(document).ready(function() {
  const urlParams = new URLSearchParams(window.location.search);
  const code = urlParams.get('code');
  const $welcomeTitle = $('#welcomeTitle');
  const $invitationDesc = $('#invitationDesc');
  const $inviterName = $('#inviterName');
  const $signUpBtn = $('#signUpBtn');
  const $error = $('#errorMessage');
  const $logInLink = $('#logInLink');
  let invitationData = null;

  if (!code) {
    $error.text('Invalid invitation link.').removeClass('hidden');
    $invitationDesc.hide();
    $signUpBtn.hide();
    $logInLink && $logInLink.hide();
    return;
  }

  fetch(`/v1/invitations/verify?code=${encodeURIComponent(code)}`)
    .then(async res => {
      if (!res.ok) {
        const msg = res.status === 410 ? 'Invitation link has expired.' : 'Invitation not found or invalid.';
        throw new Error(msg);
      }
      return res.json();
    })
    .then(inv => {
      invitationData = inv;
      // 根据邀请类型填充欢迎语和邀请人
      let welcomeName = inv.receiverName || '';
      let inviter = inv.senderName || '-';
      if (inv.type === 'tenant_to_landlord') {
        $invitationDesc.html(`Your tenant, <span class=\"font-bold\">${inviter}</span> has invited you to Report Rentals. Create an account to get started`);
      } else if (inv.type === 'landlord_to_tenant') {
        $invitationDesc.html(`Your landlord, <span class=\"font-bold\">${inviter}</span> has invited you to Report Rentals. Create an account to get started`);
      } else {
        $invitationDesc.html(`<span class=\"font-bold\">${inviter}</span> has invited you to Report Rentals. Create an account to get started`);
      }
      $welcomeTitle.text(`Welcome${welcomeName ? ' ' + welcomeName : ''}`);
      $signUpBtn.show();
      $logInLink && $logInLink.show();
      // 设置登录链接参数
      if ($logInLink && invitationData) {
        let receiverEmail = invitationData.receiverEmail;
        $logInLink.attr('href', `/pages/login/?email=${encodeURIComponent(receiverEmail)}&fromInvitation=1&code=${encodeURIComponent(code)}`);
      }
    })
    .catch(err => {
      $error.text(err.message).removeClass('hidden');
      $invitationDesc.hide();
      $signUpBtn.hide();
      $logInLink && $logInLink.hide();
    });

  $signUpBtn.on('click', function() {
    if (!invitationData) return;
    // 跳转到注册页，带上邮箱、code、fromInvitation
    let receiverEmail = invitationData.receiverEmail;
    const signupUrl = `/pages/signup/?email=${encodeURIComponent(receiverEmail)}&fromInvitation=1&code=${encodeURIComponent(code)}`;
    window.location.href = signupUrl;
  });

  // 如果页面有 Log In 按钮（如后续加上），可用如下方式绑定：
  $(document).on('click', '#logInBtn', function() {
    if (!invitationData) return;
    let receiverEmail = invitationData.receiverEmail;
    const loginUrl = `/pages/login/?email=${encodeURIComponent(receiverEmail)}&fromInvitation=1&code=${encodeURIComponent(code)}`;
    window.location.href = loginUrl;
  });
}); 