// 导入依赖
import $ from 'jquery'
import { leaseApi, propertyApi, userApi } from '../../services'
import { checkAuthStatusAndRedirect } from '../../services/util'
import Mustache from 'mustache'
import tenantListTemplate from '../../templates/tenant-list.mst?raw'
import paymentListTemplate from '../../templates/payment-list.mst?raw'
import leaseDetailInfoTemplate from '../../templates/lease-detail-info.mst?raw'
import confirmDeleteModalTemplate from '../../templates/confirm-delete-modal.mst?raw'
import deletionConstraintModalTemplate from '../../templates/deletion-constraint-modal.mst?raw'
import upgradePromptModalTemplate from '../../templates/upgrade-prompt-modal.mst?raw'
import endLeaseModalTemplate from '../../templates/end-lease-modal.mst?raw'
import tenantViewTemplate from '../../templates/tenant-view.mst?raw'
import { unsavedChangesDetector } from '../../services/unsaved-changes'
import duplicateFileModalTemplate from '../../templates/duplicate-file-modal.mst?raw'
import paymentViewTemplate from '../../templates/payment-view.mst?raw'
import errorMessagesTemplate from '../../templates/error-messages.mst?raw'
import jsPDF from 'jspdf'
import leaseDetailTenantTemplate from '../../templates/lease-detail-tenant.mst?raw'
import { renderSidebarNav } from '../../services/sidebar'

// Get lease ID from URL
const urlParams = new URLSearchParams(window.location.search)
const leaseId = urlParams.get('id')

// Format date to YYYY-MM-DD
function formatDate(dateStr) {
  if (!dateStr) return 'No date';
  try {
    return new Date(dateStr).toISOString().split('T')[0];
  } catch (error) {
    return 'Invalid date';
  }
}

// 格式化文件大小
function formatFileSize(bytes) {
  if (bytes === 0) return '0 Bytes';
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// 在文件开头添加全局变量
let monthlyRent = 0;
let currentOwingBalance = 0;
let pendingFileDelete = false; // 标记是否有待删除的文件
let currentLease = null; // 存储当前租约信息

// 多文件上传相关变量
let uploadedDocuments = []; // 存储已上传的文档
let pendingUploads = []; // 存储待上传的文件
let pendingDeletes = []; // 存储待删除的文档ID

// Tenant显示相关变量
let currentTenantView = 'current'; // 'current' 或 'past'
let currentLeaseData = null; // 存储当前lease数据

// Tenant view专用变量
let currentTenantViewTenantView = 'current'; // tenant view下的tenant显示状态

// 渲染tenant列表
function renderTenantList() {
  if (!currentLeaseData) return;

  const tenants = currentTenantView === 'current'
    ? (currentLeaseData.currentTenants || [])
    : (currentLeaseData.pastTenants || []);

  const renderedTenantHtml = Mustache.render(tenantListTemplate, { tenants });
  $('#tenant-list').html(renderedTenantHtml);

  // 缓存当前显示的租客到全局变量，供点击行时使用
  window.currentTenants = tenants;
}

// 初始化tenant切换按钮
function initTenantToggle() {
  // 处理单个切换按钮点击
  $(document).on('click', '#tenant-toggle-btn', function() {
    // 切换状态
    currentTenantView = currentTenantView === 'current' ? 'past' : 'current';
    updateTenantToggleUI();
    renderTenantList();
  });
}

// 更新tenant切换按钮的UI状态
function updateTenantToggleUI() {
  const toggleBtn = $('#tenant-toggle-btn');
  if (currentTenantView === 'current') {
    toggleBtn.text('Show past tenants');
  } else {
    toggleBtn.text('Show current tenants');
  }
}

// Tenant view专用函数
// 渲染tenant view下的tenant列表
function renderTenantListTenantView() {
  if (!currentLeaseData) return;

  const tenants = currentTenantViewTenantView === 'current'
    ? (currentLeaseData.currentTenants || [])
    : (currentLeaseData.pastTenants || []);

  const tenantRows = tenants.map(tenant => `
    <tr class="bg-white">
      <td class="p-4">${tenant.firstName} ${tenant.lastName}</td>
      <td class="p-4">${tenant.email}</td>
    </tr>
  `).join('');

  const noTenantsRow = tenants.length === 0 ? `
    <tr class="bg-white">
      <td class="p-4 text-gray-400" colspan="2">No tenants</td>
    </tr>
  ` : '';

  const tableHtml = `
    <table class="w-full rounded-md overflow-hidden text-xs border">
      <thead>
        <tr class="bg-slate-200 text-black uppercase border-b border-slate-300">
          <th class="p-4">Tenant Name</th>
          <th class="p-4">Email</th>
        </tr>
      </thead>
      <tbody>
        ${tenantRows}${noTenantsRow}
      </tbody>
    </table>
  `;

  $('#tenant-list-tenant-view').html(tableHtml);
}

// 初始化tenant view的tenant切换按钮
function initTenantToggleTenantView() {
  // 处理单个切换按钮点击
  $(document).on('click', '#tenant-toggle-btn-tenant-view', function() {
    // 切换状态
    currentTenantViewTenantView = currentTenantViewTenantView === 'current' ? 'past' : 'current';
    updateTenantToggleUITenantView();
    renderTenantListTenantView();
  });
}

// 更新tenant view的tenant切换按钮UI状态
function updateTenantToggleUITenantView() {
  const toggleBtn = $('#tenant-toggle-btn-tenant-view');
  if (currentTenantViewTenantView === 'current') {
    toggleBtn.text('Show past tenants');
  } else {
    toggleBtn.text('Show current tenants');
  }
}
const MAX_FILES = 10; // 最大文件数量

// Make file arrays globally accessible for unsaved changes detection
window.pendingUploads = pendingUploads;
window.pendingDeletes = pendingDeletes;

// Variables for duplicate file handling
let pendingDuplicateFile = null;
let duplicateFileInfo = null;

// Global function to close modal and restore scrolling
window.closeModal = function() {
  document.getElementById('modal').innerHTML = '';
  // 恢复背景滚动
  document.body.style.overflow = '';
  document.documentElement.style.overflow = '';
};

// 多文件上传相关函数
function updateFileCount() {
  // 计算有效文件数量（排除deleted和final_deleted状态的文件）
  const activeUploadedFiles = uploadedDocuments.filter(doc =>
    doc.status !== 'deleted' && doc.status !== 'final_deleted'
  ).length;
  const totalFiles = activeUploadedFiles + pendingUploads.length - pendingDeletes.length;
  $('#file-count').text(`${totalFiles} of ${MAX_FILES} files`);
}

function renderUploadedFiles() {
  const container = $('#uploaded-files');
  container.empty();

  // 渲染已上传的文档（只显示非deleted和final_deleted状态的文件）
  uploadedDocuments.forEach(doc => {
    if (!pendingDeletes.includes(doc.id) && doc.status !== 'deleted' && doc.status !== 'final_deleted') {
      const fileItem = createFileItem(doc, true);
      container.append(fileItem);
    }
  });

  // 渲染待上传的文件
  pendingUploads.forEach((file, index) => {
    const fileItem = createFileItem(file, false, index);
    container.append(fileItem);
  });

  updateFileCount();
  updateUploadAreaDisplay();
}

function updateUploadAreaDisplay() {
  // 计算有效文件数量（排除deleted和final_deleted状态的文件）
  const activeUploadedFiles = uploadedDocuments.filter(doc =>
    doc.status !== 'deleted' && doc.status !== 'final_deleted'
  ).length;
  const totalFiles = activeUploadedFiles + pendingUploads.length - pendingDeletes.length;
  const uploadArea = $('#file-upload-area');

  if (totalFiles >= MAX_FILES) {
    // 达到最大文件数量，显示限制提示
    uploadArea.html(`
      <svg class="w-8 h-8 text-gray-400 mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M9 19l3 3m0 0l3-3m-3 3V10"></path>
      </svg>
      <p class="text-red-600 font-medium mb-1">Max upload limit reached. Delete a file to add a new one.</p>
      <p class="text-gray-500 text-sm">Supported formats: pdf, png, jpg, jpeg, doc</p>
      <input id="file-input" class="hidden" type="file" multiple accept=".pdf,.png,.jpg,.jpeg,.doc,.docx" />
    `);
    uploadArea.removeClass('hover:border-gray-400 cursor-pointer');
  } else {
    // 正常上传区域 - 恢复原始HTML结构
    uploadArea.html(`
      <svg class="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
      </svg>
      <div class="text-center">
        <span class="text-gray-600">Drag and drop or </span>
        <label class="text-blue-600 hover:text-blue-800 cursor-pointer underline">
          browse
          <input id="file-input" class="hidden" type="file" multiple accept=".pdf,.png,.jpg,.jpeg,.doc,.docx" />
        </label>
        <span class="text-gray-600"> to upload</span>
      </div>
      <span class="text-gray-500 text-sm">Supported formats: pdf, png, jpg, jpeg, doc</span>
    `);
    uploadArea.addClass('hover:border-gray-400 cursor-pointer');
  }
}

function createFileItem(item, isUploaded, index = null) {
  const fileName = isUploaded ? item.fileName : item.name;
  const fileSize = isUploaded ? item.fileSize : item.size;
  const fileId = isUploaded ? item.id : `pending-${index}`;

  return $(`
    <div class="flex items-center justify-between p-2 bg-white rounded border hover:bg-gray-50" data-file-id="${fileId}">
      <div class="flex items-center gap-2 flex-1 min-w-0">
        <svg class="w-4 h-4 text-gray-500 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
        </svg>
        <div class="flex-1 min-w-0">
          ${isUploaded ?
            `<div class="text-sm font-medium text-blue-600 hover:text-blue-800 cursor-pointer truncate" onclick="downloadFile('${item.id}')">${fileName}</div>` :
            `<div class="text-sm font-medium text-gray-900 truncate">${fileName}</div>`
          }
          <div class="text-xs text-gray-500">${formatFileSize(fileSize)}</div>
        </div>
      </div>
      <div class="flex items-center">
        <button class="text-red-600 hover:text-red-800 p-1" onclick="removeFile('${fileId}', ${isUploaded})" title="Remove file">
          <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
          </svg>
        </button>
      </div>
    </div>
  `);
}

// 检查是否有上传的文件
function hasUploadedFile() {
  // 计算实际的文件数量：已上传的文件 - 待删除的文件 + 待上传的文件
  const actualUploadedFiles = uploadedDocuments.filter(doc => !pendingDeletes.includes(doc.id)).length;
  const actualPendingFiles = pendingUploads.length;
  return actualUploadedFiles > 0 || actualPendingFiles > 0;
}

// 页面加载完成后的交互初始化
$(document).ready(async () => {
  // 获取当前用户信息并渲染侧边栏
  const userData = await userApi.getUserInfo();
  await renderSidebarNav(userData);
  
  await loadLeaseDetail();

  // Initialize form handlers
  initAddTenantForm()
  initAddPaymentForm()
  initDeleteButton()
  initRowClickHandlers()
  initSaveButton()
  initDownloadButton()
  initLeaseStatusHandler()
  initTenantToggle()
  initTenantToggleTenantView() // 初始化tenant view的切换功能

  // 初始化重复文件模态框事件处理器
  initDuplicateFileModalHandlers()

  // 初始化回收站功能
  initRecycleBinHandlers()

  // Initialize unsaved changes detection for all users in landlord view
  const isLandlordView = (userData.viewType || '').toLowerCase() === 'landlord';

  if (isLandlordView) {
    // Wait a bit for the page to fully load before initializing
    setTimeout(() => {
      unsavedChangesDetector.init({
        excludeSelectors: [
          '#property-name', // Property name is readonly in lease detail
          '#unit'           // Rental unit is readonly in lease detail
        ],
        includeFileChanges: true
      });
    }, 1000); // Increased timeout to ensure page is fully loaded
  } else {
  }

  // 绑定租客视图下的PDF下载按钮
  $(document).on('click', '#download-pdf-btn', function() {
    downloadLeasePDF();
  });

  // 新增：如果URL有code参数，自动弹出Add Tenant弹窗并填充
  const invitationCode = urlParams.get('code');
  if (invitationCode) {

    // 创建一个函数来尝试触发弹窗
    const tryTriggerModal = (attempts = 0) => {
      const maxAttempts = 10;

      // 触发Add Tenant弹窗
      const addTenantButton = document.querySelector("button[hx-get='./modal-add-tenant.html']");
      if (addTenantButton) {
        addTenantButton.click();
        return;
      }

      // 尝试用jQuery选择器
      const $addTenantButton = $("button[hx-get='./modal-add-tenant.html']");
      if ($addTenantButton.length > 0) {
        $addTenantButton.trigger('click');
        return;
      }

      // 如果还没找到按钮，继续尝试
      if (attempts < maxAttempts) {
        setTimeout(() => tryTriggerModal(attempts + 1), 500);
      } else {
      }
    };

    // 延迟执行，确保页面加载完成
    setTimeout(() => {
      tryTriggerModal();
      // 监听弹窗出现后填充表单
      const fillForm = async () => {
        try {
          const res = await fetch(`/v1/invitations/verify?code=${encodeURIComponent(invitationCode)}`);
          if (res.ok) {
            const inv = await res.json();
            // 只填 sender 的名字和邮箱
            if (inv.senderName) {
              const nameParts = inv.senderName.split(' ');
              $('#first-name').val(nameParts[0] || '');
              $('#middle-name').val(nameParts.length === 3 ? nameParts[1] : '');
              $('#last-name').val(nameParts.length === 3 ? nameParts[2] : (nameParts[1] || ''));
            }
            if (inv.senderEmail) $('#email').val(inv.senderEmail);
            if (inv.senderId) {
              window.invitationTenantId = inv.senderId;
            }
          }
        } catch (e) {
        }
      };
      // 监听弹窗DOM出现
      const observer = new MutationObserver(() => {
        if ($('#first-name').length) {
          fillForm();
          observer.disconnect();
        }
      });
      const modal = document.getElementById('modal');
      if (modal) observer.observe(modal, { childList: true, subtree: true });
    }, 1500); // 增加延迟时间，确保页面完全加载
  }
})

// Initialize delete button
function initDeleteButton() {
  // Show confirmation modal when delete button is clicked
  $(document).on('click', '#delete-lease-btn', async function() {
    try {
      // 先检查是否可以删除
      const checkResult = await leaseApi.checkLeaseDeletion(leaseId);

      if (checkResult.canDelete) {
        // 可以删除，显示确认框
        const modalHtml = Mustache.render(confirmDeleteModalTemplate, {
          title: 'Delete Lease',
          confirmButtonId: 'confirm-delete-lease-btn'
        });
        $('#modal').html(modalHtml);
        // 禁用背景滚动
        document.body.style.overflow = 'hidden';
        document.documentElement.style.overflow = 'hidden';
      }
    } catch (error) {
      console.error('Failed to check lease deletion constraints:', error);

      // 检查是否是约束错误（通过错误消息内容判断）
      const errorMessage = error.message || '';
      if (errorMessage.includes('cannot delete lease')) {
        // 解析错误消息以提供更友好的提示
        let friendlyMessage = errorMessage;
        let instructions = '';

        if (errorMessage.includes('tenant(s)')) {
          // 处理不同类型的tenant约束错误
          if (errorMessage.includes('current tenant(s) and') && errorMessage.includes('past tenant(s)')) {
            // 同时有current和past tenants
            const currentMatch = errorMessage.match(/(\d+) current tenant\(s\)/);
            const pastMatch = errorMessage.match(/(\d+) past tenant\(s\)/);
            const currentCount = currentMatch ? currentMatch[1] : 'some';
            const pastCount = pastMatch ? pastMatch[1] : 'some';
            friendlyMessage = `This lease has ${currentCount} current tenant(s) and ${pastCount} past tenant(s) that must be deleted first.`;
          } else if (errorMessage.includes('current tenant(s)')) {
            // 只有current tenants
            const currentCount = errorMessage.match(/(\d+) current tenant\(s\)/)?.[1] || 'some';
            friendlyMessage = `This lease has ${currentCount} current tenant(s) that must be deleted first.`;
          } else if (errorMessage.includes('past tenant(s)')) {
            // 只有past tenants
            const pastCount = errorMessage.match(/(\d+) past tenant\(s\)/)?.[1] || 'some';
            friendlyMessage = `This lease has ${pastCount} past tenant(s) that must be deleted first.`;
          }
          instructions = 'Please delete all tenants before deleting the lease.';
        }

        // 显示约束错误模态框
        const constraintModalHtml = Mustache.render(deletionConstraintModalTemplate, {
          entityType: 'Lease',
          message: friendlyMessage,
          instructions: instructions,
          hasInstructions: instructions !== ''
        });
        $('#modal').html(constraintModalHtml);

        // 禁用背景滚动
        document.body.style.overflow = 'hidden';
        document.documentElement.style.overflow = 'hidden';
      } else {
        // 其他错误，显示通用错误消息
        alert('Failed to check deletion constraints: ' + errorMessage);
      }
    }
  });

  // Handle confirm delete action for lease deletion
  $(document).on('click', '#confirm-delete-lease-btn', async function() {
    try {
      // 执行硬删除操作
      await leaseApi.deleteLease(leaseId);
      window.location.href = '../leases/index.html';
    } catch (error) {
      console.error('Failed to delete lease:', error);
      alert('Failed to delete lease: ' + (error.message || 'Unknown error'));
    }
  });
}

async function loadLeaseDetail() {
  try {
    const lease = await leaseApi.getLeaseById(leaseId)
    // 保存当前租约信息到全局变量
    currentLease = lease;
    // 获取当前用户信息
    const userData = window.currentUser || JSON.parse(localStorage.getItem('user') || '{}')
    // 检查landlord view权限
    const isLandlordView = (userData.viewType || '').toLowerCase() === 'landlord'
    const isActualLandlord = lease.isCurrentUserLandlord

    if (isLandlordView && !isActualLandlord) {
      // 用户试图以landlord view访问但不是真正的landlord
      const modalHtml = `
        <div class="fixed inset-0 z-50">
          <div class="fixed inset-0 bg-black bg-opacity-50"></div>
          <div class="fixed inset-0 flex items-center justify-center">
            <div class="bg-white rounded-lg p-6 max-w-md mx-4">
              <h3 class="text-lg font-semibold mb-4">Access Denied</h3>
              <p class="text-gray-600 mb-6">You do not have landlord permissions for this lease.</p>
              <div class="flex justify-end">
                <button
                  class="px-4 py-2 bg-slate-800 text-white rounded hover:bg-slate-900"
                  onclick="window.location.href = '/pages/home/'"
                >
                  OK
                </button>
              </div>
            </div>
          </div>
        </div>
      `;
      document.getElementById('modal').innerHTML = modalHtml;
      return;
    }

    if ((userData.viewType || '').toLowerCase() === 'tenant') {
      // 渲染租户视图
      const tenants = (lease.currentTenants || []).map(t => ({
        name: [t.firstName, t.middleName, t.lastName].filter(Boolean).join(' '),
        email: t.email
      }));
      const paymentsRes = await leaseApi.getPayments(leaseId);
      // 按日期排序（最新的在前）并只取最近6条记录
      const sortedPayments = (paymentsRes.items || [])
        .sort((a, b) => new Date(b.date || b.dt) - new Date(a.date || a.dt))
        .slice(0, 6);
      const payments = sortedPayments.map(p => ({
        date: formatDate(p.date || p.dt),
        amount: p.amount || p.amt,
        remainingBalance: p.remainingBalance || p.remBal
      }));
      // 准备文档数据（只显示非deleted和final_deleted状态的文件）
      const documents = (lease.documents || [])
        .filter(doc => doc.status !== 'deleted' && doc.status !== 'final_deleted')
        .map(doc => ({
          id: doc.id,
          fileName: doc.fileName,
          formattedFileSize: formatFileSize(doc.fileSize || 0)
        }));

      // 存储lease数据供tenant view使用
      currentLeaseData = lease;

      const renderedHtml = Mustache.render(leaseDetailTenantTemplate, {
        propertyName: lease.propertyName,
        roomName: lease.roomName,
        rentAmount: lease.rentAmount,
        status: lease.status,
        propertyAddress: lease.propertyAddress,
        landlordName: lease.landlordName,
        landlordContact: [lease.landlordEmail, lease.landlordPhone].filter(Boolean).join(', '),
        rentDueDayWithSuffix: getDayWithSuffix(lease.rentDueDay),
        additionalMonthlyFees: lease.additionalMonthlyFees,
        tenants,
        payments,
        documents: documents,
        documentCount: documents.length,
        rentReporting: lease.rentRep || lease.rentReporting,
        autoPay: lease.autoPay
      });
      $('main').html(renderedHtml);

      // 渲染tenant view的tenant列表
      renderTenantListTenantView();
    } else {
      // landlord视图：渲染可编辑模板
      const renderedLeaseHtml = Mustache.render(leaseDetailInfoTemplate, {
        propertyName: lease.propertyName,
        roomName: lease.roomName,
        rentDueDay: lease.rentDueDay || 1,
        rentAmount: lease.rentAmount,
        additionalMonthlyFees: lease.additionalMonthlyFees,
        status: lease.status,
        status_active: lease.status === 'active',
        status_ended: lease.status === 'ended',
        notes: lease.notes || '',
        rentReporting: lease.rentRep || lease.rentReporting,
        autoPay: lease.autoPay
      })
      $('#lease-detail-info').html(renderedLeaseHtml)

      // 渲染文档显示
      renderDocumentDisplay(lease)

      // 同步Options区域checkbox状态
      $('#rent-reporting').prop('checked', lease.rentRep || lease.rentReporting)
      $('#auto-pay').prop('checked', lease.autoPay)
      // 填充lease notes字段到textarea
      $('textarea#lease-notes').val(lease.notes || '')
      // 初始化lease status的previous状态
      $('#lease-status').data('previous-status', lease.status)
      // 动态显示Rent Due Day后缀和范围限制
      updateRentDueSuffix($('#rent-due-day').val())

      // 点击容器任何地方都聚焦到输入框
      $('#rent-due-container').on('click', function() {
        $('#rent-due-day').focus()
      })

      $('#rent-due-day').on('input', function() {
        let val = $(this).val()
        // 自动范围限制：小于1变成1，大于31变成31
        if (val !== '') {
          const day = parseInt(val, 10)
          if (!isNaN(day)) {
            if (day < 1) {
              $(this).val(1)
              val = '1'
            } else if (day > 31) {
              $(this).val(31)
              val = '31'
            }
          }
        }
        updateRentDueSuffix(val)
      })
      // 添加blur事件处理，确保用户离开输入框时也进行范围检查
      $('#rent-due-day').on('blur', function() {
        let val = $(this).val().trim()
        if (val === '') {
          // 如果输入框为空，设置为默认值1
          $(this).val(1)
          updateRentDueSuffix('1')
        } else {
          const day = parseInt(val, 10)
          if (!isNaN(day)) {
            if (day < 1) {
              $(this).val(1)
              updateRentDueSuffix('1')
            } else if (day > 31) {
              $(this).val(31)
              updateRentDueSuffix('31')
            }
          } else {
            // 如果输入的不是数字，设置为默认值1
            $(this).val(1)
            updateRentDueSuffix('1')
          }
        }
      })
      function updateRentDueSuffix(val) {
        const day = parseInt(val, 10)
        let suffix = ''
        let hint = ''
        let suffixText = ''

        if (!isNaN(day)) {
          // 计算序数后缀
          if (day >= 11 && day <= 13) suffix = 'th'
          else switch (day % 10) {
            case 1: suffix = 'st'; break
            case 2: suffix = 'nd'; break
            case 3: suffix = 'rd'; break
            default: suffix = 'th'
          }

          // 设置特殊提示和后缀文本
          if (day === 1) {
            suffixText = suffix
            hint = '(First day of the month)'
          } else if (day === 31) {
            suffixText = suffix
            hint = '(Last day of the month)'
          } else {
            suffixText = suffix + ' of every month'
            hint = ''
          }
        } else {
          // 如果不是有效数字，使用默认值
          suffixText = 'st'
          hint = '(First day of the month)'
        }

        // 更新后缀和提示文本
        $('#rent-due-suffix').text(suffixText)
        $('#rent-due-hint').text(hint)
      }
      // 存储lease数据并渲染tenant列表
      currentLeaseData = lease;
      renderTenantList();
      // Load and render payment list
      const payments = await leaseApi.getPayments(leaseId)
      // 按日期排序（最新的在前）并只取最近6条记录
      const sortedPayments = (payments.items || [])
        .sort((a, b) => new Date(b.date || b.dt) - new Date(a.date || a.dt))
        .slice(0, 6);
      const formattedPayments = {
        items: sortedPayments.map(payment => ({
          ...payment,
          date: formatDate(payment.date)
        })),
        total: payments.total
      }
      const renderedPaymentHtml = Mustache.render(paymentListTemplate, { payments: formattedPayments.items })
      $('#payment-list').html(renderedPaymentHtml)
    }
  } catch (error) {
    alert('Failed to load lease detail')
  }
}

function validateTenantForm() {
  let valid = true;
  // 清除所有错误提示和高亮
  $('#first-name').removeClass('border-red-500');
  $('#first-name-error').addClass('hidden');
  $('#last-name').removeClass('border-red-500');
  $('#last-name-error').addClass('hidden');
  $('#email').removeClass('border-red-500');
  $('#email-error').addClass('hidden');
  $('#dateBirth').removeClass('border-red-500');
  $('#dob-error').addClass('hidden');
  $('#phone-number').removeClass('border-red-500');
  $('#phone-number-error').addClass('hidden');
  $('#SIN').removeClass('border-red-500');
  $('#sin-error').addClass('hidden');

  // 校验 First Name
  const firstName = $('#first-name').val().trim();
  if (!firstName) {
    $('#first-name').addClass('border-red-500');
    $('#first-name-error').removeClass('hidden').text('First name is required');
    valid = false;
  }

  // 校验 Last Name
  const lastName = $('#last-name').val().trim();
  if (!lastName) {
    $('#last-name').addClass('border-red-500');
    $('#last-name-error').removeClass('hidden').text('Last name is required');
    valid = false;
  }

  // 校验 email
  const email = $('#email').val().trim();
  if (!email) {
    $('#email').addClass('border-red-500');
    $('#email-error').removeClass('hidden').text('Email is required');
    valid = false;
  } else {
    // 检查email格式是否有效
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      $('#email').addClass('border-red-500');
      $('#email-error').removeClass('hidden').text('Invalid email format');
      valid = false;
    }
  }

  // 校验生日
  const dob = $('#dateBirth').val();
  if (!dob) {
    $('#dateBirth').addClass('border-red-500');
    $('#dob-error').removeClass('hidden').text('Date of birth is required');
    valid = false;
  } else {
    // 检查是否满18岁
    const dobDate = new Date(dob);
    const now = new Date();
    const age = now.getFullYear() - dobDate.getFullYear();
    const m = now.getMonth() - dobDate.getMonth();
    const d = now.getDate() - dobDate.getDate();
    let is18 = false;
    if (age > 18) is18 = true;
    else if (age === 18) {
      if (m > 0) is18 = true;
      else if (m === 0 && d >= 0) is18 = true;
    }
    if (!is18) {
      $('#dateBirth').addClass('border-red-500');
      $('#dob-error').removeClass('hidden').text('Tenant must be at least 18 years old');
      valid = false;
    }
  }

  // 校验 phone number（可为空，有值时必须为10位数字）
  const phone = $('#phone-number').val() ? $('#phone-number').val().trim() : '';
  if (phone) {
    if (!/^\d{10}$/.test(phone)) {
      $('#phone-number').addClass('border-red-500');
      $('#phone-number-error').removeClass('hidden');
      valid = false;
    } else {
      $('#phone-number').removeClass('border-red-500');
      $('#phone-number-error').addClass('hidden');
    }
  } else {
    $('#phone-number').removeClass('border-red-500');
    $('#phone-number-error').addClass('hidden');
  }

  // 校验 SIN number（可为空，有值时必须为9位数字）
  const sin = $('#SIN').val() ? $('#SIN').val().trim() : '';
  if (sin) {
    if (!/^\d{9}$/.test(sin)) {
      $('#SIN').addClass('border-red-500');
      $('#sin-error').removeClass('hidden');
      valid = false;
    } else {
      $('#SIN').removeClass('border-red-500');
      $('#sin-error').addClass('hidden');
    }
  } else {
    $('#SIN').removeClass('border-red-500');
    $('#sin-error').addClass('hidden');
  }

  return valid;
}

function initAddTenantForm() {
  // 防止重复绑定事件
  if (window.addTenantFormInitialized) {
    return;
  }
  window.addTenantFormInitialized = true;

  // Add date input validation to prevent invalid year input (works for both add and edit modals)
  $(document).on('input', '#dateBirth', function() {
    const input = $(this);
    const value = input.val();

    // Check if the year part is more than 4 digits
    if (value) {
      const parts = value.split('-');
      if (parts[0] && parts[0].length > 4) {
        // Truncate year to 4 digits
        const truncatedYear = parts[0].substring(0, 4);
        const newValue = truncatedYear + (parts[1] ? '-' + parts[1] : '') + (parts[2] ? '-' + parts[2] : '');
        input.val(newValue);
      }
    }
  });

  // Also add keydown event to prevent typing more than 4 digits in year
  $(document).on('keydown', '#dateBirth', function(e) {
    const input = $(this);
    const value = input.val();
    const cursorPos = input[0].selectionStart;

    // If cursor is in year position (first 4 characters) and year already has 4 digits
    if (cursorPos <= 4 && value) {
      const yearPart = value.split('-')[0];
      if (yearPart && yearPart.length >= 4 && cursorPos <= 4) {
        // Allow backspace, delete, tab, escape, enter, and arrow keys
        if ($.inArray(e.keyCode, [46, 8, 9, 27, 13, 37, 38, 39, 40]) !== -1 ||
            // Allow Ctrl+A, Ctrl+C, Ctrl+V, Ctrl+X
            (e.keyCode === 65 && e.ctrlKey === true) ||
            (e.keyCode === 67 && e.ctrlKey === true) ||
            (e.keyCode === 86 && e.ctrlKey === true) ||
            (e.keyCode === 88 && e.ctrlKey === true)) {
          return;
        }
        // Stop the keypress if it's a number and we're at the year limit
        if ((e.shiftKey || (e.keyCode < 48 || e.keyCode > 57)) && (e.keyCode < 96 || e.keyCode > 105)) {
          return;
        }
        e.preventDefault();
      }
    }
  });

  $(document).on('click', '#submit-add-tenant', async function(event) {
    event.preventDefault();
    if (!validateTenantForm()) return;
    const button = $(this)
    const originalText = button.text()

    try {
      button.prop('disabled', true).text('Adding...')

      const tenantData = {
        firstName: $('#first-name').val(),
        middleName: $('#middle-name').val(),
        lastName: $('#last-name').val(),
        email: $('#email').val(),
        phoneNumber: $('#phone-number').val(),
        sinNumber: $('#SIN').val(),
        notes: $('#tenant-notes').val(),
        tenantType: $('#tenant-type').val() || 'current'
      }
      // Add optional fields only if they have values
      const dateOfBirth = $('#dateBirth').val()
      if (dateOfBirth) {
        tenantData.dateOfBirth = new Date(dateOfBirth).toISOString()
      }

      // 新增：如果有 invitationTenantId，则加到 tenantData.tenantId
      if (window.invitationTenantId) {
        tenantData.tenantId = window.invitationTenantId;
      }

      await leaseApi.addTenant(leaseId, tenantData)
      
      const urlParamsForInvitation = new URLSearchParams(window.location.search);
      const invitationCodeForUpdate = urlParamsForInvitation.get('code');

      if (invitationCodeForUpdate) {
        try {
          const res = await fetch(`/v1/invitations/verify?code=${encodeURIComponent(invitationCodeForUpdate)}`);
          if (res.ok) {
            const inv = await res.json();
            // 新增：调用后端接口修改status为active，并写入leaseId
            const updateRes = await fetch(`/v1/invitations/${encodeURIComponent(invitationCodeForUpdate)}/status`, {
              method: 'PUT',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify({ status: 'active', leaseId })
            });
            if (updateRes.ok || updateRes.status === 204) {
            } else {
            }
          } else {
          }
        } catch (e) {
        }
      } else {
        // 如果不是通过邀请链接创建的，发送邀请邮件
        try {
          // 获取当前lease信息
          const lease = await leaseApi.getLeaseById(leaseId);
          
          // 创建邀请
          const invitationPayload = {
            receiverName: `${tenantData.firstName} ${tenantData.lastName}`,
            receiverEmail: tenantData.email,
            receiverPhone: tenantData.phoneNumber,
            propertyName: lease.propertyName,
            propertyAddress: lease.propertyAddress,
            fromDate: lease.startDate,
            toDate: lease.endDate,
            rentAmount: lease.rentAmount,
            type: 'landlord_to_tenant',
            leaseId: leaseId  // 添加leaseId
          };
          
          // 创建invitation（邮件会在后端自动发送）
          await fetch('/v1/invitations', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(invitationPayload)
          });
          
        } catch (e) {
        }
      }

      // 清除modal
      document.getElementById('modal').innerHTML = ''
      // 恢复背景滚动
      document.body.style.overflow = '';
      document.documentElement.style.overflow = '';
      // 刷新页面但去掉 code 参数
      const url = new URL(window.location.href)
      url.searchParams.delete('code')
      window.location.href = url.toString()

    } catch (error) {
      alert(error.message || 'Failed to add tenant')
    } finally {
      button.prop('disabled', false).text(originalText)
    }
  })
}

function initAddPaymentForm() {
  $(document).on('click', '#submit-add-payment', async function(event) {
    event.preventDefault()
    const button = $(this)
    const originalText = button.text()

    try {
      button.prop('disabled', true).text('Adding...')

      const paymentData = {
        date: new Date($('#payment-date').val()).toISOString(),
        amount: parseFloat($('#amount-paid').val()) || 0,
        remainingBalance: parseFloat($('#remaining-balance').val()) || 0
      }

      await leaseApi.addPayment(leaseId, paymentData)
      
      // Clear modal and refresh page
      document.getElementById('modal').innerHTML = ''
      // 恢复背景滚动
      document.body.style.overflow = '';
      document.documentElement.style.overflow = '';
      window.location.reload()

    } catch (error) {
      alert(error.message || 'Failed to add payment')
    } finally {
      button.prop('disabled', false).text(originalText)
    }
  })

  // 在模态框打开时初始化owing balance值和tenant form
  $(document).on('htmx:afterSwap', async function(event) {
    if (event.detail.target.id === 'modal') {
      // 禁用背景滚动
      if (event.detail.target.innerHTML.trim() !== '') {
        document.body.style.overflow = 'hidden';
        document.documentElement.style.overflow = 'hidden';
      }

      if (event.detail.target.innerHTML.includes('Add Payment')) {
      try {
        // 获取当前lease信息
        const lease = await leaseApi.getLeaseById(leaseId);
        monthlyRent = lease.rentAmount || 0;
        currentOwingBalance = lease.owingBalance || 0; // 使用实际的欠款余额
        // 设置初始的remaining balance
        $('#remaining-balance').val(currentOwingBalance.toFixed(2));
        // 设置Payment Date为今天
        const today = new Date();
        const yyyy = today.getFullYear();
        const mm = String(today.getMonth() + 1).padStart(2, '0');
        const dd = String(today.getDate()).padStart(2, '0');
        $('#payment-date').val(`${yyyy}-${mm}-${dd}`);
        // 为amount-paid输入框添加input事件监听
        $('#amount-paid').on('input', function() {
          // 只有当remaining balance没有被手动修改过时，才自动计算
          if (!$('#remaining-balance').data('manually-edited')) {
            const amountPaid = parseFloat($(this).val()) || 0;
            const remainingBalance = currentOwingBalance - amountPaid; // 使用owingBalance计算
            $('#remaining-balance').val(remainingBalance.toFixed(2));
          }
        });
        // 标记remaining balance是否被手动修改过
        $('#remaining-balance').on('input', function() {
          $(this).data('manually-edited', true);
        });
        // 当remaining balance失去焦点且为空时，重新开始自动计算
        $('#remaining-balance').on('blur', function() {
          if (!$(this).val()) {
            $(this).data('manually-edited', false);
            const amountPaid = parseFloat($('#amount-paid').val()) || 0;
            const remainingBalance = currentOwingBalance - amountPaid; // 使用owingBalance计算
            $(this).val(remainingBalance.toFixed(2));
          }
        });
      } catch (error) {
      }
      } else if (event.detail.target.innerHTML.includes('Add Tenant')) {
        // 初始化Add Tenant表单（现在有防重复绑定机制）
        initAddTenantForm();
      }
    }
  });
}

// Initialize row click handlers for tenants and payments
function initRowClickHandlers() {
  // Handle tenant row click
  // TODO: use dom id
  $(document).on('click', '#tenant-list tr:not(.bg-slate-200)', async function() {
    if ($(this).hasClass('text-center')) return; // Skip "No tenants found" row
    const tenantId = $(this).data('tenant-id');
    try {
      // 直接从window.currentTenants查找
      const tenant = window.currentTenants.find(t => t.id === tenantId || t._id === tenantId);
      if (!tenant) throw new Error('Tenant not found');
      // Show modal with existing data
      const modalHtml = Mustache.render(tenantViewTemplate, {
        ...tenant,
        dateOfBirth: tenant.dateOfBirth ? formatDate(tenant.dateOfBirth) : '',
        // 根据当前显示的tenant类型决定下拉菜单选项
        isCurrentTenant: currentTenantView === 'current'
      });
      $('#modal').html(modalHtml);
      // 禁用背景滚动
      document.body.style.overflow = 'hidden';
      document.documentElement.style.overflow = 'hidden';
    } catch (error) {
      alert('Failed to load tenant details');
    }
  });

  // Handle payment row click
  $(document).on('click', '#payment-list tr:not(.bg-slate-200)', async function() {
    if ($(this).hasClass('text-center')) return; // Skip "No payments found" row
    
    const paymentDate = $(this).find('td:nth-child(1) span').text();
    const paymentAmount = $(this).find('td:nth-child(2)').text().replace('$', '');
    
    try {
      // Find payment by date and amount since we don't have ID in the row
      const payments = await leaseApi.getPayments(leaseId);
      const payment = payments.items.find(payment => 
        formatDate(payment.date) === paymentDate && 
        payment.amount.toString() === paymentAmount
      );
      if (!payment) throw new Error('Payment not found');
      
      // Show modal with existing data
      const modalHtml = Mustache.render(paymentViewTemplate, {
        ...payment,
        date: formatDate(payment.date)
      });
      $('#modal').html(modalHtml);
      // 禁用背景滚动
      document.body.style.overflow = 'hidden';
      document.documentElement.style.overflow = 'hidden';

      // 初始化Update Payment弹窗逻辑
      setTimeout(async () => {
        try {
          // 获取当前lease信息以获取实时owingBalance
          const lease = await leaseApi.getLeaseById(leaseId);
          const currentOwingBalance = lease.owingBalance || 0;

          // 获取原始付款金额，用于计算修改前的原始欠款
          const originalAmount = payment.amount || 0;

          // 计算修改前的原始欠款：当前余额 + 原始付款金额
          const originalOwingBalance = currentOwingBalance + originalAmount;

          // 设置初始的remaining balance为当前owingBalance
          $('#remaining-balance').val(currentOwingBalance.toFixed(2));

          // 保存原始付款金额到data属性，供计算使用
          $('#payment-amount').data('original-amount', originalAmount);

          // 为amount输入框添加input事件监听，自动重新计算remaining balance
          $('#payment-amount').on('input', function() {
            // 只有当remaining balance没有被手动修改过时，才自动计算
            if (!$('#remaining-balance').data('manually-edited')) {
              const newAmountPaid = parseFloat($(this).val()) || 0;
              // 计算新的余额：原始欠款 - 新的付款金额
              const remainingBalance = originalOwingBalance - newAmountPaid;
              $('#remaining-balance').val(remainingBalance.toFixed(2));
            }
          });

          // 标记remaining balance是否被手动修改过
          $('#remaining-balance').on('input', function() {
            $(this).data('manually-edited', true);
          });

          // 当remaining balance失去焦点且为空时，重新开始自动计算
          $('#remaining-balance').on('blur', function() {
            if (!$(this).val()) {
              $(this).data('manually-edited', false);
              const newAmountPaid = parseFloat($('#payment-amount').val()) || 0;
              const remainingBalance = originalOwingBalance - newAmountPaid;
              $(this).val(remainingBalance.toFixed(2));
            }
          });

        } catch (error) {
        }
      }, 0);

    } catch (error) {
      alert('Failed to load payment details');
    }
  });

  // Handle tenant action dropdown change
  $(document).on('change', '#tenant-action-dropdown', async function(event) {
    const selectedAction = $(this).val();
    const tenantId = $(this).data('tenant-id');
    const dropdown = $(this);

    if (!selectedAction) return; // No action selected

    let confirmMessage = '';
    let apiCall = null;
    let successMessage = '';

    if (selectedAction === 'move-to-past') {
      confirmMessage = 'Are you sure you want to move this tenant to past tenants?';
      apiCall = () => leaseApi.moveTenantToPast(leaseId, tenantId);
      successMessage = 'Tenant moved to past successfully';
    } else if (selectedAction === 'move-to-current') {
      confirmMessage = 'Are you sure you want to move this tenant to current tenants?';
      apiCall = () => leaseApi.moveTenantToCurrent(leaseId, tenantId);
      successMessage = 'Tenant moved to current successfully';
    }

    if (!confirm(confirmMessage)) {
      dropdown.val(''); // Reset dropdown selection
      return;
    }

    try {
      dropdown.prop('disabled', true);
      await apiCall();

      // Close modal and refresh page
      $('#modal').html('');
      // 恢复背景滚动
      document.body.style.overflow = '';
      document.documentElement.style.overflow = '';
      await loadLeaseDetail();
    } catch (error) {
      alert(error.message || 'Failed to move tenant');
      dropdown.val(''); // Reset dropdown selection on error
    } finally {
      dropdown.prop('disabled', false);
    }
  });

  // Handle delete tenant button click (hard delete)
  $(document).on('click', '#delete-tenant', async function(event) {
    event.preventDefault(); // 阻止表单提交

    const tenantId = $(this).data('tenant-id');
    const button = $(this);
    const originalText = button.text();

    // 从当前租客数据中查找该租客
    const tenant = window.currentTenants.find(t => t.id === tenantId || t._id === tenantId);
    if (!tenant) {
      alert('Tenant not found');
      return;
    }

    // 检查租客是否受保护 (注意字段名可能是 isProtected 或 IsProtected)
    if (tenant.isProtected || tenant.IsProtected) {
      // 显示保护错误模态框
      const constraintModalHtml = Mustache.render(deletionConstraintModalTemplate, {
        title: 'Cannot Delete Tenant',
        message: 'Cannot delete protected tenant. This tenant is protected due to Metro2 reporting requirements',
        isConstraintError: true
      });
      $('#modal').html(constraintModalHtml);

      // 禁用背景滚动
      document.body.style.overflow = 'hidden';
      document.documentElement.style.overflow = 'hidden';
      return;
    }

    // 租客未受保护，显示删除确认框
    if (!confirm('Are you sure you want to permanently delete this tenant? This action cannot be undone.')) {
      return;
    }

    try {
      button.prop('disabled', true).text('Deleting...');
      await leaseApi.deleteLeaseTenant(leaseId, tenantId);

      // Close modal and refresh page
      $('#modal').html('');
      // 恢复背景滚动
      document.body.style.overflow = '';
      document.documentElement.style.overflow = '';
      await loadLeaseDetail();
    } catch (error) {
      alert(error.message || 'Failed to delete tenant');
    } finally {
      button.prop('disabled', false).text(originalText);
    }
  });

  // Handle delete payment button click
  $(document).on('click', '#delete-payment', async function(event) {
    event.preventDefault(); // 阻止表单提交

    const paymentId = $(this).data('payment-id');
    const button = $(this);
    const originalText = button.text();

    try {
      // 先获取payment信息检查是否受保护
      const payments = await leaseApi.getPayments(leaseId);
      const payment = payments.items.find(p => p.id === paymentId);

      if (!payment) {
        alert('Payment not found');
        return;
      }

      // 检查payment是否受保护
      if (payment.isProtected) {
        // 显示保护错误模态框
        const constraintModalHtml = Mustache.render(deletionConstraintModalTemplate, {
          title: 'Cannot Delete Payment',
          message: 'Cannot delete protected payment. This payment is protected due to Metro2 reporting requirements',
          isConstraintError: true
        });
        $('#modal').html(constraintModalHtml);

        // 禁用背景滚动
        document.body.style.overflow = 'hidden';
        document.documentElement.style.overflow = 'hidden';
        return;
      }

      // Payment未受保护，显示删除确认框
      const modalHtml = Mustache.render(confirmDeleteModalTemplate, {
        title: 'Delete Payment',
        message: 'Are you sure you want to permanently delete this payment? This action cannot be undone.'
      });
      $('#modal').html(modalHtml);

      // 禁用背景滚动
      document.body.style.overflow = 'hidden';
      document.documentElement.style.overflow = 'hidden';

      // 处理确认删除
      $('#confirm-delete-btn').off('click').on('click', async function() {
        try {
          button.prop('disabled', true).text('Deleting...');
          await leaseApi.removePayment(leaseId, paymentId);

          // Close modal and refresh page
          $('#modal').html('');
          // 恢复背景滚动
          document.body.style.overflow = '';
          document.documentElement.style.overflow = '';
          await loadLeaseDetail();
        } catch (error) {
          alert(error.message || 'Failed to delete payment');
        } finally {
          button.prop('disabled', false).text(originalText);
        }
      });

    } catch (error) {
      console.error('Failed to check payment protection:', error);
      alert('Failed to check payment status');
    }
  });

  // Handle update tenant button click
  $(document).on('click', '#update-tenant', async function(event) {
    event.preventDefault();
    if (!validateTenantForm()) return;
    const button = $(this);
    const originalText = button.text();
    const tenantId = button.data('tenant-id');

    try {
      button.prop('disabled', true).text('Updating...');

      const tenantData = {
        firstName: $('#first-name').val(),
        middleName: $('#middle-name').val(),
        lastName: $('#last-name').val(),
        email: $('#email').val(),
        phoneNumber: $('#phone-number').val(),
        sinNumber: $('#SIN').val(),
        notes: $('#tenant-notes').val()
      };

      // Add optional fields only if they have values
      const dateOfBirth = $('#dateBirth').val();
      if (dateOfBirth) {
        tenantData.dateOfBirth = new Date(dateOfBirth).toISOString();
      }

      // 调用leaseApi.updateLeaseTenant
      await leaseApi.updateLeaseTenant(leaseId, tenantId, tenantData);
      
      // Close modal and refresh page
      $('#modal').html('');
      // 恢复背景滚动
      document.body.style.overflow = '';
      document.documentElement.style.overflow = '';
      await loadLeaseDetail();

    } catch (error) {
      alert(error.message || 'Failed to update tenant');
    } finally {
      button.prop('disabled', false).text(originalText);
    }
  });

  // Handle update payment button click
  $(document).on('click', '#update-payment', async function(event) {
    event.preventDefault();
    const button = $(this);
    const originalText = button.text();
    const paymentId = button.data('payment-id');

    try {
      button.prop('disabled', true).text('Updating...');

      const paymentData = {
        date: new Date($('#payment-date').val()).toISOString(),
        amount: parseFloat($('#payment-amount').val()) || 0,
        remainingBalance: parseFloat($('#remaining-balance').val()) || 0
      };

      await leaseApi.updatePayment(leaseId, paymentId, paymentData);
      
      // Close modal and refresh page
      $('#modal').html('');
      // 恢复背景滚动
      document.body.style.overflow = '';
      document.documentElement.style.overflow = '';
      await loadLeaseDetail();

    } catch (error) {
      alert(error.message || 'Failed to update payment');
    } finally {
      button.prop('disabled', false).text(originalText);
    }
  });

  // Handle close modal button click
  $(document).on('click', '#close-modal', function() {
    $('#modal').html('');
    // 恢复背景滚动
    document.body.style.overflow = '';
    document.documentElement.style.overflow = '';
  });

  // Handle View Plans button click
  $(document).on('click', '#view-plans-btn', function() {
    window.location.href = '../account/index.html#Plans';
  });

  // Handle Upload File button click
  $(document).on('click', '#upload-file-btn', function() {
    // 关闭模态框
    $('#modal').html('');
    document.body.style.overflow = '';
    document.documentElement.style.overflow = '';

    // 触发文件选择
    $('input[type="file"]').click();
  });

  // Handle rent reporting checkbox change
  $(document).on('change', '#rent-reporting', function() {
    const isChecked = $(this).is(':checked');

    // 如果用户尝试勾选rent reporting，检查权限和文件
    if (isChecked) {
      const userData = window.currentUser || JSON.parse(localStorage.getItem('user') || '{}');

      // 检查权限：不是vip用户
      if (userData.accountType !== 'vip') {
        // 显示升级提示弹窗
        const modalHtml = Mustache.render(upgradePromptModalTemplate, {
          title: 'Rent Reporting',
          message: 'This feature is not included in your current plan. You can unlock it by upgrading to a paid plan.'
        });
        $('#modal').html(modalHtml);
        // 禁用背景滚动
        document.body.style.overflow = 'hidden';
        document.documentElement.style.overflow = 'hidden';

        // 取消勾选checkbox
        $(this).prop('checked', false);
        return;
      }

      // 检查是否有上传的文件
      if (!hasUploadedFile()) {
        // 显示文件上传提示弹窗
        const modalHtml = Mustache.render(upgradePromptModalTemplate, {
          title: 'Rent Reporting',
          message: 'A lease document is required to enable rent reporting. Please upload a lease document first.',
          buttonText: 'Upload File',
          buttonId: 'upload-file-btn'
        });
        $('#modal').html(modalHtml);
        // 禁用背景滚动
        document.body.style.overflow = 'hidden';
        document.documentElement.style.overflow = 'hidden';

        // 取消勾选checkbox
        $(this).prop('checked', false);
        return;
      }
    }
  });
}

// 初始化保存按钮功能
function initSaveButton() {
  // 更具体的选择器，只匹配底部的Save Changes按钮
  $(document).on('click', '.fixed.bottom-0 .bg-slate-800.text-white', async function() {
    const button = $(this);
    const originalText = button.text();

    try {
      button.prop('disabled', true).text('Saving...');
      // 获取当前用户信息
      const userData = window.currentUser || JSON.parse(localStorage.getItem('user') || '{}');
      // 检查权限：不是vip且勾选了rent reporting
      if ((userData.accountType !== 'vip') && $('#rent-reporting').is(':checked')) {
        const modalHtml = Mustache.render(upgradePromptModalTemplate, {
          title: 'Rent Reporting',
          message: 'This feature is not included in your current plan. You can unlock it by upgrading to a paid plan.'
        });
        $('#modal').html(modalHtml);
        // 禁用背景滚动
        document.body.style.overflow = 'hidden';
        document.documentElement.style.overflow = 'hidden';
        button.prop('disabled', false).text(originalText);
        return;
      }

      // 检查是否勾选了rent reporting但没有上传文件
      if ($('#rent-reporting').is(':checked') && !hasUploadedFile()) {
        const modalHtml = Mustache.render(upgradePromptModalTemplate, {
          title: 'Rent Reporting',
          message: 'A lease document is required to enable rent reporting. Please upload a lease document first.',
          buttonText: 'Upload File',
          buttonId: 'upload-file-btn'
        });
        $('#modal').html(modalHtml);
        // 禁用背景滚动
        document.body.style.overflow = 'hidden';
        document.documentElement.style.overflow = 'hidden';
        //alert('This feature isn’t included in your current plan. You can unlock it with pay-per-use, or by upgrading to a paid plan that includes it.');
        button.prop('disabled', false).text(originalText);
        return;
      }

      // 1. 处理文件删除
      for (const fileId of pendingDeletes) {
        try {
          const deleteResponse = await fetch(`/v1/leases/${leaseId}/document/${fileId}`, {
            method: 'DELETE'
          });
          if (!deleteResponse.ok) {
          }
        } catch (error) {
        }
      }

      // 2. 处理文件上传
      const uploadResults = [];
      for (const file of pendingUploads) {
        try {
          const formData = new FormData();
          formData.append('file', file);

          const uploadResponse = await fetch(`/v1/leases/${leaseId}/document`, {
            method: 'POST',
            body: formData
          });

          if (!uploadResponse.ok) {
            throw new Error(`Failed to upload file: ${file.name}`);
          }

          const result = await uploadResponse.json();
          uploadResults.push(result); // 使用完整的返回结果
        } catch (error) {
          alert(`Failed to upload ${file.name}: ${error.message}`);
        }
      }
      // 2. 获取当前租约信息
      const currentLeaseData = await leaseApi.getLeaseById(leaseId);

      // 3. 检查rent reporting状态 - 如果没有文件则强制关闭
      let rentReportingEnabled = $('#rent-reporting').is(':checked');
      if (rentReportingEnabled && !hasUploadedFile()) {
        rentReportingEnabled = false;
        $('#rent-reporting').prop('checked', false);
      }

      // 4. 获取更新后的表单数据
      const updatedLeaseData = {
        ...currentLeaseData, // 保留原有数据
        rentDueDay: parseInt($('#rent-due-day').val()) || 1,
        rentAmount: parseFloat($('#monthly-rent').val()) || 0,
        additionalMonthlyFees: parseFloat($('#additional-fees').val()) || 0,
        status: $('#lease-status').val(),
        notes: $('textarea#lease-notes').val() || '',
        rentReporting: rentReportingEnabled,
        autoPay: $('#auto-pay').is(':checked')
      };
      
      // 4. 更新文档信息（多文件支持）
      if (uploadResults.length > 0) {
        // 如果有新上传的文件，更新文档列表
        const newDocuments = uploadResults.map(result => {
          const doc = result.document || result; // 兼容不同的返回格式
          return {
            id: doc.id || doc.ID,
            fileId: doc.fileId || doc.FileID,
            fileName: doc.fileName || doc.FileName,
            fileSize: doc.fileSize || doc.FileSize,
            fileType: doc.fileType || doc.FileType,
            uploadedAt: doc.uploadedAt || doc.UploadedAt,
            uploadedBy: doc.uploadedBy || doc.UploadedBy,
            filePath: doc.filePath || doc.FilePath,
            status: 'normal',
            generatedName: doc.generatedName || doc.GeneratedName
          };
        });

        // 合并现有文档和新上传的文档
        const existingDocs = uploadedDocuments.filter(doc => !pendingDeletes.includes(doc.id));
        updatedLeaseData.documents = [...existingDocs, ...newDocuments];
      } else {
        // 如果没有新上传，只更新现有文档列表（移除已删除的）
        updatedLeaseData.documents = uploadedDocuments.filter(doc => !pendingDeletes.includes(doc.id));
      }

      // 不再更新单文件字段，让后端处理兼容性
      
      // 5. 发送更新请求
      await leaseApi.updateLease(leaseId, updatedLeaseData);

      // 6. 清空待处理列表
      pendingUploads = [];
      pendingDeletes = [];

      // Update global references
      window.pendingUploads = pendingUploads;
      window.pendingDeletes = pendingDeletes;

      // 7. 刷新页面显示更新后的数据
      await loadLeaseDetail();

      // 8. Mark as saved for unsaved changes detection
      if (unsavedChangesDetector) {
        unsavedChangesDetector.markAsSaved();
      }

      // 9. 显示成功消息
      alert('Lease updated successfully');
    } catch (error) {
      alert('Failed to update lease: ' + error.message);
    } finally {
      button.prop('disabled', false).text(originalText);
    }
  });
}

// 下载Lease PDF
async function downloadLeasePDF() {
  try {
    // 获取完整的lease信息
    const lease = await leaseApi.getLeaseById(leaseId);
    const tenants = await leaseApi.getTenants(leaseId);
    const payments = await leaseApi.getPayments(leaseId);

    // 创建PDF文档
    const doc = new jsPDF();
    const pageWidth = doc.internal.pageSize.getWidth();
    
    // 添加标题
    doc.setFontSize(20);
    doc.text('Lease Record', pageWidth/2, 20, { align: 'center' });
    
    // 设置正文字体大小
    doc.setFontSize(12);
    
    // 添加基本信息
    let y = 40;
    doc.text(`Property Name: ${lease.propertyName}`, 20, y);
    y += 10;
    doc.text(`Property Address: ${lease.propertyAddress || ''}`, 20, y);
    y += 10;
    doc.text(`Unit: ${lease.roomName}`, 20, y);
    y += 10;
    doc.text(`Rent Due Date: ${lease.rentDueDay || 1}`, 20, y);
    y += 10;
    doc.text(`Monthly Rent: $${lease.rentAmount || 0}`, 20, y);
    y += 10;
    doc.text(`Additional Monthly Fees: $${lease.additionalMonthlyFees || 0}`, 20, y);
    y += 10;
    doc.text(`Key Deposit: $${lease.keyDeposit || 0}`, 20, y);
    y += 10;
    doc.text(`Rent Deposit: $${lease.rentDeposit || 0}`, 20, y);
    y += 10;
    doc.text(`Other Deposit: $${lease.otherDeposits || 0}`, 20, y);
    y += 10;
    doc.text(`Status: ${lease.status || 'Unknown'}`, 20, y);
    y += 15;

    // 添加租户信息（不包含sin number、phone number、note）
    if (tenants && tenants.items && tenants.items.length > 0) {
      doc.text('Tenants:', 20, y);
      y += 10;

      // 添加租户表头
      doc.setFontSize(10);
      doc.text('Name', 25, y);
      doc.text('Date of Birth', 80, y);
      doc.text('Email', 120, y);
      y += 5;

      // 添加分隔线
      doc.line(20, y, pageWidth - 20, y);
      y += 7;

      // 遍历所有租户
      tenants.items.forEach(tenant => {
        if (y > 270) {
          doc.addPage();
          y = 20;
        }

        // 姓名（包括中间名）
        const fullName = [tenant.firstName, tenant.middleName, tenant.lastName]
          .filter(Boolean)
          .join(' ');
        doc.text(fullName, 25, y);

        // 出生日期
        const dob = tenant.dateOfBirth ? formatDate(tenant.dateOfBirth) : 'N/A';
        doc.text(dob, 80, y);

        // 邮箱
        doc.text(tenant.email || 'N/A', 120, y);

        y += 10;
      });
      y += 10;
    }

    // 添加付款记录
    if (payments && payments.items && payments.items.length > 0) {
      if (y > 250) {
        doc.addPage();
        y = 20;
      }
      doc.setFontSize(12);
      doc.text('Payment History:', 20, y);
      y += 10;

      // 添加付款表头
      doc.setFontSize(10);
      doc.text('Date', 25, y);
      doc.text('Amount', 75, y);
      doc.text('Remaining Balance', 125, y);
      y += 5;

      // 添加分隔线
      doc.line(20, y, pageWidth - 20, y);
      y += 7;

      // 遍历所有付款记录
      payments.items.forEach(payment => {
        if (y > 270) {
          doc.addPage();
          y = 20;
        }
        const date = formatDate(payment.date);
        const amount = `$${payment.amount}`;
        const balance = `$${payment.remainingBalance}`;

        doc.text(date, 25, y);
        doc.text(amount, 75, y);
        doc.text(balance, 125, y);
        y += 7;
      });
      y += 10;
    }
    
    // 生成文件名
    const fileName = 'lease_record.pdf';
    
    // 下载PDF
    doc.save(fileName);
    
  } catch (error) {
    alert('Failed to generate PDF');
  }
}

// 初始化下载按钮事件
function initDownloadButton() {
  $('#download-metro2').on('click', downloadLeasePDF);
}

// 渲染文档显示区域
function renderDocumentDisplay(lease) {
  const userData = window.currentUser || JSON.parse(localStorage.getItem('user') || '{}')
  const isLandlord = (userData.viewType || '').toLowerCase() !== 'tenant'
  const isOwner = lease.userID === userData.id || lease.usrId === userData.id

  if (isLandlord && isOwner) {
    // Landlord view - 初始化多文件上传
    // 从lease数据中提取文档信息
    uploadedDocuments = [];
    if (lease.documents && Array.isArray(lease.documents)) {
      uploadedDocuments = lease.documents
        .filter(doc => doc.fileName && doc.fileName.trim() !== '') // 过滤掉空文档
        .map(doc => ({
          id: doc.id || doc.ID,
          fileId: doc.fileId || doc.FileID,
          fileName: doc.fileName || doc.FileName,
          fileSize: doc.fileSize || doc.FileSize || 0,
          fileType: doc.fileType || doc.FileType || 'application/pdf',
          uploadedAt: doc.uploadedAt || doc.UploadedAt,
          uploadedBy: doc.uploadedBy || doc.UploadedBy,
          filePath: doc.filePath || doc.FilePath,
          generatedName: doc.generatedName || doc.GeneratedName,
          status: doc.status || doc.Status || '', // 添加status字段
          isProtected: doc.isProtected || doc.IsProtected || false // 添加isProtected字段
        }));
    }
    // 单文件字段已移除，不再需要兼容性处理

    // 清空待处理列表
    pendingUploads = [];
    pendingDeletes = [];

    // Update global references
    window.pendingUploads = pendingUploads;
    window.pendingDeletes = pendingDeletes;

    // 渲染文件列表
    renderUploadedFiles();
  } else {
    // Tenant/Admin view - 只能查看和下载
    const documentDisplay = $('#lease-document-display')
    // 过滤出非deleted和final_deleted状态的文件
    const activeDocuments = (lease.documents || []).filter(doc =>
      doc.status !== 'deleted' && doc.status !== 'final_deleted'
    );

    if (activeDocuments.length > 0) {
      // 显示有效文档
      const documentsHtml = activeDocuments.map(doc => `
        <a href="/v1/leases/download/${doc.id}" target="_blank" class="flex items-center gap-2 hover:bg-gray-100 bg-gray-50 px-3 py-1.5 rounded-md text-gray-700 mb-2">
          <svg class="w-4 h-4 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z" />
          </svg>
          <span>${doc.fileName}</span>
        </a>
      `).join('');
      documentDisplay.html(documentsHtml);
    } else {
      documentDisplay.html('<span class="text-gray-500 text-sm">No file uploaded</span>')
    }
  }
}

// 新增：日期后缀处理函数
function getDayWithSuffix(day) {
  if (typeof day !== 'number') return '';
  if (day >= 11 && day <= 13) return day + 'th';
  switch (day % 10) {
    case 1: return day + 'st';
    case 2: return day + 'nd';
    case 3: return day + 'rd';
    default: return day + 'th';
  }
}

// 多文件选择处理
$(document).on('change', '#file-input', function() {
  const files = Array.from(this.files);
  // 计算有效文件数量（排除deleted和final_deleted状态的文件）
  const activeUploadedFiles = uploadedDocuments.filter(doc =>
    doc.status !== 'deleted' && doc.status !== 'final_deleted'
  ).length;
  const currentTotal = activeUploadedFiles + pendingUploads.length - pendingDeletes.length;

  // 检查文件数量限制
  if (currentTotal >= MAX_FILES) {
    this.value = ''; // 清空文件选择
    return;
  }

  // 如果添加这些文件会超过限制，只添加能添加的文件
  const availableSlots = MAX_FILES - currentTotal;
  const filesToAdd = files.slice(0, availableSlots);

  // 验证文件类型和大小
  const validFiles = [];
  for (const file of filesToAdd) {
    if (validateFile(file)) {
      validFiles.push(file);
    }
  }

  // 添加到待上传列表
  pendingUploads.push(...validFiles);

  // 重新渲染文件列表
  renderUploadedFiles();

  // 清空文件输入
  this.value = '';

  // Mark as changed for unsaved changes detection
  if (typeof unsavedChangesDetector !== 'undefined') {
    unsavedChangesDetector.markAsChanged();
  }
});

// 检查文件名是否重复
function checkDuplicateFileName(file) {
  const fileName = file.name;

  // 检查与已上传文件的重复（排除待删除的文件和deleted/final_deleted状态的文件）
  const existingFile = uploadedDocuments.find(doc =>
    doc.fileName === fileName &&
    !pendingDeletes.includes(doc.id) &&
    doc.status !== 'deleted' &&
    doc.status !== 'final_deleted'
  );

  if (existingFile) {
    return {
      isDuplicate: true,
      type: 'uploaded',
      existingFile: existingFile
    };
  }

  // 检查与待上传文件的重复
  const pendingFile = pendingUploads.find(pendingFile => pendingFile.name === fileName);
  if (pendingFile) {
    return {
      isDuplicate: true,
      type: 'pending',
      existingFile: pendingFile
    };
  }

  return { isDuplicate: false };
}

// 显示重复文件确认模态框
function showDuplicateFileModal(file, duplicateInfo) {
  pendingDuplicateFile = file;
  duplicateFileInfo = duplicateInfo;

  const modalHtml = duplicateFileModalTemplate;
  $('#modal').html(modalHtml);

  // 禁用背景滚动
  document.body.style.overflow = 'hidden';
  document.documentElement.style.overflow = 'hidden';
}

// 处理重复文件替换
function handleFileReplace() {
  if (!pendingDuplicateFile || !duplicateFileInfo) return;

  if (duplicateFileInfo.type === 'uploaded') {
    // 如果是已上传的文件，添加到待删除列表
    if (!pendingDeletes.includes(duplicateFileInfo.existingFile.id)) {
      pendingDeletes.push(duplicateFileInfo.existingFile.id);
    }
  } else if (duplicateFileInfo.type === 'pending') {
    // 如果是待上传的文件，从待上传列表中移除
    const index = pendingUploads.indexOf(duplicateFileInfo.existingFile);
    if (index > -1) {
      pendingUploads.splice(index, 1);
    }
  }

  // 添加新文件到待上传列表
  pendingUploads.push(pendingDuplicateFile);

  // 重新渲染文件列表
  renderUploadedFiles();

  // Mark as changed for unsaved changes detection
  if (typeof unsavedChangesDetector !== 'undefined') {
    unsavedChangesDetector.markAsChanged();
  }

  // 清理临时变量
  pendingDuplicateFile = null;
  duplicateFileInfo = null;

  // 关闭模态框
  $('#modal').html('');
  document.body.style.overflow = '';
  document.documentElement.style.overflow = '';
}

// 文件验证函数
function validateFile(file) {
  const allowedTypes = ['application/pdf', 'image/png', 'image/jpeg', 'image/jpg', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'];
  const maxSizePDF = 12 * 1024 * 1024; // 12MB for PDF and DOC
  const maxSizeImage = 4 * 1024 * 1024; // 4MB for images

  if (!allowedTypes.includes(file.type)) {
    alert(`File "${file.name}" has an unsupported format. Please upload PDF, PNG, JPG, or DOC files.`);
    return false;
  }

  const maxSize = file.type.startsWith('image/') ? maxSizeImage : maxSizePDF;
  if (file.size > maxSize) {
    const maxSizeMB = maxSize / (1024 * 1024);
    alert(`File "${file.name}" is too large. Maximum size is ${maxSizeMB}MB.`);
    return false;
  }

  // 检查重复文件名
  const duplicateCheck = checkDuplicateFileName(file);
  if (duplicateCheck.isDuplicate) {
    showDuplicateFileModal(file, duplicateCheck);
    return false; // 暂时返回false，等待用户确认
  }

  return true;
}

// 全局函数：移除文件
window.removeFile = function(fileId, isUploaded) {
  if (isUploaded) {
    // 已上传的文件 - 检查是否受保护
    const fileDocument = uploadedDocuments.find(doc => doc.id === fileId);
    if (fileDocument && fileDocument.isProtected) {
      // 显示保护错误模态框
      const constraintModalHtml = Mustache.render(deletionConstraintModalTemplate, {
        title: 'Cannot Delete Document',
        message: 'Cannot delete protected document. This document is protected due to Metro2 reporting requirements',
        isConstraintError: true
      });
      $('#modal').html(constraintModalHtml);

      // 禁用背景滚动
      document.body.style.overflow = 'hidden';
      document.documentElement.style.overflow = 'hidden';
      return;
    }

    // 文档未受保护，直接添加到待删除列表
    if (!pendingDeletes.includes(fileId)) {
      pendingDeletes.push(fileId);
    }

    renderUploadedFiles();

    // 检查删除文件后是否还有文件，如果没有文件了且租赁报告是开启的，则自动关闭
    if (!hasUploadedFile() && $('#rent-reporting').is(':checked')) {
      $('#rent-reporting').prop('checked', false);
    }

    // Mark as changed for unsaved changes detection
    if (typeof unsavedChangesDetector !== 'undefined') {
      unsavedChangesDetector.markAsChanged();
    }

  } else {
    // 待上传的文件 - 从待上传列表中移除
    const index = parseInt(fileId.replace('pending-', ''));
    if (index >= 0 && index < pendingUploads.length) {
      pendingUploads.splice(index, 1);
    }

    renderUploadedFiles();

    // 检查删除文件后是否还有文件，如果没有文件了且租赁报告是开启的，则自动关闭
    if (!hasUploadedFile() && $('#rent-reporting').is(':checked')) {
      $('#rent-reporting').prop('checked', false);
    }

    // Mark as changed for unsaved changes detection
    if (typeof unsavedChangesDetector !== 'undefined') {
      unsavedChangesDetector.markAsChanged();
    }
  }
};

// 全局函数：下载文件
window.downloadFile = function(fileId) {
  window.open(`/v1/leases/download/${fileId}`, '_blank');
};

// 拖拽上传功能
$(document).ready(function() {
  const uploadArea = $('#file-upload-area');

  // 防止默认拖拽行为
  ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
    uploadArea.on(eventName, function(e) {
      e.preventDefault();
      e.stopPropagation();
    });
  });

  // 拖拽进入和悬停时的视觉反馈
  ['dragenter', 'dragover'].forEach(eventName => {
    uploadArea.on(eventName, function() {
      uploadArea.addClass('border-blue-500 bg-blue-50');
    });
  });

  ['dragleave', 'drop'].forEach(eventName => {
    uploadArea.on(eventName, function() {
      uploadArea.removeClass('border-blue-500 bg-blue-50');
    });
  });

  // 处理文件拖拽放置
  uploadArea.on('drop', function(e) {
    const files = Array.from(e.originalEvent.dataTransfer.files);
    // 计算有效文件数量（排除deleted和final_deleted状态的文件）
    const activeUploadedFiles = uploadedDocuments.filter(doc =>
      doc.status !== 'deleted' && doc.status !== 'final_deleted'
    ).length;
    const currentTotal = activeUploadedFiles + pendingUploads.length - pendingDeletes.length;

    // 检查文件数量限制
    if (currentTotal >= MAX_FILES) {
      return; // 已达到限制，不处理拖拽
    }

    // 如果添加这些文件会超过限制，只添加能添加的文件
    const availableSlots = MAX_FILES - currentTotal;
    const filesToAdd = files.slice(0, availableSlots);

    // 验证文件类型和大小
    const validFiles = [];
    for (const file of filesToAdd) {
      if (validateFile(file)) {
        validFiles.push(file);
      }
    }

    // 添加到待上传列表
    pendingUploads.push(...validFiles);

    // 重新渲染文件列表
    renderUploadedFiles();

    // Mark as changed for unsaved changes detection
    if (typeof unsavedChangesDetector !== 'undefined') {
      unsavedChangesDetector.markAsChanged();
    }
  });
});

// 初始化重复文件模态框事件处理器
function initDuplicateFileModalHandlers() {
  // 处理取消按钮点击
  $(document).on('click', '#cancel-duplicate-file-btn, #duplicate-file-close, #duplicate-file-overlay', function() {
    // 清理临时变量
    pendingDuplicateFile = null;
    duplicateFileInfo = null;

    // 关闭模态框
    $('#modal').html('');
    document.body.style.overflow = '';
    document.documentElement.style.overflow = '';
  });

  // 处理替换按钮点击
  $(document).on('click', '#replace-file-btn', function() {
    handleFileReplace();
  });
}

// Initialize lease status change handler
function initLeaseStatusHandler() {
  // Handle lease status dropdown change
  $(document).on('change', '#lease-status', function() {
    const selectedStatus = $(this).val();
    const previousStatus = $(this).data('previous-status') || 'active';

    // Check if user is trying to change status to 'ended'
    if (selectedStatus === 'ended' && previousStatus !== 'ended') {
      // Show end lease confirmation modal
      const modalHtml = Mustache.render(endLeaseModalTemplate);
      $('#modal').html(modalHtml);
      // 禁用背景滚动
      document.body.style.overflow = 'hidden';
      document.documentElement.style.overflow = 'hidden';

      // Revert the dropdown to previous status until confirmed
      $(this).val(previousStatus);

      // Store the current selection for later use
      $(this).data('pending-status', 'ended');
    } else {
      // For other status changes, update the previous status
      $(this).data('previous-status', selectedStatus);
    }
  });

  // Handle confirm end lease action
  $(document).on('click', '#confirm-end-lease-btn', function() {
    // Update the dropdown to 'ended' status
    $('#lease-status').val('ended').data('previous-status', 'ended');

    // Close the modal
    $('#modal').html('');
    // 恢复背景滚动
    document.body.style.overflow = '';
    document.documentElement.style.overflow = '';

    // Optional: You could trigger save here or let user click save button
    // For now, just update the UI and let user save manually
  });
}

// 回收站相关功能
function initRecycleBinHandlers() {
  // 打开回收站弹窗
  $('#recycle-bin-btn').on('click', function() {
    openRecycleBinModal();
  });

  // 关闭回收站弹窗
  $(document).on('click', '#close-recycle-modal, #done-recycle-modal', function() {
    closeRecycleBinModal();
  });

  // 点击背景关闭弹窗
  $(document).on('click', '#recycle-bin-modal', function(e) {
    if (e.target === this) {
      closeRecycleBinModal();
    }
  });

  // 恢复文件
  $(document).on('click', '.restore-file-btn', function() {
    const documentId = $(this).data('document-id');
    const $button = $(this);

    // 立即禁用按钮防止重复点击
    $button.prop('disabled', true).addClass('cursor-not-allowed bg-gray-400').removeClass('bg-blue-600 hover:bg-blue-700').text('Restoring...');

    restoreFile(documentId);
  });
}

// 打开回收站弹窗
async function openRecycleBinModal() {
  try {
    // 禁用背景滚动
    document.body.style.overflow = 'hidden';
    document.documentElement.style.overflow = 'hidden';

    // 显示弹窗
    $('#recycle-bin-modal').removeClass('hidden');

    // 加载已删除的文件
    await loadDeletedFiles();
  } catch (error) {
    console.error('Failed to open recycle bin:', error);
    alert('Failed to load deleted files');
    closeRecycleBinModal();
  }
}

// 关闭回收站弹窗
function closeRecycleBinModal() {
  $('#recycle-bin-modal').addClass('hidden');
  // 恢复背景滚动
  document.body.style.overflow = '';
  document.documentElement.style.overflow = '';
}

// 加载已删除的文件
async function loadDeletedFiles() {
  try {
    const response = await leaseApi.getDeletedDocuments(leaseId);

    if (response.success && response.documents) {
      renderDeletedFiles(response.documents);
    } else {
      renderDeletedFiles([]);
    }
  } catch (error) {
    console.error('Failed to load deleted files:', error);
    renderDeletedFiles([]);
  }
}

// 渲染已删除的文件列表
function renderDeletedFiles(deletedFiles) {
  const $filesList = $('#deleted-files-list');
  const $emptyState = $('#empty-recycle-bin');
  const $warningMessage = $('#file-limit-warning');

  if (deletedFiles.length === 0) {
    $filesList.empty();
    $emptyState.removeClass('hidden');
    $warningMessage.addClass('hidden');
    return;
  }

  $emptyState.addClass('hidden');

  // 计算当前数据库中的有效文件数量（不考虑pending删除操作）
  const activeUploadedFiles = uploadedDocuments.filter(doc =>
    doc.status !== 'deleted' && doc.status !== 'final_deleted'
  ).length;
  const totalActiveFiles = activeUploadedFiles + pendingUploads.length;

  // 显示或隐藏警告消息
  const isAtLimit = totalActiveFiles >= MAX_FILES;
  if (isAtLimit) {
    $warningMessage.removeClass('hidden');
  } else {
    $warningMessage.addClass('hidden');
  }

  const filesHtml = deletedFiles.map(file => {
    const daysLeftColor = file.daysLeft <= 3 ? 'text-red-600' :
                         file.daysLeft <= 7 ? 'text-orange-600' : 'text-gray-600';

    const restoreButtonHtml = isAtLimit
      ? `<button class="bg-gray-400 text-white px-3 py-1 rounded text-sm cursor-not-allowed" disabled>
           Restore
         </button>`
      : `<button class="restore-file-btn bg-blue-600 text-white px-3 py-1 rounded text-sm hover:bg-blue-700 transition"
                 data-document-id="${file.id}">
           Restore
         </button>`;

    return `
      <div class="flex items-center justify-between p-3 border rounded-lg">
        <div class="flex items-center space-x-3">
          <svg class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
          </svg>
          <div>
            <div class="font-medium text-sm">${file.fileName}</div>
            <div class="text-xs text-gray-500">${formatFileSize(file.fileSize)}</div>
            <div class="text-xs text-gray-500">Uploaded ${formatDate(file.uploadedAt)}</div>
            <div class="text-xs ${daysLeftColor}">${file.daysLeft} days left</div>
          </div>
        </div>
        ${restoreButtonHtml}
      </div>
    `;
  }).join('');

  $filesList.html(filesHtml);
}

// 恢复文件
async function restoreFile(documentId) {
  try {
    const response = await leaseApi.restoreDocument(leaseId, documentId);

    if (response.success) {
      // 重新加载租约数据以更新文档列表
      await loadLeaseDetail();

      // 重新加载已删除文件列表（在更新文档列表后）
      await loadDeletedFiles();
    } else {
      // 如果恢复失败（比如达到文件限制），静默处理，只刷新显示
      await loadDeletedFiles();
    }
  } catch (error) {
    console.error('Failed to restore file:', error);

    // 检查错误消息是否包含重复文件名的提示
    const errorMessage = error.message || '';
    if (errorMessage.includes('already exists')) {
      // 显示重复文件检测弹窗
      showDuplicateFileNameModal(errorMessage);
    } else {
      alert('Failed to restore file: ' + errorMessage);
    }
  }
}

// 显示文件数量限制弹窗
function showFileLimitModal() {
  const modalHtml = `
    <div class="fixed inset-0 z-50">
      <div class="fixed inset-0 bg-black bg-opacity-50"></div>
      <div class="fixed inset-0 flex items-center justify-center p-4">
        <div class="bg-white rounded-lg shadow-xl w-full max-w-md p-6">
          <h3 class="text-lg font-semibold text-gray-900 mb-4">File Limit Reached</h3>
          <p class="text-gray-700 mb-6">Cannot restore file. Maximum number of files (10) would be exceeded. Please delete some files first.</p>
          <div class="flex justify-end">
            <button onclick="closeModal()" class="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700">
              OK
            </button>
          </div>
        </div>
      </div>
    </div>
  `;

  $('#modal').html(modalHtml);
  // 禁用背景滚动
  document.body.style.overflow = 'hidden';
  document.documentElement.style.overflow = 'hidden';
}

// 显示重复文件名弹窗
function showDuplicateFileNameModal(errorMessage) {
  // 先关闭回收站弹窗
  closeRecycleBinModal();

  // 稍微延迟一下再显示新弹窗，确保旧弹窗完全关闭
  setTimeout(() => {
    const modalHtml = duplicateFileModalTemplate;
    $('#modal').html(modalHtml);

    // 禁用背景滚动
    document.body.style.overflow = 'hidden';
    document.documentElement.style.overflow = 'hidden';

    // 添加事件处理器
    $('#cancel-duplicate-file-btn, #duplicate-file-close, #duplicate-file-overlay').off('click').on('click', function() {
      closeModal();
    });

    $('#replace-file-btn').off('click').on('click', function() {
      closeModal(); // Replace按钮也只是关闭弹窗
    });
  }, 100);
}