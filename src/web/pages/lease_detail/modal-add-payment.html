<div class="fixed inset-0 z-50">
  <!-- Dark overlay with click handler to close -->
  <div
    class="fixed inset-0 bg-black bg-opacity-50 pointer-events-auto"
    onclick="document.getElementById('modal').innerHTML = ''; document.body.style.overflow = ''; document.documentElement.style.overflow = '';"
  ></div>

  <!-- Modal container -->
  <div
    class="fixed inset-0 flex items-center justify-center pointer-events-none"
  >
    <div
      class="bg-white sm:rounded-lg shadow-xl sm:w-9/12 max-w-[1120px] w-full h-full sm:max-h-[90vh] sm:min-h-[70vh] overflow-y-auto pointer-events-auto relative flex flex-col"
    >
      <!-- Modal Header -->
      <div
        class="sticky top-0 px-6 py-4 border-b bg-white z-10 flex justify-between items-center"
      >
        <div>
          <h3 class="text-lg font-semibold text-gray-900">Add Payment</h3>
          <p class="text-sm text-gray-500">Add a payment to this lease</p>
        </div>
        <button
          class="p-2 hover:bg-gray-100 rounded-full"
          onclick="document.getElementById('modal').innerHTML = ''; document.body.style.overflow = ''; document.documentElement.style.overflow = '';"
        >
          <svg
            class="w-5 h-5 text-gray-500"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M6 18L18 6M6 6l12 12"
            />
          </svg>
        </button>
      </div>

      <!-- Modal Body -->
      <div class="px-6 py-4 flex-1">
        <form>
          <div class="flex flex-wrap">
            <div class="w-full flex flex-col sm:flex-row gap-4 mt-4">
              <!-- Line 2-->
              <div class="flex flex-wrap w-full">
                <label class="w-full mb-2" for="payment-date">Payment Date</label>
                <input
                  class="p-2 py-4 w-full border border-gray-300 rounded-md focus:outline-none"
                  id="payment-date"
                  type="date"
                  value=""
                />
              </div>
              <div class="flex flex-wrap w-full">
                <label class="w-full mb-2" for="amount-paid">Amount Paid</label>
                <div class="relative w-full">
                  <span class="absolute inset-y-0 left-0 flex items-center pl-3 text-gray-500">$</span>
                  <input
                    type="text"
                    id="amount-paid"
                    class="pl-8 py-4 w-full border border-gray-300 rounded-md focus:outline-none"
                    placeholder="0"
                  />
                </div>
              </div>
            </div>
            <div class="w-full flex gap-4 mt-4 mb-8">
              <!-- Line 3-->
              <div class="flex flex-wrap w-full">
                <label class="w-full mb-2" for="remaining-balance">Remaining Balance</label>
                <div class="relative w-full">
                  <span class="absolute inset-y-0 left-0 flex items-center pl-3 text-gray-500">$</span>
                  <input
                    type="text"
                    id="remaining-balance"
                    placeholder="0"
                    class="pl-8 py-4 w-full border border-gray-300 rounded-md focus:outline-none"
                  />
                </div>
                <div class="w-full mt-1">
                  <span class="text-xs text-gray-500">Auto-calculated, but you can edit manually</span>
                </div>
              </div>
            </div>
          </div>
        </form>
      </div>

      <!-- Modal Footer -->
      <div
        class="sticky bottom-0 left-0 right-0 px-6 py-4 bg-gray-50 rounded-b-lg flex justify-between gap-2 border-t mt-auto"
      >
        <button
          class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50"
          onclick="document.getElementById('modal').innerHTML = ''; document.body.style.overflow = ''; document.documentElement.style.overflow = '';"
        >
          Cancel
        </button>
        <button
          id="submit-add-payment"
          class="px-4 py-2 text-sm font-medium text-white bg-slate-800 rounded-md hover:bg-slate-900"
        >
          Add payment
        </button>
      </div>
    </div>
  </div>
</div>
