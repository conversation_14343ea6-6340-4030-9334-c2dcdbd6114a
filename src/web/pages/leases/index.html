{{> header }}
<body class="min-h-screen bg-gray-200">
  <div class="flex min-h-screen">
    {{> navbar}}
      <!-- Right Content Box -->
      <main class="w-full bg-slate-100 lg:px-16 md:px-8 px-4 lg:ml-64 pb-20">
        <div class="max-w-[1440px] w-full mx-auto mb-32">
          <div class="flex-1 mt-20 lg:mt-8 pb-0 w-full">
            <h1 class="text-2xl">My Leases</h1>
            <!-- Search Bar -->
            <div class="mt-8 w-full">
              <div class="flex gap-2">
                <input
                  type="text"
                  id="lease-search"
                  placeholder="Search for a property or name"
                  class="p-2 px-10 border border-1 rounded text-sm flex-1 w-full sm:max-w-[300px]"
                  style="
                    background-image: url('data:image/svg+xml,%3Csvg xmlns=\'http://www.w3.org/2000/svg\' class=\'h-4 w-4\' viewBox=\'0 0 24 24\' fill=\'none\' stroke=\'%236B7280\' stroke-width=\'2\'%3E%3Cpath stroke-linecap=\'round\' stroke-linejoin=\'round\' d=\'M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z\'%3E%3C/path%3E%3C/svg%3E');
                    background-position: 8px 8px;
                    background-repeat: no-repeat;
                    background-size: 20px;
                  "
                />
                <button
                  class="hidden sm:block bg-slate-800 hover:bg-slate-900 transition text-white px-4 py-2 rounded text-sm whitespace-nowrap"
                >
                  Search
                </button>
              </div>
            </div>

            <!-- Filter and Create Section -->
            <div
              class="flex flex-col justify-between sm:items-center sm:flex-row w-full gap-4 sm:mt-8 mt-4"
            >
              <!-- Filter By -->
              <div class="flex flex-row gap-4 items-center">
                <span class="text-sm text-gray-500 whitespace-nowrap">Filter By:</span>
                <div class="relative flex-1 md:w-44 text-sm">
                  <select
                    class="p-2 w-full focus:outline-gray-100 border border-gray-200 pr-8 appearance-none rounded-md bg-white"
                    id="role"
                  >
                  <option>Current Leases</option>
                  <option>Past Leases</option>
                  <option>Show All</option>
                  </select>
                  <svg
                    class="w-4 h-4 absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-500"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M19 9l-7 7-7-7"
                    />
                  </svg>
                </div>
              </div>

              <!-- Create Lease Button -->
              <div class="w-fit flex justify-end">
                <button
                  id="lease-action-btn"
                  class="flex items-center whitespace-nowrap bg-slate-800 hover:bg-slate-900 transition text-white px-4 py-2 gap-2 rounded text-sm"
                >
                  <svg
                    class="w-5 h-5"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M12 4v16m8-8H4"
                    />
                  </svg>
                  Create Lease
                </button>
                <!-- 仅租客视图显示 -->
                <button
                  id="tenant-add-lease-btn"
                  style="display:none"
                  class="flex items-center whitespace-nowrap bg-slate-800 hover:bg-slate-900 transition text-white px-4 py-2 gap-2 rounded text-sm"
                  type="button"
                >
                  <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"/>
                  </svg>
                  Add Lease
                </button>
              </div>
            </div>
          </div>

          <!-- Mobile/Tablet View -->
          <div class="lg:hidden mt-4">
            <div id="lease-list-mobile">
              <!-- Mobile lease list will be rendered here -->
              <div class="mt-4 rounded-md overflow-hidden" style="box-shadow: 0 0 0 1px rgb(203 213 225)">
                <div class="flex flex-wrap p-4 bg-white">
                  <span class="font-medium w-full mb-2 text-center text-gray-500">Loading leases...</span>
                </div>
              </div>
            </div>

            <!-- Mobile Pagination -->
            <div id="lease-pagination-mobile" class="mt-4">
              <!-- Mobile pagination will be rendered here -->
            </div>
          </div>

          <!-- Desktop View -->
          <div class="hidden lg:block">
            <table
              class="w-full text-sm rounded-md overflow-hidden mt-4"
              style="box-shadow: 0 0 0 1px rgb(203 213 225)"
            >
              <thead>
                <tr
                  class="bg-slate-200 text-left text-black uppercase text-xs border-b border-slate-300"
                >
                  <th class="p-4 w-3/12">
                    <span class="flex items-center gap-2">
                      Name
                      <img
                        src="/assets/selector.svg"
                        alt="Sort"
                        class="w-4 h-4"
                      />
                    </span>
                  </th>
                  <th class="p-4">
                    <span class="flex items-center gap-2">
                      Unit
                      <img
                        src="/assets/selector.svg"
                        alt="Sort"
                        class="w-4 h-4"
                      />
                    </span>
                  </th>
                  <th class="p-4">
                    <span class="flex items-center gap-2">
                      Last Payment
                      <img
                        src="/assets/selector.svg"
                        alt="Sort"
                        class="w-4 h-4"
                      />
                    </span>
                  </th>
                  <th class="p-4">
                    <span class="flex items-center gap-2">
                      Rent Report
                      <img
                        src="/assets/selector.svg"
                        alt="Sort"
                        class="w-4 h-4"
                      />
                    </span>
                  </th>
                  <th class="p-4">
                    <span class="flex items-center gap-2">
                      Owing Balance
                      <img
                        src="/assets/selector.svg"
                        alt="Sort"
                        class="w-4 h-4"
                      />
                    </span>
                  </th>
                </tr>
              </thead>
              <tbody id="lease-list-tbody">
                <!-- Lease list will be rendered here -->
                <tr>
                  <td colspan="5" class="p-4 text-center">Loading leases...</td>
                </tr>
            </tbody>
            </table>

            <!-- Pagination container -->
            <div id="lease-pagination" class="mt-4">
              <!-- Pagination will be rendered here -->
            </div>
          </div>
        </div>
  </div>


</div>
    </main>

    <!-- Modal Container -->
    <div id="modal" class=""></div>

    {{> footer }}
<script type="module" src="./main.js"></script>