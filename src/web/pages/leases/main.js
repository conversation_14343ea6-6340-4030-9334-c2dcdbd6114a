// 导入依赖
import $ from 'jquery'
import { propertyApi, leaseApi, userApi } from '../../services'
import { checkAuthStatusAndRedirect } from '../../services/util'
import Mustache from 'mustache'
import leaseListTemplate from '../../templates/lease-list.mst?raw'
import leaseListMobileTemplate from '../../templates/lease-list-mobile.mst?raw'
import duplicateFileModalTemplate from '../../templates/duplicate-file-modal.mst?raw'
import paginationTemplate from '../../templates/pagination.mst?raw'
import paginationMobileTemplate from '../../templates/pagination-mobile.mst?raw'
import errorMessagesTemplate from '../../templates/error-messages.mst?raw'
import limitReachedModalTemplate from '../../templates/limit-reached-modal.mst?raw'
import selectOptionsTemplate from '../../templates/select-options.mst?raw'
import { filterAndPaginate, initSearch } from '../../services/util'
import { renderSidebarNav } from '../../services/sidebar'
import tenantLeaseListTemplate from '../../templates/tenant-lease-list.mst?raw'
import jsPDF from 'jspdf'

// Store properties data
let propertiesData = null;
let currentPage = 1;
const itemsPerPage = 10;
let searchQuery = '';
let allLeases = [];
// 排序状态
let sortField = 'tenantName'; // 默认按租户名称排序
let sortAscending = true;
let filterType = 'Current Leases'; // 和property页面保持一致，初始值与下拉框option一致

// 初始化全局数据对象
if (!window.leaseFormData) window.leaseFormData = {};

// 租约文件上传相关变量
let leaseAgreementFiles = []; // 存储多个文件
const MAX_LEASE_FILES = 10; // 最大文件数量限制

// Variables for duplicate file handling in lease creation
let pendingDuplicateFile = null;
let duplicateFileInfo = null;

// Global function to close modal and restore scrolling
window.closeModal = function() {
  // 清理文件变量
  leaseAgreementFiles = [];
  pendingDuplicateFile = null;
  duplicateFileInfo = null;

  document.getElementById('modal').innerHTML = '';
  // 恢复背景滚动
  document.body.style.overflow = '';
  document.documentElement.style.overflow = '';
};

// 计算基于到期日的月数
function calculateMonthsBasedOnDueDay(startDate, currentDate, rentDueDay) {
  if (startDate > currentDate) {
    return 0;
  }

  if (rentDueDay < 1 || rentDueDay > 31) {
    rentDueDay = 1;
  }

  let months = 0;
  let checkDate = new Date(startDate.getFullYear(), startDate.getMonth(), 1);
  const currentMonth = new Date(currentDate.getFullYear(), currentDate.getMonth(), 1);

  while (checkDate <= currentMonth) {
    // 计算这个月的租金到期日
    let dueDate = new Date(checkDate.getFullYear(), checkDate.getMonth(), rentDueDay);

    // 如果到期日超过了该月的最后一天，调整为该月最后一天
    const lastDayOfMonth = new Date(checkDate.getFullYear(), checkDate.getMonth() + 1, 0).getDate();
    if (rentDueDay > lastDayOfMonth) {
      dueDate = new Date(checkDate.getFullYear(), checkDate.getMonth(), lastDayOfMonth);
    }

    // 如果当前日期已经过了这个月的租金到期日，则计算这个月的租金
    if (currentDate >= dueDate) {
      months++;
    }

    // 移动到下一个月
    checkDate.setMonth(checkDate.getMonth() + 1);
  }

  return months;
}

// 计算初始余额
function calculateInitialBalance(startDate, rentAmount, additionalFees, rentDueDay) {
  if (!startDate || !rentAmount) {
    return 0;
  }

  const start = new Date(startDate);
  const now = new Date();
  const monthlyRent = rentAmount + (additionalFees || 0);

  // 如果开始日期在未来，余额为0
  if (start > now) {
    return 0;
  }

  // 计算月数
  const months = calculateMonthsBasedOnDueDay(start, now, rentDueDay);

  // 至少计算1个月
  const finalMonths = Math.max(months, 1);

  return finalMonths * monthlyRent;
}

// 全局的 updateBalance 函数，供多处调用
function updateBalance() {
  const startDate = $('#start-date').val();
  const rentAmount = parseFloat($('#rent-amount').val()) || 0;
  const additionalFees = parseFloat($('#additional-fees').val()) || 0;

  if (!startDate || !rentAmount) {
    $('#initial-balance').val('0.00');
    return;
  }

  // 计算 rentDueDay
  let rentDueDay = 1;
  if (startDate) {
    const dateParts = startDate.split('-');
    if (dateParts.length === 3) {
      const day = parseInt(dateParts[2], 10);
      if (!isNaN(day) && day >= 1 && day <= 31) {
        rentDueDay = day;
      }
    }
  }

  const calculatedBalance = calculateInitialBalance(startDate, rentAmount, additionalFees, rentDueDay);
  $('#initial-balance').val(calculatedBalance.toFixed(2));
}

// 初始化 balance 计算相关的事件监听器
function initBalanceCalculation() {
  // 这个函数现在只负责初始计算，事件监听器在页面加载时绑定
  setTimeout(updateBalance, 100);
}

// 页面加载完成后的交互初始化
$(document).ready(async function() {
  // 新增：每次进入都刷新侧边栏，保证导航栏和viewType一致
  const userData = await userApi.getUserInfo();
  await renderSidebarNav(userData);

  // 初始化balance相关的事件监听器（使用事件委托，在页面加载时就绑定）
  // 监听相关字段的变化
  $(document).on('input change', '#start-date, #rent-amount, #additional-fees', function() {
    if (!$('#manual-balance').is(':checked')) {
      updateBalance();
    }
  });

  // 监听手动设置复选框
  $(document).on('change', '#manual-balance', function() {
    const isManual = $(this).is(':checked');
    const $initialBalance = $('#initial-balance');

    if (isManual) {
      // 启用手动设置：移除readonly，清除所有禁用样式
      $initialBalance.prop('readonly', false)
                    .prop('disabled', false)
                    .removeClass('disabled:bg-gray-100')
                    .removeClass('disabled:text-gray-500')
                    .removeClass('disabled:cursor-not-allowed')
                    .removeClass('bg-gray-100')
                    .addClass('bg-white')
                    .css('background-color', 'white')
                    .css('color', 'black')
                    .css('cursor', 'text');

      // 确保可以获得焦点
      setTimeout(() => {
        $initialBalance.focus();
      }, 50);
    } else {
      // 禁用手动设置：添加readonly，添加禁用样式
      $initialBalance.prop('readonly', true)
                    .removeClass('bg-white')
                    .addClass('bg-gray-100')
                    .css('background-color', '#f3f4f6')
                    .css('color', '#6b7280')
                    .css('cursor', 'not-allowed');
      // 重新计算
      updateBalance();
    }
  });

  // 监听initial-balance输入框的点击事件，确保在手动模式下可以编辑
  $(document).on('click focus', '#initial-balance', function() {
    const $this = $(this);
    const isManualMode = $('#manual-balance').is(':checked');

    if (isManualMode) {
      // 确保在手动模式下输入框是可编辑的
      $this.prop('readonly', false)
           .prop('disabled', false)
           .css('background-color', 'white')
           .css('color', 'black')
           .css('cursor', 'text');
    }
  });



  // 检查用户信息
  window.initViewType = userData.viewType; // 保存初始viewType
  
  // await checkAuthStatusAndRedirect();
  
  // 检查是否是从邀请链接跳转来的
  const localUrlParams = new URLSearchParams(window.location.search);
  const fromInvitation = localUrlParams.get('fromInvitation');
  const propertyId = localUrlParams.get('propertyId');
  const roomId = localUrlParams.get('roomId');
  const invitationCode = localUrlParams.get('code');
  const action = localUrlParams.get('action');
  
  // 先加载 property 数据
  try {
    const response = await propertyApi.getProperties();
    propertiesData = response.items;

    // 只有在 property 数据加载好后再弹窗
    if (fromInvitation === '1' && propertyId && roomId && invitationCode) {
      $('#modal').load('./modal-create-lease.html', function() {
        const propertySelect = document.getElementById('property-select');
        if (propertySelect) {
          populatePropertySelect();
          $(propertySelect).val(propertyId);
          $(propertySelect).trigger('change');
          setTimeout(async () => {
            const roomSelect = document.getElementById('room-select');
            if (roomSelect) {
              $(roomSelect).val(roomId);
            }
            // 新增：根据 code 获取 invitation 并自动填充表单
            try {
              const res = await fetch(`/v1/invitations/verify?code=${encodeURIComponent(invitationCode)}`);
              if (res.ok) {
                const inv = await res.json();

                // 填充表单数据
                if (inv.fromDate) $('#start-date').val(inv.fromDate);
                if (inv.rentAmount) $('#rent-amount').val(inv.rentAmount);

                // 存invitation id到全局变量
                window.currentInvitationId = inv.id;

                // 确保balance计算逻辑已初始化
                setTimeout(() => {
                  // 确保checkbox状态正确
                  const $manualBalance = $('#manual-balance');
                  const $initialBalance = $('#initial-balance');

                  if ($manualBalance.length && $initialBalance.length) {
                    // 默认为自动计算模式
                    $manualBalance.prop('checked', false);
                    $initialBalance.prop('readonly', true)
                                  .prop('disabled', false)
                                  .removeClass('bg-white')
                                  .addClass('bg-gray-100')
                                  .css('background-color', '#f3f4f6')
                                  .css('color', '#6b7280')
                                  .css('cursor', 'not-allowed');

                    // 触发balance计算
                    updateBalance();
                  }
                }, 200);
              }
            } catch (e) {
              console.error('Failed to fetch invitation:', e);
            }
          }, 500);
        }
      });
    }

    // 检查是否从Create Property页面返回，需要自动打开Create Lease弹窗
    if (action === 'create') {
      setTimeout(() => {
        $('#lease-action-btn').trigger('click');
      }, 300);
    }
  } catch (error) {
    console.error('Failed to load properties:', error);
  }
  
  // Initialize click handler for property groups (desktop)
  $(document).on('click', '.property-group', function() {
    const $element = $(this);
    const $nextRows = $element.nextUntil('.property-group');

    // Toggle visibility of lease rows
    $nextRows.toggleClass('hidden');

    // Toggle the arrow rotation
    $element.find('.group-arrow').toggleClass('rotate-90');
  });

  // Initialize click handler for property groups (mobile)
  $(document).on('click', '.property-group-mobile', function() {
    const $element = $(this);
    const $leaseDetails = $element.siblings('.lease-details-mobile');

    // Toggle visibility of lease details
    $leaseDetails.toggleClass('hidden');

    // Toggle the arrow rotation
    $element.find('.group-arrow').toggleClass('rotate-90');
  });
  
  // Initialize search
  initSearch('#lease-search', (query) => {
    searchQuery = query;
    currentPage = 1; // Reset to first page when search changes
    loadLeaseList();
  });
  
  // Set up pagination event handlers
  $(document).on('click', '.pagination-btn.prev:not([disabled])', function() {
    if (currentPage > 1) {
      currentPage--;
      loadLeaseList();
    }
  });

  $(document).on('click', '.pagination-btn.next:not([disabled])', function() {
    currentPage++;
    loadLeaseList();
  });

  $(document).on('click', '.pagination-btn.page', function() {
    const page = parseInt($(this).data('page'), 10);
    if (page !== currentPage) {
      currentPage = page;
      loadLeaseList();
    }
  });

  // 初始化排序功能
  initSorting();

  // 初始化filter功能 - 必须在数据加载之前
  initFilterControls();

  initCreateLeaseForm();

  // 初始化租约文件上传功能
  initLeaseFileUpload();

  // 初始化重复文件模态框处理器
  initDuplicateFileModalHandlers();

  try {
    // 注意：userData已经在上面获取过了，这里不需要重复获取

    // 如果是tenant view，获取所有相关的invitation
    if ((userData.viewType || '').toLowerCase() === 'tenant') {
      let currentLease = [];
      let pastLeases = [];
      // 获取所有active的invitation
      const invRes = await leaseApi.getInvitations({ status: 'active' });

      if (invRes.items && invRes.items.length > 0) {
        // 处理active invitation
        const activeInvs = invRes.items;
        for (const inv of activeInvs) {
          if (inv.leaseId) {
            const lease = await leaseApi.getLeaseById(inv.leaseId);
            if (lease && lease.id) {
              const leaseCard = {
                id: lease.id,
                address: inv.propertyAddress || inv.propertyName || '',
                leasePeriod: (lease.startDate ? lease.startDate : '') + ' - ' + (lease.endDate ? lease.endDate : 'Present'),
                landlordName: inv.senderName || inv.senderEmail || '',
                overdueBalance: lease.owingBalance || 0,
                paymentStatus: lease.status || '',
                pendingVerification: false,
                verified: true // 绿色active
              };
              if (lease.status === 'active') {
                currentLease.push(leaseCard);
              } else {
                pastLeases.push(leaseCard);
              }
            }
          }
        }
      }

      const renderedHtml = Mustache.render(tenantLeaseListTemplate, {
        currentLease,
        pastLeases
      });
      // 找到包含搜索栏和筛选器的主容器，替换从table开始的内容
      const $mainContainer = $('.max-w-\\[1440px\\]').first();
      $mainContainer.html(renderedHtml);
      // 绑定底部 Add Lease 按钮弹窗事件
      $(document).off('click', '#add-lease-btn').on('click', '#add-lease-btn', function() {
        $('#modal').load('./modal-add-lease.html');
      });
      return;
    } else if ((userData.viewType || '').toLowerCase() === 'landlord') {
      // landlord view: 直接调用loadLeaseList，它会处理数据获取和过滤
      await loadLeaseList();
      return;
    }
  } catch (e) {
    // 可以根据需要处理未登录等情况
    console.error('Error loading leases:', e);
  }

  // 自动弹窗+autofill
  const urlParams = new URLSearchParams(window.location.search);
  const code = urlParams.get('code');
  if (urlParams.get('fromInvitation') === '1' && code) {
    setTimeout(() => {
      $('#lease-action-btn').trigger('click');
    }, 300);
    fetch(`/v1/invitations/verify?code=${encodeURIComponent(code)}`)
      .then(res => res.ok ? res.json() : Promise.reject('Invitation not found'))
      .then(inv => {
        const observer = new MutationObserver(() => {
          if ($('#tenant-name').length) {
            $('#tenant-name').val(inv.receiverName || inv.senderName || '');
            $('#tenant-email').val(inv.receiverEmail || inv.senderEmail || '');
            $('#from-date').val(inv.fromDate || '');
            $('#to-date').val(inv.toDate || '');
            $('#rent-amount').val(inv.rentAmount || '');
            observer.disconnect();
          }
        });
        const modal = document.getElementById('modal');
        if (modal) observer.observe(modal, { childList: true, subtree: true });
      });
  }

  // 处理从dashboard Create Property按钮跳转过来的情况
  if (urlParams.get('openCreateLease') === '1') {
    setTimeout(() => {
      const leaseBtn = document.getElementById('lease-action-btn');
      if (leaseBtn) {
        leaseBtn.click();
      } else {
        console.error('lease-action-btn not found');
      }
    }, 500);
  }

// 初始化filter控件
function initFilterControls() {
  // 设置filter下拉框的初始值
  $('#role').val(filterType);

  // 监听筛选框变化
  $('#role').on('change', function() {
    filterType = $(this).val();
    currentPage = 1; // 重置到第一页
    loadLeaseList();
  });
}
});

// 初始化排序功能
function initSorting() {
  // 处理表头点击事件
  $(document).on('click', 'th .flex', function() {
    const columnText = $(this).text().trim().toLowerCase();
    
    // 根据列名决定排序字段
    let field;
    switch(columnText) {
      case 'name':
        field = 'tenantName';
        break;
      case 'room':
        field = 'roomName';
        break;
      case 'last payment':
        field = 'lastPaymentDate';
        break;
      case 'rent report':
        field = 'isActive'; // 这里保持 isActive，因为我们已经将其映射为 rentReporting 状态
        break;
      case 'owing balance':
        field = 'owingBalance';
        break;
      default:
        field = 'tenantName';
    }
    
    // 如果点击的是当前排序字段，则切换排序方向
    if (field === sortField) {
      sortAscending = !sortAscending;
    } else {
      sortField = field;
      sortAscending = true;
    }
    
    // 更新视觉指示
    updateSortingIndicators(field, sortAscending);
    
    // 重新加载列表
    loadLeaseList();
  });
}

// 更新排序指示器
function updateSortingIndicators(field, ascending) {
  // 移除所有表头的排序指示
  $('th .flex img').attr('src', '/assets/selector.svg');
  
  // 获取对应的表头
  let thElement;
  switch(field) {
    case 'tenantName':
      thElement = $('th:contains("Name")');
      break;
    case 'roomName':
      thElement = $('th:contains("Room")');
      break;
    case 'lastPaymentDate':
      thElement = $('th:contains("Last Payment")');
      break;
    case 'isActive':
      thElement = $('th:contains("Rent Report")');
      break;
    case 'owingBalance':
      thElement = $('th:contains("Owing Balance")');
      break;
  }
  
  // 更新排序图标
  if (thElement && thElement.length) {
    const imgElement = thElement.find('img');
    imgElement.attr('src', ascending ? '/assets/sort-up.svg' : '/assets/sort-down.svg');
  }
}

// Load and render lease list
async function loadLeaseList() {
  try {
    // 每次都重新请求后端数据，保证筛选和数据同步
    // 为landlord view传递role参数
    const leaseResponse = await leaseApi.getLeases({ role: 'landlord' });

    allLeases = leaseResponse.items.map(lease => ({
      ...lease,
      isActive: lease.rentRep || lease.rentReporting || false, // 兼容两种字段，用于rent reporting显示
      isLeaseActive: (lease.status || '').toLowerCase() === 'active', // 用于icon显示
      lastPaymentDate: lease.lastPaymentDate || ''
    }));

    // 新增：根据筛选类型过滤
    let filtered = allLeases;
    if (filterType === 'Current Leases') {
      filtered = allLeases.filter(l => (l.status || '').toLowerCase() === 'active' || (l.status || '').toLowerCase() === 'pending');
    } else if (filterType === 'Past Leases') {
      filtered = allLeases.filter(l => (l.status || '').toLowerCase() === 'ended');
    } else if (filterType === 'Show All') {
      filtered = allLeases.filter(l => (l.status || '').toLowerCase() !== 'deleted');
    }

    // Group leases by property
    const leasesByProperty = {};

    // Filter and paginate leases
    const { items: paginatedLeases, pagination } = filterAndPaginate(
      filtered,
      searchQuery,
      currentPage,
      itemsPerPage,
      (lease, query) => {
        // Search predicate function
        const searchableFields = [
          lease.propertyName,           // Property name
          lease.roomName,              // Room name
          lease.status,                // Lease status
          lease.tenantName,            // Tenant name
          lease.owingBalance?.toString(), // Owing balance
          lease.lastPaymentDate        // Last payment date
        ];

        // Convert query to lowercase for case-insensitive search
        query = query.toLowerCase();

        // Check if any of the fields contain the search query
        return searchableFields.some(field => 
          field?.toString().toLowerCase().includes(query)
        );
      },
      { field: sortField, ascending: sortAscending }
    );

    // Process each lease in the current page
    for (const lease of paginatedLeases) {
      // Initialize property group if not exists
      if (!leasesByProperty[lease.propertyName]) {
        leasesByProperty[lease.propertyName] = {
          propertyName: lease.propertyName,
          totalOwingBalance: 0,
          leases: []
        };
      }

      // Add lease to property group
      leasesByProperty[lease.propertyName].leases.push(lease);

      // Update total owing balance for the property
      leasesByProperty[lease.propertyName].totalOwingBalance += lease.owingBalance || 0;
    }

    // Convert to array for template
    const propertyGroups = Object.values(leasesByProperty);

    // Render desktop template
    const renderedHtml = Mustache.render(leaseListTemplate, { propertyGroups });
    $('#lease-list-tbody').html(renderedHtml);

    // Render mobile template
    const renderedMobileHtml = Mustache.render(leaseListMobileTemplate, { propertyGroups });
    $('#lease-list-mobile').html(renderedMobileHtml);

    // Render desktop pagination
    const renderedPagination = Mustache.render(paginationTemplate, { pagination });
    $('#lease-pagination').html(renderedPagination);

    // Render mobile pagination
    const renderedMobilePagination = Mustache.render(paginationMobileTemplate, { pagination });
    $('#lease-pagination-mobile').html(renderedMobilePagination);

  } catch (error) {
    console.error('Failed to load leases:', error);
    const errorHtml = Mustache.render(errorMessagesTemplate, { leaseListError: true });
    $('#lease-list-tbody').html(errorHtml);

    // Also show error in mobile view
    const mobileErrorHtml = `
      <div class="mt-4 rounded-md overflow-hidden" style="box-shadow: 0 0 0 1px rgb(203 213 225)">
        <div class="flex flex-wrap p-4 bg-white">
          <span class="font-medium w-full mb-2 text-center text-red-500">Failed to load leases</span>
        </div>
      </div>
    `;
    $('#lease-list-mobile').html(mobileErrorHtml);
  }
}

// Helper function to get room name using lease details
async function getRoomName(leaseId) {
  try {
    const leaseDetail = await leaseApi.getLeaseById(leaseId);
    const room = await propertyApi.getRoomById(leaseDetail.propertyId, leaseDetail.roomId);
    return room ? room.name : 'Unknown Room';
  } catch (error) {
    console.error('Failed to get room name:', error);
    return 'Unknown Room';
  }
}

// Helper function to get last payment date from lease details
async function getLastPaymentDate(leaseId) {
  try {
    const leaseDetail = await leaseApi.getLeaseById(leaseId);
    return leaseDetail.lastPaymentDate ? new Date(leaseDetail.lastPaymentDate).toLocaleDateString() : 'No payments';
  } catch (error) {
    console.error('Failed to get last payment date:', error);
    return 'Unknown';
  }
}

// Helper function to get tenant name using lease details
async function getPrimaryTenantName(leaseId) {
  try {
    const leaseDetail = await leaseApi.getLeaseById(leaseId);
    if (!leaseDetail) {
      console.error('Lease not found:', leaseId);
      return 'Unknown Tenant';
    }

    if (!leaseDetail.tenantId) {
      return 'No Tenant';
    }

    const tenants = await leaseApi.getTenants(leaseId);
    if (!tenants || !tenants.items || tenants.items.length === 0) {
      console.error('No tenants found for lease:', leaseId);
      return 'Unknown Tenant';
    }

    // 找到主要租户（通过tenantId匹配）
    const primaryTenant = tenants.items.find(tenant => tenant.id === leaseDetail.tenantId);
    if (!primaryTenant) {
      console.error('Primary tenant not found for lease:', leaseId);
      return 'Unknown Tenant';
    }

    return `${primaryTenant.firstName} ${primaryTenant.lastName}`;
  } catch (error) {
    console.error('Failed to get tenant name:', error);
    return 'Unknown Tenant';
  }
}

// Initialize create lease form
export function initCreateLeaseForm() {
  // 使用命名空间防止重复绑定
  const namespace = '.createLeaseForm';

  // 先解绑之前的事件处理器
  $(document).off(namespace);

  // Listen for HTMX after swap event
  $(document).on('htmx:afterSwap' + namespace, function(event) {
    const propertySelect = document.getElementById('property-select');
    if (propertySelect) {
      populatePropertySelect();

      // 初始化 balance 相关的事件监听器
      initBalanceCalculation();

      // 确保Initial Balance输入框的初始状态正确
      setTimeout(() => {
        const $initialBalance = $('#initial-balance');
        const $manualBalance = $('#manual-balance');
        if ($initialBalance.length && $manualBalance.length) {
          // 默认为自动计算模式
          $initialBalance.prop('readonly', true)
                        .prop('disabled', false)
                        .removeClass('bg-white')
                        .addClass('bg-gray-100')
                        .css('background-color', '#f3f4f6')
                        .css('color', '#6b7280')
                        .css('cursor', 'not-allowed');
          $manualBalance.prop('checked', false);

          // 如果有日期和租金数据，触发计算
          const startDate = $('#start-date').val();
          const rentAmount = $('#rent-amount').val();
          if (startDate && rentAmount) {
            updateBalance();
          }
        }
      }, 100);
    }
  });

  // Use event delegation for dynamically loaded modal
  $(document).on('click' + namespace, '#modal #create-lease-btn', async function(event) {
    event.preventDefault();
    event.stopPropagation();

    // First check user lease limit
    try {
      const limitResponse = await $.ajax({
        url: '/v1/leases/check-limit',
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      })

      // If limit check fails, don't proceed with form validation
      if (!limitResponse.canCreate) {
        return;
      }
    } catch (error) {
      console.error('Failed to check lease limit:', error)

      // Check if it's a limit error
      if (error.responseJSON && error.responseJSON.error === 'limit_reached') {
        // Show limit reached modal
        const modalHtml = Mustache.render(limitReachedModalTemplate, {
          isProperty: false,
          isLease: true,
          limit: error.responseJSON.limit
        })
        $('#modal').html(modalHtml)
        // Disable background scrolling
        document.body.style.overflow = 'hidden'
        document.documentElement.style.overflow = 'hidden'
        return;
      } else {
        // Show generic error and don't proceed
        alert('Failed to check lease limit. Please try again.')
        return;
      }
    }

    // 必填项校验
    let valid = true;
    const requiredFields = [
      { id: 'property-select', label: 'Property Name' },
      { id: 'room-select', label: 'Unit' },
      { id: 'start-date', label: 'Move-In Date' },
      { id: 'rent-amount', label: 'Monthly Rent' }
    ];
    requiredFields.forEach(f => {
      const $el = $('#' + f.id);
      const $err = $('#' + f.id + '-error');
      if (!$el.val() || $el.val().trim() === '' || ($el.is('select') && $el.val() === '')) {
        $el.addClass('border-red-500');
        $err.removeClass('hidden').text('Required');
        valid = false;
      } else {
        $el.removeClass('border-red-500');
        $err.addClass('hidden');
      }
    });
    if (!valid) return;

    // 从move-in date获取rentDueDay
    const startDateValue = $('#start-date').val();
    let rentDueDay = 1; // 默认值

    if (startDateValue) {
      // 避免时区问题：直接从日期字符串解析日期部分
      // 格式: "2025-05-01" -> 获取 "01" -> 转换为数字 1
      const dateParts = startDateValue.split('-');
      if (dateParts.length === 3) {
        const day = parseInt(dateParts[2], 10);
        if (!isNaN(day) && day >= 1 && day <= 31) {
          rentDueDay = day;
        }
      }
    }
    
    const leaseData = {
      propertyId: $('#property-select option:selected').data('id'),
      roomId: $('#room-select option:selected').data('id'),
      startDate: $('#start-date').val(),
      rentDueDay: rentDueDay, // 使用计算的rentDueDay
      rentAmount: parseFloat($('#rent-amount').val()) || 0,
      additionalMonthlyFees: parseFloat($('#additional-fees').val()) || 0,
      keyDeposit: parseFloat($('#key-deposit').val()) || 0,
      rentDeposit: parseFloat($('#rent-deposit').val()) || 0,
      otherDeposits: parseFloat($('#other-deposits').val()) || 0,
      owingBalance: parseFloat($('#initial-balance').val()) || 0,
      status: 'active'
    };

    // 如果有invitation id，传入invId
    if (window.currentInvitationId) {
      leaseData.invId = window.currentInvitationId;
    }

    const $button = $(this);
    const originalText = $button.text();

    try {
      // Disable button and show loading state
      $button.prop('disabled', true).text('Creating...');
      
      const newLease = await leaseApi.createLease(leaseData);

      // 如果有文件需要上传，先上传文件
      if (leaseAgreementFiles.length > 0 && newLease && newLease.id) {
        try {
          // 逐个上传文件
          for (const file of leaseAgreementFiles) {
            const formData = new FormData();
            formData.append('file', file);

            const uploadResponse = await fetch(`/v1/leases/${newLease.id}/document`, {
              method: 'POST',
              body: formData
            });

            if (!uploadResponse.ok) {
              console.error(`Failed to upload lease agreement file: ${file.name}`);
              // 不阻止租约创建，只是记录错误
            }
          }
        } catch (uploadError) {
          console.error('Error uploading lease agreement files:', uploadError);
          // 不阻止租约创建，只是记录错误
        }
      }

      // 清理文件变量
      leaseAgreementFiles = [];

      // Close modal
      closeModal();
      // 跳转到 lease detail 页面，带上 code 参数
      const urlParams = new URLSearchParams(window.location.search);
      const code = urlParams.get('code');
      if (newLease && newLease.id) {
        let detailUrl = `/pages/lease_detail/?id=${newLease.id}`;
        if (code) {
          detailUrl += `&code=${encodeURIComponent(code)}`;
        }
        window.location.href = detailUrl;
      } else {
        window.location.reload();
      }
      
    } catch (error) {
      console.error('Failed to create lease:', error);

      // 检查是否是限制错误
      if (error.responseJSON && error.responseJSON.error === 'limit_reached') {
        // 显示限制弹窗
        const modalHtml = Mustache.render(limitReachedModalTemplate, {
          isProperty: false,
          isLease: true,
          limit: error.responseJSON.limit
        });
        $('#modal').html(modalHtml);
        // 禁用背景滚动
        document.body.style.overflow = 'hidden';
        document.documentElement.style.overflow = 'hidden';
      } else {
        // 显示普通错误消息
        alert(error.message || 'Failed to create lease');
      }
    } finally {
      // Restore button state
      $button.prop('disabled', false).text(originalText);
    }
  });

  // Prevent form submission
  $(document).on('submit' + namespace, '#modal form', function(event) {
    event.preventDefault();
    return false;
  });
}

// Handle property selection change
$(document).on('change', '#property-select', async function() {
  const selectedOption = $(this).find('option:selected');
  const propertyId = selectedOption.data('id');
  const action = selectedOption.data('action');

  // 检查是否选择了"Create Property"选项
  if (action === 'create-property') {
    // 保存当前状态到localStorage，以便从property页面返回时恢复
    localStorage.setItem('returnToCreateLease', 'true');
    // 跳转到properties页面并自动打开create property弹窗
    window.location.href = '../properties/?action=create&return=lease';
    return;
  }

  if (!propertyId) {
    const defaultRoomOptionHtml = Mustache.render(selectOptionsTemplate, { defaultRoomOption: true });
    $('#room-select').html(defaultRoomOptionHtml).prop('disabled', true);
    return;
  }

  try {
    const property = await propertyApi.getPropertyById(propertyId);
    
    // 只过滤状态为vacant的room
    const vacantRooms = property.rooms.filter(room => room.status === 'vacant');
    
    if (vacantRooms.length === 0) {
      // No vacant units available, reset room selection
      $('#room-select').html('<option value="">No vacant units available</option>').prop('disabled', true);
      return;
    }
    
    const roomOptionsHtml = Mustache.render(selectOptionsTemplate, {
      roomOptions: true,
      rooms: vacantRooms
    });
    
    $('#room-select').html(roomOptionsHtml).prop('disabled', false);
  } catch (error) {
    console.error('Failed to load units:', error);
    const roomLoadErrorHtml = Mustache.render(selectOptionsTemplate, { roomLoadError: true });
    $('#room-select').html(roomLoadErrorHtml).prop('disabled', true);
  }
});

// Populate property select with stored data
function populatePropertySelect() {
  const $propertySelect = $('#property-select');
  
  if (!$propertySelect.length) {
    return;
  }
  
  if (!propertiesData) {
    const propertyLoadErrorHtml = Mustache.render(selectOptionsTemplate, { propertyLoadError: true });
    $propertySelect.html(propertyLoadErrorHtml).prop('disabled', true);
    return;
  }
  
  const propertyOptionsHtml = Mustache.render(selectOptionsTemplate, {
    propertyOptions: true,
    properties: propertiesData.filter(property => property.status === 'active').map(property => ({
      id: property.id,
      name: property.name
    }))
  });
  
  $propertySelect.html(propertyOptionsHtml).prop('disabled', false);
    
}

// 第一步弹窗Continue
$(document).off('click', '#add-lease-continue-btn').on('click', '#add-lease-continue-btn', function(e) {
  e.preventDefault();
  // 校验第一个弹窗必填项
  const name = $('#landlord-name').val().trim();
  const email = $('#landlord-email').val().trim();
  const phone = $('#landlord-phone').val().trim();
  let valid = true;
  if (!name) { $('#landlord-name').addClass('border-red-500'); valid = false; } else { $('#landlord-name').removeClass('border-red-500'); }
  if (!email) { $('#landlord-email').addClass('border-red-500'); valid = false; } else { $('#landlord-email').removeClass('border-red-500'); }
  if (!phone) { $('#landlord-phone').addClass('border-red-500'); valid = false; } else { $('#landlord-phone').removeClass('border-red-500'); }
  if (!valid) return;
  // 保存数据
  window.leaseFormData.landlordName = name;
  window.leaseFormData.landlordEmail = email;
  window.leaseFormData.landlordPhone = phone;
  // 校验通过，加载第二步弹窗
  $('#modal').load('./modal-lease-address.html');
});

// 第二步弹窗Continue
$(document).off('click', '#lease-address-continue-btn').on('click', '#lease-address-continue-btn', function(e) {
  e.preventDefault();
  let valid = true;
  const required = [
    { sel: '#address' },
    { sel: '#city' },
    { sel: '#country' },
    { sel: '#province' },
    { sel: '#postal-code' }
  ];
  required.forEach(f => {
    const $el = $(f.sel);
    if (!$el.val() || $el.val() === '') {
      $el.addClass('border-red-500');
      valid = false;
    } else {
      $el.removeClass('border-red-500');
    }
  });
  if (!valid) return;
  // 保存数据
  window.leaseFormData.street = $('#street-address').val();
  window.leaseFormData.address = $('#address').val();
  window.leaseFormData.unit = $('#unit').val();
  window.leaseFormData.city = $('#city').val();
  window.leaseFormData.country = $('#country').val();
  window.leaseFormData.province = $('#province').val();
  window.leaseFormData.postal = $('#postal-code').val();
  // 校验通过，加载第三步弹窗
  $('#modal').load('./modal-lease-info.html');
});

// 第三步弹窗Continue
$(document).off('click', '#lease-info-continue-btn').on('click', '#lease-info-continue-btn', function(e) {
  e.preventDefault();
  const type = $('#lease-type').val();
  const checked = $('#is-current-tenant').is(':checked');
  const from = $('#lease-from').val();
  let to = '';
  let needDateCheck = true;
  let valid = true;
  const rent = $('#monthly-rent').val();
  if (type === 'current' && checked) {
    to = new Date().toISOString().slice(0, 10); // today
    needDateCheck = false;
  } else {
    to = $('#lease-to').val();
  }
  // 必填校验
  if (!from) {
    $('#lease-from').addClass('border-red-500');
    valid = false;
  } else {
    $('#lease-from').removeClass('border-red-500');
  }
  if (!(type === 'current' && checked) && !to) {
    $('#lease-to').addClass('border-red-500');
    valid = false;
  } else {
    $('#lease-to').removeClass('border-red-500');
  }
  if (!rent) {
    $('#monthly-rent').addClass('border-red-500');
    valid = false;
  } else {
    $('#monthly-rent').removeClass('border-red-500');
  }
  // 只要不是current+checked，始终校验日期先后
  if (needDateCheck && from && to && new Date(from) >= new Date(to)) {
    $('#lease-date-error').show();
    $('#lease-from').addClass('border-red-500');
    $('#lease-to').addClass('border-red-500');
    valid = false;
  } else {
    $('#lease-date-error').hide();
    if (from) $('#lease-from').removeClass('border-red-500');
    if (to) $('#lease-to').removeClass('border-red-500');
  }
  if (!valid) return;
  // 保存数据
  window.leaseFormData.leaseType = type;
  window.leaseFormData.isCurrentTenant = checked;
  window.leaseFormData.leaseFrom = from;
  window.leaseFormData.leaseTo = (type === 'current' && checked) ? 'Present' : to;
  window.leaseFormData.monthlyRent = rent;
  // 校验通过，加载确认弹窗并填充数据
  $('#modal').load('./modal-lease-confirm.html', function() {
    const d = window.leaseFormData;
    $('#confirm-landlord-name').text(d.landlordName || '');
    $('#confirm-landlord-email').text(d.landlordEmail || '');
    $('#confirm-landlord-phone').text(d.landlordPhone || '');
    // 地址拼接
    let fullAddress = '';
    if (d.street) fullAddress += d.street + ', ';
    if (d.city) fullAddress += d.city + ', ';
    if (d.province) fullAddress += d.province + ', ';
    if (d.country) fullAddress += d.country + ', ';
    if (d.postal) fullAddress += d.postal;
    $('#confirm-lease-address').text(fullAddress);
    $('#confirm-lease-rent').text(d.monthlyRent ? ('$' + d.monthlyRent) : '');
    // Duration: From xxx to xxx
    let duration = '';
    if (d.leaseFrom && d.leaseTo) {
      duration = 'From ' + d.leaseFrom + ' to ' + d.leaseTo;
    }
    $('#confirm-lease-duration').text(duration);
  });
});

// 监听租约类型切换，former时隐藏checkbox和Present输入框
$(document).on('change', '#lease-type', function() {
  const type = $(this).val();
  if (type === 'current') {
    $('#current-tenant-checkbox-row').show();
    if ($('#is-current-tenant').is(':checked')) {
      $('#lease-to').hide();
      $('#lease-to-present').show();
    } else {
      $('#lease-to').show();
      $('#lease-to-present').hide();
    }
  } else {
    $('#current-tenant-checkbox-row').hide();
    $('#lease-to').show();
    $('#lease-to-present').hide();
    $('#is-current-tenant').prop('checked', false);
  }
});

// 弹窗加载时初始化一次
$(document).on('htmx:afterSwap', function() {
  const type = $('#lease-type').val();
  if (type === 'current') {
    $('#current-tenant-checkbox-row').show();
    if ($('#is-current-tenant').is(':checked')) {
      $('#lease-to').hide();
      $('#lease-to-present').show();
    } else {
      $('#lease-to').show();
      $('#lease-to-present').hide();
    }
  } else {
    $('#current-tenant-checkbox-row').hide();
    $('#lease-to').show();
    $('#lease-to-present').hide();
    $('#is-current-tenant').prop('checked', false);
  }
});

// 监听checkbox联动Present输入框（需保证在租约类型为current时生效）
$(document).on('change', '#is-current-tenant', function() {
  const type = $('#lease-type').val();
  if (type === 'current') {
    if ($(this).is(':checked')) {
      $('#lease-to').hide();
      $('#lease-to-present').show();
      $('#lease-to').val(''); // 清空to日期
      $('#lease-date-error').hide();
    } else {
      $('#lease-to').show();
      $('#lease-to-present').hide();
    }
  }
});




// 绑定Add Lease按钮
$(document).off('click', '#add-lease-final-btn').on('click', '#add-lease-final-btn', async function() {
  const d = window.leaseFormData;
  // 只用页面初始化时的viewType
  let viewType = window.initViewType || '';
  let type = '';
  if (viewType.toLowerCase() === 'tenant') {
    type = 'tenant_to_landlord';
  } else if (viewType.toLowerCase() === 'landlord') {
    type = 'landlord_to_tenant';
  }
  const invitationPayload = {
    receiverName: d.landlordName,
    receiverEmail: d.landlordEmail,
    receiverPhone: d.landlordPhone,
    propertyName: d.address || d.street || '',
    propertyAddress: `${d.street || ''} ${d.unit || ''} ${d.city || ''} ${d.province || ''} ${d.country || ''} ${d.postal || ''}`.replace(/\s+/g, ' ').trim(),
    fromDate: d.leaseFrom,
    toDate: d.leaseTo,
    rentAmount: parseFloat(d.monthlyRent) || 0,
    type: type
  };
  try {
    await leaseApi.createInvitation(invitationPayload);
    alert('Invitation sent to landlord successfully!');
    closeModal();
  } catch (e) {
    alert('Failed to send invitation: ' + (e.message || e));
  }
});

// 初始化租约文件上传功能
function initLeaseFileUpload() {
  // 使用命名空间防止重复绑定
  const namespace = '.leaseFileUpload';

  // 先解绑之前的事件处理器
  $(document).off(namespace);

  // 文件选择处理
  $(document).on('change' + namespace, '#lease-file-input', function() {
    const files = Array.from(this.files);
    if (files.length > 0) {
      handleMultipleFileSelection(files);
      // 清空input以允许重复选择相同文件
      this.value = '';
    }
  });

  // 拖拽上传处理
  $(document).on('dragenter' + namespace + ' dragover' + namespace, '#lease-file-upload-area', function(e) {
    e.preventDefault();
    e.stopPropagation();
    $(this).addClass('border-blue-500 bg-blue-50');
  });

  $(document).on('dragleave' + namespace + ' drop' + namespace, '#lease-file-upload-area', function(e) {
    e.preventDefault();
    e.stopPropagation();
    $(this).removeClass('border-blue-500 bg-blue-50');
  });

  $(document).on('drop' + namespace, '#lease-file-upload-area', function(e) {
    const files = Array.from(e.originalEvent.dataTransfer.files);
    if (files.length > 0) {
      handleMultipleFileSelection(files);
    }
  });

  // 点击上传区域触发文件选择 - 添加防护机制
  $(document).on('click' + namespace, '#lease-file-upload-area', function(e) {
    // 检查点击的目标是否是label或input，如果是则让默认行为处理
    const target = $(e.target);
    if (target.is('label') || target.closest('label').length > 0 || target.is('input')) {
      return; // 让label的默认行为处理文件选择
    }

    // 只有点击其他区域时才手动触发文件选择
    e.preventDefault();
    e.stopPropagation();

    // 确保只触发一次文件选择
    const fileInput = $('#lease-file-input');
    if (fileInput.length > 0 && !fileInput.prop('disabled')) {
      // 临时禁用input防止重复点击
      fileInput.prop('disabled', true);

      // 触发文件选择
      fileInput[0].click();

      // 短暂延迟后重新启用
      setTimeout(() => {
        fileInput.prop('disabled', false);
      }, 100);
    }
  });

  // 移除单个文件处理
  $(document).on('click' + namespace, '.lease-file-remove', function() {
    const fileName = $(this).data('filename');
    removeLeaseFile(fileName);
  });
}

// 处理多文件选择
function handleMultipleFileSelection(files) {
  for (const file of files) {
    // 检查文件数量限制
    if (leaseAgreementFiles.length >= MAX_LEASE_FILES) {
      alert(`Maximum ${MAX_LEASE_FILES} files allowed`);
      break;
    }

    // 验证文件
    if (!validateLeaseFile(file)) {
      continue;
    }

    // 检查重复文件名
    const existingFile = leaseAgreementFiles.find(f => f.name === file.name);
    if (existingFile) {
      // 存储待处理的重复文件信息
      pendingDuplicateFile = file;
      duplicateFileInfo = { fileName: file.name };
      showDuplicateFileModal();
      continue;
    }

    // 添加文件到列表
    leaseAgreementFiles.push(file);
  }

  // 更新UI显示
  updateLeaseFilesList();
}

// 验证租约文件
function validateLeaseFile(file) {
  const allowedTypes = ['application/pdf', 'image/png', 'image/jpeg', 'image/jpg', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'];
  const maxSizePDF = 12 * 1024 * 1024; // 12MB for PDF and DOC
  const maxSizeImage = 4 * 1024 * 1024; // 4MB for images

  if (!allowedTypes.includes(file.type)) {
    alert(`File "${file.name}" has an unsupported format. Please upload PDF, PNG, JPG, or DOC files.`);
    return false;
  }

  const maxSize = file.type.startsWith('image/') ? maxSizeImage : maxSizePDF;
  if (file.size > maxSize) {
    const maxSizeMB = maxSize / (1024 * 1024);
    alert(`File "${file.name}" is too large. Maximum size is ${maxSizeMB}MB.`);
    return false;
  }

  return true;
}

// 更新文件列表显示
function updateLeaseFilesList() {
  const $filesList = $('#lease-uploaded-files');
  const $fileCount = $('#lease-file-count');

  // 更新文件计数
  $fileCount.text(`${leaseAgreementFiles.length} of ${MAX_LEASE_FILES} files`);

  // 清空现有列表
  $filesList.empty();

  // 添加每个文件
  leaseAgreementFiles.forEach(file => {
    const fileItem = $(`
      <div class="flex items-center justify-between p-3 bg-gray-50 rounded-md border">
        <div class="flex items-center gap-2">
          <svg class="w-5 h-5 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
          </svg>
          <span class="text-sm text-gray-700">${file.name}</span>
        </div>
        <button class="lease-file-remove text-red-500 hover:text-red-700 text-sm" data-filename="${file.name}">
          Remove
        </button>
      </div>
    `);
    $filesList.append(fileItem);
  });
}

// 移除文件
function removeLeaseFile(fileName) {
  leaseAgreementFiles = leaseAgreementFiles.filter(file => file.name !== fileName);
  updateLeaseFilesList();
}

// 显示重复文件处理模态框
function showDuplicateFileModal() {
  // 不覆盖现有的modal，而是直接添加到body中，并设置更高的z-index
  const modalHtml = duplicateFileModalTemplate.replace('z-50', 'z-[60]');
  $('body').append(`<div id="duplicate-file-modal">${modalHtml}</div>`);

  // 不需要禁用背景滚动，因为create lease modal已经禁用了
}

// 处理重复文件替换
function handleFileReplace() {
  if (!pendingDuplicateFile || !duplicateFileInfo) return;

  // 移除旧文件，添加新文件
  leaseAgreementFiles = leaseAgreementFiles.filter(f => f.name !== duplicateFileInfo.fileName);
  leaseAgreementFiles.push(pendingDuplicateFile);
  updateLeaseFilesList();

  // 只关闭重复文件弹窗，不影响create lease弹窗
  $('#duplicate-file-modal').remove();

  // 清理临时变量
  pendingDuplicateFile = null;
  duplicateFileInfo = null;
}

// 初始化重复文件模态框事件处理器
function initDuplicateFileModalHandlers() {
  // 处理取消按钮点击
  $(document).on('click', '#cancel-duplicate-file-btn, #duplicate-file-close, #duplicate-file-overlay', function() {
    // 清理临时变量
    pendingDuplicateFile = null;
    duplicateFileInfo = null;

    // 只关闭重复文件弹窗，不影响create lease弹窗
    $('#duplicate-file-modal').remove();
  });

  // 处理替换按钮点击
  $(document).on('click', '#replace-file-btn', function() {
    handleFileReplace();
  });
}

// 绑定租客视图下的Download Record按钮
$(document).on('click', '#download-record-btn', async function() {
  try {
    // 收集当前页面展示的currentLease（active lease和pending invitation）
    let currentLease = [];
    const userData = window.currentUser || JSON.parse(localStorage.getItem('user') || '{}');
    if ((userData.viewType || '').toLowerCase() === 'tenant') {
      // 获取租客所有lease
      const leaseResponse = await leaseApi.getLeases({ role: 'tenant' });
      if (leaseResponse.items && leaseResponse.items.length > 0) {
        // 拼接property address
        const leaseWithAddress = await Promise.all(leaseResponse.items.map(async lease => {
          let addressStr = lease.propertyName || '';
          if (lease.propertyId) {
            try {
              const property = await propertyApi.getPropertyById(lease.propertyId);
              if (property && (property.addr || property.address)) {
                const addr = property.addr || property.address;
                addressStr = `${addr.street || ''}${addr.unit ? ' ' + addr.unit : ''}, ${addr.city || ''}, ${addr.prov || ''}, ${addr.country || ''} ${addr.zip || addr.zipCode || ''}`.replace(/\s+,/g, ',').replace(/,\s*,/g, ',').replace(/\s+/g, ' ').trim();
              }
            } catch (e) {}
          }
          return {
            address: addressStr,
            leasePeriod: (lease.startDate ? lease.startDate : '') + ' - ' + (lease.endDate ? lease.endDate : 'Present'),
            landlordName: lease.landlordName || '',
            overdueBalance: lease.owingBalance || 0,
            paymentStatus: lease.status || ''
          };
        }));
        leaseWithAddress.forEach(leaseData => {
          if (leaseData.paymentStatus === 'active' || leaseData.paymentStatus === 'pending') {
            currentLease.push(leaseData);
          }
        });
      }
      // 获取pending和active invitation
      const invRes = await leaseApi.getInvitations({ type: 'tenant_to_landlord' });
      if (invRes.items && invRes.items.length > 0) {
        const pendingInvs = invRes.items.filter(inv => inv.status === 'pending');
        pendingInvs.forEach(inv => {
          currentLease.push({
            address: inv.propertyAddress || inv.propertyName || '',
            leasePeriod: (inv.fromDate ? inv.fromDate : '') + ' - ' + (inv.toDate ? inv.toDate : 'Present'),
            landlordName: inv.receiverName || inv.receiverEmail || '',
            overdueBalance: 0,
            paymentStatus: 'pending'
          });
        });
      }
    }
    if (currentLease.length === 0) {
      alert('No lease records available for export');
      return;
    }
    // 生成PDF
    const doc = new jsPDF();
    doc.setFontSize(16);
    doc.text('Lease Records', 20, 20);
    doc.setFontSize(12);
    let y = 40;
    // 遍历所有租约记录
    currentLease.forEach((lease, idx) => {
      if (y > 270) {
        doc.addPage();
        y = 20;
      }
      doc.text(`Property Address: ${lease.address}`, 20, y); y += 10;
      doc.text(`Lease Period: ${lease.leasePeriod}`, 20, y); y += 10;
      doc.text(`Landlord Name: ${lease.landlordName}`, 20, y); y += 10;
      doc.text(`Overdue Balance: $${lease.overdueBalance}`, 20, y); y += 10;
      doc.text(`Status: ${lease.paymentStatus}`, 20, y); y += 20;
    });
    doc.save('lease_records.pdf');
  } catch (err) {
    console.error('Failed to generate lease records PDF:', err);
    alert('Failed to generate lease records PDF');
  }
});

// Handle Create Lease button click (main page button)
$(document).on('click', '#lease-action-btn', async function(e) {
  e.preventDefault()

  try {
    // Check user lease limit first
    const response = await $.ajax({
      url: '/v1/leases/check-limit',
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('token')}`
      }
    })

    // If no limit reached, load the create lease modal
    if (response.canCreate) {
      // Load the create lease modal and populate property data
      const modalResponse = await $.get('./modal-create-lease.html')
      $('#modal').html(modalResponse)

      // Manually trigger the property population since we're not using HTMX
      const propertySelect = document.getElementById('property-select')
      if (propertySelect) {
        populatePropertySelect()
        // Initialize balance calculation
        initBalanceCalculation()
      }
    }
  } catch (error) {
    console.error('Failed to check lease limit:', error)

    // Check if it's a limit error
    if (error.responseJSON && error.responseJSON.error === 'limit_reached') {
      // Show limit reached modal
      const modalHtml = Mustache.render(limitReachedModalTemplate, {
        isProperty: false,
        isLease: true,
        limit: error.responseJSON.limit
      })
      $('#modal').html(modalHtml)
      // Disable background scrolling
      document.body.style.overflow = 'hidden'
      document.documentElement.style.overflow = 'hidden'
    } else {
      // Show generic error
      alert('Failed to check lease limit. Please try again.')
    }
  }
});