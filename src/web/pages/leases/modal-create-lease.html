<div class="fixed inset-0 z-50">
  <!-- Dark overlay with click handler to close -->
  <div
    class="fixed inset-0 bg-black bg-opacity-50 pointer-events-auto"
    onclick="closeModal()"
  ></div>

  <!-- Modal container -->
  <div
    class="fixed inset-0 flex items-center justify-center pointer-events-none"
  >
    <div
      class="bg-white sm:rounded-lg shadow-xl sm:w-9/12 w-full h-full sm:max-h-[90vh] sm:min-h-[70vh] overflow-y-auto pointer-events-auto relative flex flex-col"
    >
      <!-- Modal Header -->
      <div
        class="sticky top-0 px-6 py-4 border-b bg-white z-10 flex justify-between items-center"
      >
        <div>
          <h3 class="text-lg font-semibold text-gray-900">Create Lease</h3>
          <p>Add a new lease to your property</p>
        </div>
        <button
          class="p-2 hover:bg-gray-100 rounded-full"
          onclick="closeModal()"
        >
          <svg
            class="w-5 h-5 text-gray-500"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M6 18L18 6M6 6l12 12"
            />
          </svg>
        </button>
      </div>

      <!-- Modal Body -->
      <div class="px-6 py-4 flex-1">
        <form>
          <div class="flex flex-wrap">
            <div class="w-full flex flex-col lg:flex-row gap-4 mt-4">
              <div class="flex flex-wrap w-full">
                <label class="w-full mb-2" for="property-select">Property Name *<span id="property-select-error" class="text-red-500 text-xs ml-2 hidden">Required</span></label>
                <select
                  class="p-2 w-full border border-gray-300 rounded-md focus:outline-none bg-white h-[52px]"
                  id="property-select"
                  disabled
                >
                  <option value="">Loading properties...</option>
                </select>
              </div>
              <div class="flex flex-wrap w-full">
                <label class="w-full mb-2" for="room-select">Unit *<span id="room-select-error" class="text-red-500 text-xs ml-2 hidden">Required</span></label>
                <select
                  class="p-2 w-full border border-gray-300 rounded-md focus:outline-none bg-white h-[52px]"
                  id="room-select"
                  disabled
                >
                  <option value="">Select a property first</option>
                </select>
              </div>
            </div>
            <div class="w-full flex flex-col lg:flex-row gap-4 mt-4">
              <!-- Line 2-->
              <div class="flex flex-wrap w-full lg:w-1/3">
                <label class="w-full mb-2" for="start-date">Move-In Date *<span id="start-date-error" class="text-red-500 text-xs ml-2 hidden">Required</span></label>
                <div class="relative w-full">
                  <span class="absolute inset-y-0 left-0 flex items-center pl-3 text-gray-500 pointer-events-none">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                    </svg>
                  </span>
                  <input
                    class="pl-8 py-4 w-full h-[52px] border border-gray-300 rounded-md focus:outline-none bg-white [&::-webkit-calendar-picker-indicator]:hidden"
                    id="start-date"
                    placeholder="Select a date"
                    type="date"
                    onclick="this.showPicker()"
                  />
                </div>
              </div>
              <div class="flex flex-wrap w-full lg:w-1/3">
                <label class="w-full mb-2" for="rent-amount">Monthly Rent *<span id="rent-amount-error" class="text-red-500 text-xs ml-2 hidden">Required</span></label>
                <div class="relative w-full">
                  <span
                    class="absolute inset-y-0 left-0 flex items-center pl-3 text-gray-500"
                    >$</span
                  >
                  <input
                    type="text"
                    id="rent-amount"
                    placeholder="1"
                    class="pl-8 py-4 w-full border border-gray-300 rounded-md focus:outline-none bg-white min-h-[44px]"
                  />
                </div>
              </div>
              <div class="flex flex-wrap w-full lg:w-1/3">
                <label class="w-full mb-2" for="additional-fees">Additional Monthly Fees</label>
                <div class="relative w-full">
                  <span
                    class="absolute inset-y-0 left-0 flex items-center pl-3 text-gray-500"
                    >$</span
                  >
                  <input
                    type="text"
                    id="additional-fees"
                    placeholder="0"
                    class="pl-8 py-4 w-full border border-gray-300 rounded-md focus:outline-none bg-white min-h-[44px]"
                  />
                </div>
              </div>
            </div>
            <div class="w-full flex flex-col lg:flex-row gap-4 mt-4">
              <!-- Line 3-->
              <div class="flex flex-wrap w-full">
                <label class="w-full mb-2" for="key-deposit">Key Deposit</label>
                <div class="relative w-full">
                  <span
                    class="absolute inset-y-0 left-0 flex items-center pl-3 text-gray-500"
                    >$</span
                  >
                  <input
                    type="text"
                    id="key-deposit"
                    placeholder="0"
                    class="pl-8 py-4 w-full border border-gray-300 rounded-md focus:outline-none bg-white min-h-[44px]"
                  />
                </div>
              </div>
              <div class="flex flex-wrap w-full">
                <label class="w-full mb-2" for="rent-deposit">Rent Deposit</label>
                <div class="relative w-full">
                  <span
                    class="absolute inset-y-0 left-0 flex items-center pl-3 text-gray-500"
                    >$</span
                  >
                  <input
                    type="text"
                    id="rent-deposit"
                    placeholder="0"
                    class="pl-8 py-4 w-full border border-gray-300 rounded-md focus:outline-none bg-white min-h-[44px]"
                  />
                </div>
              </div>
              <div class="flex flex-wrap w-full">
                <label class="w-full mb-2" for="other-deposits">Other Deposits</label>
                <div class="relative w-full">
                  <span
                    class="absolute inset-y-0 left-0 flex items-center pl-3 text-gray-500"
                    >$</span
                  >
                  <input
                    type="text"
                    id="other-deposits"
                    placeholder="0"
                    class="pl-8 py-4 w-full border border-gray-300 rounded-md focus:outline-none bg-white min-h-[44px]"
                  />
                </div>
              </div>
            </div>
            <div class="w-full flex flex-col lg:flex-row gap-4 mt-4">
              <!-- Line 4: Initial Balance -->
              <div class="flex flex-wrap w-full">
                <label class="w-full mb-2" for="initial-balance">Initial Balance</label>
                <div class="relative w-full">
                  <span
                    class="absolute inset-y-0 left-0 flex items-center pl-3 text-gray-500"
                    >$</span
                  >
                  <input
                    type="text"
                    id="initial-balance"
                    placeholder="0"
                    class="pl-8 py-4 w-full border border-gray-300 rounded-md focus:outline-none bg-white min-h-[44px] disabled:bg-gray-100 disabled:text-gray-500 disabled:cursor-not-allowed"
                    readonly
                  />
                </div>
                <div class="w-full mt-2">
                  <label class="flex items-center">
                    <input type="checkbox" id="manual-balance" class="mr-2">
                    <span class="text-sm text-gray-600">Manually set initial balance</span>
                  </label>
                </div>
              </div>
              <div class="flex flex-wrap w-full lg:hidden">
                <!-- Empty space to maintain layout on desktop, hidden on mobile -->
              </div>
              <div class="flex flex-wrap w-full lg:hidden">
                <!-- Empty space to maintain layout on desktop, hidden on mobile -->
              </div>
            </div>

            <!-- Lease Agreement Upload Section -->
            <div class="w-full mt-6">
              <div class="flex justify-between items-center mb-4">
                <h3 class="text-lg font-semibold text-gray-900">Lease Agreement</h3>
                <span id="lease-file-count" class="text-sm text-gray-600">0 of 10 files</span>
              </div>

              <!-- File Upload Area -->
              <div id="lease-file-upload-area" class="py-6 flex flex-col items-center justify-center w-full border-2 border-dashed rounded-md border-gray-300 gap-2 hover:border-gray-400 transition-colors cursor-pointer">
                <svg class="w-6 h-6 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
                </svg>
                <div class="text-center">
                  <span class="text-gray-600">Drag and drop or </span>
                  <label class="text-blue-600 hover:text-blue-800 cursor-pointer underline">
                    browse
                    <input id="lease-file-input" class="hidden" type="file" multiple accept=".pdf,.png,.jpg,.jpeg,.doc,.docx" />
                  </label>
                  <span class="text-gray-600"> to upload</span>
                </div>
                <span class="text-gray-500 text-sm">Supported formats: pdf, png, jpg, jpeg, doc (Optional)</span>
              </div>

              <!-- Uploaded Files List -->
              <div id="lease-uploaded-files" class="space-y-2 max-h-40 overflow-y-auto mt-3">
                <!-- Files will be dynamically added here -->
              </div>
            </div>
          </div>
        </form>
      </div>

      <!-- Modal Footer -->
      <div
        class="sticky bottom-0 left-0 right-0 px-6 py-4 bg-gray-50 sm:rounded-b-lg flex justify-between gap-2 border-t mt-auto"
      >
        <button
          class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50"
          onclick="closeModal()"
        >
          Cancel
        </button>
        <button
          id="create-lease-btn"
          class="px-4 py-2 text-sm font-medium text-white bg-slate-800 rounded-md hover:bg-slate-900"
        >
          Create Lease
        </button>
      </div>
    </div>
  </div>
</div>
