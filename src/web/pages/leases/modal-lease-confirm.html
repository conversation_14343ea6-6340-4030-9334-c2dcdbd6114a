<div class="fixed inset-0 z-50">
  <div class="fixed inset-0 bg-black bg-opacity-50 pointer-events-auto" onclick="document.getElementById('modal').innerHTML = ''"></div>
  <div class="fixed inset-0 flex items-center justify-center pointer-events-none">
    <div class="bg-white sm:rounded-lg shadow-xl sm:w-8/12 w-full h-full sm:max-h-[90vh] sm:min-h-[60vh] overflow-y-auto pointer-events-auto relative flex flex-col">
      <div class="sticky top-0 px-6 py-4 border-b bg-white z-10 flex justify-between items-center">
        <div>
          <h3 class="text-xl font-semibold text-gray-900">Add Lease</h3>
          <p class="text-sm text-gray-600">Add information about your current lease</p>
        </div>
        <button class="p-2 hover:bg-gray-100 rounded-full" onclick="document.getElementById('modal').innerHTML = ''">
          <svg class="w-5 h-5 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
      </div>
      <div class="px-6 py-8 flex-1">
        <p class="mb-6">A confirmation message will be sent to your landlord's email.</p>
        <h2 class="text-2xl font-semibold mb-4">Landlord Information</h2>
        <div class="mb-6">
          <div class="flex mb-2"><span class="font-semibold w-40">Name:</span><span id="confirm-landlord-name"></span></div>
          <div class="flex mb-2"><span class="font-semibold w-40">Email:</span><span id="confirm-landlord-email"></span></div>
          <div class="flex mb-2"><span class="font-semibold w-40">Phone Number:</span><span id="confirm-landlord-phone"></span></div>
        </div>
        <h2 class="text-2xl font-semibold mb-4">Lease Information</h2>
        <div class="mb-6">
          <div class="flex mb-2"><span class="font-semibold w-40">Address:</span><span id="confirm-lease-address"></span></div>
          <div class="flex mb-2"><span class="font-semibold w-40">Monthly Rent:</span><span id="confirm-lease-rent"></span></div>
          <div class="flex mb-2"><span class="font-semibold w-40">Duration:</span><span id="confirm-lease-duration"></span></div>
        </div>
      </div>
      <div class="absolute bottom-0 left-0 right-0 px-6 py-4 bg-gray-50 sm:rounded-b-lg flex justify-between gap-2 border-t">
        <button class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50" onclick="document.getElementById('modal').innerHTML = ''">Cancel</button>
        <button id="add-lease-final-btn" class="px-4 py-2 text-sm font-medium text-white bg-black rounded-md hover:bg-gray-800">Add Lease</button>
      </div>
    </div>
  </div>
</div> 