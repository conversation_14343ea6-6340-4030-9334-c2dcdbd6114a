{{> landing-header}}
  <body>
    <!-- NAV BAR-->
    {{> landing-navbar}}

    <!-- Main content-->
    <main class="flex flex-col items-center min-h-screen w-full bg-white px-8">
      <div class="flex flex-col items-center mt-16 gap-8 md:w-[552px]">
        <h2 class="font-bold text-2xl">Welcome back</h2>
        <p>
          Don't have an account?
          <a
            href="{{baseUrl}}/pages/signup/"
            class="text-rmred hover:text-rmred-dark font-medium underline"
            >Create an account</a
          >
        </p>
        <!-- Social Login Buttons -->
        <div class="flex flex-col gap-4 w-full">
          <!-- <div id="googleLoginBtn" class="g-signin2 bg-gray-200" data-onsuccess="onSignIn">(placeholder) Google signin</div> -->
          <button
            id="realMasterLoginBtn"
            class="w-full px-6 py-3 bg-rmred hover:bg-rmred-dark text-white rounded-md font-medium cursor-pointer transition-colors duration-100"
          >
            Sign in with RealMaster
          </button>
          <!-- OAuth error message container -->
          <div id="oauthError" class="text-red-500 text-sm text-center hidden"></div>
        </div>
        <hr class="w-full border-gray-300" />
        <div class="flex flex-col gap-8 w-full">
          <div class="flex flex-col gap-4 w-full">
            <label for="email" class="font-medium">Email</label>
            <input
              type="email"
              id="email"
              name="email"
              class="w-full px-4 py-3 border border-gray-300 rounded-md focus:outline-none focus:border-rmred"
              placeholder="Enter your email"
            />
            <!-- Email error message container -->
            <div id="emailError" class="text-red-500 text-sm hidden"></div>
          </div>
          <div class="flex flex-col gap-4 w-full">
            <label for="password" class="font-medium">Password</label>
            <input
              type="password"
              id="password"
              name="password"
              class="w-full px-4 py-3 border border-gray-300 rounded-md focus:outline-none focus:border-rmred"
              placeholder="Enter your password"
            />
            <!-- Password error message container -->
            <div id="passwordError" class="text-red-500 text-sm hidden"></div>
          </div>
          <div class="flex items-center gap-2">
            <input
              type="checkbox"
              id="remember"
              name="remember"
              class="w-4 h-4 accent-rmred cursor-pointer"
            />
            <label for="remember" class="text-gray-700 cursor-pointer">
              Remember me
            </label>
          </div>
          <button
            id="loginBtn"
            class="w-full px-6 py-3 bg-rmred hover:bg-rmred-dark text-white rounded-md font-medium cursor-pointer transition-colors duration-100"
          >
            Sign In
          </button>
          <a
            href="{{baseUrl}}/pages/reset-password/"
            class="text-center text-rmred hover:text-rmred-dark font-medium underline"
          >
            Forgot your password?
          </a>
          <!-- Error message container -->
          <div id="errorMessage" class="text-rmred text-center hidden"></div>
        </div>
      </div>
  </main>
{{> landing-footer}}
<script type="module" src="./main.js"></script>