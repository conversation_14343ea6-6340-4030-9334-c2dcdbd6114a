// Import dependencies
import $ from 'jquery'
import { authApi } from '../../services'
import { API_BASE_URL, ENDPOINTS, STATUS } from '../../services/config'

// Store state
let isLoading = false;

// Page initialization
$(document).ready(function() {
  // 自动填充邮箱（如有email参数）
  const urlParams = new URLSearchParams(window.location.search);
  const invitationEmail = urlParams.get('email');
  const fromInvitation = urlParams.get('fromInvitation');
  if (invitationEmail) {
    $('#email').val(invitationEmail);
  }
  // Initialize form handlers
  handleRegularLogin(fromInvitation);
  handleOAuthLogin();

  // Add Enter key support for login form
  $('#email, #password').on('keypress', function(event) {
    if (event.which === 13) { // Enter key
      event.preventDefault();
      $('#loginBtn').click();
    }
  });
});

// Initialize login form
function handleRegularLogin(fromInvitation) {
  const urlParams = new URLSearchParams(window.location.search);
  $('#loginBtn').on('click', async function(event) {
    event.preventDefault();
    
    if (isLoading) return;
    
    const emailInput = $('#email');
    const passwordInput = $('#password');
    const rememberInput = $('#remember');
    
    // Basic validation
    if (!validateLoginForm(emailInput.val(), passwordInput.val())) {
      return;
    }

    const $button = $(this);
    const originalText = $button.text();

    try {
      // Show loading state
      isLoading = true;
      $button.prop('disabled', true)
      clearAllErrors();
      
      const response = await authApi.login({
        email: emailInput.val(),
        password: passwordInput.val(),
        remember: rememberInput.prop('checked')
      });
      
      // Check if login was successful
      if (response.ok === STATUS.SUCCESS) {
        // 如果是邀请流程，根据invitation type决定行为
        if (fromInvitation === '1') {
          const code = urlParams.get('code') || '';
          if (code) {
            try {
              // 获取invitation信息
              const invRes = await fetch(`/v1/invitations/verify?code=${encodeURIComponent(code)}`);
              if (invRes.ok) {
                const inv = await invRes.json();
                // 根据type决定行为
                if (inv.type === 'landlord_to_tenant') {
                  // 更新invitation状态
                  await fetch(`/v1/invitations/${encodeURIComponent(code)}/status`, {
                    method: 'PUT',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ status: 'active', leaseId: inv.leaseId })
                  });
                  // 切换viewType为tenant
                  await import('../../services').then(({ userApi }) => userApi.updateViewType('tenant'));
                  // 跳转到tenant view
                  window.location.href = window.config.baseUrl + '/pages/leases/';
                } else if (inv.type === 'tenant_to_landlord') {
                  // 切换viewType为landlord
                  await import('../../services').then(({ userApi }) => userApi.updateViewType('landlord'));
                  // 跳转到property创建页
                  window.location.href = window.config.baseUrl + '/pages/properties/?autoOpen=1&fromInvitation=1&code=' + encodeURIComponent(code);
                }
              }
            } catch (e) {
              window.location.href = window.config.baseUrl + '/pages/home/';
            }
          } else {
            window.location.href = window.config.baseUrl + '/pages/home/';
          }
        } else {
          if (response.user && response.user.role === 'admin') {
            window.location.href = window.config.baseUrl + '/pages/admin/';
          } else {
            window.location.href = window.config.baseUrl + '/pages/home/';
          }
        }
      } else {
        throw new Error('Login failed');
      }
      
    } catch (error) {

      // 检查是否是未验证账户的错误
      if (error.message && error.message.includes('Email not verified')) {
        // 跳转到激活页面
        const email = emailInput.val();
        window.location.href = window.config.baseUrl + '/pages/verification/?email=' + encodeURIComponent(email);
        return;
      }

      // Handle specific error types and show them in appropriate locations
      handleLoginError(error.message || 'Login failed. Please try again.');
    } finally {
      // Reset loading state
      isLoading = false;
      $button.prop('disabled', false).text(originalText);
    }
  });
}

// Initialize OAuth login
function handleOAuthLogin() {
  if (isLoading) return;

  // OAuth
  $('#realMasterLoginBtn').on('click', function(event) {
    event.preventDefault();

    try {
      isLoading = true;
      $(this).prop('disabled', true);
      clearAllErrors();

      // Direct redirect to OAuth endpoint
      window.location.href = `${API_BASE_URL}${ENDPOINTS.AUTH.OAUTH_LOGIN}`;

    } catch (error) {
      // Show error message in OAuth error container
      $('#oauthError').text(error.message || 'OAuth login failed. Please try again.').removeClass('hidden');

      // Reset loading state only if redirect fails
      isLoading = false;
      $(this).prop('disabled', false);
    }
  });
}

// Form validation
function validateLoginForm(email, password) {
  // Clear all previous errors
  clearAllErrors();

  let hasErrors = false;

  if (!email || !email.trim()) {
    showFieldError('email', 'Please enter your email address.');
    hasErrors = true;
  } else if (!isValidEmail(email)) {
    showFieldError('email', 'Please enter a valid email address.');
    hasErrors = true;
  }

  if (!password || !password.trim()) {
    showFieldError('password', 'Please enter your password.');
    hasErrors = true;
  }

  return !hasErrors;
}

// Email validation helper
function isValidEmail(email) {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

// Show field-specific error message
function showFieldError(fieldName, message) {
  const errorDiv = $(`#${fieldName}Error`);
  const inputField = $(`#${fieldName}`);

  errorDiv.text(message).removeClass('hidden');
  inputField.addClass('border-red-500').removeClass('border-gray-300');
}

// Clear all error messages
function clearAllErrors() {
  // Clear field-specific errors
  $('#emailError, #passwordError, #oauthError').addClass('hidden');

  // Reset input field borders
  $('#email, #password').removeClass('border-red-500').addClass('border-gray-300');

  // Clear general error message
  $('#errorMessage').addClass('hidden');
}

// Handle login errors and display them in appropriate locations
function handleLoginError(errorMessage) {
  // Password-related errors
  if (errorMessage.includes('Incorrect password') ||
      errorMessage.includes('password')) {
    showFieldError('password', errorMessage);
    return;
  }

  // Email-related errors
  if (errorMessage.includes('Email does not match any account') ||
      errorMessage.includes('Please use OAuth to login') ||
      errorMessage.includes('email')) {
    showFieldError('email', errorMessage);
    return;
  }

  // Account status errors - show in general error area
  if (errorMessage.includes('Account is disabled') ||
      errorMessage.includes('contact support')) {
    $('#errorMessage').text(errorMessage).removeClass('hidden');
    return;
  }

  // Default: show in general error area
  $('#errorMessage').text(errorMessage).removeClass('hidden');
}