{{> landing-header}}
  <body>
    <!-- NAV BAR-->
    {{> landing-navbar}}

    <!-- Main content-->
    <main class="flex flex-col items-center min-h-screen w-full bg-white px-4 lg:px-8">
      <div class="flex flex-col w-full items-center mt-16 gap-8 max-w-[1024px]">
        <h4 class="text-4xl font-bold lg:text-left text-center">
          Make Rental Management Easy. Start free.
        </h4>
        <p class="text-lg lg:text-left text-center">
          Unlock a better way to manage your rentals. No credit card needed.
        </p>
        <div class="inline-flex p-1 w-64 bg-gray-100 rounded-full">
          <button
            id="monthly-button"
            onclick="switchPricing()"
            class="px-8 py-2 rounded-full w-1/2 bg-white text-gray-900 font-medium shadow-sm transition-all duration-200"
          >
            Monthly
          </button>
          <button
            id="annual-button"
            onclick="switchPricing()"
            class="flex flex-col px-8 rounded-full w-1/2 text-gray-600 font-medium hover:text-gray-900 transition-all duration-200"
          >
            Annual
            <span class="text-xs">Save 20%</span>
          </button>
        </div>

        <div
          id="pricing-plan"
          hx-get="./components/pricing-monthly.html"
          hx-trigger="load"
          hx-swap="innerHTML"
        ></div>

        <!-- Feature Comparison -->
        <div
          class="flex flex-col w-full max-w-[992px] border border-gray-200 rounded-xl"
        >
          <div
            class="flex justify-between items-center w-full p-8 rounded-md text-lg"
            onclick="toggleFeatures()"
          >
            <p>Compare all features</p>
            <img src="/assets/Plus.svg" />
          </div>
          <div
            id="feature-comp"
            class="max-h-0 overflow-hidden transition-[max-height] duration-700 ease-in-out"
          >
            <div
              id="feat-comp-grid"
              class="grid grid-cols-1 md:grid-cols-4 w-full"
            >
              <!-- Header Row - hide on mobile -->
              <div class="hidden md:block p-4"></div>
              <div class="hidden md:flex p-4 flex-col items-center">
                <p class="font-medium">Free</p>
                <a href="/pages/signup/" class="w-full">
                  <button
                    class="hidden md:block px-4 py-2 w-full bg-white border border-rmred hover:bg-gray-50 text-rmred rounded-md text-base font-medium cursor-pointer transition-colors duration-100 mt-4"
                  >
                    Get Started
                  </button>
                </a>
              </div>
              <div class="hidden md:flex p-4 flex-col items-center">
                <p class="font-medium">Investor</p>
                <a href="/pages/signup/" class="w-full">
                  <button
                    class="hidden md:block px-4 py-2 w-full bg-rmred hover:bg-rmred-dark text-white rounded-md text-base font-medium cursor-pointer transition-colors duration-100 mt-4"
                  >
                    Get Started
                  </button>
                </a>
              </div>
              <div class="hidden md:flex p-4 flex-col items-center">
                <p class="font-medium">Property Manager</p>
                <a href="/pages/signup/" class="w-full">
                  <button
                    class="hidden md:block px-4 py-2 w-full bg-white border border-rmred hover:bg-gray-50 text-rmred rounded-md text-base font-medium cursor-pointer transition-colors duration-100 mt-4"
                  >
                    Get Started
                  </button>
                </a>
              </div>

              <!-- Feature Sections -->
              <div class="md:contents">
                <!-- Equifax Reporting -->
                <div
                  class="flex items-center justify-center lg:justify-start lg:min-h-full bg-gray-50 p-6 md:p-4 md:bg-transparent text-xl md:text-base font-medium text-center md:text-left"
                >
                  Rent reporting to Equifax
                </div>
                <div class="grid grid-cols-3 md:contents">
                  <div class="py-4 text-center border-t md:border-0">
                    <span
                      class="block font-medium mb-2 md:hidden min-h-12 sm:min-h-0"
                      >Free</span
                    >
                  </div>
                  <div class="py-4 text-center border-t md:border-0">
                    <span
                      class="block font-medium mb-2 md:hidden min-h-12 sm:min-h-0"
                      >Investor</span
                    >
                    20 leases per month
                  </div>
                  <div class="py-4 text-center border-t md:border-0">
                    <span
                      class="block font-medium mb-2 md:hidden min-h-12 sm:min-h-0"
                      >Property Manager</span
                    >
                    Unlimited
                  </div>
                </div>
                <!-- Additional Reporting-->
                <div
                  class="flex items-center justify-center lg:justify-start lg:min-h-full bg-gray-50 p-6 md:p-4 md:bg-transparent text-xl md:text-base font-medium text-center md:text-left"
                >
                  Additional report cost
                </div>
                <div class="grid grid-cols-3 md:contents">
                  <div class="py-4 text-center border-t md:border-0">
                    <span
                      class="block font-medium mb-2 md:hidden min-h-12 sm:min-h-0"
                      >Free</span
                    >
                  </div>
                  <div class="py-4 text-center border-t md:border-0">
                    <span
                      class="block font-medium mb-2 md:hidden min-h-12 sm:min-h-0"
                      >Investor</span
                    >
                    $1 per additional
                  </div>
                  <div class="py-4 text-center border-t md:border-0">
                    <span
                      class="block font-medium mb-2 md:hidden min-h-12 sm:min-h-0"
                      >Property Manager</span
                    >
                    Unlimited
                  </div>
                </div>

                <!-- Reporting Board Access -->
                <div
                  class="flex items-center justify-center lg:justify-start lg:min-h-full bg-gray-50 p-6 md:p-4 md:bg-transparent text-xl md:text-base font-medium text-center md:text-left"
                >
                  Reporting board access
                </div>
                <div class="grid grid-cols-3 md:contents">
                  <div class="py-4 text-center border-t md:border-0">
                    <span
                      class="block font-medium mb-2 md:hidden min-h-12 sm:min-h-0"
                      >Free</span
                    >
                    <div class="p-4 flex justify-center items-center">
                      <img src="/assets/check-circle.svg" class="w-6" />
                    </div>
                  </div>
                  <div class="py-4 text-center border-t md:border-0">
                    <span
                      class="block font-medium mb-2 md:hidden min-h-12 sm:min-h-0"
                      >Investor</span
                    >
                    <div class="p-4 flex justify-center items-center">
                      <img src="/assets/check-circle.svg" class="w-6" />
                    </div>
                  </div>
                  <div class="py-4 text-center border-t md:border-0">
                    <span
                      class="block font-medium mb-2 md:hidden min-h-12 sm:min-h-0"
                      >Property Manager</span
                    >
                    <div class="p-4 flex justify-center items-center">
                      <img src="/assets/check-circle.svg" class="w-6" />
                    </div>
                  </div>
                </div>

                <!-- Unlimited lease tracking -->
                <div
                  class="flex items-center justify-center lg:justify-start lg:min-h-full bg-gray-50 p-6 md:p-4 md:bg-transparent text-xl md:text-base font-medium text-center md:text-left"
                >
                  Unlimited lease tracking
                </div>
                <div class="grid grid-cols-3 md:contents">
                  <div class="py-4 text-center border-t md:border-0">
                    <span
                      class="block font-medium mb-2 md:hidden min-h-12 sm:min-h-0"
                      >Free</span
                    >
                  </div>
                  <div class="py-4 text-center border-t md:border-0">
                    <span
                      class="block font-medium mb-2 md:hidden min-h-12 sm:min-h-0"
                      >Investor</span
                    >
                    <div class="p-4 flex justify-center items-center">
                      <img src="/assets/check-circle.svg" class="w-6" />
                    </div>
                  </div>
                  <div class="py-4 text-center border-t md:border-0">
                    <span
                      class="block font-medium mb-2 md:hidden min-h-12 sm:min-h-0"
                      >Property Manager</span
                    >
                    <div class="p-4 flex justify-center items-center">
                      <img src="/assets/check-circle.svg" class="w-6" />
                    </div>
                  </div>
                </div>
                <div
                  class="flex items-center justify-center lg:justify-start lg:min-h-full bg-gray-50 p-6 md:p-4 md:bg-transparent text-xl md:text-base font-medium text-center md:text-left"
                >
                  Unlimited property tracking
                </div>
                <div class="grid grid-cols-3 md:contents">
                  <div class="py-4 text-center border-t md:border-0">
                    <span
                      class="block font-medium mb-2 md:hidden min-h-12 sm:min-h-0"
                      >Free</span
                    >
                  </div>
                  <div class="py-4 text-center border-t md:border-0">
                    <span
                      class="block font-medium mb-2 md:hidden min-h-12 sm:min-h-0"
                      >Investor</span
                    >
                    <div class="p-4 flex justify-center items-center">
                      <img src="/assets/check-circle.svg" class="w-6" />
                    </div>
                  </div>
                  <div class="py-4 text-center border-t md:border-0">
                    <span
                      class="block font-medium mb-2 md:hidden min-h-12 sm:min-h-0"
                      >Property Manager</span
                    >
                    <div class="p-4 flex justify-center items-center">
                      <img src="/assets/check-circle.svg" class="w-6" />
                    </div>
                  </div>
                </div>
                <div
                  class="flex items-center justify-center lg:justify-start lg:justify-start lg:min-h-full bg-gray-50 p-6 md:p-4 md:bg-transparent text-xl md:text-base font-medium text-center md:text-left"
                >
                  Team management
                </div>
                <div class="grid grid-cols-3 md:contents">
                  <div class="py-4 text-center border-t md:border-0">
                    <span
                      class="block font-medium mb-2 md:hidden min-h-12 sm:min-h-0"
                      >Free</span
                    >
                  </div>
                  <div class="py-4 text-center border-t md:border-0">
                    <span
                      class="block font-medium mb-2 md:hidden min-h-12 sm:min-h-0"
                      >Investor</span
                    >
                  </div>
                  <div class="py-4 text-center border-t md:border-0">
                    <span
                      class="block font-medium mb-2 md:hidden min-h-12 sm:min-h-0"
                      >Property Manager</span
                    >
                    <div class="p-4 flex justify-center items-center">
                      <img src="/assets/check-circle.svg" class="w-6" />
                    </div>
                  </div>
                </div>

                <!-- Automated tenant notifs-->
                <div
                  class="flex items-center justify-center lg:justify-start lg:min-h-full bg-gray-50 p-6 md:p-4 md:bg-transparent text-xl md:text-base font-medium text-center md:text-left"
                >
                  Automated tenant notifications
                </div>
                <div class="grid grid-cols-3 md:contents">
                  <div class="py-4 text-center border-t md:border-0">
                    <span
                      class="block font-medium mb-2 md:hidden min-h-12 sm:min-h-0"
                      >Free</span
                    >
                  </div>
                  <div class="py-4 text-center border-t md:border-0">
                    <span
                      class="block font-medium mb-2 md:hidden min-h-12 sm:min-h-0"
                      >Investor</span
                    >
                  </div>
                  <div class="py-4 text-center border-t md:border-0">
                    <span
                      class="block font-medium mb-2 md:hidden min-h-12 sm:min-h-0"
                      >Property Manager</span
                    >
                    <div class="p-4 flex justify-center items-center">
                      <img src="/assets/check-circle.svg" class="w-6" />
                    </div>
                  </div>
                </div>
                <div
                  class="flex items-center justify-center lg:justify-start lg:min-h-full bg-gray-50 p-6 md:p-4 md:bg-transparent text-xl md:text-base font-medium text-center md:text-left"
                >
                  Priority customer support
                </div>
                <div class="grid grid-cols-3 md:contents">
                  <div class="py-4 text-center border-t md:border-0">
                    <span
                      class="block font-medium mb-2 md:hidden min-h-12 sm:min-h-0"
                      >Free</span
                    >
                  </div>
                  <div class="py-4 text-center border-t md:border-0">
                    <span
                      class="block font-medium mb-2 md:hidden min-h-12 sm:min-h-0"
                      >Investor</span
                    >
                    <div class="p-4 flex justify-center items-center">
                      <img src="/assets/check-circle.svg" class="w-6" />
                    </div>
                  </div>
                  <div class="py-4 text-center border-t md:border-0">
                    <span
                      class="block font-medium mb-2 md:hidden min-h-12 sm:min-h-0"
                      >Property Manager</span
                    >
                    <div class="p-4 flex justify-center items-center">
                      <img src="/assets/check-circle.svg" class="w-6" />
                    </div>
                  </div>
                </div>
                <!-- Setup Assist -->
                <div
                  class="flex items-center justify-center lg:justify-start lg:min-h-full bg-gray-50 p-6 md:p-4 md:bg-transparent text-xl md:text-base font-medium text-center md:text-left"
                >
                  Account setup assistance
                </div>
                <div class="grid grid-cols-3 md:contents">
                  <div class="py-4 text-center border-t md:border-0">
                    <span
                      class="block font-medium mb-2 md:hidden min-h-12 sm:min-h-0"
                      >Free</span
                    >
                  </div>
                  <div class="py-4 text-center border-t md:border-0">
                    <span
                      class="block font-medium mb-2 md:hidden min-h-12 sm:min-h-0"
                      >Investor</span
                    >
                    <div class="p-4 flex justify-center items-center">
                      <img src="/assets/check-circle.svg" class="w-6" />
                    </div>
                  </div>
                  <div class="py-4 text-center border-t md:border-0">
                    <span
                      class="block font-medium mb-2 md:hidden min-h-12 sm:min-h-0"
                      >Property Manager</span
                    >
                    <div class="p-4 flex justify-center items-center">
                      <img src="/assets/check-circle.svg" class="w-6" />
                    </div>
                  </div>
                </div>
                <div
                  class="flex items-center justify-center lg:justify-start lg:min-h-full bg-gray-50 p-6 md:p-4 md:bg-transparent text-xl md:text-base font-medium text-center md:text-left"
                >
                  API integration
                </div>
                <div class="grid grid-cols-3 md:contents">
                  <div class="py-4 text-center border-t md:border-0">
                    <span
                      class="block font-medium mb-2 md:hidden min-h-12 sm:min-h-0"
                      >Free</span
                    >
                  </div>
                  <div class="py-4 text-center border-t md:border-0">
                    <span
                      class="block font-medium mb-2 md:hidden min-h-12 sm:min-h-0"
                      >Investor</span
                    >
                  </div>
                  <div class="py-4 text-center border-t md:border-0">
                    <span
                      class="block font-medium mb-2 md:hidden min-h-12 sm:min-h-0"
                      >Property Manager</span
                    >
                    <div class="p-4 flex justify-center items-center">
                      <img src="/assets/check-circle.svg" class="w-6" />
                    </div>
                  </div>
                </div>
                <div
                  class="flex items-center lg:min-h-full justify-center lg:justify-start bg-gray-50 p-6 md:p-4 md:bg-transparent text-xl md:text-base font-medium text-center md:text-left"
                >
                  Custom team roles
                </div>
                <div class="grid grid-cols-3 md:contents">
                  <div class="py-4 text-center border-t md:border-0">
                    <span
                      class="block font-medium mb-2 md:hidden min-h-12 sm:min-h-0"
                      >Free</span
                    >
                  </div>
                  <div class="py-4 text-center border-t md:border-0">
                    <span
                      class="block font-medium mb-2 md:hidden min-h-12 sm:min-h-0"
                      >Investor</span
                    >
                  </div>
                  <div class="py-4 text-center border-t md:border-0">
                    <span
                      class="block font-medium mb-2 md:hidden min-h-12 sm:min-h-0"
                      >Property Manager</span
                    >
                  </div>
                </div>
                <div
                  class="flex items-center justify-center lg:justify-start lg:min-h-full bg-gray-50 p-6 md:p-4 md:bg-transparent text-xl md:text-base font-medium text-center md:text-left"
                >
                  Customized branding
                </div>
                <div class="grid grid-cols-3 md:contents">
                  <div class="py-4 text-center border-t md:border-0">
                    <span
                      class="block font-medium mb-2 md:hidden min-h-12 sm:min-h-0"
                      >Free</span
                    >
                  </div>
                  <div class="py-4 text-center border-t md:border-0">
                    <span
                      class="block font-medium mb-2 md:hidden min-h-12 sm:min-h-0"
                      >Investor</span
                    >
                  </div>
                  <div class="py-4 text-center border-t md:border-0">
                    <span
                      class="block font-medium mb-2 md:hidden min-h-12 sm:min-h-0"
                      >Property Manager</span
                    >
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- FAQ -->
        <div
          class="flex flex-col justify-center items-center w-full mt-16 mb-16 max-w-[950px]"
        >
          <h3 class="font-bold text-2xl text-center">
            Frequently Asked Questions
          </h3>
          <!-- FAQ Wrapper -->
          <div
            class="flex flex-col w-full mt-16 [&_*]:border-gray-300 leading-relaxed"
          >
            <!-- FAQ question -->
            <div class="flex flex-col w-full p-6 gap-4 border-t">
              <div
                class="flex justify-between cursor-pointer"
                onclick="toggleFAQ(this)"
              >
                <h4 class="text-lg w-full lg:w-4/5">
                  Is Report Rentals free to use?
                </h4>
                <img src="/assets/Chevron-down.svg" class="w-6" />
              </div>
              <div class="hidden w-full lg:w-4/5">
                <p>
                  Report Rentals has a free plan which is ideal for first time
                  users or individuals wanting to track a few leases. However,
                  for users managing more than 3 leases, upgrading to the
                  Investor plan is recommended.
                </p>
              </div>
            </div>

            <div class="flex flex-col w-full p-6 gap-4 border-t">
              <div
                class="flex justify-between cursor-pointer"
                onclick="toggleFAQ(this)"
              >
                <h4 class="text-lg w-full lg:w-4/5">
                  How does Report Rentals' pricing work?
                </h4>
                <img src="/assets/Chevron-down.svg" class="w-6" />
              </div>
              <div class="hidden w-full lg:w-4/5">
                <p>
                  The free version of Report Rentals is available for individual
                  users. The Investor plan is priced at CAD $15.99 per user per
                  month with annual billing, or CAD $19.99 per user per month
                  with monthly billing. For organizations managing an extensive
                  portfolio, the Property Manager plan offers an
                  enterprise-level solution.
                </p>
              </div>
            </div>

            <div class="flex flex-col w-full p-6 gap-4 border-t">
              <div
                class="flex justify-between cursor-pointer"
                onclick="toggleFAQ(this)"
              >
                <h4 class="text-lg w-full lg:w-4/5">
                  Do you offer any discounts?
                </h4>
                <img src="/assets/Chevron-down.svg" class="w-6" />
              </div>
              <div class="hidden w-full lg:w-4/5">
                <p>
                  Yes! By choosing a yearly plan, you will receive a 20%
                  discount.
                </p>
              </div>
            </div>

            <div class="flex flex-col w-full p-6 gap-4 border-t">
              <div
                class="flex justify-between cursor-pointer"
                onclick="toggleFAQ(this)"
              >
                <h4 class="text-lg w-full lg:w-4/5">
                  How can I manage my billing?
                </h4>
                <img src="/assets/Chevron-down.svg" class="w-6" />
              </div>
              <div class="hidden w-full lg:w-4/5">
                <p>
                  You can manage your subscription and billing information
                  through
                </p>
              </div>
            </div>

            <div class="flex flex-col w-full p-6 gap-4 border-t">
              <div
                class="flex justify-between cursor-pointer"
                onclick="toggleFAQ(this)"
              >
                <h4 class="text-lg w-full lg:w-4/5">
                  How would I update my plans?
                </h4>
                <img src="/assets/Chevron-down.svg" class="w-6" />
              </div>
              <div class="hidden w-full lg:w-4/5">
                <p>
                  You can manage your subscription and billing information at
                  any time by accessing your billing section in your account
                  settings. From there, click "change plan" to upgrade or
                  downgrade your plan.
                </p>
              </div>
            </div>

            <div class="flex flex-col w-full p-6 gap-4 border-t">
              <div
                class="flex justify-between cursor-pointer"
                onclick="toggleFAQ(this)"
              >
                <h4 class="text-lg w-full lg:w-4/5">
                  Can my landlord send my information to Equifax without me
                  knowing?
                </h4>
                <img src="/assets/Chevron-down.svg" class="w-6" />
              </div>
              <div class="hidden w-full lg:w-4/5">
                <p>
                  No your landlord cannot begin sending rent reports to Equifax
                  withour your knowledge. In order to enable rent reporting,
                  tenants will receive a consent form to be digitally signed.
                </p>
                <br />
                <p>
                  Additionally, tenants will receive reminder emails for
                  upcoming and missed payments, as well as the opportunity to
                  dispute inaccurate information by messaging
                  <a
                    class="text-rmred font-medium"
                    href="mailto:<EMAIL>"
                    ><EMAIL></a
                  >.
                </p>
              </div>
            </div>

            <div class="flex flex-col w-full p-6 gap-4 border-t border-b">
              <div
                class="flex justify-between cursor-pointer"
                onclick="toggleFAQ(this)"
              >
                <h4 class="text-lg w-full lg:w-4/5">
                  I still have unanswered questions, what can I do?
                </h4>
                <img src="/assets/Chevron-down.svg" class="w-6" />
              </div>
              <div class="hidden w-full lg:w-4/5">
                <p>
                  We will do our best to help, please contact us with your
                  questions at
                  <a
                    class="text-rmred font-medium"
                    href="mailto:<EMAIL>"
                    ><EMAIL></a
                  >
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </main>

    <!--Footer-->
    {{> landing-footer}}

    <script src="/assets/htmx.min.js"></script>
    <script>
      let currentPricingType = 'monthly';

      function switchPricing() {
        const monthlyBtn = document.getElementById("monthly-button");
        const annualBtn = document.getElementById("annual-button");
        const priceList = document.getElementById("pricing-plan");

        if (!priceList) {
          return;
        }

        if (currentPricingType === 'monthly') {
          currentPricingType = 'annual';
          annualBtn.classList.add("bg-white", "shadow-sm", "text-gray-900");
          annualBtn.classList.remove("text-gray-600");
          monthlyBtn.classList.remove("bg-white", "shadow-sm", "text-gray-900");
          monthlyBtn.classList.add("text-gray-600");

          fetch("/pages/pricing/components/pricing-annual.html")
            .then(response => response.text())
            .then(html => {
              priceList.innerHTML = html;
            });
        } else {
          currentPricingType = 'monthly';
          monthlyBtn.classList.add("bg-white", "shadow-sm", "text-gray-900");
          monthlyBtn.classList.remove("text-gray-600");
          annualBtn.classList.remove("bg-white", "shadow-sm", "text-gray-900");
          annualBtn.classList.add("text-gray-600");

          fetch("/pages/pricing/components/pricing-monthly.html")
            .then(response => response.text())
            .then(html => {
              priceList.innerHTML = html;
            });
        }
      }

      function togglePricing(type) {
        const monthlyBtn = document.getElementById("monthly-button");
        const annualBtn = document.getElementById("annual-button");
        const priceList = document.getElementById("pricing-plan");

        if (priceList) {
          currentPricingType = type;
          if (type === "monthly") {
            monthlyBtn.classList.add("bg-white", "shadow-sm", "text-gray-900");
            monthlyBtn.classList.remove("text-gray-600");
            annualBtn.classList.remove("bg-white", "shadow-sm", "text-gray-900");
            annualBtn.classList.add("text-gray-600");
            fetch("/pages/pricing/components/pricing-monthly.html")
              .then(response => response.text())
              .then(html => {
                priceList.innerHTML = html;
              });
          } else {
            annualBtn.classList.add("bg-white", "shadow-sm", "text-gray-900");
            annualBtn.classList.remove("text-gray-600");
            monthlyBtn.classList.remove("bg-white", "shadow-sm", "text-gray-900");
            monthlyBtn.classList.add("text-gray-600");
            fetch("/pages/pricing/components/pricing-annual.html")
              .then(response => response.text())
              .then(html => {
                priceList.innerHTML = html;
              });
          }
        } else {
        }
      }

      function toggleFeatures() {
        const featureComp = document.getElementById("feature-comp");
        const grid = document.getElementById("feat-comp-grid");

        if (featureComp && grid) {
          if (
            featureComp.style.maxHeight === "0px" ||
            !featureComp.style.maxHeight
          ) {
            featureComp.style.maxHeight = grid.scrollHeight + "px";
          } else {
            featureComp.style.maxHeight = "0px";
          }
        } else {
          console.error("Element with id 'feature-comp' or 'feat-comp-grid' not found.");
        }
      }

      function toggleFAQ(elem) {
        const parents = elem.parentNode;
        const contentBox = parents.childNodes;

        contentBox[3].classList.toggle("hidden");
        contentBox[1]
          .getElementsByTagName("img")[0]
          .classList.toggle("rotate-180");
      }

      window.addEventListener('DOMContentLoaded', function() {
        document.querySelectorAll('#contact-us-btn').forEach(function(btn) {
          btn.addEventListener('click', function(e) {
            e.preventDefault();
            window.scrollTo({ top: document.body.scrollHeight, behavior: 'smooth' });
          });
        });
      });


    </script>
<script type="module" src="./main.js"></script>
</body>
</html> 