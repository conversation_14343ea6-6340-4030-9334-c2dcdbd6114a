import { redirectLoggedInUsersToHome } from '../../services/util.js';

// Check if user is logged in and redirect to home if they are
document.addEventListener('DOMContentLoaded', async () => {
  await redirectLoggedInUsersToHome();
});

function toggleFeatures() {
  const featureComp = document.getElementById('feature-comp');
  const grid = document.getElementById('feat-comp-grid');

  if (featureComp.style.maxHeight === '0px' || !featureComp.style.maxHeight) {
    featureComp.style.maxHeight = grid.scrollHeight + 'px';
  } else {
    featureComp.style.maxHeight = '0px';
  }
}

function toggleFAQ(elem) {
  const parents = elem.parentNode;
  const contentBox = parents.childNodes;

  contentBox[3].classList.toggle('hidden');
  contentBox[1].getElementsByTagName('img')[0].classList.toggle('rotate-180');
}