// 导入依赖
import $ from 'jquery'
import { propertyApi } from '../../services'
import { checkAuthStatusAndRedirect } from '../../services/util'
import Mustache from 'mustache'
import propertyListTemplate from '../../templates/property-list.mst?raw'
import propertyListMobileTemplate from '../../templates/property-list-mobile.mst?raw'
import paginationTemplate from '../../templates/pagination.mst?raw'
import paginationMobileTemplate from '../../templates/pagination-mobile.mst?raw'
import errorMessagesTemplate from '../../templates/error-messages.mst?raw'
import limitReachedModalTemplate from '../../templates/limit-reached-modal.mst?raw'
import { filterAndPaginate, initSearch } from '../../services/util'
import { userApi } from '../../services'
import { renderSidebarNav } from '../../services/sidebar'

// Store state
let currentPage = 1;
const itemsPerPage = 10;
let searchQuery = '';
let allProperties = [];
// 排序状态
let sortField = 'name'; // 默认按名称排序
let sortAscending = true;

// Global function to close modal and restore scrolling
window.closeModal = function() {
  document.getElementById('modal').innerHTML = '';
  // 恢复背景滚动
  document.body.style.overflow = '';
  document.documentElement.style.overflow = '';
};
let filterType = 'Current Properties'; // 新增：筛选类型，默认显示当前物业

// 页面加载完成后的交互初始化
$(document).ready(async function() {
  // autoOpen: 登录邀请流程后自动弹窗
  const urlParams = new URLSearchParams(window.location.search);
  let invitationData = null;
  const code = urlParams.get('code');
  const action = urlParams.get('action');
  const returnTo = urlParams.get('return');

  if (urlParams.get('autoOpen') === '1') {
    setTimeout(() => {
      $('#property-action-btn').trigger('click');
    }, 300);
  }

  // 检查是否从Create Lease页面跳转过来
  if (action === 'create' && returnTo === 'lease') {
    setTimeout(() => {
      $('#property-action-btn').trigger('click');
    }, 300);
  }
  // autofill: 如果有 code，获取 invitation 并填充表单
  if (code) {
    fetch(`/v1/invitations/verify?code=${encodeURIComponent(code)}`)
      .then(res => res.ok ? res.json() : Promise.reject('Invitation not found'))
      .then(inv => {
        invitationData = inv;
        // 存invitation id到全局变量
        window.currentInvitationId = inv.id;
        // 使用 MutationObserver 监听 #modal 内容变化
        const modal = document.getElementById('modal');
        if (modal) {
          const observer = new MutationObserver(() => {
            if ($('#property-name').length && invitationData) {
              $('#property-name').val(invitationData.propertyName || '');
              // 用空格分割 propertyAddress 并智能识别
              const addr = invitationData.propertyAddress || '';
              const parts = addr.trim().split(/\s+/);
              if (parts.length >= 6) {
                const postalCode = parts.slice(-2).join(' ');
                const country = parts[parts.length - 3];
                const province = parts[parts.length - 4];
                const city = parts[parts.length - 5];
                const address = parts.slice(0, parts.length - 5).join(' ');
                $('#address').val(address);
                $('#city').val(city);
                $('#province').val(province);
                $('#country').val(country);
                $('#postal-code').val(postalCode);
              } else {
                // fallback: 全部填到 address
                $('#address').val(addr);
              }
              $('#unit-number').val(invitationData.unit || '');
              observer.disconnect(); // 填充一次后断开监听
            }
          });
          observer.observe(modal, { childList: true, subtree: true });
        }
      })
      .catch(err => {
        console.error('fetch invitation error', err);
      });
  }
  // await checkAuthStatusAndRedirect();

  try {
    const userData = await userApi.getUserInfo();
    await renderSidebarNav(userData);
  } catch (e) {
    // 可以根据需要处理未登录等情况
  }

  // Initialize search
  initSearch('#property-search', (query) => {
    searchQuery = query;
    currentPage = 1; // Reset to first page when search changes
    loadPropertyList();
  });

  // Set up pagination event handlers
  $(document).on('click', '.pagination-btn.prev:not([disabled])', function() {
    if (currentPage > 1) {
      currentPage--;
      loadPropertyList();
    }
  });

  $(document).on('click', '.pagination-btn.next:not([disabled])', function() {
    currentPage++;
    loadPropertyList();
  });

  $(document).on('click', '.pagination-btn.page', function() {
    const page = parseInt($(this).data('page'), 10);
    if (page !== currentPage) {
      currentPage = page;
      loadPropertyList();
    }
  });

  // 初始化排序功能
  initSorting();
  
  // 初始化创建物业表单提交
  initCreatePropertyForm();
  
  // 监听筛选框变化
  $('#role').on('change', function() {
    filterType = $(this).val();
    loadPropertyList();
  });
  
  // 加载物业列表
  loadPropertyList();
});

// 初始化排序功能
function initSorting() {
  // 处理表头点击事件
  $(document).on('click', 'th .flex', function() {
    const columnText = $(this).text().trim().toLowerCase();
    
    // 根据列名决定排序字段
    let field;
    switch(columnText) {
      case 'property name':
        field = 'name';
        break;
      case 'room':
        field = 'totalRooms';
        break;
      case 'vacant':
        field = 'vacantRooms';
        break;
      case 'balance':
        field = 'owingBalance';
        break;
      default:
        field = 'name';
    }
    
    // 如果点击的是当前排序字段，则切换排序方向
    if (field === sortField) {
      sortAscending = !sortAscending;
    } else {
      sortField = field;
      sortAscending = true;
    }
    
    // 更新视觉指示
    updateSortingIndicators(field, sortAscending);
    
    // 重新加载列表
    loadPropertyList();
  });
}

// 更新排序指示器
function updateSortingIndicators(field, ascending) {
  // 移除所有表头的排序指示
  $('th .flex img').attr('src', '/assets/selector.svg');
  
  // 获取对应的表头
  let thElement;
  switch(field) {
    case 'name':
      thElement = $('th:contains("Property Name")');
      break;
    case 'totalRooms':
      thElement = $('th:contains("Room")');
      break;
    case 'vacantRooms':
      thElement = $('th:contains("Vacant")');
      break;
    case 'owingBalance':
      thElement = $('th:contains("Balance")');
      break;
  }
  
  // 更新排序图标
  if (thElement && thElement.length) {
    const imgElement = thElement.find('img');
    imgElement.attr('src', ascending ? '/assets/sort-up.svg' : '/assets/sort-down.svg');
  }
}

// 加载物业列表
async function loadPropertyList() {
  try {
    // Get all properties if not already loaded
      const response = await propertyApi.getProperties();

      if (!response.items && response.total != 0) {
        console.error('Invalid response format:', response);
        throw new Error('Invalid response format from API');
      }
      allProperties = response.items;
    // 新增：根据筛选类型过滤
    let filtered = allProperties;

    if (filterType === 'Current Properties') {
      filtered = allProperties.filter(p => p.status === 'active');
    } else if (filterType === 'Past Properties') {
      filtered = allProperties.filter(p => p.status === 'inactive' || p.status === 'archived');
    } else if (filterType === 'Show All') {
      filtered = allProperties.filter(p => p.status === 'active' || p.status === 'inactive' || p.status === 'archived');
    }

    // 渲染时为每个property增加isInactive字段
    filtered = filtered.map(p => ({ ...p, isInactive: p.status === 'inactive' }));

    // Filter and paginate properties
    const { items: paginatedProperties, pagination } = filterAndPaginate(
      filtered,
      searchQuery,
      currentPage,
      itemsPerPage,
      (property, query) => {
        // Define all searchable fields
        const searchableFields = [
          property.name,                      // Property name
          property.address?.street,           // Street address
          property.address?.unit,             // Unit number
          property.address?.city,             // City
          property.address?.prov,             // Province
          property.address?.zipCode,          // Postal/Zip code
          property.propertyType,              // Property type
          property.totalRooms?.toString(),    // Total rooms
          property.vacantRooms?.toString(),   // Vacant rooms
          property.status,                    // Status
          property.notes                      // Notes
        ];

        // Convert query to lowercase for case-insensitive search
        query = query.toLowerCase();

        // Check if any of the fields contain the search query
        return searchableFields.some(field => 
          field?.toString().toLowerCase().includes(query)
        );
      },
      { field: sortField, ascending: sortAscending } // 排序选项
    );

    // Render desktop view
    const renderedHtml = Mustache.render(propertyListTemplate, { properties: paginatedProperties });
    $('#property-list-tbody').html(renderedHtml);

    // Render mobile view
    const renderedMobileHtml = Mustache.render(propertyListMobileTemplate, { properties: paginatedProperties });
    $('#property-list-mobile').html(renderedMobileHtml);

    // Render desktop pagination
    const renderedPagination = Mustache.render(paginationTemplate, { pagination });
    $('#property-pagination').html(renderedPagination);

    // Render mobile pagination
    const renderedMobilePagination = Mustache.render(paginationMobileTemplate, { pagination });
    $('#property-pagination-mobile').html(renderedMobilePagination);
  } catch (error) {
    console.error('Failed to load properties:', error);
    const errorHtml = Mustache.render(errorMessagesTemplate, { propertyListError: true });
    $('#property-list-tbody').html(errorHtml);

    // Also show error in mobile view
    const mobileErrorHtml = `
      <div class="mt-4 rounded-md overflow-hidden" style="box-shadow: 0 0 0 1px rgb(203 213 225)">
        <div class="flex flex-wrap p-4 bg-white">
          <span class="font-medium w-full mb-2 text-center text-red-500">Failed to load properties</span>
        </div>
      </div>
    `;
    $('#property-list-mobile').html(mobileErrorHtml);
  }
}

// Validation functions
function validateAddressInput(inputElement, maxLength, fieldName) {
  const value = inputElement.value;
  const isValid = value.length <= maxLength;
  
  if (!isValid) {
    inputElement.classList.add('border-red-500');
    const errorMessage = `${fieldName} must be ${maxLength} characters or less`;
    
    // Create or update error message
    let errorDiv = inputElement.nextElementSibling;
    if (!errorDiv || !errorDiv.classList.contains('error-message')) {
      errorDiv = document.createElement('div');
      errorDiv.classList.add('error-message', 'text-red-500', 'text-sm', 'mt-1');
      inputElement.parentNode.insertBefore(errorDiv, inputElement.nextSibling);
    }
    errorDiv.textContent = errorMessage;
  } else {
    inputElement.classList.remove('border-red-500');
    const errorDiv = inputElement.nextElementSibling;
    if (errorDiv && errorDiv.classList.contains('error-message')) {
      errorDiv.remove();
    }
  }
  
  return isValid;
}

function validatePostalCode(inputElement) {
  // Remove any existing formatting to get raw input
  let value = inputElement.value.toUpperCase().replace(/\s+/g, '');

  // Format the postal code as user types (add space after 3rd character)
  if (value.length > 3) {
    value = value.slice(0, 3) + ' ' + value.slice(3);
  }

  // Update the input value with formatted version
  inputElement.value = value;

  // Check if it matches Canadian postal code format (A1A 1A1)
  const isValid = /^[A-Z]\d[A-Z] \d[A-Z]\d$/.test(value);

  if (!isValid) {
    inputElement.classList.add('border-red-500');
    // Use the predefined error span in the label instead of creating a new one
    const errorSpan = document.getElementById('postal-code-error');
    if (errorSpan) {
      errorSpan.classList.remove('hidden');
      errorSpan.textContent = 'Invalid Canadian postal code';
    }
  } else {
    inputElement.classList.remove('border-red-500');
    // Hide the predefined error span
    const errorSpan = document.getElementById('postal-code-error');
    if (errorSpan) {
      errorSpan.classList.add('hidden');
    }
  }

  return isValid;
}

function validateProvince(inputElement) {
  // Convert to uppercase and remove spaces
  let value = inputElement.value.toUpperCase().trim();
  inputElement.value = value;
  
  // Check if it's exactly 2 characters
  const isValid = /^[A-Z]{2}$/.test(value);
  
  if (!isValid) {
    inputElement.classList.add('border-red-500');
    const errorMessage = 'Province must be exactly 2 letters (e.g., ON, BC)';
    
    // Create or update error message
    let errorDiv = inputElement.nextElementSibling;
    if (!errorDiv || !errorDiv.classList.contains('error-message')) {
      errorDiv = document.createElement('div');
      errorDiv.classList.add('error-message', 'text-red-500', 'text-sm', 'mt-1');
      inputElement.parentNode.insertBefore(errorDiv, inputElement.nextSibling);
    }
    errorDiv.textContent = errorMessage;
  } else {
    inputElement.classList.remove('border-red-500');
    const errorDiv = inputElement.nextElementSibling;
    if (errorDiv && errorDiv.classList.contains('error-message')) {
      errorDiv.remove();
    }
  }
  
  return isValid;
}

// 初始化创建物业表单
export function initCreatePropertyForm() {
  // Add input validation event listeners
  $(document).on('input', '#address', function() {
    validateAddressInput(this, 32, 'Address');
  });
  
  $(document).on('input', '#unit-number', function() {
    validateAddressInput(this, 32, 'Unit');
  });
  
  $(document).on('input', '#city', function() {
    validateAddressInput(this, 20, 'City');
  });
  
  $(document).on('input', '#province', function() {
    validateProvince(this);
  });
  
  $(document).on('input', '#postal-code', function() {
    validatePostalCode(this);
  });

  // Use event delegation on document body for dynamically loaded modal
  $(document).on('click', '#modal #create-property-btn', async function(event) {
    event.preventDefault();
    event.stopPropagation();

    // First check user property limit
    try {
      const limitResponse = await $.ajax({
        url: '/v1/properties/check-limit',
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      })

      // If limit check fails, don't proceed with form validation
      if (!limitResponse.canCreate) {
        return;
      }
    } catch (error) {
      console.error('Failed to check property limit:', error)

      // Check if it's a limit error
      if (error.responseJSON && error.responseJSON.error === 'limit_reached') {
        // Show limit reached modal
        const modalHtml = Mustache.render(limitReachedModalTemplate, {
          isProperty: true,
          isLease: false,
          limit: error.responseJSON.limit
        })
        $('#modal').html(modalHtml)
        // Disable background scrolling
        document.body.style.overflow = 'hidden'
        document.documentElement.style.overflow = 'hidden'
        return;
      } else {
        // Show generic error and don't proceed
        alert('Failed to check property limit. Please try again.')
        return;
      }
    }

    // 必填项校验
    let valid = true;
    const requiredFields = [
      { id: 'property-name', label: 'Property Name' },
      { id: 'address', label: 'Address' },
      { id: 'city', label: 'City/Town' },
      { id: 'province', label: 'Province' },
      { id: 'country', label: 'Country' },
      { id: 'postal-code', label: 'Postal Code' }
    ];
    requiredFields.forEach(f => {
      const $el = $('#' + f.id);
      const $err = $('#' + f.id + '-error');
      if (!$el.val() || $el.val().trim() === '') {
        $el.addClass('border-red-500');
        $err.removeClass('hidden').text('Required');
        valid = false;
      } else {
        $el.removeClass('border-red-500');
        $err.addClass('hidden');
      }
    });
    if (!valid) return;

    // 验证postal code格式
    const postalCodeInput = document.getElementById('postal-code');
    if (postalCodeInput && !validatePostalCode(postalCodeInput)) {
      // 错误提示已经在validatePostalCode函数中显示
      return;
    }

    const propertyData = {
      name: $('#property-name').val(),
      address: {
        street: $('#address').val(),
        city: $('#city').val(),
        prov: $('#province').val(),
        country: $('#country').val(),
        zipCode: $('#postal-code').val(),
        unit: $('#unit-number').val()
      },
      propertyType: $('#property-type').val(),
      notes: $('#property-notes').val()
    };

    // 如果有invitation id，传入invId
    if (window.currentInvitationId) {
      propertyData.invId = window.currentInvitationId;
    }

    const $button = $(this);
    const originalText = $button.text();

    try {
      // 禁用按钮，显示加载状态
      $button.prop('disabled', true).text('Creating...');
      
      // 发送创建请求
      const newProperty = await propertyApi.createProperty(propertyData);
      
      
      // 创建默认房间
      if (newProperty && newProperty.id) {
        try {
          const roomData = {
            name: 'Default Unit',
            status: 'vacant',
            type: 'studio',
            notes: ''
          };
          await propertyApi.createRoom(newProperty.id, roomData);
        } catch (roomError) {
          console.error('Failed to create default unit:', roomError);
          // 不中断流程，即使创建default unit失败
        }
      }
      
      // 关闭模态框
      document.getElementById('modal').innerHTML = '';
      
      // 刷新物业列表
      await loadPropertyList();
      
      // autofill: 如果是邀请流程，自动跳转到 lease 创建页
      const localUrlParams = new URLSearchParams(window.location.search);
      const localCode = localUrlParams.get('code');
      if (localUrlParams.get('fromInvitation') === '1' && localCode && newProperty && newProperty.id) {
        let defaultRoomId = '';
        try {
          const roomData = {
            name: 'Main Room',
            status: 'vacant',
            type: 'studio',
            notes: ''
          };
          const roomRes = await propertyApi.createRoom(newProperty.id, roomData);
          defaultRoomId = roomRes && roomRes.id ? roomRes.id : '';
        } catch {}
        window.location.href = `/pages/leases/?propertyId=${newProperty.id}&roomId=${defaultRoomId}&code=${encodeURIComponent(localCode)}&fromInvitation=1`;
        return;
      }
      
    } catch (error) {
      console.error('Failed to create property:', error);

      // 检查是否是限制错误
      if (error.responseJSON && error.responseJSON.error === 'limit_reached') {
        // 显示限制弹窗
        const modalHtml = Mustache.render(limitReachedModalTemplate, {
          isProperty: true,
          isLease: false,
          limit: error.responseJSON.limit
        });
        $('#modal').html(modalHtml);
        // 禁用背景滚动
        document.body.style.overflow = 'hidden';
        document.documentElement.style.overflow = 'hidden';
      } else {
        // 显示普通错误消息
        alert(error.message || 'Failed to create property');
      }
    } finally {
      // 恢复按钮状态
      $button.prop('disabled', false).text(originalText);
    }
  });

  // Handle Cancel button in Create Property modal
  $(document).on('click', '#cancel-create-property-btn', function(event) {
    event.preventDefault();
    event.stopPropagation();

    // 检查URL参数，判断是否从Create Lease页面跳转过来
    const currentUrlParams = new URLSearchParams(window.location.search);
    const currentAction = currentUrlParams.get('action');
    const currentReturnTo = currentUrlParams.get('return');

    if (currentAction === 'create' && currentReturnTo === 'lease') {
      // 跳转回Create Lease页面并自动打开弹窗
      window.location.href = '../leases/?action=create';
    } else {
      // 正常关闭弹窗
      document.getElementById('modal').innerHTML = '';
    }
  });

  // Listen for HTMX afterSwap event to bind cancel button for special cases
  $(document).on('htmx:afterSwap', function(event) {
    if (event.target.id === 'modal') {
      setTimeout(() => {
        const modal = document.getElementById('modal');

        // 尝试通过ID查找Cancel按钮
        let cancelBtn = document.getElementById('cancel-create-property-btn');

        // 如果通过ID找不到，尝试通过文本内容查找
        if (!cancelBtn) {
          const buttons = modal.querySelectorAll('button');
          for (let btn of buttons) {
            if (btn.textContent.trim() === 'Cancel') {
              cancelBtn = btn;
              break;
            }
          }
        }

        if (cancelBtn) {
          // 检查URL参数
          const currentUrlParams = new URLSearchParams(window.location.search);
          const currentAction = currentUrlParams.get('action');
          const currentReturnTo = currentUrlParams.get('return');

          if (currentAction === 'create' && currentReturnTo === 'lease') {
            // 移除任何现有的事件监听器
            cancelBtn.onclick = null;

            // 直接绑定点击事件
            cancelBtn.addEventListener('click', function(e) {
              e.preventDefault();
              e.stopPropagation();
              e.stopImmediatePropagation();
              window.location.href = '../leases/?action=create';
            }, true);
          }
        }
      }, 100);
    }
  });

  // Prevent form submission
  $(document).on('submit', '#modal form', function(event) {
    event.preventDefault();
    return false;
  });

  // Handle Create Property button click (main page button)
  $(document).on('click', '#property-action-btn', async function(e) {
    e.preventDefault()

    try {
      // Check user property limit first
      const response = await $.ajax({
        url: '/v1/properties/check-limit',
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      })

      // If no limit reached, load the create property modal
      if (response.canCreate) {
        // Load the create property modal using HTMX-like behavior
        const modalResponse = await $.get('./modal-create-property.html')
        $('#modal').html(modalResponse)
      }
    } catch (error) {
      console.error('Failed to check property limit:', error)

      // Check if it's a limit error
      if (error.responseJSON && error.responseJSON.error === 'limit_reached') {
        // Show limit reached modal
        const modalHtml = Mustache.render(limitReachedModalTemplate, {
          isProperty: true,
          isLease: false,
          limit: error.responseJSON.limit
        })
        $('#modal').html(modalHtml)
        // Disable background scrolling
        document.body.style.overflow = 'hidden'
        document.documentElement.style.overflow = 'hidden'
      } else {
        // Show generic error
        alert('Failed to check property limit. Please try again.')
      }
    }
  })
}