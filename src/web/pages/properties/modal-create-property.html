<div class="fixed inset-0 z-50">
  <!-- Dark overlay with click handler to close -->
  <div
    class="fixed inset-0 bg-black bg-opacity-50 pointer-events-auto"
    onclick="document.getElementById('modal').innerHTML = ''"
  ></div>

  <!-- Modal container -->
  <div
    class="fixed inset-0 flex items-center justify-center pointer-events-none"
  >
    <div
      class="bg-white sm:rounded-lg shadow-xl sm:w-9/12 w-full h-full sm:max-h-[90vh] sm:min-h-[70vh] overflow-y-auto pointer-events-auto relative flex flex-col"
    >
      <!-- Modal Header -->
      <div
        class="sticky top-0 px-6 py-4 border-b bg-white z-10 flex justify-between items-center"
      >
        <div>
          <h3 class="text-lg font-semibold text-gray-900">Create Property</h3>
          <p>Add a new property</p>
        </div>
        <button
          class="p-2 hover:bg-gray-100 rounded-full"
          onclick="document.getElementById('modal').innerHTML = ''"
        >
          <svg
            class="w-5 h-5 text-gray-500"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M6 18L18 6M6 6l12 12"
            />
          </svg>
        </button>
      </div>

      <!-- Modal Body -->
      <div class="px-6 py-4 flex-1">
        <form>
          <div class="flex flex-wrap">
            <div class="w-full">
              <label class="mb-2" for="property-name">Property Name *<span id="property-name-error" class="text-red-500 text-xs ml-2 hidden">Required</span></label>
              <input
                class="p-2 py-4 w-full border border-gray-300 rounded-md focus:outline-none"
                id="property-name"
                placeholder="Property name"
              />
            </div>
            
            <div class="w-full flex flex-col md:flex-row gap-4 mt-4">
              <div class="flex flex-wrap md:w-1/2 w-full">
                <label class="w-full mb-2" for="address">Address *<span id="address-error" class="text-red-500 text-xs ml-2 hidden">Required</span></label>
                <input
                  class="p-2 py-4 w-full border border-gray-300 rounded-md focus:outline-none focus:ring-0 focus:border-gray-300"
                  id="address"
                  placeholder="Address"
                />
              </div>
              <div class="flex flex-wrap md:w-1/2 w-full">
                <label class="w-full mb-2" for="unit-number">Unit</label>
                <input
                  class="p-2 py-4 w-full border border-gray-300 rounded-md focus:outline-none focus:ring-0 focus:border-gray-300"
                  id="unit-number"
                  placeholder="Unit"
                />
              </div>
            </div>
            
            <div class="w-full flex flex-col md:flex-row gap-4 mt-4">
              <div class="flex flex-wrap md:w-1/2 w-full">
                <label class="w-full mb-2" for="city">City/Town *<span id="city-error" class="text-red-500 text-xs ml-2 hidden">Required</span></label>
                <input
                  class="p-2 py-4 w-full border border-gray-300 rounded-md focus:outline-none focus:ring-0 focus:border-gray-300"
                  id="city"
                  placeholder="City/Town"
                />
              </div>
              <div class="flex flex-wrap md:w-1/2 w-full">
                <label class="w-full mb-2" for="province">Province *<span id="province-error" class="text-red-500 text-xs ml-2 hidden">Required</span></label>
                <select
                  class="p-2 w-full border border-gray-300 rounded-md focus:outline-none focus:ring-0 focus:border-gray-300 bg-white h-[52px]"
                  id="province"
                  required
                >
                  <option value="">Select Province</option>
                  <option value="AB">AB - Alberta</option>
                  <option value="BC">BC - British Columbia</option>
                  <option value="MB">MB - Manitoba</option>
                  <option value="NB">NB - New Brunswick</option>
                  <option value="NL">NL - Newfoundland and Labrador</option>
                  <option value="NS">NS - Nova Scotia</option>
                  <option value="ON">ON - Ontario</option>
                  <option value="PE">PE - Prince Edward Island</option>
                  <option value="QC">QC - Quebec</option>
                  <option value="SK">SK - Saskatchewan</option>
                  <option value="NT">NT - Northwest Territories</option>
                  <option value="NU">NU - Nunavut</option>
                  <option value="YT">YT - Yukon</option>
                </select>
              </div>
            </div>
            
            <div class="w-full flex flex-col md:flex-row gap-4 mt-4 mb-4">
              <div class="flex flex-wrap md:w-1/2 w-full">
                <label class="w-full mb-2" for="country">Country *<span id="country-error" class="text-red-500 text-xs ml-2 hidden">Required</span></label>
                <input
                  class="p-2 py-4 w-full border border-gray-300 rounded-md focus:outline-none focus:ring-0 focus:border-gray-300"
                  id="country"
                  list="countries"
                  placeholder=""
                  value="Canada"
                  readonly
                />
              </div>
              <div class="flex flex-wrap md:w-1/2 w-full">
                <label class="w-full mb-2" for="postal-code">Postal Code *<span id="postal-code-error" class="text-red-500 text-xs ml-2 hidden">Required</span></label>
                <input
                  class="p-2 py-4 w-full border border-gray-300 rounded-md focus:outline-none focus:ring-0 focus:border-gray-300"
                  id="postal-code"
                  placeholder="Postal Code"
                />
              </div>
            </div>
          </div>
        </form>
      </div>

      <!-- Modal Footer -->
      <div
        class="sticky bottom-0 left-0 right-0 px-6 py-4 bg-gray-50 sm:rounded-b-lg flex justify-between gap-2 border-t mt-auto"
      >
        <button
          id="cancel-create-property-btn"
          class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50"
        >
          Cancel
        </button>
        <button
          id="create-property-btn"
          class="px-4 py-2 text-sm font-medium text-white bg-slate-800 rounded-md hover:bg-slate-900"
        >
          Create Property
        </button>
      </div>
    </div>
  </div>
</div>