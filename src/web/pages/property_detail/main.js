import $ from 'jquery'
import { propertyApi, leaseApi } from '../../services'
import Mustache from 'mustache'
import propertyDetailInfoTemplate from '../../templates/property-detail-info.mst?raw'
import confirmDeleteModalTemplate from '../../templates/confirm-delete-modal.mst?raw'
import deletionConstraintModalTemplate from '../../templates/deletion-constraint-modal.mst?raw'
import archivePropertyModalTemplate from '../../templates/archive-property-modal.mst?raw'
import roomViewTemplate from '../../templates/room-view.mst?raw'
import roomListTemplate from '../../templates/room-list.mst?raw'
import { checkAuthStatusAndRedirect, sortItems } from '../../services/util'
import { jsPDF } from 'jspdf'
import { userApi } from '../../services'
import { renderSidebarNav } from '../../services/sidebar'
import { unsavedChangesDetector } from '../../services/unsaved-changes'
import duplicateFileModalTemplate from '../../templates/duplicate-file-modal.mst?raw'

// 排序状态
let sortField = 'name'; // 默认按房间名称排序
let sortAscending = true;
let propertyData = null; // 存储完整属性数据
let propertyId = null; // 存储属性ID
let pendingFileDelete = false; // 标记是否有待删除的文件

// 多文件上传相关变量
let uploadedDocuments = []; // 存储已上传的文档
let pendingUploads = []; // 存储待上传的文件
let pendingDeletes = []; // 存储待删除的文档ID
const MAX_FILES = 10; // 最大文件数量

// Make file arrays globally accessible for unsaved changes detection
window.pendingUploads = pendingUploads;
window.pendingDeletes = pendingDeletes;

// Variables for duplicate file handling
let pendingDuplicateFile = null;
let duplicateFileInfo = null;

// Global function to close modal and restore scrolling
window.closeModal = function() {
  document.getElementById('modal').innerHTML = '';
  // 恢复背景滚动
  document.body.style.overflow = '';
  document.documentElement.style.overflow = '';
};

// 多文件上传相关函数
function updateFileCount() {
  // 计算有效文件数量（排除deleted和final_deleted状态的文件）
  const activeUploadedFiles = uploadedDocuments.filter(doc =>
    doc.status !== 'deleted' && doc.status !== 'final_deleted'
  ).length;
  const totalFiles = activeUploadedFiles + pendingUploads.length - pendingDeletes.length;
  $('#file-count').text(`${totalFiles} of ${MAX_FILES} files`);
}

function renderUploadedFiles() {
  const container = $('#uploaded-files');
  container.empty();

  // 渲染已上传的文档（只显示非deleted和final_deleted状态的文件）
  uploadedDocuments.forEach(doc => {
    if (!pendingDeletes.includes(doc.id) && doc.status !== 'deleted' && doc.status !== 'final_deleted') {
      const fileItem = createFileItem(doc, true);
      container.append(fileItem);
    }
  });

  // 渲染待上传的文件
  pendingUploads.forEach((file, index) => {
    const fileItem = createFileItem(file, false, index);
    container.append(fileItem);
  });

  updateFileCount();
  updateUploadAreaDisplay();
}

function updateUploadAreaDisplay() {
  // 计算有效文件数量（排除deleted和final_deleted状态的文件）
  const activeUploadedFiles = uploadedDocuments.filter(doc =>
    doc.status !== 'deleted' && doc.status !== 'final_deleted'
  ).length;
  const totalFiles = activeUploadedFiles + pendingUploads.length - pendingDeletes.length;
  const uploadArea = $('#file-upload-area');

  if (totalFiles >= MAX_FILES) {
    // 达到最大文件数量，显示限制提示
    uploadArea.html(`
      <svg class="w-8 h-8 text-gray-400 mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M9 19l3 3m0 0l3-3m-3 3V10"></path>
      </svg>
      <p class="text-red-600 font-medium mb-1">Max upload limit reached. Delete a file to add a new one.</p>
      <p class="text-gray-500 text-sm">Supported formats: pdf, png, jpg, jpeg, doc</p>
      <input id="file-input" class="hidden" type="file" multiple accept=".pdf,.png,.jpg,.jpeg,.doc,.docx" />
    `);
    uploadArea.removeClass('hover:border-gray-400 cursor-pointer');
  } else {
    // 正常上传区域 - 恢复原始HTML结构
    uploadArea.html(`
      <svg class="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
      </svg>
      <div class="text-center">
        <span class="text-gray-600">Drag and drop or </span>
        <label class="text-blue-600 hover:text-blue-800 cursor-pointer underline">
          browse
          <input id="file-input" class="hidden" type="file" multiple accept=".pdf,.png,.jpg,.jpeg,.doc,.docx" />
        </label>
        <span class="text-gray-600"> to upload</span>
      </div>
      <span class="text-gray-500 text-sm">Supported formats: pdf, png, jpg, jpeg, doc</span>
    `);
    uploadArea.addClass('hover:border-gray-400 cursor-pointer');
  }
}

function createFileItem(item, isUploaded, index = null) {
  const fileName = isUploaded ? item.fileName : item.name;
  const fileSize = isUploaded ? item.fileSize : item.size;
  const fileId = isUploaded ? item.id : `pending-${index}`;

  return $(`
    <div class="flex items-center justify-between p-2 bg-white rounded border hover:bg-gray-50" data-file-id="${fileId}">
      <div class="flex items-center gap-2 flex-1 min-w-0">
        <svg class="w-4 h-4 text-gray-500 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
        </svg>
        <div class="flex-1 min-w-0">
          ${isUploaded ?
            `<div class="text-sm font-medium text-blue-600 hover:text-blue-800 cursor-pointer truncate" onclick="downloadFile('${item.id}')">${fileName}</div>` :
            `<div class="text-sm font-medium text-gray-900 truncate">${fileName}</div>`
          }
          <div class="text-xs text-gray-500">${formatFileSize(fileSize)}</div>
        </div>
      </div>
      <div class="flex items-center">
        <button class="text-red-600 hover:text-red-800 p-1" onclick="removeFile('${fileId}', ${isUploaded})" title="Remove file">
          <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
          </svg>
        </button>
      </div>
    </div>
  `);
}

function formatFileSize(bytes) {
  if (bytes === 0) return '0 Bytes';
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// 页面加载完成后的交互初始化
$(document).ready(async function() {
  // await checkAuthStatusAndRedirect();
  
  // 从URL获取属性ID
  const urlParams = new URLSearchParams(window.location.search);
  propertyId = urlParams.get('id');
  
  if (!propertyId) {
    console.error('No property ID provided');
    return;
  }

  // 加载物业详情
  await loadPropertyDetail(propertyId);

  // 初始化删除按钮事件
  initDeleteButton(propertyId);

  // 初始化创建房间按钮事件
  initCreateRoomButton(propertyId);

  // 初始化房间列表排序
  initRoomSorting();

  // 初始化下载按钮
  initDownloadButton();

  // 初始化行点击事件
  initRowClickHandlers();

  // 初始化property status change处理
  initPropertyStatusHandler();

  // 初始化文件上传功能
  initFileUpload();

  // 初始化重复文件模态框事件处理器
  initDuplicateFileModalHandlers();

  // 初始化回收站功能
  initRecycleBinHandlers();

  try {
    const userData = await userApi.getUserInfo();
    await renderSidebarNav(userData);

    // Initialize unsaved changes detection for all users in landlord view
    const isLandlordView = (userData.viewType || '').toLowerCase() === 'landlord';

    if (isLandlordView) {
      // Wait a bit for the page to fully load before initializing
      setTimeout(() => {
        unsavedChangesDetector.init({
          excludeSelectors: [
            // Remove exclusions for testing
          ],
          includeFileChanges: true
        });
      }, 1000); // Increased timeout to ensure page is fully loaded
    } else {
    }
  } catch (e) {
    // 可以根据需要处理未登录等情况
  }

});

// 初始化房间排序功能
function initRoomSorting() {
  // 处理表头点击事件
  $(document).on('click', 'th .flex', async function() {
    const columnText = $(this).text().trim().toLowerCase();
    
    // 根据列名决定排序字段
    let field;
    switch(columnText) {
      case 'name':
        field = 'name';
        break;
      case 'current lease':
        field = 'tenantName';
        break;
      case 'owing balance':
        field = 'owingBalance';
        break;
      default:
        field = 'name';
    }
    
    // 如果点击的是当前排序字段，则切换排序方向
    if (field === sortField) {
      sortAscending = !sortAscending;
    } else {
      sortField = field;
      sortAscending = true;
    }
    
    // 更新视觉指示
    updateSortingIndicators(field, sortAscending);
    
    // 重新渲染房间列表
    await renderRoomList();
  });
}

// 更新排序指示器
function updateSortingIndicators(field, ascending) {
  // 移除所有表头的排序指示
  $('th .flex img').attr('src', '/assets/selector.svg');
  
  // 获取对应的表头
  let thElement;
  switch(field) {
    case 'name':
      thElement = $('th:contains("Name")');
      break;
    case 'tenantName':
      thElement = $('th:contains("Current Lease")');
      break;
    case 'owingBalance':
      thElement = $('th:contains("Owing Balance")');
      break;
  }
  
  // 更新排序图标
  if (thElement && thElement.length) {
    const imgElement = thElement.find('img');
    imgElement.attr('src', ascending ? '/assets/sort-up.svg' : '/assets/sort-down.svg');
  }
}

// 渲染房间列表
async function renderRoomList() {
  if (!propertyData || !propertyData.rooms) return;

  // 为每个房间获取租客信息
  const roomsWithTenants = await Promise.all(propertyData.rooms.map(async (room) => {
    let tenantName = "No tenant";
    let leaseId = null;

    try {
      // 获取该房间的活跃租约
      const leases = await leaseApi.getLeases({
        propertyId: propertyData.id,
        status: 'active',
        limit: 1000
      });

      // 找到该房间的租约
      const roomLease = leases.items.find(lease => lease.roomId === room.id);

      if (roomLease && roomLease.currentTenants && roomLease.currentTenants.length > 0) {
        // 从 currentTenants 数组中获取第一个租客的姓名
        const firstTenant = roomLease.currentTenants[0];
        const nameParts = [firstTenant.firstName, firstTenant.middleName, firstTenant.lastName]
          .filter(Boolean);
        tenantName = nameParts.length > 0 ? nameParts.join(' ') : "No tenant";
        leaseId = roomLease.id;
      }
    } catch (error) {
      console.error(`Failed to get tenant info for room ${room.name}:`, error);
    }

    return {
      ...room,
      tenantName: tenantName,
      currentLease: leaseId ? { id: leaseId, tenantName: tenantName } : null,
      // 确保owingBalance是数字，用于排序
      owingBalance: typeof room.owingBalance === 'number' ? room.owingBalance : 0
    };
  }));

  // 根据当前排序设置排序房间
  const rooms = sortItems(roomsWithTenants, sortField, sortAscending);

  // 渲染房间列表
  const renderedRoomListHtml = Mustache.render(roomListTemplate, { rooms });
  $('#room-list').html(renderedRoomListHtml);
}

// 加载物业详情
async function loadPropertyDetail(propertyId) {
  try {
    propertyData = await propertyApi.getPropertyById(propertyId);

    // 准备province选项的数据
    const currentProvince = propertyData.address?.prov || '';
    const currentStatus = propertyData.status || 'active';
    const templateData = {
      ...propertyData,
      isAB: currentProvince === 'AB',
      isBC: currentProvince === 'BC',
      isMB: currentProvince === 'MB',
      isNB: currentProvince === 'NB',
      isNL: currentProvince === 'NL',
      isNS: currentProvince === 'NS',
      isON: currentProvince === 'ON',
      isPE: currentProvince === 'PE',
      isQC: currentProvince === 'QC',
      isSK: currentProvince === 'SK',
      isNT: currentProvince === 'NT',
      isNU: currentProvince === 'NU',
      isYT: currentProvince === 'YT',
      isActive: currentStatus === 'active',
      isInactive: currentStatus === 'inactive',
      isArchived: currentStatus === 'archived'
    };

    // 渲染物业信息
    const renderedInfoHtml = Mustache.render(propertyDetailInfoTemplate, templateData);
    $('#property-detail-info').html(renderedInfoHtml);

    // 渲染房间列表（使用默认排序）
    await renderRoomList();

    // 填充notes字段到textarea
    $('textarea#property-notes').val(propertyData.notes || '');

    // 设置status下拉菜单的默认值
    $('#status').val(propertyData.status);

    // 初始化postal code验证
    initPostalCodeValidation();

    // 渲染文档显示区域
    renderDocumentDisplay(propertyData);

  } catch (error) {
    console.error('Failed to load property detail:', error);
    alert('Failed to load property detail');
  }
}

// 初始化删除按钮
function initDeleteButton(propertyId) {
  // 点击删除按钮时先检查约束
  $(document).on('click', '#delete-property-btn', async function() {
    try {
      // 先检查是否可以删除
      const checkResult = await propertyApi.checkPropertyDeletion(propertyId);

      if (checkResult.canDelete) {
        // 可以删除，显示确认框
        const modalHtml = Mustache.render(confirmDeleteModalTemplate, {
          title: 'Delete Property'
        });
        $('#modal').html(modalHtml);
        // 禁用背景滚动
        document.body.style.overflow = 'hidden';
        document.documentElement.style.overflow = 'hidden';
      }
    } catch (error) {
      console.error('Failed to check property deletion constraints:', error);

      // 检查是否是约束错误（通过错误消息内容判断）
      const errorMessage = error.message || '';

      if (errorMessage.includes('cannot delete property')) {
        // 解析错误消息以提供更友好的提示
        let friendlyMessage = errorMessage;
        let instructions = '';

        if (errorMessage.includes('room(s)')) {
          const roomCount = errorMessage.match(/(\d+) room\(s\)/)?.[1] || 'some';
          friendlyMessage = `This property has ${roomCount} room(s) that must be deleted first.`;
          instructions = 'Please delete all rooms before deleting the property.';
        }

        // 显示约束错误模态框
        const constraintModalHtml = Mustache.render(deletionConstraintModalTemplate, {
          entityType: 'Property',
          message: friendlyMessage,
          instructions: instructions,
          hasInstructions: instructions !== ''
        });
        $('#modal').html(constraintModalHtml);

        // 禁用背景滚动
        document.body.style.overflow = 'hidden';
        document.documentElement.style.overflow = 'hidden';
      } else {
        // 其他错误，显示通用错误消息
        alert('Failed to check deletion constraints: ' + errorMessage);
      }
    }
  });

  // 确认删除
  $(document).on('click', '#confirm-delete-btn', async function() {
    try {
      // 执行硬删除操作
      await propertyApi.deleteProperty(propertyId);
      window.location.href = '../properties/index.html';
    } catch (error) {
      console.error('Failed to delete property:', error);
      alert('Failed to delete property: ' + (error.message || 'Unknown error'));
    }
  });
}

// 生成默认unit名称
function generateDefaultUnitName(existingRooms) {

  // 过滤出"Unit N"格式的名称
  const unitNumbers = existingRooms
    .filter(room => {
      const hasName = room.name && /^Unit \d+$/i.test(room.name);
      return hasName;
    })  // 匹配"Unit 数字"格式
    .map(room => {
      const number = parseInt(room.name.replace(/^Unit /i, ''));
      return number;
    })  // 提取数字
    .filter(num => !isNaN(num))  // 确保是有效数字

  // 如果没有"Unit N"格式的名称，返回"Unit 1"
  if (unitNumbers.length === 0) {
    return "Unit 1";
  }

  // 找到最大的数字，生成下一个
  const maxNumber = Math.max(...unitNumbers);
  const nextNumber = maxNumber + 1;
  return `Unit ${nextNumber}`;
}

// 监听HTMX弹窗加载完成事件
$(document).on('htmx:afterSwap', function(event) {
  // 检查是否是create room弹窗
  if (event.target.id === 'modal' && $('#room-name').length > 0) {
    // 生成默认unit名称并设置为active value
    const existingRooms = propertyData && propertyData.rooms ? propertyData.rooms : [];
    const defaultName = generateDefaultUnitName(existingRooms);
    $('#room-name').val(defaultName);
  }
});

// 初始化创建房间按钮
function initCreateRoomButton(propertyId) {
  $(document).on('click', '#create-room-btn', async function() {
    // 必填项校验
    let valid = true;
    const $roomName = $('#room-name');
    const $roomNameError = $('#room-name-error');
    let roomNameValue = $roomName.val() ? $roomName.val().trim() : '';

    if (!roomNameValue) {
      $roomName.addClass('border-red-500');
      $roomNameError.removeClass('hidden').text('Required');
      valid = false;
    } else {
      $roomName.removeClass('border-red-500');
      $roomNameError.addClass('hidden');
    }
    if (!valid) return;

    const $button = $(this);
    const originalText = $button.text();

    try {
      // 禁用按钮，显示加载状态
      $button.prop('disabled', true).text('Creating...');

      const roomData = {
        name: roomNameValue,
        notes: $('#unit-notes').val() || '',
        status: 'vacant', // Default status
      };

      await propertyApi.createRoom(propertyId, roomData);
      
      // Close modal and refresh page
      document.getElementById('modal').innerHTML = '';
      window.location.reload();
      
    } catch (error) {
      console.error('Failed to create room:', error);
      alert(error.message || 'Failed to create room');
    } finally {
      // 恢复按钮状态
      $button.prop('disabled', false).text(originalText);
    }
  });
}

// 在保存按钮中实现更新
$(document).on('click', '#save-property-btn', async function() {
  const $button = $(this);
  const originalText = $button.text();

  // 验证postal code
  const postalCodeInput = document.getElementById('postal-code');
  if (postalCodeInput && !validatePostalCodeDetail(postalCodeInput)) {
    alert('Please enter a valid Canadian postal code (e.g., A1A 1A1)');
    return;
  }

  // 验证必填字段
  let valid = true;
  const requiredFields = [
    { id: 'property-name', label: 'Property Name' },
    { id: 'street', label: 'Address' },
    { id: 'city', label: 'City/Town' },
    { id: 'province', label: 'Province' }
  ];

  requiredFields.forEach(field => {
    const $el = $('#' + field.id);
    if (!$el.val() || $el.val().trim() === '') {
      $el.addClass('border-red-500');
      valid = false;
    } else {
      $el.removeClass('border-red-500');
    }
  });

  if (!valid) {
    alert('Please fill in all required fields');
    return;
  }

  try {
    $button.prop('disabled', true).text('Saving...');

    const propertyId = new URLSearchParams(window.location.search).get('id');

    // 1. 处理文件删除
    for (const fileId of pendingDeletes) {
      try {
        await propertyApi.deleteSpecificDocument(propertyId, fileId);
      } catch (error) {
        console.error(`Failed to delete file ${fileId}:`, error);
        alert(`Failed to delete file: ${error.message}`);
        return;
      }
    }

    // 2. 处理文件上传
    const uploadResults = [];
    for (const file of pendingUploads) {
      try {
        const formData = new FormData();
        formData.append('file', file);

        const uploadResponse = await fetch(`/v1/properties/${propertyId}/document`, {
          method: 'POST',
          body: formData
        });

        if (!uploadResponse.ok) {
          throw new Error(`Failed to upload file: ${file.name}`);
        }

        const result = await uploadResponse.json();
        uploadResults.push(result);
      } catch (error) {
        console.error(`Failed to upload ${file.name}:`, error);
        alert(`Failed to upload ${file.name}: ${error.message}`);
        return;
      }
    }

    // 3. 获取当前property信息以获取最新的documents数组
    const currentPropertyData = await propertyApi.getPropertyById(propertyId);

    // 4. 构建更新后的property数据
    const updatedProperty = {
      ...currentPropertyData, // 使用最新的数据作为基础
      name: $('#property-name').val(),
      address: {
        street: $('#street').val(),
        unit: $('#unit').val(),
        city: $('#city').val(),
        prov: $('#province').val(),
        country: $('#country').val(),
        zipCode: $('#postal-code').val(),
      },
      status: $('#status').val(),
      notes: $('textarea#property-notes').val() || currentPropertyData.notes,
    };

    // 5. 清空待处理列表
    pendingUploads = [];
    pendingDeletes = [];

    // Update global references
    window.pendingUploads = pendingUploads;
    window.pendingDeletes = pendingDeletes;


    // 发送更新请求
    await propertyApi.updateProperty(propertyId, updatedProperty);

    // 如果status被设置为archived，需要批量更新该property下的所有leases
    if (updatedProperty.status === 'archived') {
      await handlePropertyArchived(propertyId);
    }

    // Mark as saved for unsaved changes detection before reload
    if (unsavedChangesDetector) {
      unsavedChangesDetector.markAsSaved();
    }

    window.location.reload();
  } catch (error) {
    console.error('Failed to update property:', error);
    alert(error.message || 'Failed to update property');
  } finally {
    $button.prop('disabled', false).text(originalText);
  }
});

// 文件选择处理
$(document).on('change', 'input[type="file"]', function() {
  const fileInput = $('#file-input');
  const filePreview = $('#file-preview');
  const fileName = $('#file-name');
  const fileLink = $('#file-link');

  if (this.files.length > 0) {
    fileInput.addClass('hidden');
    filePreview.removeClass('hidden');
    fileName.text(this.files[0].name);
    fileLink.attr('href', URL.createObjectURL(this.files[0]));

    // 重置删除标记和提示文本
    pendingFileDelete = false;
    fileInput.find('span').text('No file chosen');
  }
});

// 移除文件
$(document).on('click', '#file-preview .text-red-600', function(e) {
  e.preventDefault();

  const fileInput = $('#file-input');
  const filePreview = $('#file-preview');
  const fileLink = $('#file-link');
  const input = $('input[type="file"]');

  // 检查是否是已上传的文件还是新选择的文件
  const href = fileLink.attr('href');
  if (href && href.startsWith('/v1/properties/download/')) {
    // 已上传的文件 - 标记为待删除
    pendingFileDelete = true;
    filePreview.addClass('hidden');
    fileInput.removeClass('hidden');

    // 显示提示信息
    fileInput.find('span').text('File will be removed when you save changes');
  } else {
    // 新选择的文件 - 直接清除
    filePreview.addClass('hidden');
    fileInput.removeClass('hidden');
    input.val('');
    if (href && href.startsWith('blob:')) {
      URL.revokeObjectURL(href);
    }
  }
});

// 下载Property PDF
async function downloadPropertyPDF() {
  try {
    if (!propertyData) {
      console.error('No property data available');
      return;
    }

    // 创建PDF文档
    const doc = new jsPDF();
    const pageWidth = doc.internal.pageSize.getWidth();
    
    // 添加标题
    doc.setFontSize(20);
    doc.text('Property Details', pageWidth/2, 20, { align: 'center' });
    
    // 设置正文字体大小
    doc.setFontSize(12);
    
    // 添加基本信息
    let y = 40;
    doc.text(`Property Name: ${propertyData.name}`, 20, y);
    y += 10;
    
    // 添加地址信息
    if (propertyData.address) {
      const address = propertyData.address;
      doc.text('Address:', 20, y);
      y += 7;
      doc.text(`${address.street}${address.unit ? `, Unit ${address.unit}` : ''}`, 30, y);
      y += 7;
      doc.text(`${address.city}, ${address.prov}`, 30, y);
      y += 7;
      doc.text(`${address.country} ${address.zipCode}`, 30, y);
      y += 15;
    }
    
    // 添加房间信息摘要
    doc.text(`Total Rooms: ${propertyData.totalRooms || 0}`, 20, y);
    y += 7;
    doc.text(`Vacant Rooms: ${propertyData.vacantRooms || 0}`, 20, y);
    y += 15;

    // 添加房间详细信息
    if (propertyData.rooms && propertyData.rooms.length > 0) {
      doc.text('Rooms Details:', 20, y);
      y += 10;

      // 添加房间表头
      doc.setFontSize(10);
      doc.text('Room Name', 25, y);
      doc.text('Status', 75, y);
      doc.text('Current Tenant', 115, y);
      doc.text('Balance', 170, y);
      y += 5;

      // 添加分隔线
      doc.line(20, y, pageWidth - 20, y);
      y += 7;

      // 获取所有房间的租客信息
      const roomsWithTenants = await Promise.all(propertyData.rooms.map(async (room) => {
        let tenantName = "No tenant";

        try {
          // 获取该房间的活跃租约
          const leases = await leaseApi.getLeases({
            propertyId: propertyData.id,
            status: 'active',
            limit: 1000
          });

          // 找到该房间的租约
          const roomLease = leases.items.find(lease => lease.roomId === room.id);

          if (roomLease && roomLease.currentTenants && roomLease.currentTenants.length > 0) {
            // 从 currentTenants 数组中获取第一个租客的姓名
            const firstTenant = roomLease.currentTenants[0];
            const nameParts = [firstTenant.firstName, firstTenant.middleName, firstTenant.lastName]
              .filter(Boolean);
            tenantName = nameParts.length > 0 ? nameParts.join(' ') : "No tenant";
          }
        } catch (error) {
          console.error(`Failed to get tenant info for room ${room.name}:`, error);
        }

        return {
          ...room,
          tenantName: tenantName
        };
      }));

      // 遍历所有房间
      roomsWithTenants.forEach(room => {
        // 检查是否需要新页面
        if (y > 270) {
          doc.addPage();
          y = 20;
        }

        const tenantName = room.tenantName || 'No tenant';
        const status = room.status || 'N/A';
        const balance = room.owingBalance ? `$${room.owingBalance}` : '$0';

        doc.text(room.name || 'N/A', 25, y);
        doc.text(status, 75, y);
        doc.text(tenantName, 115, y);
        doc.text(balance, 170, y);
        y += 7;
      });
      y += 10;
    }
    
    // 添加备注（在新页面如果当前页面空间不足）
    if (propertyData.notes) {
      if (y > 250) {
        doc.addPage();
        y = 20;
      }
      doc.setFontSize(12);
      doc.text('Notes:', 20, y);
      y += 7;
      const splitNotes = doc.splitTextToSize(propertyData.notes, pageWidth - 40);
      doc.text(splitNotes, 20, y);
    }
    
    // 生成文件名
    const fileName = `${propertyData.name.replace(/[^a-z0-9]/gi, '_').toLowerCase()}_property_details.pdf`;
    
    // 下载PDF
    doc.save(fileName);
    
  } catch (error) {
    console.error('Failed to generate PDF:', error);
    alert('Failed to generate PDF');
  }
}

// 初始化下载按钮事件
function initDownloadButton() {
  $('#download-property-btn').on('click', downloadPropertyPDF);
}

// 渲染文档显示区域
function renderDocumentDisplay(property) {
  const userData = window.currentUser || JSON.parse(localStorage.getItem('user') || '{}')
  const isLandlord = (userData.viewType || '').toLowerCase() !== 'tenant'
  const isOwner = property.userID === userData.id || property.usrId === userData.id

  if (isLandlord && isOwner) {
    // Landlord view - 初始化多文件上传
    // 从property数据中提取文档信息
    uploadedDocuments = [];
    if (property.documents && Array.isArray(property.documents)) {
      uploadedDocuments = property.documents
        .filter(doc => doc.fileName && doc.fileName.trim() !== '') // 过滤掉空文档
        .map(doc => ({
          id: doc.id,
          fileName: doc.fileName,
          fileSize: doc.fileSize || 0,
          fileType: doc.fileType || 'application/pdf',
          status: doc.status || doc.Status || '' // 添加status字段
        }));
    }

    // 清空待处理列表
    pendingUploads = [];
    pendingDeletes = [];

    // Update global references
    window.pendingUploads = pendingUploads;
    window.pendingDeletes = pendingDeletes;

    // 渲染文件列表
    renderUploadedFiles();
  } else {
    // Tenant/Admin view - 只能查看和下载
    const documentDisplay = $('#property-document-display')
    documentDisplay.removeClass('hidden')

    // 隐藏上传相关元素
    $('#file-upload-area').addClass('hidden')
    $('#uploaded-files').addClass('hidden')
    $('#file-count').addClass('hidden')

    if (property.documents && property.documents.length > 0) {
      // 显示所有文档
      const documentsHtml = property.documents.map(doc => `
        <a href="/v1/properties/download/${doc.id}" target="_blank" class="flex items-center gap-2 hover:bg-gray-100 bg-gray-50 px-3 py-1.5 rounded-md text-gray-700 mb-2">
          <svg class="w-4 h-4 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z" />
          </svg>
          <span>${doc.fileName}</span>
        </a>
      `).join('');
      documentDisplay.html(documentsHtml);
    } else {
      documentDisplay.html('<span class="text-gray-500 text-sm">No file uploaded</span>')
    }
  }
}

function initRowClickHandlers() {
  // Handle room row click
  $(document).on('click', '.room-detail-trigger', async function(e) {
    e.stopPropagation(); // 防止事件冒泡
    const roomId = $(this).closest('tr').data('room-id');
    
    try {
      // 获取room详情
      const room = await propertyApi.getRoomById(propertyId, roomId);
      
      // 准备模板数据
      const templateData = {
        ...room,
        id: roomId,
        name: room.name || '',
        notes: room.notes || ''
      };
      
      // 显示带有现有数据的模态框
      const modalHtml = Mustache.render(roomViewTemplate, templateData);
      $('#modal').html(modalHtml);
      
    } catch (error) {
      console.error('Failed to load room details:', error);
      alert('Failed to load room details: ' + error.message);
    }
  });

  // 处理更新room按钮点击
  $(document).on('click', '#update-room', async function(event) {
    event.preventDefault();
    const button = $(this);
    const originalText = button.text();
    const roomId = button.data('room-id');

    try {
      button.prop('disabled', true).text('Updating...');

      // 先获取当前房间的完整信息
      const currentRoom = await propertyApi.getRoomById(propertyId, roomId);
      
      // 只更新name和notes，保持其他字段不变
      const roomData = {
        ...currentRoom, // 保持所有现有字段
        name: $('#room-name').val(),
        notes: $('#room-notes').val()
      };

      await propertyApi.updateRoom(propertyId, roomId, roomData);
      
      // 关闭模态框并刷新页面
      $('#modal').html('');
      await loadPropertyDetail(propertyId);

    } catch (error) {
      console.error('Failed to update room:', error);
      alert(error.message || 'Failed to update room');
    } finally {
      button.prop('disabled', false).text(originalText);
    }
  });

  // 处理删除room按钮点击
  $(document).on('click', '#delete-room', async function(event) {
    event.preventDefault(); // 阻止表单提交

    const roomId = $(this).data('room-id');
    const button = $(this);
    const originalText = button.text();

    try {
      // 先检查是否可以删除
      const checkResult = await propertyApi.checkRoomDeletion(propertyId, roomId);

      if (checkResult.canDelete) {
        // 可以删除，显示确认框
        if (!confirm('Are you sure you want to delete this room?')) {
          return;
        }

        button.prop('disabled', true).text('Deleting...');
        await propertyApi.deleteRoom(propertyId, roomId);

        // 关闭模态框并刷新页面
        $('#modal').html('');
        await loadPropertyDetail(propertyId);
      }
    } catch (error) {
      console.error('Failed to delete room:', error);

      // 检查是否是约束错误（通过错误消息内容判断）
      const errorMessage = error.message || '';
      if (errorMessage.includes('cannot delete room')) {
        // 解析错误消息以提供更友好的提示
        let friendlyMessage = errorMessage;
        let instructions = '';

        if (errorMessage.includes('lease(s)')) {
          const leaseCount = errorMessage.match(/(\d+) associated lease\(s\)/)?.[1] || 'some';
          friendlyMessage = `This room has ${leaseCount} associated lease(s) that must be deleted first.`;
          instructions = 'Please delete all leases before deleting the room.';
        }

        // 显示约束错误模态框
        const constraintModalHtml = Mustache.render(deletionConstraintModalTemplate, {
          entityType: 'Room',
          message: friendlyMessage,
          instructions: instructions,
          hasInstructions: instructions !== ''
        });
        $('#modal').html(constraintModalHtml);

        // 禁用背景滚动
        document.body.style.overflow = 'hidden';
        document.documentElement.style.overflow = 'hidden';
      } else {
        // 其他错误，显示通用错误消息
        alert('Failed to delete room: ' + errorMessage);
      }
    } finally {
      button.prop('disabled', false).text(originalText);
    }
  });
}

// Postal code validation function (same as in properties/main.js)
function validatePostalCodeDetail(inputElement) {
  // Remove any existing formatting to get raw input
  let value = inputElement.value.toUpperCase().replace(/\s+/g, '');

  // Format the postal code as user types (add space after 3rd character)
  if (value.length > 3) {
    value = value.slice(0, 3) + ' ' + value.slice(3);
  }

  // Update the input value with formatted version
  inputElement.value = value;

  // Check if it matches Canadian postal code format (A1A 1A1)
  const isValid = /^[A-Z]\d[A-Z] \d[A-Z]\d$/.test(value);

  if (!isValid && value.length > 0) {
    inputElement.classList.add('border-red-500');
    // Use the predefined error span in the label
    const errorSpan = document.getElementById('postal-code-error');
    if (errorSpan) {
      errorSpan.classList.remove('hidden');
    }
  } else {
    inputElement.classList.remove('border-red-500');
    // Hide the predefined error span
    const errorSpan = document.getElementById('postal-code-error');
    if (errorSpan) {
      errorSpan.classList.add('hidden');
    }
  }

  return isValid || value.length === 0; // Allow empty values
}

// Initialize postal code validation
function initPostalCodeValidation() {
  $(document).on('input', '#postal-code', function() {
    validatePostalCodeDetail(this);
  });
}

// Initialize property status change handler
function initPropertyStatusHandler() {
  let previousStatus = null;
  let pendingArchiveStatus = false;

  // Store the initial status when dropdown gets focus
  $(document).on('focus', '#status', function() {
    previousStatus = $(this).val();
  });

  // Handle status dropdown change
  $(document).on('change', '#status', function() {
    const selectedStatus = $(this).val();

    // Check if user is trying to change status to 'archived'
    if (selectedStatus === 'archived' && previousStatus !== 'archived') {
      // Show archive confirmation modal
      const modalHtml = Mustache.render(archivePropertyModalTemplate);
      $('#modal').html(modalHtml);

      // Revert the dropdown to previous status until confirmed
      $(this).val(previousStatus);

      // Store the pending status for later use
      pendingArchiveStatus = true;
    } else {
      // For other status changes, update the previous status
      previousStatus = selectedStatus;
      pendingArchiveStatus = false;
    }
  });

  // Handle archive confirmation
  $(document).on('click', '#confirm-archive-property-btn', function() {
    // Set the status dropdown to archived
    $('#status').val('archived');
    previousStatus = 'archived';
    pendingArchiveStatus = false;

    // Close the modal
    $('#modal').html('');
  });
}

// Handle property archived - batch update all leases in this property
async function handlePropertyArchived(propertyId) {
  try {
    // Call API to batch update all leases in this property
    const response = await fetch(`/v1/properties/${encodeURIComponent(propertyId)}/archive`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      }
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`Failed to archive property leases: ${errorText}`);
    }
  } catch (error) {
    console.error('Failed to handle property archived:', error);
    // Don't throw the error to prevent blocking the main save operation
    // Just log it and show a warning
    alert('Property was updated but there was an issue updating related leases. Please check the leases manually.');
  }
}

// 检查文件名是否重复
function checkDuplicateFileName(file) {
  const fileName = file.name;

  // 检查与已上传文件的重复（排除待删除的文件和deleted/final_deleted状态的文件）
  const existingFile = uploadedDocuments.find(doc =>
    doc.fileName === fileName &&
    !pendingDeletes.includes(doc.id) &&
    doc.status !== 'deleted' &&
    doc.status !== 'final_deleted'
  );

  if (existingFile) {
    return {
      isDuplicate: true,
      type: 'uploaded',
      existingFile: existingFile
    };
  }

  // 检查与待上传文件的重复
  const pendingFile = pendingUploads.find(pendingFile => pendingFile.name === fileName);
  if (pendingFile) {
    return {
      isDuplicate: true,
      type: 'pending',
      existingFile: pendingFile
    };
  }

  return { isDuplicate: false };
}

// 显示重复文件确认模态框
function showDuplicateFileModal(file, duplicateInfo) {
  pendingDuplicateFile = file;
  duplicateFileInfo = duplicateInfo;

  const modalHtml = duplicateFileModalTemplate;
  $('#modal').html(modalHtml);

  // 禁用背景滚动
  document.body.style.overflow = 'hidden';
  document.documentElement.style.overflow = 'hidden';
}

// 处理重复文件替换
function handleFileReplace() {
  if (!pendingDuplicateFile || !duplicateFileInfo) return;

  if (duplicateFileInfo.type === 'uploaded') {
    // 如果是已上传的文件，添加到待删除列表
    if (!pendingDeletes.includes(duplicateFileInfo.existingFile.id)) {
      pendingDeletes.push(duplicateFileInfo.existingFile.id);
    }
  } else if (duplicateFileInfo.type === 'pending') {
    // 如果是待上传的文件，从待上传列表中移除
    const index = pendingUploads.indexOf(duplicateFileInfo.existingFile);
    if (index > -1) {
      pendingUploads.splice(index, 1);
    }
  }

  // 添加新文件到待上传列表
  pendingUploads.push(pendingDuplicateFile);

  // 重新渲染文件列表
  renderUploadedFiles();

  // Mark as changed for unsaved changes detection
  if (typeof unsavedChangesDetector !== 'undefined') {
    unsavedChangesDetector.markAsChanged();
  }

  // 清理临时变量
  pendingDuplicateFile = null;
  duplicateFileInfo = null;

  // 关闭模态框
  $('#modal').html('');
  document.body.style.overflow = '';
  document.documentElement.style.overflow = '';
}

// 文件验证函数
function validateFile(file) {
  const allowedTypes = ['application/pdf', 'image/png', 'image/jpeg', 'image/jpg', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'];
  const maxSizeMap = {
    'application/pdf': 12 * 1024 * 1024, // 12MB for PDF
    'application/msword': 12 * 1024 * 1024, // 12MB for DOC
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document': 12 * 1024 * 1024, // 12MB for DOCX
    'image/png': 4 * 1024 * 1024, // 4MB for PNG
    'image/jpeg': 4 * 1024 * 1024, // 4MB for JPG
    'image/jpg': 4 * 1024 * 1024 // 4MB for JPG
  };

  if (!allowedTypes.includes(file.type)) {
    alert(`File type not supported: ${file.type}. Please upload PDF, DOC, DOCX, PNG, or JPG files.`);
    return false;
  }

  const maxSize = maxSizeMap[file.type];
  if (file.size > maxSize) {
    const maxSizeMB = maxSize / (1024 * 1024);
    alert(`File size too large: ${file.name}. Maximum size for ${file.type} is ${maxSizeMB}MB.`);
    return false;
  }

  // 检查重复文件名
  const duplicateCheck = checkDuplicateFileName(file);
  if (duplicateCheck.isDuplicate) {
    showDuplicateFileModal(file, duplicateCheck);
    return false; // 暂时返回false，等待用户确认
  }

  return true;
}

// 多文件选择处理
$(document).on('change', '#file-input', function() {
  const files = Array.from(this.files);
  // 计算有效文件数量（排除deleted和final_deleted状态的文件）
  const activeUploadedFiles = uploadedDocuments.filter(doc =>
    doc.status !== 'deleted' && doc.status !== 'final_deleted'
  ).length;
  const currentTotal = activeUploadedFiles + pendingUploads.length - pendingDeletes.length;

  // 检查文件数量限制
  if (currentTotal >= MAX_FILES) {
    this.value = ''; // 清空文件选择
    return;
  }

  // 如果添加这些文件会超过限制，只添加能添加的文件
  const availableSlots = MAX_FILES - currentTotal;
  const filesToAdd = files.slice(0, availableSlots);

  // 验证文件类型和大小
  const validFiles = [];
  for (const file of filesToAdd) {
    if (validateFile(file)) {
      validFiles.push(file);
    }
  }

  // 添加到待上传列表
  pendingUploads.push(...validFiles);

  // 重新渲染文件列表
  renderUploadedFiles();

  // 清空文件输入
  this.value = '';

  // Mark as changed for unsaved changes detection
  if (typeof unsavedChangesDetector !== 'undefined') {
    unsavedChangesDetector.markAsChanged();
  }
});

// 全局函数：移除文件
window.removeFile = function(fileId, isUploaded) {
  if (isUploaded) {
    // 已上传的文件 - 添加到待删除列表
    if (!pendingDeletes.includes(fileId)) {
      pendingDeletes.push(fileId);
    }
  } else {
    // 待上传的文件 - 从待上传列表中移除
    const index = parseInt(fileId.replace('pending-', ''));
    if (index >= 0 && index < pendingUploads.length) {
      pendingUploads.splice(index, 1);
    }
  }

  renderUploadedFiles();

  // Mark as changed for unsaved changes detection
  if (typeof unsavedChangesDetector !== 'undefined') {
    unsavedChangesDetector.markAsChanged();
  }
};

// 全局函数：下载文件
window.downloadFile = function(fileId) {
  window.open(`/v1/properties/download/${fileId}`, '_blank');
};

// 初始化文件上传功能
function initFileUpload() {
  const uploadArea = $('#file-upload-area');

  // 防止默认拖拽行为
  ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
    uploadArea.on(eventName, function(e) {
      e.preventDefault();
      e.stopPropagation();
    });
  });

  // 拖拽进入和悬停时的视觉反馈
  ['dragenter', 'dragover'].forEach(eventName => {
    uploadArea.on(eventName, function() {
      uploadArea.addClass('border-blue-500 bg-blue-50');
    });
  });

  ['dragleave', 'drop'].forEach(eventName => {
    uploadArea.on(eventName, function() {
      uploadArea.removeClass('border-blue-500 bg-blue-50');
    });
  });

  // 处理文件拖拽放置
  uploadArea.on('drop', function(e) {
    const files = Array.from(e.originalEvent.dataTransfer.files);
    // 计算有效文件数量（排除deleted和final_deleted状态的文件）
    const activeUploadedFiles = uploadedDocuments.filter(doc =>
      doc.status !== 'deleted' && doc.status !== 'final_deleted'
    ).length;
    const currentTotal = activeUploadedFiles + pendingUploads.length - pendingDeletes.length;

    // 检查文件数量限制
    if (currentTotal >= MAX_FILES) {
      return; // 已达到限制，不处理拖拽
    }

    // 如果添加这些文件会超过限制，只添加能添加的文件
    const availableSlots = MAX_FILES - currentTotal;
    const filesToAdd = files.slice(0, availableSlots);

    // 验证文件类型和大小
    const validFiles = [];
    for (const file of filesToAdd) {
      if (validateFile(file)) {
        validFiles.push(file);
      }
    }

    // 添加到待上传列表
    pendingUploads.push(...validFiles);

    // 重新渲染文件列表
    renderUploadedFiles();

    // Mark as changed for unsaved changes detection
    if (typeof unsavedChangesDetector !== 'undefined') {
      unsavedChangesDetector.markAsChanged();
    }
  });
}

// 初始化重复文件模态框事件处理器
function initDuplicateFileModalHandlers() {
  // 处理取消按钮点击
  $(document).on('click', '#cancel-duplicate-file-btn, #duplicate-file-close, #duplicate-file-overlay', function() {
    // 清理临时变量
    pendingDuplicateFile = null;
    duplicateFileInfo = null;

    // 关闭模态框
    $('#modal').html('');
    document.body.style.overflow = '';
    document.documentElement.style.overflow = '';
  });

  // 处理替换按钮点击
  $(document).on('click', '#replace-file-btn', function() {
    handleFileReplace();
  });
}

// 回收站相关功能
function initRecycleBinHandlers() {
  // 打开回收站弹窗
  $('#recycle-bin-btn').on('click', function() {
    openRecycleBinModal();
  });

  // 关闭回收站弹窗
  $(document).on('click', '#close-recycle-modal, #done-recycle-modal', function() {
    closeRecycleBinModal();
  });

  // 点击背景关闭弹窗
  $(document).on('click', '#recycle-bin-modal', function(e) {
    if (e.target === this) {
      closeRecycleBinModal();
    }
  });

  // 恢复文件
  $(document).on('click', '.restore-file-btn', function() {
    const documentId = $(this).data('document-id');
    const $button = $(this);

    // 立即禁用按钮防止重复点击
    $button.prop('disabled', true).addClass('cursor-not-allowed bg-gray-400').removeClass('bg-blue-600 hover:bg-blue-700').text('Restoring...');

    restoreFile(documentId);
  });
}

// 打开回收站弹窗
async function openRecycleBinModal() {
  try {
    // 禁用背景滚动
    document.body.style.overflow = 'hidden';
    document.documentElement.style.overflow = 'hidden';

    // 显示弹窗
    $('#recycle-bin-modal').removeClass('hidden');

    // 加载已删除的文件
    await loadDeletedFiles();
  } catch (error) {
    console.error('Failed to open recycle bin:', error);
    alert('Failed to load deleted files');
    closeRecycleBinModal();
  }
}

// 关闭回收站弹窗
function closeRecycleBinModal() {
  $('#recycle-bin-modal').addClass('hidden');
  // 恢复背景滚动
  document.body.style.overflow = '';
  document.documentElement.style.overflow = '';
}

// 加载已删除的文件
async function loadDeletedFiles() {
  try {
    const propertyId = new URLSearchParams(window.location.search).get('id');
    const response = await propertyApi.getDeletedDocuments(propertyId);

    if (response.success && response.documents) {
      renderDeletedFiles(response.documents);
    } else {
      renderDeletedFiles([]);
    }
  } catch (error) {
    console.error('Failed to load deleted files:', error);
    renderDeletedFiles([]);
  }
}

// 渲染已删除的文件列表
function renderDeletedFiles(deletedFiles) {
  const $filesList = $('#deleted-files-list');
  const $emptyState = $('#empty-recycle-bin');
  const $warningMessage = $('#file-limit-warning');

  if (deletedFiles.length === 0) {
    $filesList.empty();
    $emptyState.removeClass('hidden');
    $warningMessage.addClass('hidden');
    return;
  }

  $emptyState.addClass('hidden');

  // 计算当前活跃文件数量
  const activeUploadedFiles = uploadedDocuments.filter(doc =>
    doc.status !== 'deleted' && doc.status !== 'final_deleted'
  ).length;
  const totalActiveFiles = activeUploadedFiles + pendingUploads.length;

  // 显示或隐藏警告消息
  const isAtLimit = totalActiveFiles >= MAX_FILES;
  if (isAtLimit) {
    $warningMessage.removeClass('hidden');
  } else {
    $warningMessage.addClass('hidden');
  }

  const filesHtml = deletedFiles.map(file => {
    const daysLeftColor = file.daysLeft <= 3 ? 'text-red-600' :
                         file.daysLeft <= 7 ? 'text-orange-600' : 'text-gray-600';

    const restoreButtonHtml = isAtLimit
      ? `<button class="bg-gray-400 text-white px-3 py-1 rounded text-sm cursor-not-allowed" disabled>
           Restore
         </button>`
      : `<button class="restore-file-btn bg-blue-600 text-white px-3 py-1 rounded text-sm hover:bg-blue-700 transition"
                 data-document-id="${file.id}">
           Restore
         </button>`;

    return `
      <div class="flex items-center justify-between p-3 border rounded-lg">
        <div class="flex items-center space-x-3">
          <svg class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
          </svg>
          <div>
            <div class="font-medium text-sm">${file.fileName}</div>
            <div class="text-xs text-gray-500">${formatFileSize(file.fileSize)}</div>
            <div class="text-xs text-gray-500">Uploaded ${formatDate(file.uploadedAt)}</div>
            <div class="text-xs ${daysLeftColor}">${file.daysLeft} days left</div>
          </div>
        </div>
        ${restoreButtonHtml}
      </div>
    `;
  }).join('');

  $filesList.html(filesHtml);
}

// 恢复文件
async function restoreFile(documentId) {
  try {
    const propertyId = new URLSearchParams(window.location.search).get('id');
    const response = await propertyApi.restoreDocument(propertyId, documentId);

    if (response.success) {
      // 重新加载属性数据以更新文档列表
      await loadPropertyDetail(propertyId);

      // 重新加载已删除文件列表（在更新文档列表后）
      await loadDeletedFiles();
    } else {
      // 如果恢复失败（比如达到文件限制），静默处理，只刷新显示
      await loadDeletedFiles();
    }
  } catch (error) {
    console.error('Failed to restore file:', error);

    // 检查错误消息是否包含重复文件名的提示
    const errorMessage = error.message || '';
    if (errorMessage.includes('already exists')) {
      // 显示重复文件检测弹窗
      showDuplicateFileNameModal(errorMessage);
    } else {
      alert('Failed to restore file: ' + errorMessage);
    }
  }
}

// 显示文件数量限制弹窗
function showFileLimitModal() {
  const modalHtml = `
    <div class="fixed inset-0 z-50">
      <div class="fixed inset-0 bg-black bg-opacity-50"></div>
      <div class="fixed inset-0 flex items-center justify-center p-4">
        <div class="bg-white rounded-lg shadow-xl w-full max-w-md p-6">
          <h3 class="text-lg font-semibold text-gray-900 mb-4">File Limit Reached</h3>
          <p class="text-gray-700 mb-6">Cannot restore file. Maximum number of files (10) would be exceeded. Please delete some files first.</p>
          <div class="flex justify-end">
            <button onclick="closeModal()" class="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700">
              OK
            </button>
          </div>
        </div>
      </div>
    </div>
  `;

  $('#modal').html(modalHtml);
  // 禁用背景滚动
  document.body.style.overflow = 'hidden';
  document.documentElement.style.overflow = 'hidden';
}

// 显示重复文件名弹窗
function showDuplicateFileNameModal(errorMessage) {
  // 先关闭回收站弹窗
  closeRecycleBinModal();

  // 稍微延迟一下再显示新弹窗，确保旧弹窗完全关闭
  setTimeout(() => {
    const modalHtml = duplicateFileModalTemplate;
    $('#modal').html(modalHtml);

    // 禁用背景滚动
    document.body.style.overflow = 'hidden';
    document.documentElement.style.overflow = 'hidden';

    // 添加事件处理器
    $('#cancel-duplicate-file-btn, #duplicate-file-close, #duplicate-file-overlay').off('click').on('click', function() {
      closeModal();
    });

    $('#replace-file-btn').off('click').on('click', function() {
      closeModal(); // Replace按钮也只是关闭弹窗
    });
  }, 100);
}

// 格式化日期
function formatDate(dateString) {
  const date = new Date(dateString);
  return date.toLocaleDateString();
}