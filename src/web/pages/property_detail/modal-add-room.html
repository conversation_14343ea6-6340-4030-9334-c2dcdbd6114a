<div class="fixed inset-0 z-50">
  <!-- Dark overlay with click handler to close -->
  <div
    class="fixed inset-0 bg-black bg-opacity-50 pointer-events-auto"
    onclick="document.getElementById('modal').innerHTML = ''"
  ></div>

  <!-- Modal container -->
  <div
    class="fixed inset-0 flex items-center justify-center pointer-events-none"
  >
    <div
      class="bg-white sm:rounded-lg shadow-xl sm:w-9/12 w-full h-full sm:max-h-[90vh] sm:min-h-[70vh] overflow-y-auto pointer-events-auto relative flex flex-col"
    >
      <!-- Modal Header -->
      <div
        class="sticky top-0 px-6 py-4 border-b bg-white z-10 flex justify-between items-center"
      >
        <div>
          <h3 class="text-lg font-semibold text-gray-900">Create Unit</h3>
          <p>Add a unit to this property</p>
        </div>
        <button
          class="p-2 hover:bg-gray-100 rounded-full"
          onclick="document.getElementById('modal').innerHTML = ''"
        >
          <svg
            class="w-5 h-5 text-gray-500"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M6 18L18 6M6 6l12 12"
            />
          </svg>
        </button>
      </div>

      <!-- Modal Body -->
      <div class="px-6 py-4 flex-1">
        <form>
          <div class="flex flex-wrap">
            <div class="w-full flex flex-col sm:flex-row gap-4 mt-4">
              <div class="flex flex-wrap w-full">
                <label class="w-full mb-2" for="room-name">Unit Name *<span id="room-name-error" class="text-red-500 text-xs ml-2 hidden">Required</span></label>
                <input
                  class="p-2 py-4 w-full border border-gray-300 rounded-md focus:outline-none"
                  id="room-name"
                />
              </div>
            </div>
            <div class="w-full mt-8">
              <label class="w-full" for="unit-notes">
                <h2 class="text-lg font-semibold text-gray-900">Unit Notes</h2>
                <p class="text-sm text-gray-500 mb-4">
                  A place to keep notes for internal purposes. Visible to only
                  you and your team.
                </p>
              </label>
              <textarea
                class="p-4 w-full border border-gray-300 rounded-md focus:outline-none h-48 resize-none"
                id="unit-notes"
                placeholder="Add notes about the unit here"
              ></textarea>
            </div>
          </div>
        </form>
      </div>

      <!-- Modal Footer -->
      <div
        class="sticky bottom-0 left-0 right-0 px-6 py-4 bg-gray-50 sm:rounded-b-lg flex justify-between gap-2 border-t mt-auto"
      >
        <button
          class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50"
          onclick="document.getElementById('modal').innerHTML = ''"
        >
          Cancel
        </button>
        <button
          id="create-room-btn"
          class="px-4 py-2 text-sm font-medium text-white bg-slate-800 rounded-md hover:bg-slate-900"
        >
          Create Unit
        </button>
      </div>
    </div>
  </div>
</div>
