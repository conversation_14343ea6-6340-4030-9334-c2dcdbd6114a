{{> landing-header}}
  <body>
    <!-- NAV BAR-->
    {{> landing-navbar}}

    <!-- Main content-->
    <main
      class="flex flex-col items-center min-h-screen w-full bg-white px-4 lg:px-8 mb-16"
    >
      <!-- Hero Banner -->
      <div
        class="w-full max-w-[1376px] mt-0 rounded-2xl p-4 lg:px-16 lg:py-8 overflow-clip"
        style="background: linear-gradient(0deg, #fef6f6 0.44%, #fff 101.4%)"
      >
        <div
          class="flex lg:flex-row flex-col gap-8 lg:gap-16 max-w-[1200px] justify-self-center"
        >
          <div class="flex flex-col w-full lg:w-4/5 gap-8 lg:gap-8">
            <div class="flex flex-col gap-4 mt-16">
              <p class="uppercase font-bold text-sm">Rent Reporting</p>
              <h1
                class="text-2xl lg:text-5xl text-left font-medium leading-snug"
              >
                Take Control of Your Rental Business
              </h1>
            </div>
            <p>
              Reduce rental delinquencies while empowering renters to build
              credit through on-time payment reporting. A win-win for both
              property owners and tenants.
            </p>
            <div class="flex justify-center md:justify-start">
              <a href="/pages/signup/" class="w-full"
                ><button
                  class="px-6 py-3 bg-rmred w-full lg:w-auto hover:bg-rmred-dark text-white rounded-md font-base font-medium cursor-pointer transition-colors duration-100"
                >
                  Try It Now
                </button></a
              >
            </div>
          </div>
          <div
            class="w-full h-auto mt-8 lg:mt-0 lg:h-[444px] overflow-hidden rounded-xl"
          >
            <img
              src="/assets/provider-hero.png"
              class="w-full h-full object-cover"
            />
          </div>
        </div>
      </div>
      <!-- benefits -->
      <div class="flex flex-col gap-8 mt-16 max-w-[1376px] w-full">
        <h2 class="text-3xl text-center font-bold">How You Benefit</h2>
        <div
          class="flex flex-col md:flex-row justify-evenly w-full gap-8 p-4 mb-16 lg:mb-32"
        >
          <div
            class="flex flex-col w-full lg:w-1/3 p-8 gap-8 items-center bg-gray-50 hover:bg-gray-200 transition-colors duration-100 rounded-lg"
          >
            <img src="/assets/landlord-b1.svg" class="w-20 sm:w-28 h-auto" />
            <h4 class="text-2xl lg:text-xl font-bold text-center">
              Boost Rent Payments
            </h4>
            <p class="text-center">
              Tenants are more inclined to pay off any outstanding rental debt
              when informed.
            </p>
          </div>
          <div
            class="flex flex-col w-full lg:w-1/3 p-8 gap-8 items-center bg-gray-50 hover:bg-gray-200 rounded-lg"
          >
            <img src="/assets/landlord-b2.svg" class="w-20 sm:w-28 h-auto" />
            <h4 class="text-2xl lg:text-xl font-bold text-center">
              Report To Equifax
            </h4>
            <p class="text-center">
              On the 15th of every month, we send the month's report to Equifax
              on your behalf.
            </p>
          </div>
          <div
            class="flex flex-col w-full lg:w-1/3 p-8 gap-8 items-center bg-gray-50 hover:bg-gray-200 rounded-lg"
          >
            <img src="/assets/landlord-b3.svg" class="w-20 sm:w-28 h-auto" />
            <h4 class="text-2xl lg:text-xl font-bold text-center">
              Stay Coordinated
            </h4>
            <p class="text-center">
              Work seamlessly with your team to track and update payment
              information.
            </p>
          </div>
        </div>
      </div>

      <!-- Main content -->
      <div class="flex flex-col gap-24 lg:gap-44 w-full max-w-[1288px]">
        <div class="flex flex-col lg:flex-row justify-between w-full">
          <img
            src="/assets/landlord-i1.png"
            class="w-full lg:w-1/2 bg-gray-200 rounded-4xl"
          />
          <div class="w-full lg:max-w-5/12 pt-16 lg:px-6 px-0">
            <h4 class="text-3xl font-bold text-left">
              Non-paying tenants are stressful
            </h4>
            <p class="text-base lg:text-lg pt-8 leading-relaxed">
              Reduce rental delinquencies while empowering renters to build
              credit through on-time payment reporting. A win-win for both
              property owners and tenants!
            </p>
          </div>
        </div>
        <div class="flex flex-col lg:flex-row justify-between w-full">
          <div
            class="w-full lg:max-w-5/12 pt-16 lg:px-6 px-0 order-last lg:order-first"
          >
            <h4 class="text-3xl font-bold text-left">
              Formal records for debt recovery
            </h4>
            <p class="text-base lg:text-lg pt-8 leading-relaxed">
              Keep detailed records of tenant payments to streamline debt
              recovery if issues arise. Accurate documentation helps protect you
              and supports your claims when needed.
            </p>
          </div>
          <img
            src="/assets/landlord-i2.png"
            class="w-full lg:w-1/2 bg-gray-200 rounded-4xl order-first lg:order-last"
          />
        </div>
        <div class="flex flex-col lg:flex-row justify-between w-full">
          <img
            src="/assets/landlord-i3.png"
            class="w-full lg:w-1/2 bg-gray-200 rounded-4xl"
          />
          <div class="w-full lg:max-w-5/12 pt-16 lg:px-6 px-0">
            <h4 class="text-3xl font-bold text-left">
              Streamlined rental report generation
            </h4>
            <p class="text-base lg:text-lg pt-8 leading-relaxed">
              With automated report generation, you only need to log an entry
              when tenants miss a payment—no action is required if payments are
              made as agreed.
            </p>
          </div>
        </div>
      </div>

      <!-- end CTA -->
      <div class="flex flex-col items-center gap-8 lg:gap-16 mt-24">
        <h3 class="text-2xl lg:text-3xl font-bold text-center lg:text-left">
          Start making informed rental decisions
        </h3>
        <a href="/pages/signup/" class="w-full flex justify-center"
          ><button
            class="px-6 py-3 bg-rmred w-full lg:w-auto hover:bg-rmred-dark text-white rounded-md font-base font-medium cursor-pointer transition-colors duration-100"
          >
            Get Started
          </button>
        </a>
      </div>
    </main>

    <!--Footer-->
    {{> landing-footer}}

    <script src="/assets/htmx.min.js"></script>
    <script>


      function toggleFeatures() {
        const featureComp = document.getElementById("feature-comp");
        const grid = document.getElementById("feat-comp-grid");

        if (featureComp && grid) {
          if (
            featureComp.style.maxHeight === "0px" ||
            !featureComp.style.maxHeight
          ) {
            featureComp.style.maxHeight = grid.scrollHeight + "px";
          } else {
            featureComp.style.maxHeight = "0px";
          }
        } else {
          console.error("Element with id 'feature-comp' or 'feat-comp-grid' not found.");
        }
      }

      function toggleFAQ(elem) {
        const parents = elem.parentNode;
        const contentBox = parents.childNodes;

        contentBox[3].classList.toggle("hidden");
        contentBox[1]
          .getElementsByTagName("img")[0]
          .classList.toggle("rotate-180");
      }
    </script>
    <script type="module" src="./main.js"></script>
  </body>
</html>
