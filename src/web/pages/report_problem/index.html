{{> header }}
<body class="min-h-screen bg-gray-200">
  <div class="flex min-h-screen">
    {{> navbar}}

    <!-- Main Content -->
    <main class="w-full bg-slate-100 lg:px-16 md:px-8 px-4 lg:ml-64 pb-20">
      <div class="max-w-[1440px] w-full mx-auto mb-32">
        <h1 class="text-2xl font-semibold mt-8 mb-6">Submit a report to Rental Report Team</h1>

        <!-- Form Section -->
        <div class="bg-white rounded-2xl shadow-xl w-full p-10">
          <form id="reportProblemForm" class="flex flex-col gap-8" enctype="multipart/form-data">
            <!-- Category -->
            <div class="flex flex-col gap-2">
              <label for="problemType" class="font-medium text-gray-700">Category</label>
              <select
                id="problemType"
                name="problemType"
                class="w-full border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-slate-300 focus:border-slate-400 px-4 py-3 text-base bg-white"
                required
              >
                <option value="" disabled selected>Select a category</option>
                <option value="bug">Bug</option>
                <option value="feature">Feature Request</option>
                <option value="feedback">Feedback</option>
                <option value="other">Other</option>
              </select>
            </div>

            <!-- Subject -->
            <div class="flex flex-col gap-2">
              <label for="subject" class="font-medium text-gray-700">Subject</label>
              <input
                type="text"
                id="subject"
                name="subject"
                class="w-full border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-slate-300 focus:border-slate-400 px-4 py-3 text-base"
                placeholder="Enter subject"
                required
              />
            </div>

            <!-- Related Object - Changed from dropdown to text input -->
            <!-- <div class="flex flex-col gap-2">
              <label for="relatedObject" class="font-medium text-gray-700">
                Related Object <span class="text-gray-400 text-sm">(Optional)</span>
              </label>
              <input
                type="text"
                id="relatedObject"
                name="relatedObject"
                class="w-full border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-slate-300 focus:border-slate-400 px-4 py-3 text-base"
                placeholder="Enter related project or object (optional)"
              />
            </div> -->

            <!-- Description -->
            <div class="flex flex-col gap-2">
              <label for="description" class="font-medium text-gray-700">Description</label>
              <textarea
                id="description"
                name="description"
                rows="5"
                class="w-full border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-slate-300 focus:border-slate-400 px-4 py-3 text-base resize-y"
                placeholder="Describe the issue or request..."
                required
                maxlength="1000"
              ></textarea>
              <div class="char-count text-right text-sm text-gray-500 mt-1">
                <span id="charCount">0</span>/1000
              </div>
            </div>

            <!-- Attachments -->
            <div class="flex flex-col gap-2">
              <label for="attachment" class="font-medium text-gray-700">Attachments</label>
              <input
                type="file"
                id="attachment"
                name="attachment"
                class="w-full border border-gray-300 rounded-lg shadow-sm px-4 py-2 text-sm file:border-0 file:rounded-md file:bg-slate-100 file:px-4 file:py-2 file:text-gray-700"
                accept=".jpg,.jpeg,.png,.pdf"
              />
              <span class="text-gray-400 text-sm">Max size 2MB</span>
            </div>

            <!-- Contact Email - Commented out for future use -->
            <!-- <div class="flex flex-col gap-2">
              <label for="contactEmail" class="font-medium text-gray-700">Contact Email</label>
              <input
                type="email"
                id="contactEmail"
                name="contactEmail"
                class="w-full bg-gray-100 border border-gray-300 rounded-lg shadow-sm px-4 py-3 text-base"
                disabled
              />
            </div> -->

            <!-- Submit Button -->
            <div class="flex justify-end">
              <button
                type="submit"
                class="bg-slate-800 hover:bg-slate-900 text-white px-8 py-3 rounded-lg text-base font-medium transition"
              >
                Submit Report
              </button>
            </div>
          </form>
        </div>
      </div>
    </main>
  </div>
<script type="module" src="./main.js"></script>
</body>
</html>
