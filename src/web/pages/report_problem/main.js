import { userApi } from '../../services';
import { renderSidebarNav } from '../../services/sidebar';

document.addEventListener('DOMContentLoaded', async function() {
    try {
        const userData = await userApi.getUserInfo();
        await renderSidebarNav(userData);
    } catch (e) {
    }
    const form = document.getElementById('reportProblemForm');
    const description = document.getElementById('description');
    const charCount = document.getElementById('charCount');
    const attachment = document.getElementById('attachment');
    // const contactEmail = document.getElementById('contactEmail'); // Commented out for future use

    // Initialize user info - Commented out for future use
    // initializeUserInfo();
    // Load related objects - Commented out since related object is now text input
    // loadRelatedObjects();

    // Character count
    description.addEventListener('input', function() {
        const count = this.value.length;
        charCount.textContent = count;
        if (count > 1000) {
            this.value = this.value.substring(0, 1000);
            charCount.textContent = 1000;
        }
    });

    // File upload validation
    attachment.addEventListener('change', function(e) {
        const file = e.target.files[0];
        if (file) {
            const validTypes = ['image/jpeg', 'image/png', 'application/pdf'];
            const maxSize = 2 * 1024 * 1024; // 2MB

            if (!validTypes.includes(file.type)) {
                alert('Please upload a JPG, PNG, or PDF file');
                this.value = '';
                return;
            }

            if (file.size > maxSize) {
                alert('File size cannot exceed 2MB');
                this.value = '';
                return;
            }
        }
    });

    // Form submit
    form.addEventListener('submit', async function(e) {
        e.preventDefault();

        if (!validateForm()) {
            return;
        }

        const formData = new FormData(form);
        try {
            const response = await submitProblem(e);
            if (response.success) {
                showSuccessMessage(response.message || 'Your report has been submitted successfully.');
                form.reset();
                charCount.textContent = '0';
            } else {
                showErrorMessage(response.message || 'Submission failed, please try again later.');
            }
        } catch (error) {
            showErrorMessage(error.message || 'Submission failed, please try again later.');
        }
    });
});

// Initialize user info - Commented out for future use
// async function initializeUserInfo() {
//     try {
//         const response = await fetch('/v1/user', {
//             headers: {
//                 'Authorization': `Bearer ${localStorage.getItem('token')}`
//             }
//         });
//         if (!response.ok) {
//             throw new Error(`HTTP error! status: ${response.status}`);
//         }
//         const data = await response.json();
//         if (data.email) {
//             document.getElementById('contactEmail').value = data.email;
//         }
//     } catch (error) {
//         console.error('Failed to get user info:', error);
//         showErrorMessage('Unable to get user information, please ensure you are logged in');
//     }
// }

// Load related objects - Commented out since related object is now text input
// async function loadRelatedObjects() {
//     try {
//         const response = await fetch('/v1/user/related-objects', {
//             headers: {
//                 'Authorization': `Bearer ${localStorage.getItem('token')}`
//             }
//         });

//         if (!response.ok) {
//             const errorData = await response.json().catch(() => ({}));
//             throw new Error(`HTTP error! status: ${response.status}, message: ${errorData.message || response.statusText}`);
//         }

//         const data = await response.json();
//         // 兼容后端返回结构
//         let objects = [];
//         if (Array.isArray(data.objects)) {
//             objects = data.objects;
//         } else {
//             if (Array.isArray(data.properties)) {
//                 objects = objects.concat(data.properties.map(obj => ({
//                     id: obj.id,
//                     name: obj.name || obj.address,
//                     type: 'property'
//                 })));
//             }
//             if (Array.isArray(data.leases)) {
//                 objects = objects.concat(data.leases.map(obj => ({
//                     id: obj.id,
//                     name: obj.roomName || obj.propertyName || obj.id,
//                     type: 'lease'
//                 })));
//             }
//         }

//         // 更新相关对象列表
//         const objectsList = document.getElementById('relatedObject');
//         if (!objectsList) {
//             console.error('Related object select element not found');
//             return;
//         }

//         objectsList.innerHTML = '<option value="">Please select a related object</option>';

//         if (objects.length > 0) {
//             objects.forEach(obj => {
//                 const option = document.createElement('option');
//                 option.value = obj.id;
//                 option.textContent = `${obj.name} (${obj.type})`;
//                 objectsList.appendChild(option);
//             });
//         } else {
//             const option = document.createElement('option');
//             option.value = '';
//             option.textContent = 'No related objects found';
//             objectsList.appendChild(option);
//         }
//     } catch (error) {
//         console.error('Failed to load related objects:', error);
//         showErrorMessage(`Failed to load related objects: ${error.message}`);
//     }
// }

// Form validation
function validateForm() {
    const subject = document.getElementById('subject').value;
    const problemType = document.getElementById('problemType').value;
    const description = document.getElementById('description').value;

    if (!subject.trim()) {
        showErrorMessage('Please enter a subject');
        return false;
    }

    if (!problemType) {
        showErrorMessage('Please select a category');
        return false;
    }

    if (!description.trim()) {
        showErrorMessage('Please enter a description');
        return false;
    }

    return true;
}

// Submit problem
async function submitProblem(event) {
    event.preventDefault();
    
    try {
        const form = event.target;
        const formData = new FormData();
        
        // 添加必要的表单字段
        formData.append('subject', form.querySelector('#subject').value.trim());
        formData.append('problemType', form.querySelector('#problemType').value);
        formData.append('description', form.querySelector('#description').value.trim());
        
        // 添加可选字段
        const relatedObject = form.querySelector('#relatedObject');
        if (relatedObject && relatedObject.value) {
            formData.append('relatedObject', relatedObject.value);
        }

        // 添加文件
        const fileInput = document.getElementById('attachment');
        if (fileInput.files.length > 0) {
            formData.append('attachment', fileInput.files[0]);
        }

        const response = await fetch('/v1/problems', {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${localStorage.getItem('token')}`
            },
            body: formData
        });

        if (!response.ok) {
            const errorData = await response.json().catch(() => ({}));
            throw new Error(errorData.error || `Submission failed: ${response.status}`);
        }

        const result = await response.json();
        
        if (result.success) {
            showSuccessMessage(result.message || 'Report has been submitted');
            form.reset();
            return result;
        } else {
            throw new Error(result.error || 'Submission failed');
        }
    } catch (error) {
        showErrorMessage(error.message || 'Submission failed, please try again later');
        throw error;
    }
}

// Show success message
function showSuccessMessage(message) {
    alert(message);
}

// Show error message
function showErrorMessage(message) {
    alert(message);
} 