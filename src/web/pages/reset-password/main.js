// Import dependencies
import $ from 'jquery'
import { authApi } from '../../services'
import Mustache from 'mustache'
import errorMessagesTemplate from '../../templates/error-messages.mst?raw'

// Store state
let isLoading = false;

// Page initialization
$(document).ready(function() {
  initResetPasswordForm();
});

// Initialize reset password form
function initResetPasswordForm() {
  const sendCodeBtn = $('#sendCodeBtn');
  const resetPasswordBtn = $('#resetPasswordBtn');
  const resendButton = $('#resendButton');
  const errorMessage = $('#errorMessage');
  const verificationError = $('#verificationError');

  // Initialize password validation
  initPasswordValidation();
  
  // Handle send code button click
  sendCodeBtn.on('click', async function() {
    if (isLoading) return;
    
    const emailInput = $('#email');
    const email = emailInput.val().trim();
    
    // Basic validation
    if (!validateEmail(email)) {
      errorMessage.text('Please enter a valid email address').removeClass('hidden');
      return;
    }
    
    const $button = $(this);
    const originalText = $button.text();
    
    try {
      // Show loading state
      isLoading = true;
      $button.prop('disabled', true).text('Sending...');
      errorMessage.addClass('hidden');
      
      // Call API to send verification code
      await authApi.sendResetCode({ email });
      
      // Show step 2
      $('#step1').addClass('hidden');
      $('#step2').removeClass('hidden');
      $('#userEmail').text(email);
      
    } catch (error) {
      errorMessage.text(error.message || 'Failed to send verification code').removeClass('hidden');
    } finally {
      // Reset loading state
      isLoading = false;
      $button.prop('disabled', false).text(originalText);
    }
  });
  
  // Handle reset password button click
  resetPasswordBtn.on('click', async function() {
    if (isLoading) return;
    
    const email = $('#userEmail').text();
    const code = $('#verificationCode').val().trim();
    const newPassword = $('#newPassword').val();
    const confirmPassword = $('#confirmPassword').val();
    
    // Basic validation
    if (!validateResetForm(code, newPassword, confirmPassword)) {
      return;
    }
    
    const $button = $(this);
    const originalText = $button.text();
    
    try {
      // Show loading state
      isLoading = true;
      $button.prop('disabled', true).text('Resetting...');
      verificationError.addClass('hidden');
      
      // Call API to reset password
      await authApi.resetPassword({
        email,
        code,
        newPassword
      });
      
      // Redirect to login page
      window.location.href = window.config.baseUrl + '/pages/login/';
      
    } catch (error) {
      verificationError.text(error.message || 'Failed to reset password').removeClass('hidden');
    } finally {
      // Reset loading state
      isLoading = false;
      $button.prop('disabled', false).text(originalText);
    }
  });
  
  // Handle resend button click
  resendButton.on('click', async function() {
    if (isLoading) return;
    
    const email = $('#userEmail').text();
    const $button = $(this);
    const originalText = $button.text();
    
    try {
      // Show loading state
      isLoading = true;
      $button.prop('disabled', true).text('Resending...');
      verificationError.addClass('hidden');
      
      // Call API to resend code
      await authApi.sendResetCode({ email });
      
      // Show success message
      verificationError.text('Verification code resent').removeClass('hidden text-red-500').addClass('text-green-500');
      
      // Hide success message after 5 seconds
      setTimeout(() => {
        verificationError.addClass('hidden');
      }, 5000);
      
    } catch (error) {
      verificationError.text(error.message || 'Failed to resend code').removeClass('hidden text-green-500').addClass('text-red-500');
    } finally {
      // Reset loading state
      isLoading = false;
      $button.prop('disabled', false).text(originalText);
    }
  });
}

// Form validation
function validateEmail(email) {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

function validateResetForm(code, newPassword, confirmPassword) {
  const verificationError = $('#verificationError');

  if (!code || code.length !== 6 || !/^\d+$/.test(code)) {
    verificationError.text('Please enter a valid 6-digit verification code').removeClass('hidden');
    return false;
  }

  if (!newPassword || !newPassword.trim()) {
    verificationError.text('Please enter a new password').removeClass('hidden');
    return false;
  }

  // Validate password using the same rules as signup
  if (!isValidPassword(newPassword)) {
    verificationError.text('Password must be at least 8 characters and contain at least 1 alphabetical character (A-Z)').removeClass('hidden');
    return false;
  }

  if (newPassword !== confirmPassword) {
    verificationError.text('Passwords do not match').removeClass('hidden');
    return false;
  }

  return true;
}

// Password validation helper (same as signup page)
function isValidPassword(password) {
  // At least 8 characters
  if (password.length < 8) {
    return false;
  }

  // Contains at least 1 alphabetical character (A-Z)
  const hasAlphabetical = /[A-Za-z]/.test(password);
  if (!hasAlphabetical) {
    return false;
  }

  return true;
}

// Initialize real-time password validation
function initPasswordValidation() {
  const passwordInput = $('#newPassword');
  const confirmPasswordInput = $('#confirmPassword');

  // Add hint text after password input
  if (passwordInput.length && !passwordInput.siblings('.password-hint').length) {
    passwordInput.after('<div class="password-hint text-sm text-gray-600 mt-1">Password must be at least 8 characters and contain at least 1 alphabetical character (A-Z)</div>');
  }

  passwordInput.on('input', function() {
    const password = $(this).val();
    const passwordHint = $(this).siblings('.password-hint');

    if (password.length === 0) {
      // Reset to default hint when empty
      passwordHint.removeClass('text-red-500 text-green-500').addClass('text-gray-600')
        .text('Password must be at least 8 characters and contain at least 1 alphabetical character (A-Z)');
      $(this).removeClass('border-red-500 border-green-500').addClass('border-gray-300');
      return;
    }

    const isValid = isValidPassword(password);

    if (isValid) {
      // Valid password
      passwordHint.removeClass('text-red-500 text-gray-600').addClass('text-green-500')
        .text('✓ Password meets requirements');
      $(this).removeClass('border-red-500 border-gray-300').addClass('border-green-500');
    } else {
      // Invalid password
      passwordHint.removeClass('text-green-500 text-gray-600').addClass('text-red-500')
        .text('Password must be at least 8 characters and contain at least 1 alphabetical character (A-Z)');
      $(this).removeClass('border-green-500 border-gray-300').addClass('border-red-500');
    }

    // Also validate confirm password if it has content
    validateConfirmPassword();
  });

  confirmPasswordInput.on('input', validateConfirmPassword);

  function validateConfirmPassword() {
    const password = passwordInput.val();
    const confirmPassword = confirmPasswordInput.val();

    if (confirmPassword.length === 0) {
      confirmPasswordInput.removeClass('border-red-500 border-green-500').addClass('border-gray-300');
      return;
    }

    if (password === confirmPassword) {
      confirmPasswordInput.removeClass('border-red-500 border-gray-300').addClass('border-green-500');
    } else {
      confirmPasswordInput.removeClass('border-green-500 border-gray-300').addClass('border-red-500');
    }
  }
}