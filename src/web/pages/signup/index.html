{{> landing-header}}
  <body>
    <!-- NAV BAR-->
    {{> landing-navbar}}

    <!-- Main content-->
    <main class="flex flex-col items-center min-h-screen w-full bg-white px-8">
      <div class="flex flex-col items-center mt-16 gap-8 md:w-[552px]">
        <h2 class="font-bold text-2xl">Create an account</h2>
        <p>
          Already have an account?
          <a
            href="{{baseUrl}}/pages/login/"
            class="text-rmred hover:text-rmred-dark font-medium underline"
            >Sign in</a
          >
        </p>
        <!-- <div class="g-signin2 bg-gray-200" data-onsuccess="onSignIn">(placeholder) Google create account</div> -->
        <button
            id="realMasterSignupBtn"
            class="w-full px-6 py-3 bg-rmred hover:bg-rmred-dark text-white rounded-md font-medium cursor-pointer transition-colors duration-100"
        >
            Sign up with RealMaster
        </button>
        <!-- OAuth error message container -->
        <div id="oauthError" class="text-red-500 text-sm text-center hidden"></div>
        <hr class="w-full border-gray-300" />
        <div class="flex flex-col gap-8 w-full">
          <div class="flex flex-col md:flex-row gap-4 w-full">
            <div class="flex flex-col gap-4 w-full">
              <label for="firstName" class="font-medium">First Name <span class="text-red-500">*</span></label>
              <input
                type="text"
                id="firstName"
                name="firstName"
                class="w-full px-4 py-3 border border-gray-300 rounded-md focus:outline-none focus:border-rmred"
                placeholder=""
              />
              <div id="firstNameError" class="text-red-500 text-sm hidden"></div>
            </div>
            <div class="flex flex-col gap-4 w-full">
              <label for="lastName" class="font-medium">Last Name <span class="text-red-500">*</span></label>
              <input
                type="text"
                id="lastName"
                name="lastName"
                class="w-full px-4 py-3 border border-gray-300 rounded-md focus:outline-none focus:border-rmred"
                placeholder=""
              />
              <div id="lastNameError" class="text-red-500 text-sm hidden"></div>
            </div>
          </div>
          <div class="flex flex-col gap-4 w-full">
            <label for="email" class="font-medium">Email <span class="text-red-500">*</span></label>
            <input
              type="email"
              id="email"
              name="email"
              class="w-full px-4 py-3 border border-gray-300 rounded-md focus:outline-none focus:border-rmred"
              placeholder="Enter your email"
            />
            <div id="emailError" class="text-red-500 text-sm hidden"></div>
          </div>
          <div class="flex flex-col gap-4 w-full">
            <label for="password" class="font-medium">Password <span class="text-red-500">*</span></label>
            <input
              type="password"
              id="password"
              name="password"
              class="w-full px-4 py-3 border border-gray-300 rounded-md focus:outline-none focus:border-rmred"
              placeholder="Enter your password"
            />
            <div class="text-sm text-gray-600">
              Password must be at least 8 characters and contain at least 1 alphabetical character (A-Z)
            </div>
            <div id="passwordError" class="text-red-500 text-sm hidden"></div>
          </div>
          <div class="flex flex-col gap-4 w-full">
            <label for="verify-password" class="font-medium"
              >Verify Password <span class="text-red-500">*</span></label
            >
            <input
              type="password"
              id="verify-password"
              name="verify-password"
              class="w-full px-4 py-3 border border-gray-300 rounded-md focus:outline-none focus:border-rmred"
              placeholder="Re-enter your password"
            />
            <div id="verifyPasswordError" class="text-red-500 text-sm hidden"></div>
          </div>
          <!-- Referral Code Section -->
          <div class="flex flex-col gap-2">
            <label for="referral" class="text-gray-600 text-sm"
              >Have a referral code?</label
            >
            <div class="flex gap-2">
              <input
                type="text"
                id="referral"
                name="referral"
                placeholder="Enter code"
                class="w-full px-4 py-3 border border-gray-300 rounded-md focus:outline-none focus:border-rmred"
              />
              <button
                class="px-4 py-2 bg-rmred hover:bg-rmred-dark text-white rounded-md font-medium transition-colors duration-200"
              >
                Apply
              </button>
            </div>
          </div>
          <div class="flex flex-col gap-4 w-full">
            <label class="font-medium">Account Type</label>
            <div class="flex gap-8">
              <div class="flex items-center gap-2">
                <input
                  type="radio"
                  id="renter"
                  name="account-type"
                  value="renter"
                  class="accent-rmred w-4 h-4 cursor-pointer"
                />
                <label for="renter" class="cursor-pointer">Renter</label>
              </div>
              <div class="flex items-center gap-2">
                <input
                  type="radio"
                  id="provider"
                  name="account-type"
                  value="provider"
                  class="accent-rmred w-4 h-4 cursor-pointer"
                  checked
                />
                <label for="provider" class="cursor-pointer">Property Provider</label>
              </div>
            </div>
          </div>

          <div class="flex items-start gap-2">
            <input
              type="checkbox"
              id="terms"
              name="terms"
              class="w-4 h-4 accent-rmred cursor-pointer mt-1"
            />
            <label for="terms" class="text-gray-700 cursor-pointer">
              I agree to the
              <a href="/pages/tac.html" class="text-rmred hover:text-rmred-dark underline">Terms and Conditions</a>
              and
              <a href="/pages/privacypolicy.html" class="text-rmred hover:text-rmred-dark underline">Privacy Policy</a>
            </label>
          </div>
          <button
            class="w-full px-6 py-3 bg-rmred hover:bg-rmred-dark text-white rounded-md font-medium cursor-pointer transition-colors duration-100"
          >
            Create Account
          </button>
          <!-- Error message container -->
          <div id="errorMessage" class="text-rmred text-center hidden"></div>
        </div>
      </div>
    </main>

    <!--Footer-->
    {{> landing-footer}}
<script type="module" src="./main.js"></script>
