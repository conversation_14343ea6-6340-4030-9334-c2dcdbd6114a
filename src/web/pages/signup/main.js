// Import dependencies
import $ from 'jquery'
import { authApi } from '../../services'
import { API_BASE_URL, ENDPOINTS, STATUS } from '../../services/config'

// Store state
let isLoading = false;

// Page initialization
$(document).ready(function() {

  // 自动填充邮箱（如有email参数）
  const urlParams = new URLSearchParams(window.location.search);
  const invitationEmail = urlParams.get('email');
  if (invitationEmail) {
    $('#email').val(invitationEmail);
  }

  // Initialize form handlers
  initSignupForm();
  handleOAuthSignup();
  initPasswordValidation();

  // Add Enter key support for signup form
  $('#firstName, #lastName, #email, #password, #verify-password').on('keypress', function(event) {
    if (event.which === 13) { // Enter key
      event.preventDefault();
      $('button:contains("Create Account")').click();
    }
  });

  // 推荐码Apply按钮事件
  $(document).on('click', 'button:contains("Apply")', async function(event) {
    // 只处理推荐码Apply按钮
    if ($(this).text().trim() !== 'Apply') return;
    event.preventDefault();
    const code = $('#referral').val().trim();
    let $msg = $('#referral-validate-msg');
    if ($msg.length === 0) {
      $msg = $('<div id="referral-validate-msg" class="mt-2 text-sm"></div>');
      $(this).parent().append($msg);
    }
    if (!code) {
      $msg.text('Please enter a referral code.').removeClass('text-green-600').addClass('text-red-600').show();
      return;
    }
    try {
      const res = await fetch(`/v1/referral-codes/${encodeURIComponent(code)}/validation`, {
        method: 'GET'
      });
      const data = await res.json();
      if (data.valid) {
        $msg.text('Referral code is valid!').removeClass('text-red-600').addClass('text-green-600').show();
      } else {
        $msg.text(data.error || 'Invalid referral code.').removeClass('text-green-600').addClass('text-red-600').show();
      }
    } catch (e) {
      $msg.text('Network error, please try again.').removeClass('text-green-600').addClass('text-red-600').show();
    }
  });
});

// Initialize signup form
function initSignupForm() {
  $('button:contains("Create Account")').on('click', async function(event) {
    event.preventDefault();
    
    if (isLoading) return;
    
    const firstNameInput = $('#firstName');
    const lastNameInput = $('#lastName');
    const emailInput = $('#email');
    const passwordInput = $('#password');
    const verifyPasswordInput = $('#verify-password');
    const termsCheckbox = $('#terms');
    const accountType = $('input[name="account-type"]:checked');
    const referralCode = $('#referral');

    // Basic validation
    if (!validateSignupForm(firstNameInput.val(), lastNameInput.val(), emailInput.val(), passwordInput.val(), verifyPasswordInput.val(), termsCheckbox.prop('checked'), accountType.val())) {
      return;
    }
    
    const $button = $(this);
    const originalText = $button.text();
    
    try {
      // Show loading state
      isLoading = true;
      $button.prop('disabled', true).text('Creating account...');
      clearFieldErrors();
      
      // 组合first name和last name成为username
      const username = `${firstNameInput.val().trim()} ${lastNameInput.val().trim()}`;

      const response = await authApi.signup({
        email: emailInput.val(),
        password: passwordInput.val(),
        username: username,
        accountType: accountType.val(),
        viewType: accountType.val() === 'provider' ? 'landlord' : 'tenant',
        referralCode: referralCode.val() || undefined
      });
      
      // 获取URL参数
      const urlParams = new URLSearchParams(window.location.search);
      let verificationUrl = window.config.baseUrl + '/pages/verification/?email=' + encodeURIComponent(emailInput.val());
      if (urlParams.get('fromInvitation') === '1' && urlParams.get('code')) {
        verificationUrl += '&fromInvitation=1&code=' + encodeURIComponent(urlParams.get('code'));
      }
      // 跳转到邮箱验证页面，带上邀请参数
      window.location.href = verificationUrl;
      
    } catch (error) {
      // Handle specific error types and show them in appropriate locations
      handleSignupError(error.message || 'Signup failed. Please try again.');
    } finally {
      // Reset loading state
      isLoading = false;
      $button.prop('disabled', false).text(originalText);
    }
  });
}

// Form validation
function validateSignupForm(firstName, lastName, email, password, verifyPassword, termsAccepted, accountType) {
  // 清除所有之前的错误信息
  clearFieldErrors();

  let hasErrors = false;

  // 验证First Name
  if (!firstName || !firstName.trim()) {
    showFieldError('firstName', 'First name is required.');
    hasErrors = true;
  }

  // 验证Last Name
  if (!lastName || !lastName.trim()) {
    showFieldError('lastName', 'Last name is required.');
    hasErrors = true;
  }

  // 验证Email
  if (!email || !email.trim()) {
    showFieldError('email', 'Email is required.');
    hasErrors = true;
  } else if (!isValidEmail(email)) {
    showFieldError('email', 'Please enter a valid email address.');
    hasErrors = true;
  }

  // 验证Password
  if (!password || !password.trim()) {
    showFieldError('password', 'Password is required.');
    hasErrors = true;
  } else if (!isValidPassword(password)) {
    showFieldError('password', 'Password must be at least 8 characters and contain at least 1 alphabetical character (A-Z).');
    hasErrors = true;
  }

  // 验证Verify Password
  if (!verifyPassword || !verifyPassword.trim()) {
    showFieldError('verifyPassword', 'Please confirm your password.');
    hasErrors = true;
  } else if (password !== verifyPassword) {
    showFieldError('verifyPassword', 'Passwords do not match. Please make sure both password fields are identical.');
    hasErrors = true;
  }

  // 验证Account Type
  if (!accountType) {
    $('#errorMessage').text('Please select an account type.').removeClass('hidden');
    hasErrors = true;
  }

  // 验证Terms
  if (!termsAccepted) {
    $('#errorMessage').text('You need to accept the Terms of Service to continue.').removeClass('hidden');
    hasErrors = true;
  }

  return !hasErrors;
}

// 显示字段错误信息
function showFieldError(fieldName, message) {
  const errorDiv = $(`#${fieldName}Error`);
  const inputField = $(`#${fieldName}`);

  errorDiv.text(message).removeClass('hidden');
  inputField.addClass('border-red-500').removeClass('border-gray-300');
}

// 清除所有字段错误信息
function clearFieldErrors() {
  const fields = ['firstName', 'lastName', 'email', 'password', 'verifyPassword'];
  fields.forEach(field => {
    $(`#${field}Error`).addClass('hidden');
    $(`#${field}`).removeClass('border-red-500').addClass('border-gray-300');
  });
  $('#errorMessage, #oauthError').addClass('hidden');
}

// Email validation helper
function isValidEmail(email) {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

// Password validation helper
function isValidPassword(password) {
  // At least 8 characters
  if (password.length < 8) {
    return false;
  }

  // Contains at least 1 alphabetical character (A-Z)
  const hasAlphabetical = /[A-Za-z]/.test(password);
  if (!hasAlphabetical) {
    return false;
  }

  return true;
}

// Handle signup errors and display them in appropriate locations
function handleSignupError(errorMessage) {
  // Email-related errors
  if (errorMessage.includes('Email is already registered') ||
      errorMessage.includes('Please enter a valid email address') ||
      errorMessage.includes('email')) {
    showFieldError('email', errorMessage);
    return;
  }

  // Password-related errors
  if (errorMessage.includes('Password') && errorMessage.includes('required')) {
    showFieldError('password', errorMessage);
    return;
  }

  // General validation errors
  if (errorMessage.includes('Email and password are required')) {
    // This is a general validation error, show in general area
    $('#errorMessage').text(errorMessage).removeClass('hidden');
    return;
  }

  // Default: show in general error area
  $('#errorMessage').text(errorMessage).removeClass('hidden');
}

// Initialize real-time password validation
function initPasswordValidation() {
  const passwordInput = $('#password');
  const passwordHint = passwordInput.siblings('.text-sm.text-gray-600');

  passwordInput.on('input', function() {
    const password = $(this).val();

    if (password.length === 0) {
      // Reset to default hint when empty
      passwordHint.removeClass('text-red-500 text-green-500').addClass('text-gray-600')
        .text('Password must be at least 8 characters and contain at least 1 alphabetical character (A-Z)');
      $(this).removeClass('border-red-500 border-green-500').addClass('border-gray-300');
      return;
    }

    const isValid = isValidPassword(password);

    if (isValid) {
      // Valid password
      passwordHint.removeClass('text-red-500 text-gray-600').addClass('text-green-500')
        .text('✓ Password meets requirements');
      $(this).removeClass('border-red-500 border-gray-300').addClass('border-green-500');
    } else {
      // Invalid password - show specific requirements
      let errorMsg = 'Password requirements: ';
      const requirements = [];

      if (password.length < 8) {
        requirements.push('at least 8 characters');
      }

      if (!/[A-Za-z]/.test(password)) {
        requirements.push('at least 1 alphabetical character (A-Z)');
      }

      errorMsg += requirements.join(', ');

      passwordHint.removeClass('text-green-500 text-gray-600').addClass('text-red-500')
        .text(errorMsg);
      $(this).removeClass('border-green-500 border-gray-300').addClass('border-red-500');
    }
  });
}

// Initialize OAuth login
function handleOAuthSignup() {
    if (isLoading) return;
  
    // OAuth
    $('#realMasterSignupBtn').on('click', function(event) {
      event.preventDefault();
      
      try {
        isLoading = true;
        $(this).prop('disabled', true);
        clearFieldErrors();
        
        // Direct redirect to OAuth endpoint
        window.location.href = `${API_BASE_URL}${ENDPOINTS.AUTH.OAUTH_LOGIN}`;
        
      } catch (error) {
        // Show error message in OAuth error container
        $('#oauthError').text(error.message || 'OAuth login failed. Please try again.').removeClass('hidden');

        // Reset loading state only if redirect fails
        isLoading = false;
        $(this).prop('disabled', false);
      }
    });
  }