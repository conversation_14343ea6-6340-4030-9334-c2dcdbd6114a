// Import dependencies
import $ from 'jquery'
import { authApi } from '../../services'
import Mustache from 'mustache'
import errorMessagesTemplate from '../../templates/error-messages.mst?raw'

// Store state
let isLoading = false;

// Page initialization
$(document).ready(function() {
  initVerificationForm();
  displayUserEmail();
});

// Display user email from URL parameter
function displayUserEmail() {
  const urlParams = new URLSearchParams(window.location.search);
  const email = urlParams.get('email');
  if (email) {
    $('#userEmail').text(email);
  }
}

// Initialize verification form
function initVerificationForm() {
  const verificationInput = $('#verificationCode');
  const verifyButton = $('#verifyButton');
  const resendButton = $('#resendButton');
  const errorMessage = $('#errorMessage');
  
  // Focus on input when page loads
  verificationInput.focus();

  // Handle verify button click
  verifyButton.on('click', function() {
    submitVerificationCode();
  });

  // Handle Enter key on verification code input
  verificationInput.on('keypress', function(event) {
    if (event.which === 13) {
      submitVerificationCode();
    }
  });

  // Handle input to ensure only numbers
  verificationInput.on('input', function(event) {
    const value = event.target.value;
    const numericValue = value.replace(/[^0-9]/g, '');
    $(this).val(numericValue);
  });

  // Handle resend button click
  resendButton.on('click', function() {
    resendVerificationCode();
  });
}

// Resend verification code
async function resendVerificationCode() {
  if (isLoading) return;
  
  const errorMessage = $('#errorMessage');
  const resendButton = $('#resendButton');
  const email = new URLSearchParams(window.location.search).get('email');
  
  if (!email) {
    errorMessage.text('Email not found. Please return to login.').removeClass('hidden');
    return;
  }
  
  try {
    isLoading = true;
    resendButton.prop('disabled', true);
    
    // Call the resend code API using the authApi service
    await authApi.resendCode({ email });
    
    // Show success message
    errorMessage.text('Verification code resent').removeClass('hidden text-red-500').addClass('text-green-500');
    
    // Hide success message after 5 seconds
    setTimeout(() => {
      errorMessage.addClass('hidden');
    }, 5000);
    
  } catch (error) {
    errorMessage.text(error.message || 'Failed to resend code').removeClass('hidden text-green-500').addClass('text-red-500');
  } finally {
    isLoading = false;
    resendButton.prop('disabled', false);
  }
}

// Submit verification code
async function submitVerificationCode() {
  if (isLoading) return;

  const verificationInput = $('#verificationCode');
  const errorMessage = $('#errorMessage');
  const verifyButton = $('#verifyButton');
  const code = verificationInput.val().trim();
  const email = new URLSearchParams(window.location.search).get('email');
  
  // Basic validation
  if (!email) {
    errorMessage.text('Email not found. Please return to login.').removeClass('hidden');
    return;
  }
  
  if (!code || code.length !== 6 || !/^\d+$/.test(code)) {
    errorMessage.text('Please enter a valid 6-digit verification code').removeClass('hidden');
    verificationInput.focus();
    return;
  }

  try {
    isLoading = true;
    verifyButton.prop('disabled', true);
    
    // Call the API to verify the code using the authApi service
    await authApi.verifyEmail({ email, code });
    // 检查URL参数，决定跳转
    const urlParams = new URLSearchParams(window.location.search);
    if (urlParams.get('fromInvitation') === '1' && urlParams.get('code')) {
      const code = urlParams.get('code');
      // 获取invitation信息
      try {
        const invRes = await fetch('/v1/invitations/verify?code=' + encodeURIComponent(code));
        if (invRes.ok) {
          const inv = await invRes.json();
          // 根据type决定行为
          if (inv.type === 'landlord_to_tenant') {
            // 更新invitation状态
            await fetch(`/v1/invitations/${encodeURIComponent(code)}/status`, {
              method: 'PUT',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify({ status: 'active', leaseId: inv.leaseId })
            });
            // 切换viewType为tenant
            await import('../../services').then(({ userApi }) => userApi.updateViewType('tenant'));
            // 跳转到tenant view
            window.location.href = window.config.baseUrl + '/pages/leases/';
            return;
          } else if (inv.type === 'tenant_to_landlord') {
            // 切换viewType为landlord
            await import('../../services').then(({ userApi }) => userApi.updateViewType('landlord'));
            // 跳转到property创建页
            window.location.href = window.config.baseUrl + '/pages/properties/?autoOpen=1&fromInvitation=1&code=' + encodeURIComponent(code);
            return;
          }
        }
      } catch (e) {
        // ignore, fallback to home
      }
    }
    window.location.href = window.config.baseUrl + '/pages/home/';
    
  } catch (error) {
    errorMessage.text(error.message || 'Invalid verification code').removeClass('hidden');
    verificationInput.focus();
    
  } finally {
    isLoading = false;
    verifyButton.prop('disabled', false);
  }
} 