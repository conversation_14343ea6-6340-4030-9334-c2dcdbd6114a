<header class="sticky top-0 z-50">
  <!-- Desktop Nav -->
  <nav
    class="flex w-full lg:justify-center justify-between bg-gray-50 px-8 py-2"
    style="box-shadow: 0 0 0 1px rgb(237, 237, 237)"
  >
    <div class="flex justify-between w-full max-w-[1376px] h-v">
      <div class="flex items-center">
        <nav class="flex gap-4 items-center text-sm">
          <!-- Mobile: Hamburger button first -->
          <button
            onClick="openDrawer()"
            class="lg:hidden p-2 rounded-md hover:bg-gray-100"
          >
            <svg
              class="w-6 h-6"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M4 6h16M4 12h16M4 18h16"
              />
            </svg>
          </button>
          <!-- Desktop: Logo on left with nav items -->
          <a href="{{baseUrl}}/" class="hidden lg:block mr-4">
            <img src="/assets/logo-graphic.svg" class="max-h-8" />
          </a>
          <a
            href="{{baseUrl}}/pages/providers/"
            class="hidden lg:block hover:bg-gray-200 py-3 px-4 rounded-md font-medium"
            >Housing Providers</a
          >
          <a
            href="{{baseUrl}}/pages/renters/"
            class="hidden lg:block hover:bg-gray-200 py-3 px-4 rounded-md font-medium"
            >Renters</a
          >
          <a
            href="{{baseUrl}}/pages/pricing/"
            class="hidden lg:block hover:bg-gray-200 py-3 px-4 rounded-md font-medium"
            >Pricing</a
          >
          <a
            href="{{baseUrl}}/pages/blog/"
            class="hidden lg:block hover:bg-gray-200 py-3 px-4 rounded-md font-medium"
            >Blog</a
          >
        </nav>
      </div>
      <div class="flex items-center">
        <!-- Mobile: Logo on right -->
        <a href="{{baseUrl}}/" class="lg:hidden flex items-center h-12 w-32">
          <img src="/assets/logo-graphic.svg" alt="" class="w-full h-auto" />
        </a>
        <!-- Desktop: Login/Signup buttons -->
        <div class="hidden lg:flex gap-8 items-center text-sm h-4 py-6">
          <div class="min-w-[1px] py-4 bg-gray-300 h-full"></div>
          <a
            href="{{baseUrl}}/pages/login/"
            class="text-gray-700 hover:text-rmred-dark font-bold"
            >Log In</a
          >

          <a href="{{baseUrl}}/pages/signup/"
            ><button
              class="px-4 py-3 bg-rmred hover:bg-rmred-dark text-white rounded-md font-base font-medium cursor-pointer transition-colors duration-100"
            >
              Get Started
            </button></a
          >
        </div>
      </div>
    </div>
  </nav>

  <!-- Mobile drawer -->
  <div
    id="mobile-nav"
    class="flex flex-col absolute w-full h-0 overflow-hidden transition-all duration-400 ease-in-out lg:hidden z-50"
  >
    <div class="bg-white w-full px-8 py-2 h-full">
      <a href="{{baseUrl}}/pages/providers/"
        ><button class="w-full bg-white min-h-16 text-left">
          Housing Providers
        </button>
      </a>
      <a href="{{baseUrl}}/pages/renters/"
        ><button class="w-full bg-white min-h-16 text-left">Renters</button></a
      >
      <a href="{{baseUrl}}/pages/pricing/"
        ><button class="w-full bg-white min-h-16 text-left">Pricing</button></a
      >
      <a href="{{baseUrl}}/pages/blog/"
        ><button class="w-full bg-white min-h-16 text-left">Blog</button></a
      >
      <a href="{{baseUrl}}/pages/login/"
        ><button
          class="mt-8 px-4 py-3 w-full bg-white border-1 border-rmred hover:bg-gray-50 text-rmred rounded-md font-base font-medium cursor-pointer transition-colors duration-100"
        >
          Log in
        </button></a
      >
      <a href="{{baseUrl}}/pages/signup/"
        ><button
          class="mt-8 w-full px-6 py-3 bg-rmred hover:bg-rmred-dark text-white rounded-md font-medium cursor-pointer transition-colors duration-100"
        >
          Sign Up
        </button></a
      >
    </div>
  </div>
</header>

<script>
  function openDrawer() {
    const drawer = document.getElementById("mobile-nav")

    if (drawer.classList.contains("h-0")) {
      drawer.classList.add("h-screen")
      drawer.classList.remove("h-0")
    } else {
      drawer.classList.add("h-0")
      drawer.classList.remove("h-screen")
    }
  }
</script>
