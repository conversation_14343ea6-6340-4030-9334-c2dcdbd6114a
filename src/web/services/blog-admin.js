// Blog administration utilities
// Helper functions for managing blog posts

export class BlogAdmin {
  /**
   * Generate a template for a new blog post main.js file
   * @param {string} slug - The slug of the new blog post
   * @returns {string} - The JavaScript code for the main.js file
   */
  static generateMainJsTemplate(slug) {
    return `import { redirectLoggedInUsersToHome } from '../../../services/util.js';
import blogService from '../../../services/blog.js';
import { BlogUI } from '../../../services/blog-ui.js';

// Get current post slug from URL
const currentSlug = '${slug}';

async function loadRelatedArticles() {
  try {
    const container = document.getElementById('related-articles');
    if (!container) return;

    // Get related posts from blog service
    const relatedPosts = await blogService.getRelatedPosts(currentSlug, 4);
    
    if (relatedPosts.length === 0) {
      container.innerHTML = '<p class="text-gray-500">No related articles found.</p>';
      return;
    }

    // Get base URL for navigation
    const baseUrl = BlogUI.getBaseUrl();

    // Generate HTML for related articles
    const articlesHTML = relatedPosts.map(post => 
      BlogUI.createRelatedArticleCard(post, baseUrl)
    ).join('');
    
    container.innerHTML = articlesHTML;
  } catch (error) {
    console.error('Error loading related articles:', error);
  }
}

// Check if user is logged in and redirect to home if they are
document.addEventListener('DOMContentLoaded', async () => {
  try {
    await redirectLoggedInUsersToHome();

    // Load related articles
    await loadRelatedArticles();
  } catch (error) {
    console.error('Error during page initialization:', error);
  }
});`;
  }

  /**
   * Generate instructions for adding a new blog post
   * @param {Object} postData - The blog post data
   * @returns {string} - Instructions for adding the post
   */
  static generateAddPostInstructions(postData) {
    return `
To add a new blog post "${postData.title}":

1. Add the post data to src/web/services/blog.js in the posts array:
   {
     id: ${postData.id},
     slug: '${postData.slug}',
     title: '${postData.title}',
     excerpt: '${postData.excerpt}',
     image: '${postData.image}',
     date: '${postData.date}',
     readTime: '${postData.readTime}',
     tags: ${JSON.stringify(postData.tags)},
     featured: ${postData.featured}
   }

2. Create directory: src/web/pages/blog/${postData.slug}/

3. Create main.js file with the template provided by BlogAdmin.generateMainJsTemplate()

4. Create index.html file with proper structure including:
   - Content area for the article
   - Sidebar with "Suggested Articles" section:
     <div class="bg-white p-8 rounded-md flex flex-col gap-8">
       <span class="text-xl font-medium">Suggested Articles</span>
       <div id="related-articles" class="flex flex-col gap-4">
         <!-- Related articles will be loaded here dynamically -->
       </div>
     </div>

5. The system will automatically:
   - Show the post in the main blog page
   - Generate related articles for all content pages
   - Handle pagination
   - Maintain proper navigation
`;
  }

  /**
   * Validate blog post data
   * @param {Object} postData - The blog post data to validate
   * @returns {Object} - Validation result with isValid and errors
   */
  static validatePostData(postData) {
    const errors = [];
    
    if (!postData.slug || typeof postData.slug !== 'string') {
      errors.push('Slug is required and must be a string');
    }
    
    if (!postData.title || typeof postData.title !== 'string') {
      errors.push('Title is required and must be a string');
    }
    
    if (!postData.excerpt || typeof postData.excerpt !== 'string') {
      errors.push('Excerpt is required and must be a string');
    }
    
    if (!postData.image || typeof postData.image !== 'string') {
      errors.push('Image is required and must be a string');
    }
    
    if (!postData.date || typeof postData.date !== 'string') {
      errors.push('Date is required and must be a string');
    }
    
    if (!postData.readTime || typeof postData.readTime !== 'string') {
      errors.push('Read time is required and must be a string');
    }
    
    if (postData.tags && !Array.isArray(postData.tags)) {
      errors.push('Tags must be an array');
    }
    
    if (postData.featured !== undefined && typeof postData.featured !== 'boolean') {
      errors.push('Featured must be a boolean');
    }
    
    return {
      isValid: errors.length === 0,
      errors
    };
  }
}

// Example usage:
// console.log(BlogAdmin.generateMainJsTemplate('my-new-post'));
// console.log(BlogAdmin.generateAddPostInstructions({
//   id: 4,
//   slug: 'my-new-post',
//   title: 'My New Post',
//   excerpt: 'This is a new post about...',
//   image: '/assets/my-image.jpg',
//   date: 'January 20, 2025',
//   readTime: '3 min read',
//   tags: ['example', 'tutorial'],
//   featured: false
// }));
