// Blog UI components and utilities
// Shared UI components for blog pages

export class BlogUI {
  // Create a blog card for the main blog page
  static createBlogCard(post, baseUrl = '') {
    return `
      <a href="${baseUrl}/pages/blog/${post.slug}/" class="block">
        <div class="bg-white rounded-lg shadow-sm hover:shadow-xl cursor-pointer h-[416px] transition-all duration-300 overflow-hidden transform hover:-translate-y-1">
          <div class="h-48 overflow-hidden">
            <img src="${post.image}" alt="${post.title}" class="w-full h-full object-cover transition-transform duration-300 hover:scale-105" />
          </div>
          <div class="p-6 flex flex-col h-[256px]">
            <h2 class="text-xl font-semibold mb-4 leading-tight" style="display: -webkit-box; -webkit-line-clamp: 2; -webkit-box-orient: vertical; overflow: hidden;">${post.title}</h2>
            <p class="text-gray-600 text-sm mb-4 flex-grow leading-relaxed" style="display: -webkit-box; -webkit-line-clamp: 3; -webkit-box-orient: vertical; overflow: hidden;">${post.excerpt}</p>
            <div class="flex justify-between items-center mt-auto">
              <span class="text-sm text-gray-500">${post.date} | ${post.readTime}</span>
              <strong class="text-right text-rmred text-sm">Read more</strong>
            </div>
          </div>
        </div>
      </a>
    `;
  }

  // Create a related article card for content pages
  static createRelatedArticleCard(post, baseUrl = '') {
    return `
      <a href="${baseUrl}/pages/blog/${post.slug}/" class="block">
        <div class="bg-gray-100 flex flex-row p-3 gap-4 items-center rounded hover:bg-gray-50 transition-colors duration-200" style="min-height: 80px;">
          <div class="overflow-hidden rounded" style="width: 64px; height: 64px; flex-shrink: 0;">
            <img src="${post.image}" alt="${post.title}" style="width: 100%; height: 100%; object-fit: cover;" />
          </div>
          <div class="flex flex-col gap-2" style="flex: 1; min-width: 0;">
            <p class="font-medium text-base" style="display: -webkit-box; -webkit-line-clamp: 2; -webkit-box-orient: vertical; overflow: hidden; line-height: 1.25;">${post.title}</p>
            <div class="flex flex-row gap-2 text-sm text-gray-500" style="flex-shrink: 0;">
              <p>${post.date}</p>
              <p>|</p>
              <p>${post.readTime}</p>
            </div>
          </div>
        </div>
      </a>
    `;
  }

  // Create pagination controls
  static createPagination(totalPosts, currentPage, postsPerPage, onPageClick = 'loadPage') {
    const totalPages = Math.ceil(totalPosts / postsPerPage);

    if (totalPages <= 1) {
      return '';
    }

    let paginationHTML = '';

    for (let i = 1; i <= totalPages; i++) {
      const activeClass = i === currentPage ? 'btn-active' : '';
      paginationHTML += `
        <button class="join-item btn ${activeClass}" onclick="${onPageClick}(${i})">${i}</button>
      `;
    }

    return paginationHTML;
  }

  // Get base URL for navigation
  static getBaseUrl() {
    // Try to extract base URL from existing links or use current origin
    const existingLink = document.querySelector('a[href*="/pages/"]');
    if (existingLink) {
      return existingLink.href.split('/pages/')[0];
    }
    return window.location.origin;
  }

  // Add CSS styles for line clamping
  static addLineClampStyles() {
    if (document.getElementById('blog-line-clamp-styles')) return;

    const style = document.createElement('style');
    style.id = 'blog-line-clamp-styles';
    style.textContent = `
      .line-clamp-2 {
        display: -webkit-box;
        -webkit-line-clamp: 2;
        line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
      }
      .line-clamp-3 {
        display: -webkit-box;
        -webkit-line-clamp: 3;
        line-clamp: 3;
        -webkit-box-orient: vertical;
        overflow: hidden;
      }
    `;
    document.head.appendChild(style);
  }

  // Initialize common blog page functionality
  static init() {
    this.addLineClampStyles();
  }
}

// Auto-initialize when module is loaded
BlogUI.init();
