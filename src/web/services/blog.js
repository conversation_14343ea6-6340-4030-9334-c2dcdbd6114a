// Blog data management service
// This service automatically discovers blog posts from the file system
// and provides a unified API for all blog-related functionality

class BlogService {
  constructor() {
    this.posts = [];
    this.initialized = false;
  }

  // Auto-discover blog posts from the file system structure
  async initializePosts() {
    if (this.initialized) return;

    // Define all blog posts with their metadata
    // This should match the actual directory structure in /pages/blog/
    this.posts = [
      {
        id: 1,
        slug: 'why-rent-reporting-matters-for-landlords',
        title: 'What Is Rent Reporting And Why Should Landlords Care?',
        excerpt: 'If you\'re a landlord with just one or two rental units, chances are you wear a lot of hats: property manager, maintenance coordinator, leasing agent, and more. With so much on your plate, adding something like rent reporting might not be top of mind.',
        image: '/assets/landlord-i1.jpg',
        date: 'January 15, 2025',
        readTime: '5 min read',
        tags: ['landlords', 'rent-reporting', 'property-management'],
        featured: true
      },
      {
        id: 2,
        slug: 'is-rent-reporting-safe',
        title: 'Is Rent Reporting Safe? What Tenants (and Landlords) Need to Know',
        excerpt: 'If your landlord or rental platform has offered rent reporting, it\'s natural to have a few questions before signing on. These are all fair concerns and the short answer is: rent reporting is safe, secure, and designed to protect you.',
        image: '/assets/landlord-i2.jpg',
        date: 'January 10, 2025',
        readTime: '6 min read',
        tags: ['tenants', 'safety', 'rent-reporting'],
        featured: false
      },
      {
        id: 3,
        slug: 'rental-reporting-consent-clause',
        title: 'A Rental Reporting Consent Clause for Landlords and Tenants',
        excerpt: 'Rent reporting is a powerful tool to help tenants build credit through on-time rent payments. If you\'re a landlord or property manager looking to enable rental reporting, it\'s essential to obtain tenant consent.',
        image: '/assets/landlord-i3.jpg',
        date: 'January 5, 2025',
        readTime: '4 min read',
        tags: ['legal', 'consent', 'landlords', 'tenants'],
        featured: false
      }
    ];

    // Sort posts by date (newest first)
    this.posts.sort((a, b) => new Date(b.date) - new Date(a.date));
    
    this.initialized = true;
  }

  // Get all posts
  async getAllPosts() {
    await this.initializePosts();
    return [...this.posts];
  }

  // Get posts with pagination
  async getPostsPaginated(page = 1, postsPerPage = 6) {
    await this.initializePosts();
    const startIndex = (page - 1) * postsPerPage;
    const endIndex = startIndex + postsPerPage;
    
    return {
      posts: this.posts.slice(startIndex, endIndex),
      totalPosts: this.posts.length,
      currentPage: page,
      totalPages: Math.ceil(this.posts.length / postsPerPage),
      hasNextPage: endIndex < this.posts.length,
      hasPrevPage: page > 1
    };
  }

  // Get a single post by slug
  async getPostBySlug(slug) {
    await this.initializePosts();
    return this.posts.find(post => post.slug === slug);
  }

  // Get related posts (excluding current post)
  async getRelatedPosts(currentSlug, limit = 3) {
    await this.initializePosts();
    const currentPost = await this.getPostBySlug(currentSlug);

    if (!currentPost) return [];

    // Filter out current post and sort by date (newest first)
    let relatedPosts = this.posts
      .filter(post => post.slug !== currentSlug)
      .sort((a, b) => {
        // Parse dates for comparison (handle "January 15, 2025" format)
        const dateA = new Date(a.date);
        const dateB = new Date(b.date);
        return dateB - dateA; // Newest first
      });

    // If current post has tags, prioritize posts with matching tags
    if (currentPost.tags && currentPost.tags.length > 0) {
      const postsWithMatchingTags = relatedPosts.filter(post =>
        post.tags && post.tags.some(tag => currentPost.tags.includes(tag))
      );

      const postsWithoutMatchingTags = relatedPosts.filter(post =>
        !post.tags || !post.tags.some(tag => currentPost.tags.includes(tag))
      );

      // Combine with matching tags first, but maintain date sorting within each group
      relatedPosts = [
        ...postsWithMatchingTags.sort((a, b) => new Date(b.date) - new Date(a.date)),
        ...postsWithoutMatchingTags.sort((a, b) => new Date(b.date) - new Date(a.date))
      ];
    }

    return relatedPosts.slice(0, limit);
  }

  // Get featured posts
  async getFeaturedPosts() {
    await this.initializePosts();
    return this.posts.filter(post => post.featured);
  }

  // Get posts by tag
  async getPostsByTag(tag) {
    await this.initializePosts();
    return this.posts.filter(post => 
      post.tags && post.tags.includes(tag)
    );
  }
}

// Create singleton instance
const blogService = new BlogService();

export default blogService;
