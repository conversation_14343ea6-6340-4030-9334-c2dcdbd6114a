// API 基础配置
export const API_BASE_URL = '/v1'

// API 端点
export const ENDPOINTS = {
  // 数据类接口
  DATA: {
    LIST: '/data/list',
  },
  // HTML 片段接口
  HTML: {
    NEWS: '/html/news',
  },

  // 认证相关接口
  AUTH: {
    SIGNUP: '/auth/signup',
    LOGIN: '/auth/login',
    LOGOUT: '/auth/logout',
    OAUTH_LOGIN: '/auth/oauth/login',
    VERIFY_EMAIL: '/auth/verify-email',
    RESEND_CODE: '/auth/resend-code',
    CHECK: '/auth/check',
    SEND_RESET_CODE: '/auth/send-reset-code',
    RESET_PASSWORD: '/auth/reset-password'
  },

  // 用户相关接口
  USER: {
    INFO: '/user',
    UPDATE: '/user',
    PASSWORD: '/user/password',
    VERIFY: '/user/verify',
    GENERATE_METRO2: `/user/metro2/generate`,
    UPDATE_VIEW_TYPE: '/user/view-type',
    PAYINFO: '/user/payinfo'
  },
  
  // 物业相关接口
  PROPERTY: {
    CREATE: '/properties',
    LIST: '/properties',
    DETAIL: (id) => `/properties/${id}`,
    UPDATE: (id) => `/properties/${id}`,
    DELETE: (id) => `/properties/${id}`,
    CREATE_ROOM: (id) => `/properties/${id}/rooms`,
    DETAIL_ROOM: (id, roomId) => `/properties/${id}/rooms/${roomId}`,
    UPDATE_ROOM: (id, roomId) => `/properties/${id}/rooms/${roomId}`,
    DELETE_ROOM: (id, roomId) => `/properties/${id}/rooms/${roomId}`,
    UPLOAD_DOCUMENT: (id) => `/properties/${id}/document`,
    DELETE_DOCUMENT: (id) => `/properties/${id}/document`,
    GET_DELETED_DOCUMENTS: (id) => `/properties/${id}/documents/deleted`,
    RESTORE_DOCUMENT: (id, docId) => `/properties/${id}/documents/${docId}/restore`,
    ARCHIVE: (id) => `/properties/${id}/archive`
  },
  // 租约相关接口
  LEASE: {
    CREATE: '/leases',
    LIST: '/leases',
    DETAIL: (id) => `/leases/${id}`,
    UPDATE: (id) => `/leases/${id}`,
    DELETE: (id) => `/leases/${id}`,
    GET_TENANTS: (id) => `/leases/${id}/tenants`,
    DETAIL_TENANT: (id, tenantId) => `/leases/${id}/tenants/${tenantId}`,
    ADD_TENANT: (id) => `/leases/${id}/tenants`,
    UPDATE_TENANT: (id, tenantId) => `/leases/${id}/tenants/${tenantId}`,
    REMOVE_TENANT: (id, tenantId) => `/leases/${id}/tenants/${tenantId}`,
    GET_PAYMENTS: (id) => `/leases/${id}/tenant-payments`,
    ADD_PAYMENT: (id) => `/leases/${id}/tenant-payments`,
    UPDATE_PAYMENT: (id, paymentId) => `/leases/${id}/tenant-payments/${paymentId}`,
    REMOVE_PAYMENT: (id, paymentId) => `/leases/${id}/tenant-payments/${paymentId}`,
    GENERATE_METRO2: (id) => `/leases/${id}/metro2/generate`,
    TENANTS_UPDATE: (id, tenantId) => `/leases/${id}/tenants/${tenantId}`,
    TENANTS_SELF_UPDATE: (id) => `/leases/${id}/tenants/self`,
    // Deprecated endpoints (for backward compatibility)
    CTNTS_UPDATE: (id, tenantId) => `/leases/${id}/ctnts/${tenantId}`,
    UPLOAD_DOCUMENT: (id) => `/leases/${id}/document`,
    DELETE_DOCUMENT: (id) => `/leases/${id}/document`,
    GET_DELETED_DOCUMENTS: (id) => `/leases/${id}/documents/deleted`,
    RESTORE_DOCUMENT: (id, docId) => `/leases/${id}/documents/${docId}/restore`,
    DOWNLOAD_DOCUMENT: (fileId) => `/documents/${fileId}`,
    // Deprecated endpoint (for backward compatibility)
    DOWNLOAD_DOCUMENT_DEPRECATED: (fileId) => `/leases/download/${fileId}`
  },

  // 推荐码相关接口
  REFERRAL: {
    LIST: '/referral-codes',
    CREATE: '/referral-codes',
    APPLY: '/referral-code-applications',
    VALIDATE: (code) => `/referral-codes/${code}/validation`,
    // Deprecated endpoints (for backward compatibility)
    APPLY_DEPRECATED: '/referral-codes/apply',
    VALIDATE_DEPRECATED: '/referral-codes/validate'
  },

  // 邀请相关接口
  INVITATION: {
    LIST: '/invitations',
    CREATE: '/invitations',
    VERIFY: '/invitations/verify',
    UPDATE_STATUS: (code) => `/invitations/${code}/status`,
    // Deprecated endpoint (for backward compatibility)
    UPDATE_STATUS_DEPRECATED: '/invitations/update_status_by_code'
  }
}

// 响应状态码
export const STATUS = {
  SUCCESS: 1,
  ERROR: 0
}

// 默认配置
export const DEFAULT_CONFIG = {
  // Ajax 默认配置
  ajax: {
    dataType: 'json',
    method: 'GET'
  },
  // HTMX 默认配置
  htmx: {
    trigger: 'load once',
    swap: 'innerHTML'
  }
} 