// 消息相关的 API 服务
const InboxService = {
    // 获取收件箱消息列表
    getInboxMessages: async (page = 1, limit = 10) => {
        try {
            const response = await fetch(`/v1/messages/inbox?page=${page}&limit=${limit}`);
            if (!response.ok) throw new Error('Failed to fetch inbox messages');
            return await response.json();
        } catch (error) {
            throw error;
        }
    },

    // 获取已发送消息列表
    getSentMessages: async (page = 1, limit = 10) => {
        try {
            const response = await fetch(`/v1/messages/sent?page=${page}&limit=${limit}`);
            if (!response.ok) throw new Error('Failed to fetch sent messages');
            return await response.json();
        } catch (error) {
            throw error;
        }
    },

    // 搜索消息
    searchMessages: async (query, page = 1, limit = 10) => {
        try {
            const response = await fetch(`/v1/messages/search?q=${encodeURIComponent(query)}&page=${page}&limit=${limit}`);
            if (!response.ok) throw new Error('Failed to search messages');
            return await response.json();
        } catch (error) {
            throw error;
        }
    },

    // 获取消息详情
    getMessageDetail: async (messageId) => {
        try {
            const response = await fetch(`/v1/messages/${messageId}`);
            if (!response.ok) throw new Error('Failed to fetch message detail');
            return await response.json();
        } catch (error) {
            throw error;
        }
    },

    // 发送新消息
    sendMessage: async (messageData) => {
        try {
            const response = await fetch('/v1/messages/send', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(messageData),
            });
            
            const text = await response.text();
            let data;
            try {
                data = JSON.parse(text);
            } catch {
                data = null;
            }

            if (!response.ok) {
                throw new Error((data && data.message) || text || '发送消息失败');
            }
            
            return data;
        } catch (error) {
            throw error;
        }
    },

    // 搜索用户（用于收件人自动完成）
    searchUsers: async (query) => {
        try {
            const response = await fetch(`/v1/users/search?q=${encodeURIComponent(query)}`);
            if (!response.ok) throw new Error('Failed to search users');
            return await response.json();
        } catch (error) {
            throw error;
        }
    },

    // 获取消息模板
    getMessageTemplates: async () => {
        try {
            const response = await fetch('/v1/messages/templates');
            if (!response.ok) throw new Error('Failed to fetch message templates');
            return await response.json();
        } catch (error) {
            throw error;
        }
    },

    // 标记消息为已读
    markAsRead: async (messageId) => {
        try {
            const response = await fetch(`/v1/messages/${messageId}/read`, {
                method: 'POST',
            });
            if (!response.ok) throw new Error('Failed to mark message as read');
            return await response.json();
        } catch (error) {
            throw error;
        }
    },

    // 删除消息
    deleteMessage: async (messageId) => {
        try {
            const response = await fetch(`/v1/messages/${messageId}`, {
                method: 'DELETE',
            });
            if (!response.ok) throw new Error('Failed to delete message');
            return await response.json();
        } catch (error) {
            throw error;
        }
    }
};

export default InboxService; 