import { ENDPOINTS, DEFAULT_CONFIG } from './config'
import { request, createHtmxConfig } from './request'

// Authentication related API
export const authApi = {
  // Sign up new user
  signup: (userData) => request(ENDPOINTS.AUTH.SIGNUP, {
    ...DEFAULT_CONFIG.ajax,
    method: 'POST',
    data: JSON.stringify(userData),
    contentType: 'application/json'
  }),

  // Login user
  login: (credentials) => request(ENDPOINTS.AUTH.LOGIN, {
    ...DEFAULT_CONFIG.ajax,
    method: 'POST',
    data: JSON.stringify(credentials),
    contentType: 'application/json'
  }),

  // Verify email
  verifyEmail: (verificationData) => request(ENDPOINTS.AUTH.VERIFY_EMAIL, {
    ...DEFAULT_CONFIG.ajax,
    method: 'POST',
    data: JSON.stringify(verificationData),
    contentType: 'application/json'
  }),

  // Resend verification code
  resendCode: (emailData) => request(ENDPOINTS.AUTH.RESEND_CODE, {
    ...DEFAULT_CONFIG.ajax,
    method: 'POST',
    data: JSON.stringify(emailData),
    contentType: 'application/json'
  }),

  // Check auth status
  checkAuth: () => request(ENDPOINTS.AUTH.CHECK, {
    ...DEFAULT_CONFIG.ajax,
    method: 'GET'
  }),

  // Logout
  logout: () => request(ENDPOINTS.AUTH.LOGOUT, {
    ...DEFAULT_CONFIG.ajax,
    method: 'POST',
    contentType: 'application/json',
    data: JSON.stringify({})
  }),

  // Send reset password code
  sendResetCode: (emailData) => request(ENDPOINTS.AUTH.SEND_RESET_CODE, {
    ...DEFAULT_CONFIG.ajax,
    method: 'POST',
    data: JSON.stringify(emailData),
    contentType: 'application/json'
  }),

  // Reset password with verification code
  resetPassword: (resetData) => request(ENDPOINTS.AUTH.RESET_PASSWORD, {
    ...DEFAULT_CONFIG.ajax,
    method: 'POST',
    data: JSON.stringify(resetData),
    contentType: 'application/json'
  })
}

// user related API
export const userApi = {
  // get user info
  getUserInfo: () => request(ENDPOINTS.USER.INFO, {
    ...DEFAULT_CONFIG.ajax,
    method: 'GET'
  }),

  // update user info
  updateUser: (userData) => request(ENDPOINTS.USER.UPDATE, {
    ...DEFAULT_CONFIG.ajax,
    method: 'PUT',
    data: JSON.stringify(userData),
    contentType: 'application/json'
  }),

  // 修改密码
  updatePassword: (currentPassword, newPassword) => request(ENDPOINTS.USER.PASSWORD, {
    method: 'PUT',
    data: JSON.stringify({ currentPassword, newPassword }),
    contentType: 'application/json'
  }),

  // Generate Metro2 Report
  generateMetro2Report: () => request(ENDPOINTS.USER.GENERATE_METRO2, {
    ...DEFAULT_CONFIG.ajax,
    method: 'POST',
    responseType: 'blob'
  }),

  // 新增：切换视图类型
  updateViewType: (viewType) => request(ENDPOINTS.USER.UPDATE_VIEW_TYPE, {
    method: 'PUT',
    data: JSON.stringify({ viewType }),
    contentType: 'application/json',
    headers: {
      'Authorization': `Bearer ${localStorage.getItem('accessToken')}`
    }
  }),

  // 获取支付信息
  getPaymentInfo: () => request(ENDPOINTS.USER.PAYINFO, {
    ...DEFAULT_CONFIG.ajax,
    method: 'GET',
    headers: {
      'Authorization': `Bearer ${localStorage.getItem('accessToken')}`
    }
  })
}


// 物业相关 API
export const propertyApi = {
  // 获取物业列表
  getProperties: (limit = 1000) => {
    const url = `${ENDPOINTS.PROPERTY.LIST}?limit=${limit}`;
    return request(url, {
      ...DEFAULT_CONFIG.ajax,
      method: 'GET'
    });
  },

  // 创建新物业
  createProperty: (propertyData) => request(ENDPOINTS.PROPERTY.CREATE, {
    ...DEFAULT_CONFIG.ajax,
    method: 'POST',
    data: JSON.stringify(propertyData),
    contentType: 'application/json'
  }),

  // 获取单个物业详情
  getPropertyById: (propertyId) => request(ENDPOINTS.PROPERTY.DETAIL(propertyId), {
    ...DEFAULT_CONFIG.ajax,
    method: 'GET'
  }),

  // 更新物业信息
  updateProperty: (propertyId, propertyData) => request(ENDPOINTS.PROPERTY.UPDATE(propertyId), {
    ...DEFAULT_CONFIG.ajax,
    method: 'PUT',
    data: JSON.stringify(propertyData),
    contentType: 'application/json'
  }),

  // 检查物业是否可以删除
  checkPropertyDeletion: (propertyId) => request(`${ENDPOINTS.PROPERTY.DETAIL(propertyId)}/check-deletion`, {
    ...DEFAULT_CONFIG.ajax,
    method: 'GET'
  }),

  // 删除物业
  deleteProperty: (propertyId) => request(ENDPOINTS.PROPERTY.DELETE(propertyId), {
    ...DEFAULT_CONFIG.ajax,
    method: 'DELETE'
  }),

  // 添加房间
  createRoom: (propertyId, roomData) => request(ENDPOINTS.PROPERTY.CREATE_ROOM(propertyId), {
    ...DEFAULT_CONFIG.ajax,
    method: 'POST',
    data: JSON.stringify(roomData),
    contentType: 'application/json'
  }),

  // 获取房间详情
  getRoomById: (propertyId, roomId) => request(ENDPOINTS.PROPERTY.DETAIL_ROOM(propertyId, roomId), {
    ...DEFAULT_CONFIG.ajax,
    method: 'GET'
  }),

  // 更新房间
  updateRoom: (propertyId, roomId, roomData) => request(ENDPOINTS.PROPERTY.UPDATE_ROOM(propertyId, roomId), {
    ...DEFAULT_CONFIG.ajax,
    method: 'PUT',
    data: JSON.stringify(roomData),
    contentType: 'application/json'
  }),

  // 检查房间是否可以删除
  checkRoomDeletion: (propertyId, roomId) => request(`${ENDPOINTS.PROPERTY.DETAIL_ROOM(propertyId, roomId)}/check-deletion`, {
    ...DEFAULT_CONFIG.ajax,
    method: 'GET'
  }),

  // 删除房间
  deleteRoom: (propertyId, roomId) => request(ENDPOINTS.PROPERTY.DELETE_ROOM(propertyId, roomId), {
    ...DEFAULT_CONFIG.ajax,
    method: 'DELETE'
  }),

  // 上传文件
  uploadDocument: (propertyId, file) => {
    const formData = new FormData();
    formData.append('file', file);
    return request(ENDPOINTS.PROPERTY.UPLOAD_DOCUMENT(propertyId), {
      method: 'POST',
      data: formData,
      processData: false,
      contentType: false
    });
  },

  // 删除文档
  deleteDocument: (propertyId) => request(ENDPOINTS.PROPERTY.DELETE_DOCUMENT(propertyId), {
    ...DEFAULT_CONFIG.ajax,
    method: 'DELETE'
  }),

  // 删除特定文档
  deleteSpecificDocument: (propertyId, documentId) => request(`${ENDPOINTS.PROPERTY.DELETE_DOCUMENT(propertyId)}/${documentId}`, {
    ...DEFAULT_CONFIG.ajax,
    method: 'DELETE'
  }),

  // 获取已删除的文档（回收站）
  getDeletedDocuments: (propertyId) => request(ENDPOINTS.PROPERTY.GET_DELETED_DOCUMENTS(propertyId), {
    ...DEFAULT_CONFIG.ajax,
    method: 'GET'
  }),

  // 恢复已删除的文档
  restoreDocument: (propertyId, documentId) => request(ENDPOINTS.PROPERTY.RESTORE_DOCUMENT(propertyId, documentId), {
    ...DEFAULT_CONFIG.ajax,
    method: 'PUT',
    data: JSON.stringify({}),
    contentType: 'application/json'
  }),
}

// 租约相关 API
export const leaseApi = {
  // 获取租约列表
  getLeases: (params = {}) => {
    // Add default limit if not specified
    if (!params.limit) {
      params.limit = 1000;
    }
    const query = new URLSearchParams(params).toString();
    const url = query ? `${ENDPOINTS.LEASE.LIST}?${query}` : `${ENDPOINTS.LEASE.LIST}?limit=1000`;
    return request(url, {
      ...DEFAULT_CONFIG.ajax,
      method: 'GET'
    });
  },

  // 创建新租约
  createLease: (leaseData) => request(ENDPOINTS.LEASE.CREATE, {
    ...DEFAULT_CONFIG.ajax,
    method: 'POST',
    data: JSON.stringify(leaseData),
    contentType: 'application/json'
  }),

  // 获取单个租约详情
  getLeaseById: (leaseId) => request(ENDPOINTS.LEASE.DETAIL(leaseId), {
    ...DEFAULT_CONFIG.ajax,
    method: 'GET'
  }),

  // 更新租约信息
  updateLease: (leaseId, leaseData) => request(ENDPOINTS.LEASE.UPDATE(leaseId), {
    ...DEFAULT_CONFIG.ajax,
    method: 'PUT',
    data: JSON.stringify(leaseData),
    contentType: 'application/json'
  }),

  // 检查租约是否可以删除
  checkLeaseDeletion: (leaseId) => request(`${ENDPOINTS.LEASE.DETAIL(leaseId)}/check-deletion`, {
    ...DEFAULT_CONFIG.ajax,
    method: 'GET'
  }),

  // 删除租约
  deleteLease: (leaseId) => request(ENDPOINTS.LEASE.DELETE(leaseId), {
    ...DEFAULT_CONFIG.ajax,
    method: 'DELETE'
  }),

  // 获取租客列表
  getTenants: (leaseId) => request(ENDPOINTS.LEASE.GET_TENANTS(leaseId), {
    ...DEFAULT_CONFIG.ajax,
    method: 'GET'
  }),

  // 获取单个租客详情
  getTenantById: (leaseId, tenantId) => request(ENDPOINTS.LEASE.DETAIL_TENANT(leaseId, tenantId), {
    ...DEFAULT_CONFIG.ajax,
    method: 'GET'
  }),

  // 添加租客到租约
  addTenant: (leaseId, tenantData) => request(ENDPOINTS.LEASE.ADD_TENANT(leaseId), {
    ...DEFAULT_CONFIG.ajax,
    method: 'POST',
    data: JSON.stringify(tenantData),
    contentType: 'application/json'
  }),

  // 更新租客信息
  updateTenant: (leaseId, tenantId, tenantData) => request(ENDPOINTS.LEASE.UPDATE_TENANT(leaseId, tenantId), {
    ...DEFAULT_CONFIG.ajax,
    method: 'PUT',
    data: JSON.stringify(tenantData),
    contentType: 'application/json'
  }),

  // 删除租客
  removeTenant: (leaseId, tenantId) => request(ENDPOINTS.LEASE.REMOVE_TENANT(leaseId, tenantId), {
    ...DEFAULT_CONFIG.ajax,
    method: 'DELETE'
  }),

  // 上传租约文档
  uploadDocument: (leaseId, file) => {
    const formData = new FormData();
    formData.append('file', file);
    return request(ENDPOINTS.LEASE.UPLOAD_DOCUMENT(leaseId), {
      method: 'POST',
      data: formData,
      processData: false,
      contentType: false
    });
  },

  // 删除租约文档
  deleteDocument: (leaseId) => request(ENDPOINTS.LEASE.DELETE_DOCUMENT(leaseId), {
    ...DEFAULT_CONFIG.ajax,
    method: 'DELETE'
  }),

  // 删除特定租约文档
  deleteSpecificDocument: (leaseId, documentId) => request(`${ENDPOINTS.LEASE.DELETE_DOCUMENT(leaseId)}/${documentId}`, {
    ...DEFAULT_CONFIG.ajax,
    method: 'DELETE'
  }),

  // 获取已删除的租约文档（回收站）
  getDeletedDocuments: (leaseId) => request(ENDPOINTS.LEASE.GET_DELETED_DOCUMENTS(leaseId), {
    ...DEFAULT_CONFIG.ajax,
    method: 'GET'
  }),

  // 恢复已删除的租约文档
  restoreDocument: (leaseId, documentId) => request(ENDPOINTS.LEASE.RESTORE_DOCUMENT(leaseId, documentId), {
    ...DEFAULT_CONFIG.ajax,
    method: 'PUT',
    data: JSON.stringify({}),
    contentType: 'application/json'
  }),

  // 获取付款列表
  getPayments: (leaseId) => request(ENDPOINTS.LEASE.GET_PAYMENTS(leaseId), {
    ...DEFAULT_CONFIG.ajax,
    method: 'GET'
  }),
  

  // 添加付款到租约
  addPayment: (leaseId, paymentData) => request(ENDPOINTS.LEASE.ADD_PAYMENT(leaseId), {
    ...DEFAULT_CONFIG.ajax,
    method: 'POST',
    data: JSON.stringify(paymentData),
    contentType: 'application/json'
  }),

  // 更新付款信息
  updatePayment: (leaseId, paymentId, paymentData) => request(ENDPOINTS.LEASE.UPDATE_PAYMENT(leaseId, paymentId), {
    ...DEFAULT_CONFIG.ajax,
    method: 'PUT',
    data: JSON.stringify(paymentData),
    contentType: 'application/json'
  }),

  // 删除付款
  removePayment: (leaseId, paymentId) => request(ENDPOINTS.LEASE.REMOVE_PAYMENT(leaseId, paymentId), {
    ...DEFAULT_CONFIG.ajax,
    method: 'DELETE'
  }),

  // 创建邀请
  createInvitation: (invitationData) => request('/invitations', {
    ...DEFAULT_CONFIG.ajax,
    method: 'POST',
    data: JSON.stringify(invitationData),
    contentType: 'application/json'
  }),

  // 获取邀请列表
  getInvitations: (params) => request('/invitations', {
    ...DEFAULT_CONFIG.ajax,
    method: 'GET',
    params
  }),

  // 更新lease文档中的租客
  updateLeaseTenant: (leaseId, tenantId, tenantData) => request(ENDPOINTS.LEASE.TENANTS_UPDATE(leaseId, tenantId), {
    ...DEFAULT_CONFIG.ajax,
    method: 'PUT',
    data: JSON.stringify(tenantData),
    contentType: 'application/json'
  }),

  // 软删除lease文档中的租客（移动到past tenants）
  moveTenantToPast: (leaseId, tenantId) => request(`${ENDPOINTS.LEASE.TENANTS_UPDATE(leaseId, tenantId)}/soft`, {
    ...DEFAULT_CONFIG.ajax,
    method: 'DELETE'
  }),

  // 将past tenant移动回current tenants
  moveTenantToCurrent: (leaseId, tenantId) => request(`${ENDPOINTS.LEASE.TENANTS_UPDATE(leaseId, tenantId)}/restore`, {
    ...DEFAULT_CONFIG.ajax,
    method: 'PUT',
    data: JSON.stringify({}),
    contentType: 'application/json'
  }),

  // 硬删除lease文档中的租客（从ctnts中删除，同时删除tenant表中的记录）
  deleteLeaseTenant: (leaseId, tenantId) => request(ENDPOINTS.LEASE.TENANTS_UPDATE(leaseId, tenantId), {
    ...DEFAULT_CONFIG.ajax,
    method: 'DELETE'
  }),

  // 更新租客自己的ID
  updateTenantSelf: (leaseId) => request(ENDPOINTS.LEASE.TENANTS_SELF_UPDATE(leaseId), {
    ...DEFAULT_CONFIG.ajax,
    method: 'PUT'
  }),
}

// 导出所有 API
export default {
  property: propertyApi,
  lease: leaseApi
} 