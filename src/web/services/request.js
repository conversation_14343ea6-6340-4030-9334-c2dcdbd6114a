import $ from 'jquery'
import { API_BASE_URL, STATUS } from './config'

// 处理 API 响应
const handleResponse = (response) => {
  // 只要不是明确的 ok: 0 就算成功
  if (!response || response.ok === undefined || response.ok === 1) {
    return response || {};
  } else if (response.ok === 0) {
    throw new Error(response.message || 'failed to request');
  } else {
    return response;
  }
};

// 创建完整的 API URL
const createUrl = (endpoint) => `${API_BASE_URL}${endpoint}`

// Ajax 请求封装
export const request = async (endpoint, options = {}) => {
  try {
    // Use XMLHttpRequest for blob responses
    if (options.responseType === 'blob') {
      return new Promise((resolve, reject) => {
        const xhr = new XMLHttpRequest();
        xhr.open(options.method || 'GET', createUrl(endpoint), true);
        xhr.responseType = 'blob';
        
        xhr.onload = function() {
          if (this.status >= 200 && this.status < 300) {
            const contentType = xhr.getResponseHeader('Content-Type');
            const contentDisposition = xhr.getResponseHeader('Content-Disposition');
            resolve({
              blob: this.response,
              headers: {
                'Content-Type': contentType,
                'Content-Disposition': contentDisposition
              }
            });
          } else {
            reject(new Error('Failed to download file'));
          }
        };
        
        xhr.onerror = function() {
          reject(new Error('Network error occurred'));
        };
        
        xhr.send();
      });
    }

    // Use jQuery ajax for regular requests
    let responseData;
    let errorData;

    await $.ajax({
      url: createUrl(endpoint),
      ...options,
      success: function(data, textStatus, xhr) {
        const status = xhr.status;
        if (status === 204) {
          responseData = {};
        } else {
          responseData = data;
        }
      },
      error: function(xhr, textStatus, errorThrown) {
        // 捕获HTTP错误响应中的错误信息
        if (xhr.responseJSON && xhr.responseJSON.error) {
          errorData = xhr.responseJSON.error;
        } else if (xhr.responseText) {
          try {
            const parsed = JSON.parse(xhr.responseText);
            errorData = parsed.error || parsed.message || errorThrown;
          } catch (e) {
            errorData = errorThrown;
          }
        } else {
          errorData = errorThrown;
        }
      }
    });

    // 如果有错误数据，抛出包含错误信息的异常
    if (errorData) {
      throw new Error(errorData);
    }

    return handleResponse(responseData);
  } catch (error) {
    // 如果是jQuery的XMLHttpRequest错误对象，提取错误信息
    if (error.responseJSON && error.responseJSON.error) {
      throw new Error(error.responseJSON.error);
    } else if (error.responseText) {
      try {
        const parsed = JSON.parse(error.responseText);
        throw new Error(parsed.error || parsed.message || 'Request failed');
      } catch (parseError) {
        throw new Error(error.statusText || 'Request failed');
      }
    } else if (error.message) {
      throw error; // 已经是Error对象
    } else {
      throw new Error('Request failed');
    }
  }
}

// HTMX 配置生成器
export const createHtmxConfig = (endpoint, options = {}) => ({
  'hx-get': createUrl(endpoint),
  ...options
}) 