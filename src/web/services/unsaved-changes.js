// Unsaved Changes Detection Service
import $ from 'jquery'
import Mustache from 'mustache'
import unsavedChangesModalTemplate from '../templates/unsaved-changes-modal.mst?raw'

class UnsavedChangesDetector {
  constructor() {
    this.hasUnsavedChanges = false
    this.initialFormState = {}
    this.pendingNavigation = null
    this.isNavigating = false
    this.formSelectors = [
      'input:not([type="submit"]):not([type="button"]):not([type="reset"])', // All input types except buttons
      'textarea',
      'select'
    ]
    // Initialize default options to prevent undefined errors
    this.options = {
      excludeSelectors: [],
      includeFileChanges: false
    }
    this.initialized = false
    // Track initial file state for pending uploads/deletes
    this.initialFileState = {
      pendingUploads: 0,
      pendingDeletes: 0
    }
  }

  // Initialize the detector for a page
  init(options = {}) {

    this.options = {
      excludeSelectors: options.excludeSelectors || [],
      includeFileChanges: options.includeFileChanges !== false,
      ...options
    }
    this.initialized = true

    // Capture initial form state
    this.captureInitialState()

    // Set up change listeners
    this.setupChangeListeners()

    // Set up navigation guards
    this.setupNavigationGuards()

    // Set up save button listener to reset unsaved changes
    this.setupSaveButtonListener()
  }

  // Capture the initial state of all form fields
  captureInitialState() {
    const formElements = $(this.formSelectors.join(', '))

    this.initialFormState = {} // Reset state

    formElements.each((index, element) => {
      const $el = $(element)
      const id = $el.attr('id') || $el.attr('name') || `element_${index}`

      // Skip excluded selectors
      if (this.options.excludeSelectors.some(selector => $el.is(selector))) {
        return
      }

      if ($el.is('input[type="checkbox"], input[type="radio"]')) {
        this.initialFormState[id] = $el.is(':checked')
      } else if ($el.is('input[type="file"]')) {
        // For file inputs, we'll track if a file was selected
        this.initialFormState[id] = $el[0].files.length > 0 ? $el[0].files[0].name : ''
      } else {
        this.initialFormState[id] = $el.val() || ''
      }
    })

    // Capture initial file state for pending uploads/deletes
    if (this.options.includeFileChanges) {
      this.captureInitialFileState()
    }
  }

  // Capture initial file state for pending uploads/deletes
  captureInitialFileState() {
    // Check if global file arrays exist (they should be available in property/lease detail pages)
    if (typeof window.pendingUploads !== 'undefined' && typeof window.pendingDeletes !== 'undefined') {
      this.initialFileState = {
        pendingUploads: window.pendingUploads.length,
        pendingDeletes: window.pendingDeletes.length
      }
    } else {
      this.initialFileState = {
        pendingUploads: 0,
        pendingDeletes: 0
      }
    }
  }

  // Set up listeners for form field changes
  setupChangeListeners() {
    const self = this
  

    // Test if we can find form elements
    const testElements = $(this.formSelectors.join(', '))
  

    // Try both document delegation and direct binding
    // Document delegation for dynamically added elements
    $(document).on('input change keyup', this.formSelectors.join(', '), function() {
      const $el = $(this)
      // Skip excluded selectors
      if (self.options.excludeSelectors.some(selector => $el.is(selector))) {
        return
      }

      self.checkForChanges()
    })

    // Direct binding for existing elements
    testElements.on('input change keyup', function() {
      const $el = $(this)
  

      // Skip excluded selectors
      if (self.options.excludeSelectors.some(selector => $el.is(selector))) {
        return
      }

      self.checkForChanges()
    })

    // Special handling for file inputs
    if (this.options.includeFileChanges) {
      $(document).on('change', 'input[type="file"]', function() {
        self.checkForChanges()
      })
    }
  }

  // Check if current form state differs from initial state
  checkForChanges() {
    const formElements = $(this.formSelectors.join(', '))
    let hasChanges = false

    formElements.each((index, element) => {
      const $el = $(element)
      const id = $el.attr('id') || $el.attr('name') || `element_${index}`

      // Skip excluded selectors
      if (this.options.excludeSelectors.some(selector => $el.is(selector))) {
        return
      }

      let currentValue
      if ($el.is('input[type="checkbox"], input[type="radio"]')) {
        currentValue = $el.is(':checked')
      } else if ($el.is('input[type="file"]')) {
        currentValue = $el[0].files.length > 0 ? $el[0].files[0].name : ''
      } else {
        currentValue = $el.val() || ''
      }

      const initialValue = this.initialFormState[id]

      if (currentValue !== initialValue) {
        hasChanges = true
        return false // Break out of each loop
      }
    })

    // Check for file changes (pending uploads/deletes) if enabled
    if (this.options.includeFileChanges && !hasChanges) {
      hasChanges = this.checkForFileChanges()
    }

    this.hasUnsavedChanges = hasChanges
  }

  // Check if there are pending file uploads or deletes
  checkForFileChanges() {
    // Check if global file arrays exist
    if (typeof window.pendingUploads !== 'undefined' && typeof window.pendingDeletes !== 'undefined') {
      const currentPendingUploads = window.pendingUploads.length
      const currentPendingDeletes = window.pendingDeletes.length

      return (currentPendingUploads !== this.initialFileState.pendingUploads ||
              currentPendingDeletes !== this.initialFileState.pendingDeletes)
    }
    return false
  }

  // Set up navigation guards
  setupNavigationGuards() {
    const self = this

    // Browser navigation (back/forward/refresh)
    // Note: We disable the native browser dialog since we have custom modal handling
    // for internal navigation. For actual page unload, we let it proceed without confirmation.
    $(window).on('beforeunload', function(e) {
      // We don't prevent browser navigation to avoid the native dialog
      // Our custom modal handles internal navigation instead
      return undefined
    })

    // Internal navigation (clicking links)
    $(document).on('click', 'a[href]:not([href^="#"]):not([target="_blank"])', function(e) {
      if (self.hasUnsavedChanges && !self.isNavigating) {
        e.preventDefault()
        self.pendingNavigation = $(this).attr('href')
        self.showUnsavedChangesModal()
      }
    })

    // Sidebar navigation
    $(document).on('click', '#sidebar-nav a[href]:not([href^="#"])', function(e) {
      if (self.hasUnsavedChanges && !self.isNavigating) {
        e.preventDefault()
        self.pendingNavigation = $(this).attr('href')
        self.showUnsavedChangesModal()
      }
    })
  }

  // Set up save button listener
  setupSaveButtonListener() {
    const self = this

    // Listen for save button clicks to reset unsaved changes
    // Include various save button selectors used across the app
    const saveButtonSelectors = [
      '[id*="save"]',
      '.save-btn',
      'button:contains("Save")',
      '#save-property-btn',
      '.fixed.bottom-0 .bg-slate-800.text-white' // Lease detail save button
    ].join(', ')

    $(document).on('click', saveButtonSelectors, function() {
      // Reset unsaved changes when save is clicked
      setTimeout(() => {
        self.hasUnsavedChanges = false
        // Only capture initial state if the detector has been initialized
        if (self.initialized) {
          self.captureInitialState() // Recapture state after save
        }
      }, 100)
    })
  }

  // Show the unsaved changes modal
  showUnsavedChangesModal() {
    const modalHtml = Mustache.render(unsavedChangesModalTemplate)
    $('#modal').html(modalHtml)
    
    // Disable background scrolling
    document.body.style.overflow = 'hidden'
    document.documentElement.style.overflow = 'hidden'

    // Set up modal button handlers
    this.setupModalHandlers()
  }

  // Set up modal button handlers
  setupModalHandlers() {
    const self = this

    // Cancel button, close button, and overlay - close modal and stay on page
    $(document).on('click', '#cancel-unsaved-changes-btn, #unsaved-changes-close, #unsaved-changes-overlay', function() {
      self.closeModal()
      self.pendingNavigation = null
    })

    // Discard changes button - proceed with navigation
    $(document).on('click', '#discard-changes-btn', function() {
      self.isNavigating = true
      self.hasUnsavedChanges = false
      self.closeModal()

      if (self.pendingNavigation) {
        window.location.href = self.pendingNavigation
      }
    })
  }

  // Close the modal
  closeModal() {
    $('#modal').html('')
    
    // Re-enable background scrolling
    document.body.style.overflow = ''
    document.documentElement.style.overflow = ''
  }

  // Manually mark as having unsaved changes
  markAsChanged() {
    this.hasUnsavedChanges = true
  }

  // Manually mark as saved
  markAsSaved() {
    this.hasUnsavedChanges = false
    // Only capture initial state if the detector has been initialized
    if (this.initialized) {
      this.captureInitialState()
    }
  }

  // Check if there are unsaved changes
  hasChanges() {
    return this.hasUnsavedChanges
  }

  // Destroy the detector
  destroy() {
    $(window).off('beforeunload')
    $(document).off('click', 'a[href]:not([href^="#"]):not([target="_blank"])')
    $(document).off('click', '#sidebar-nav a[href]:not([href^="#"])')
    $(document).off('input change', this.formSelectors.join(', '))
    $(document).off('change', 'input[type="file"]')
    $(document).off('click', '[id*="save"], .save-btn, button:contains("Save")')
    $(document).off('click', '#cancel-unsaved-changes-btn')
    $(document).off('click', '#discard-changes-btn')
  }
}

// Export singleton instance
export const unsavedChangesDetector = new UnsavedChangesDetector()
