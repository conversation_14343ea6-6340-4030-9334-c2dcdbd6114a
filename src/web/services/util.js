import { authApi } from './index'
import { STATUS } from './config'
import Mustache from 'mustache'
import paginationTemplate from '../templates/pagination.mustache?raw'
import paginationNumbersTemplate from '../templates/pagination_numbers.mustache?raw'

// Pagination and search utilities

/**
 * Configuration for pagination
 * @typedef {Object} PaginationConfig
 * @property {number} itemsPerPage - Number of items per page
 * @property {number} currentPage - Current page number (1-based)
 * @property {number} totalItems - Total number of items
 * @property {Function} onPageChange - Callback when page changes
 * @property {string} containerSelector - Selector for pagination container
 */

/**
 * Initialize pagination controls
 * @param {PaginationConfig} config - Pagination configuration
 */
export function initPagination(config) {
    const totalPages = Math.ceil(config.totalItems / config.itemsPerPage);
    const container = document.querySelector(config.containerSelector);
    
    if (!container) return;

    // Prepare data for templates
    const templateData = {
        startItem: Math.min((config.currentPage - 1) * config.itemsPerPage + 1, config.totalItems),
        endItem: Math.min(config.currentPage * config.itemsPerPage, config.totalItems),
        totalItems: config.totalItems,
        isFirstPage: config.currentPage <= 1,
        isLastPage: config.currentPage >= totalPages,
        pages: generatePaginationArray(config.currentPage, totalPages)
    };

    // Register the partial template and render
    Mustache.parse(paginationTemplate);
    Mustache.parse(paginationNumbersTemplate);
    
    // Render the template
    container.innerHTML = Mustache.render(paginationTemplate, templateData, {
        pagination_numbers: paginationNumbersTemplate
    });

    // Add event listeners
    container.querySelectorAll('.pagination-btn.prev').forEach(btn => {
        btn.addEventListener('click', () => {
            if (config.currentPage > 1) {
                config.onPageChange(config.currentPage - 1);
            }
        });
    });

    container.querySelectorAll('.pagination-btn.next').forEach(btn => {
        btn.addEventListener('click', () => {
            if (config.currentPage < totalPages) {
                config.onPageChange(config.currentPage + 1);
            }
        });
    });

    container.querySelectorAll('.pagination-btn.page').forEach(btn => {
        btn.addEventListener('click', (event) => {
            const pageNum = parseInt(event.target.dataset.page);
            if (pageNum !== config.currentPage) {
                config.onPageChange(pageNum);
            }
        });
    });
}

/**
 * Sort an array of items by a field
 * @param {Array} items - Array of items to sort
 * @param {string} field - Field to sort by
 * @param {boolean} ascending - Whether to sort in ascending order
 * @returns {Array} Sorted array
 */
export function sortItems(items, field, ascending = true) {
  // Make a copy of the array to avoid modifying the original
  const sortedItems = [...items];
  
  return sortedItems.sort((a, b) => {
    // Handle nested fields using dot notation (e.g., "address.city")
    const fieldParts = field.split('.');
    let valueA = a;
    let valueB = b;
    
    // Navigate through nested objects
    for (const part of fieldParts) {
      valueA = valueA?.[part];
      valueB = valueB?.[part];
    }
    
    // Handle numeric values
    if (typeof valueA === 'number' && typeof valueB === 'number') {
      return ascending ? valueA - valueB : valueB - valueA;
    }
    
    // Handle string values
    if (typeof valueA === 'string' && typeof valueB === 'string') {
      return ascending 
        ? valueA.localeCompare(valueB) 
        : valueB.localeCompare(valueA);
    }
    
    // Handle null/undefined values
    if (valueA == null && valueB == null) return 0;
    if (valueA == null) return ascending ? -1 : 1;
    if (valueB == null) return ascending ? 1 : -1;
    
    // Default comparison
    return ascending 
      ? String(valueA).localeCompare(String(valueB))
      : String(valueB).localeCompare(String(valueA));
  });
}

/**
 * Filter and paginate a list of items
 * @param {Array} items - Array of items to filter and paginate
 * @param {string} searchQuery - Search query string
 * @param {number} currentPage - Current page number (1-based)
 * @param {number} itemsPerPage - Number of items per page
 * @param {Function} searchPredicate - Function to determine if an item matches the search query
 * @param {Object} sortOptions - Sort options {field, ascending}
 * @returns {Object} Filtered and paginated items with metadata
 */
export function filterAndPaginate(items, searchQuery, currentPage, itemsPerPage, searchPredicate, sortOptions = null) {
    // Filter items based on search query
    const filteredItems = searchQuery 
        ? items.filter(item => searchPredicate(item, searchQuery.toLowerCase()))
        : items;

    // Sort items if sort options are provided
    const sortedItems = sortOptions && sortOptions.field
        ? sortItems(filteredItems, sortOptions.field, sortOptions.ascending)
        : filteredItems;

    // Calculate pagination
    const totalItems = sortedItems.length;
    const totalPages = Math.ceil(totalItems / itemsPerPage);
    const startIndex = (currentPage - 1) * itemsPerPage;
    const endIndex = Math.min(startIndex + itemsPerPage, totalItems);
    const currentStartItem = totalItems > 0 ? startIndex + 1 : 0;
    const currentEndItem = Math.min(currentPage * itemsPerPage, totalItems);

    // Get items for current page
    const paginatedItems = sortedItems.slice(startIndex, endIndex);

    // Generate pagination data for template
    const paginationData = {
        currentPage,
        totalPages,
        totalItems,
        currentStartItem,
        currentEndItem,
        itemsPerPage,
        showingText: `Showing ${currentStartItem} to ${currentEndItem} of ${totalItems} entries`,
        hasPreviousPage: currentPage > 1,
        hasNextPage: currentPage < totalPages,
        pages: generatePaginationArray(currentPage, totalPages)
    };

    return {
        items: paginatedItems,
        pagination: paginationData
    };
}

/**
 * Generate an array representing pagination
 * @param {number} currentPage - Current page number
 * @param {number} totalPages - Total number of pages
 * @returns {Array} Array of page numbers and ellipses for rendering pagination
 */
function generatePaginationArray(currentPage, totalPages) {
    const pages = [];
    const maxVisiblePages = 5;

    if (totalPages <= maxVisiblePages) {
        // Show all pages if total pages is less than or equal to max visible pages
        for (let pageNum = 1; pageNum <= totalPages; pageNum++) {
            pages.push({
                isEllipsis: false,
                number: pageNum,
                isCurrent: pageNum === currentPage
            });
        }
    } else {
        // Always show first page
        pages.push({
            isEllipsis: false,
            number: 1,
            isCurrent: 1 === currentPage
        });

        // Calculate start and end of visible pages
        let start = Math.max(2, currentPage - 1);
        let end = Math.min(totalPages - 1, currentPage + 1);

        // Adjust start and end to show maxVisiblePages - 2 pages (excluding first and last)
        while (end - start + 1 < maxVisiblePages - 2) {
            if (start > 2) start--;
            else if (end < totalPages - 1) end++;
            else break;
        }

        // Add ellipsis if needed
        if (start > 2) {
            pages.push({ isEllipsis: true });
        }
        
        // Add middle pages
        for (let pageNum = start; pageNum <= end; pageNum++) {
            pages.push({
                isEllipsis: false,
                number: pageNum,
                isCurrent: pageNum === currentPage
            });
        }

        // Add ellipsis if needed
        if (end < totalPages - 1) {
            pages.push({ isEllipsis: true });
        }

        // Always show last page
        pages.push({
            isEllipsis: false,
            number: totalPages,
            isCurrent: totalPages === currentPage
        });
    }

    return pages;
}

/**
 * Initialize search functionality
 * @param {string} searchInputSelector - Selector for search input element
 * @param {Function} onSearch - Callback when search query changes
 */
export function initSearch(searchInputSelector, onSearch) {
    const searchInput = document.querySelector(searchInputSelector);
    if (!searchInput) return;

    let debounceTimeout;

    searchInput.addEventListener('input', (event) => {
        clearTimeout(debounceTimeout);
        debounceTimeout = setTimeout(() => {
            onSearch(event.target.value.trim());
        }, 300); // Debounce for 300ms
    });
}

// Check authentication status and redirect to home or landing page
export async function checkAuthStatusAndRedirect() {
    // Get current path once at the start
    const currentPath = window.location.pathname;
    
    try {
      const response = await authApi.checkAuth();
      
      if (response.ok === STATUS.SUCCESS) {
        // If authenticated and not already on home page, redirect to home
        if (!currentPath.includes(window.config.baseUrl + '/pages/home/')) {
          window.location.href = window.config.baseUrl + '/pages/home/';
        }
      } else {
        // If not authenticated, only redirect to index if not on login or index page
        if (currentPath !== '/' && !currentPath.includes('/login') && !currentPath.includes('/auth')) {
          window.location.href = '/';
        }
      }
    } catch (error) {
      // Only redirect if not on index, login, or auth pages
      if (currentPath !== '/' && !currentPath.includes('/login') && !currentPath.includes('/auth')) {
        window.location.href = '/';
      }
    }
}

// Check if user is logged in and redirect to home if they are
// This is for landing pages that logged-in users shouldn't see
export async function redirectLoggedInUsersToHome() {
  try {
    const response = await authApi.checkAuth();

    if (response.ok === STATUS.SUCCESS) {
      // User is logged in, redirect to home
      window.location.href = window.config.baseUrl + '/pages/home/';
      return true; // Indicates redirect happened
    }
    return false; // User not logged in, no redirect
  } catch (error) {
    // If auth check fails, user is not logged in
    return false;
  }
}

// Utility function to perform complete logout with localStorage cleanup
export async function performLogout(authApi) {
  try {
    // Call logout API
    await authApi.logout();
  } catch (error) {
    // Continue with cleanup even if API fails
  }

  // Clear all localStorage data to prevent cross-user contamination
  localStorage.removeItem('user');
  localStorage.removeItem('accessToken');

  // Clear any other user-related data that might be cached
  if (typeof window !== 'undefined') {
    window.currentUser = null;
  }

  // Redirect to login page
  window.location.href = window.config.baseUrl + '/pages/login/';
}