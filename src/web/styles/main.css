@tailwind base;
@tailwind components;
@tailwind utilities;

/* 发送消息弹窗样式 */
.fieldset {
  @apply border border-gray-300 rounded-md p-4 mb-4;
}

.fieldset-legend {
  @apply text-sm font-medium text-gray-700 px-2;
}

.input {
  @apply flex items-center gap-2 px-3 py-2 border border-gray-300 rounded-md bg-white;
}

.select {
  @apply w-full px-3 py-2 border border-gray-300 rounded-md bg-white;
}

/* 弹窗动画 */
.modal-enter {
  animation: modal-enter 0.2s ease-out;
}

.modal-leave {
  animation: modal-leave 0.2s ease-in;
}

@keyframes modal-enter {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes modal-leave {
  from {
    opacity: 1;
    transform: scale(1);
  }
  to {
    opacity: 0;
    transform: scale(0.95);
  }
}

/* 移动端滚动优化 */
@media (max-width: 768px) {
  /* 优化移动端滚动性能 */
  * {
    -webkit-overflow-scrolling: touch;
  }

  /* 防止滚动边界反弹 */
  body {
    overscroll-behavior: none;
  }

  /* 确保modal在移动端正确滚动 */
  .modal-container {
    height: 100vh;
    height: 100dvh; /* 动态视口高度，支持的浏览器会使用这个 */
  }

  /* 确保main内容不被mobile header遮挡 */
  main {
    padding-top: 4rem; /* 为mobile header留出空间 */
  }
}

/* 桌面端navbar固定定位优化 */
@media (min-width: 1024px) {
  /* 确保navbar固定在左侧 */
  #sidebar-nav {
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    height: 100vh !important;
    z-index: 40 !important;
  }
}

/* 自定义横向滚动条样式 */
.scrollbar-thin {
  scrollbar-width: thin;
  scrollbar-color: #cbd5e1 #f1f5f9;
}
.scrollbar-thumb-gray-300::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 8px;
}
.scrollbar-track-gray-100::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 8px;
}
.scrollbar-thin::-webkit-scrollbar {
  height: 8px;
  background: #f1f5f9;
  border-radius: 8px;
}
.scrollbar-thin::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 8px;
} 