<div class="max-w-[1440px] w-full mx-auto mb-32">
    <div class="flex-1 mt-20 lg:mt-8 pb-0 w-full">
        <h1 class="text-2xl">Current Lease</h1>
        {{#currentLease}}
        {{#verified}}
        <a href="../lease_detail/?id={{id}}" style="display:block;text-decoration:none;">
          <div class="flex w-full items-center bg-white border rounded-md p-6 mt-4 justify-between hover:bg-slate-50 cursor-pointer">
            <div class="flex gap-4 item-center">
              <div class="flex flex-col justify-center">
                <span class="text-xs flex items-center gap-1 mb-1 inline-block" style="color:#219653;background:#E9F9EE;border-radius:4px;padding:2px 8px 2px 6px;max-width:max-content;">
                  <svg xmlns="http://www.w3.org/2000/svg" class="inline w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="#219653" stroke-width="2"><path d="M5 13l4 4L19 7" stroke="#219653" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" fill="none"/></svg>
                  Verified Lease
                </span>
                <h2 class="text-lg font-semibold">{{address}}</h2>
                <div class="flex gap-4 text-sm">
                  <p>{{leasePeriod}}</p><p>{{landlordName}}</p>
                </div>
              </div>
            </div>
          </div>
        </a>
        {{/verified}}
        {{^verified}}
        <div class="flex w-full items-center bg-white border rounded-md p-6 mt-4 justify-between">
          <div class="flex gap-4 item-center">
            <div class="flex flex-col justify-center">
              {{#pendingVerification}}
              <span class="text-xs flex items-center gap-1 mb-1 inline-block" style="color:#CE9718;background:rgba(206,151,24,0.08);border-radius:4px;padding:2px 8px 2px 6px;max-width:max-content;">
                <svg xmlns="http://www.w3.org/2000/svg" class="inline w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="#CE9718" stroke-width="2"><circle cx="12" cy="12" r="10" stroke="#CE9718" stroke-width="2" fill="none"/><path stroke="#CE9718" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" d="M12 6v6l4 2"/></svg>
                Pending Verification
              </span>
              {{/pendingVerification}}
              <h2 class="text-lg font-semibold">{{address}}</h2>
              <div class="flex gap-4 text-sm">
                <p>{{leasePeriod}}</p><p>{{landlordName}}</p>
              </div>
            </div>
          </div>
          <div class="text-right">
            <p>Overdue Balance: ${{overdueBalance}}</p>
            <p class="italic text-sm">{{paymentStatus}}</p>
          </div>
        </div>
        {{/verified}}
        {{/currentLease}}
    </div>

    <div class="flex-1 mt-20 lg:mt-8 pb-0 w-full">
        <h1 class="text-xl">Past Leases</h1>
        {{#pastLeases}}
        <a href="../lease_detail/?id={{id}}" style="display:block;text-decoration:none;">
          <div class="flex w-full items-center bg-white border rounded-md p-6 mt-4 justify-between hover:bg-slate-50 cursor-pointer">
            <div class="flex gap-4 item-center">
                <div class="flex flex-col justify-center">
                    {{#verified}}
                    <span class="text-xs flex items-center gap-1 mb-1 inline-block" style="color:#219653;background:#E9F9EE;border-radius:4px;padding:2px 8px 2px 6px;max-width:max-content;">
                      <svg xmlns="http://www.w3.org/2000/svg" class="inline w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="#219653" stroke-width="2"><path d="M5 13l4 4L19 7" stroke="#219653" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" fill="none"/></svg>
                      Verified Lease
                    </span>
                    {{/verified}}
                    {{#pendingVerification}}
                    <span class="text-xs text-yellow-700 bg-yellow-100 rounded px-2 py-1 mb-1 inline-block">Pending Verification</span>
                    {{/pendingVerification}}
                    <h2 class="text-lg font-semibold">{{address}}</h2>
                    <div class="flex gap-4 text-sm">
                        <p>{{leasePeriod}}</p><p>{{landlordName}}</p>
                    </div>
                </div>
            </div>
            <div></div>
          </div>
        </a>
        {{/pastLeases}}
    </div>

    <!-- 固定底部按钮栏 -->
    <div class="fixed bottom-0 right-0 lg:left-64 w-full lg:w-[calc(100%-16rem)] bg-white border-t flex justify-between items-center px-8 py-4 z-50" style="box-shadow: 0 -2px 8px 0 rgba(0,0,0,0.03);">
        <button class="border px-6 py-2 rounded" id="download-record-btn">Download Record</button>
        <button class="bg-black text-white px-6 py-2 rounded" id="add-lease-btn">Add Lease</button>
    </div>
</div> 